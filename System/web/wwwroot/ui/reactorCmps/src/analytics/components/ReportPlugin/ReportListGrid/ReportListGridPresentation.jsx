import { partition } from 'lodash';
import PropTypes from 'prop-types';
import React, { memo, useMemo } from 'react';
import EmptyState from 'reactor2/src/Atomic/components/Atoms/EmptyState';
import useTokenManager from 'reactor2/src/Atomic/components/Helpers/Language/useTokenManager';
import { EMPTYSTATE_IMAGE_RESULTS } from 'reactor2/src/constants/emptyStateImageConstants';
import { spacing } from 'reactor2/src/Styles/styleVariables';
import useReportListSearchFilter from 'reactorCmps/src/analytics/components/ReportPlugin/ReportCard/hooks/useReportListSearchFilter';
import ReportCard from 'reactorCmps/src/analytics/components/ReportPlugin/ReportCard/ReportCard';
import { getReportActivateWhenMapping, isNativeCubeReport } from 'reactorCmps/src/analytics/helpers/Report/reportHelpers';

const containerStyle = {
	display: 'grid',
	gap: spacing.spacing4,
	gridTemplateColumns: 'repeat(2, minmax(auto, 180px))',
	justifyContent: 'center',
	alignItems: 'center'
};

function ReportListGridPresentation({ defaultframeData, filter, search, setReport }) {
	const getToken = useTokenManager();
	const filteredReports = useReportListSearchFilter(filter, search);

	const reportActivateWhenMapping = useMemo(() => {
		return getReportActivateWhenMapping(defaultframeData);
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [defaultframeData.data.length, defaultframeData.selected.length]);

	const cards = useMemo(() => {
		return partition(filteredReports, isNativeCubeReport)
			.flat()
			.map(report => (
				<ReportCard
					activateWhen={report.activateWhen}
					description={report.description}
					disabled={!reportActivateWhenMapping[report.activateWhen]}
					icon={report.icon}
					isCustomized={report.isCustomized}
					isFavorite={report.isFavorite}
					key={report.oid + '_' + report.identifier}
					name={report.name}
					onClick={() => setReport(report)}
					testSelector={report.identifier}
					type={report.type}
				/>
			));
	}, [filteredReports, reportActivateWhenMapping, setReport]);

	const reportListGridContent = useMemo(() => {
		if (filteredReports.length === 0)
			return <EmptyState infoMessage={getToken('307366')} image={EMPTYSTATE_IMAGE_RESULTS} />;

		return (
			<section style={containerStyle} data-test-selector={'reportsList'}>
				{ cards }
			</section>
		);
	}, [cards, filteredReports.length, getToken]);

	return reportListGridContent;
}

ReportListGridPresentation.propTypes = {
	defaultframeData: PropTypes.object.isRequired,
	setReport: PropTypes.func.isRequired,
	filter: PropTypes.string,
	search: PropTypes.string
};

ReportListGridPresentation.displayName = 'Analytics/components/ReportPlugin/ReportListGrid/ReportListGridPresentation';

export default memo(ReportListGridPresentation);