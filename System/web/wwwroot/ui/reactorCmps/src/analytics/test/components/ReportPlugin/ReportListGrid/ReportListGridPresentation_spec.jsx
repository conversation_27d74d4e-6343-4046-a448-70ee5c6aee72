/* eslint-env mocha */
import React from 'react';
import { expect } from 'chai';
import { mount } from 'enzyme';

import ReportListGridPresentation from 'reactorCmps/src/analytics/components/ReportPlugin/ReportListGrid/ReportListGridPresentation';

import * as useTokenManagerModule from 'reactor2/src/Atomic/components/Helpers/Language/useTokenManager';
import * as useReportListSearchFilterModule from 'reactorCmps/src/analytics/components/ReportPlugin/ReportCard/hooks/useReportListSearchFilter';
import * as reportHelpersModule from 'reactorCmps/src/analytics/helpers/Report/reportHelpers';
import EmptyState from 'reactor2/src/Atomic/components/Atoms/EmptyState';
import ReportCard from 'reactorCmps/src/analytics/components/ReportPlugin/ReportCard/ReportCard';

describe('<ReportListGridPresentation />', () => {
	let getTokenStub, useReportListSearchFilterStub, getReportActivateWhenMappingStub, isNativeCubeReportStub;
	let defaultframeData, setReportSpy;

	beforeEach(() => {
		getTokenStub = sinon.stub().returns('EmptyState message');
		sinon.stub(useTokenManagerModule, 'default').returns(getTokenStub);

		useReportListSearchFilterStub = sinon.stub(useReportListSearchFilterModule, 'default');

		getReportActivateWhenMappingStub = sinon.stub(reportHelpersModule, 'getReportActivateWhenMapping');
		isNativeCubeReportStub = sinon.stub(reportHelpersModule, 'isNativeCubeReport');

		setReportSpy = sinon.spy();

		defaultframeData = {
			data: [{ foo: 'bar' }],
			selected: [1, 2, 3]
		};
	});

	afterEach(() => {
		sinon.restore();
	});

	it('renders <EmptyState /> when there are no filtered reports', () => {
		useReportListSearchFilterStub.returns([]);
		getReportActivateWhenMappingStub.returns({});

		const wrapper = mount(
			<ReportListGridPresentation
				defaultframeData={defaultframeData}
				filter={'abc'}
				search={'def'}
				setReport={setReportSpy}
			/>
		);

		expect(wrapper.find(EmptyState)).to.have.lengthOf(1);
		expect(wrapper.find(EmptyState).props().infoMessage).to.equal('EmptyState message');
	});

	it('renders all <ReportCard /> with correct props when filtered reports exist', () => {
		const filteredReports = [
			{
				oid: 1,
				identifier: 'AAA',
				activateWhen: 'always',
				description: 'desc1',
				icon: 'icon1',
				isCustomized: false,
				isFavorite: true,
				name: 'Name1',
				type: 'native'
			},
			{
				oid: 2,
				identifier: 'BBB',
				activateWhen: 'never',
				description: 'desc2',
				icon: 'icon2',
				isCustomized: true,
				isFavorite: false,
				name: 'Name2',
				type: 'notNative'
			}
		];

		useReportListSearchFilterStub.returns(filteredReports);

		isNativeCubeReportStub.onFirstCall().returns(true);
		isNativeCubeReportStub.onSecondCall().returns(false);

		getReportActivateWhenMappingStub.returns({
			always: true,
			never: false
		});

		const wrapper = mount(
			<ReportListGridPresentation
				defaultframeData={defaultframeData}
				filter={'abc'}
				search={'def'}
				setReport={setReportSpy}
			/>
		);

		const section = wrapper.find('section[data-test-selector="reportsList"]');

		expect(section).to.have.lengthOf(1);
		expect(wrapper.find(ReportCard)).to.have.lengthOf(2);

		const card1 = wrapper.find(ReportCard).at(0);

		expect(card1.props().disabled).to.equal(false);
		expect(card1.props().name).to.equal('Name1');
		expect(card1.props().testSelector).to.equal('AAA');
		expect(card1.props().onClick).to.be.a('function');

		const card2 = wrapper.find(ReportCard).at(1);

		expect(card2.props().disabled).to.equal(true);
		expect(card2.props().name).to.equal('Name2');
		expect(card2.props().testSelector).to.equal('BBB');
		expect(card2.props().onClick).to.be.a('function');

		card1.props().onClick();
		expect(setReportSpy.calledOnceWith(filteredReports[0])).to.be.true;
		card2.props().onClick();
		expect(setReportSpy.calledWith(filteredReports[1])).to.be.true;
	});

	it('recomputes activateWhen mapping when defaultframeData changes', () => {
		useReportListSearchFilterStub.returns([]);
		defaultframeData = { data: [1, 2], selected: [3, 4, 5, 6] };
		getReportActivateWhenMappingStub.returns({});

		mount(
			<ReportListGridPresentation
				defaultframeData={defaultframeData}
				filter={'f'}
				search={'s'}
				setReport={setReportSpy}
			/>
		);
		expect(getReportActivateWhenMappingStub.calledWith(defaultframeData)).to.be.true;
	});

	it('uses memoization correctly and reflects new props', () => {
		useReportListSearchFilterStub.returns([]);
		const mapping = { always: true };

		getReportActivateWhenMappingStub.returns(mapping);

		const wrapper = mount(
			<ReportListGridPresentation
				defaultframeData={defaultframeData}
				filter={'x'}
				search={'y'}
				setReport={setReportSpy}
			/>
		);

		expect(getReportActivateWhenMappingStub.calledOnce).to.be.true;

		wrapper.setProps({ defaultframeData: { data: [0], selected: [1, 2, 3] } });
		expect(getReportActivateWhenMappingStub.calledOnce).to.be.true;

		wrapper.setProps({ defaultframeData: { data: [0, 1], selected: [1, 2, 3, 4] } });
		expect(getReportActivateWhenMappingStub.calledTwice).to.be.true;
	});

	it('does not crash without filter and search (optional props)', () => {
		useReportListSearchFilterStub.returns([]);
		getReportActivateWhenMappingStub.returns({});
		const wrapper = mount(
			<ReportListGridPresentation
				defaultframeData={defaultframeData}
				setReport={setReportSpy}
			/>
		);

		expect(wrapper.find(EmptyState)).to.have.lengthOf(1);
	});

	it('covers empty or malformed cards do not crash render', () => {
		const filteredReports = [
			{
				oid: 9,
				identifier: 'X',
				activateWhen: 'never',
				description: 'n/a',
				icon: 'weird-icon',
				isCustomized: false,
				isFavorite: false,
				name: 'Unknown',
				type: 'unexpected'
			}
		];

		useReportListSearchFilterStub.returns(filteredReports);
		isNativeCubeReportStub.returns(false);
		getReportActivateWhenMappingStub.returns({ never: false });

		const wrapper = mount(
			<ReportListGridPresentation
				defaultframeData={defaultframeData}
				filter={''}
				search={''}
				setReport={setReportSpy}
			/>
		);

		expect(wrapper.find(ReportCard)).to.have.lengthOf(1);
		expect(wrapper.find(ReportCard).props().disabled).to.be.true;
	});
});