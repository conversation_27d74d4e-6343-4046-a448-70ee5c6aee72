
import React from 'react';
import { mount } from 'enzyme';
import ReportListError from 'reactorCmps/src/analytics/components/ReportPlugin/ReportList/ReportListError';
import ReportError from 'reactorCmps/src/analytics/components/ReportPlugin/ReportError/ReportError';

describe('ReportListError tests', () => {
	let wrapper;
	const fetchReportsSpy = sinon.spy();
	const mockVision = { name: 'TestVision' };
	const getSelectedVisionSpy = sinon.stub().returns(mockVision);

	const defaultframeData = {
		screen: {
			cdIsosystem: 1,
			cdMenu: 2,
			getSelectedVision: getSelectedVisionSpy
		}
	};

	beforeEach(() => {
		wrapper = mount(
			<ReportListError
				defaultframeData={defaultframeData}
				fetchReports={fetchReportsSpy}
			/>
		);
	});

	afterEach(() => {
		wrapper.unmount();
		fetchReportsSpy.resetHistory();
		getSelectedVisionSpy.resetHistory();
	});

	it('should render ReportError component', () => {
		expect(wrapper.find(ReportError)).to.have.length(1);
	});

	it('should pass correct props to ReportError', () => {
		const reportErrorProps = wrapper.find(ReportError).props();

		expect(reportErrorProps.infoMessage).to.equal('Mocked 312156');
		expect(typeof reportErrorProps.linkAction).to.equal('function');
	});

	it('should call fetchReports with correct parameters when retry action is triggered', () => {
		const reportErrorProps = wrapper.find(ReportError).props();

		reportErrorProps.linkAction();

		expect(getSelectedVisionSpy.calledOnce).to.be.true;
		expect(fetchReportsSpy.calledOnce).to.be.true;
		expect(fetchReportsSpy.calledWith(1, 2, 'TestVision')).to.be.true;
	});
});