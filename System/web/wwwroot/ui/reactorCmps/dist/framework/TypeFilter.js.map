{"version": 3, "file": "framework/TypeFilter.js", "sources": ["webpack://watch1749037385376/./src/framework/components/TypeFilter.css", "webpack://watch1749037385376/./src/framework/components/TypeFilter.jsx", "webpack://watch1749037385376/./src/framework/components/TypeListTemp.jsx", "webpack://watch1749037385376/./src/framework/components/TypeFilter.css?2fcb"], "sourcesContent": ["// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.newTypeFilterExtCmp [role=\"presentation\"] {\n\toverflow: visible;\n}`, \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "var PropTypes = require(\"prop-types\");\nvar React = require(\"react\");\nvar createReactClass = require(\"create-react-class\");\nvar AsyncLoading = require(\"reactor/src/Atomic/components/Helpers/AsyncLoading/AsyncLoading\");\nvar reactRedux = require(\"react-redux\");\nvar Provider = reactRedux.Provider;\nvar easyGrid = require(\"reactor/src/FlexGrid/components/Helpers/EasyGrid\");\nvar Connector = require(\"Connector\");\nvar TempList = require(\"./TypeListTemp\");\n\nvar easy = easyGrid({\n\tgridName: \"typeFilter\"\n});\n\nrequire('./TypeFilter.css');\nrequire(\"reactorCmps/tokens/general\");\n\nmodule.exports = createReactClass({\n\tpropTypes: {\n\t\tclasses: PropTypes.object.isRequired,\n\t\tselected: PropTypes.string,\n\t\tcurrentClass: PropTypes.number,\n\t\tonRender: PropTypes.func,\n\t\tclassLoaderListener: PropTypes.func,\n\t\tchangeListener: PropTypes.func\n\t},\n\n\tgetDefaultProps() {\n\t\treturn {\n\t\t\tcurrentClass: 1\n\t\t};\n\t},\n\n\tgetInitialState() {\n\t\tvar me = this;\n\n\t\treturn {\n\t\t\tactiveClass: me.props.currentClass,\n\t\t\tisFirstEnter: true\n\t\t};\n\t},\n\n\tcomponentDidMount() {\n\t\tvar me = this;\n\n\t\tme.refs.async.getWrappedInstance().loadData();\n\t\tif ( me.props.classLoaderListener ) {\n\t\t\tme.props.classLoaderListener(me.changeClassLoader);\n\t\t}\n\t},\n\n\tchangeClassLoader(event) {\n\t\tvar me = this;\n\n\t\tme.setState({activeClass: event.value});\n\t\tme.reload();\n\t},\n\n\treload() {\n\t\treturn this.refs.async.getWrappedInstance().loadData(true);\n\t},\n\n\tclear() {\n\t\treturn this.refs.async.getWrappedInstance().refs.list.reset();\n\t},\n\n\tselect(selectedType) {\n\t\treturn this.refs.async.getWrappedInstance().refs.list.setSelected(selectedType);\n\t},\n\n\tonUpdate() {\n\t\tif (window.search) {\n\t\t\twindow.search('full');\n\t\t}\n\t},\n\n\tonChangeType(type) {\n\t\tvar me = this;\n\n\t\tme.setState({isFirstEnter: false});\n\t\tme.props.changeListener(type, me.state.currentClass);\n\t},\n\n\tgetChildren(asyncData) {\n\t\tvar me = this,\n\t\t\tisFirstEnter = me.state.isFirstEnter,\n\t\t\tselectedType = ( isFirstEnter ) ? me.props.selected : '';\n\n\t\tif (!asyncData.results) {\n\t\t\treturn null;\n\t\t}\n\n\t\treturn (\n\t\t\t<TempList\n\t\t\t\tselected={selectedType}\n\t\t\t\tdata={asyncData.results}\n\t\t\t\tref=\"list\"\n\t\t\t\tonRender={me.props.onRender}\n\t\t\t\tonChange={me.onChangeType}\n\t\t\t\tonUpdate={me.onUpdate} />\n\t\t);\n\t},\n\n\tretrieveData() {\n\t\tvar activeClass = this.state.activeClass,\n\t\t\tdata = this.props.classes[activeClass];\n\n\t\treturn Connector.callBaseclass(data.url, { data });\n\t},\n\n\trender() {\n\t\tvar me = this;\n\n\t\treturn (\n\t\t\t<Provider store={easy.Store}>\n\t\t\t\t<AsyncLoading\n\t\t\t\t\tref=\"async\"\n\t\t\t\t\toid=\"typeFilter\"\n\t\t\t\t\tfireLoad\n\t\t\t\t\tloadFn={me.retrieveData}\n\t\t\t\t\tgetChildren={me.getChildren}\n\t\t\t\t/>\n\t\t\t</Provider>\n\t\t);\n\t}\n});", "var PropTypes = require('prop-types');\nvar React = require(\"react\");\n\nvar createReactClass = require('create-react-class');\n\nvar SelectableList = require(\"reactor/src/Form/components/Mols/SelectableList/SelectableList.jsx\");\nvar SelectableItem = require(\"reactor/src/Form/components/Mols/SelectableList/SelectableItem.jsx\");\nvar ImageLabel = require(\"reactor/src/Atomic/components/Mols/ImageTextView.jsx\");\nvar Badge = require(\"reactor/src/Atomic/components/Atoms/Badge.jsx\");\n\nvar imageStyle = {\n\tflex: '1 1 auto',\n\tmsFlex: '1 1 auto'\n};\n\nvar itemContainerStyle = {\n\tdisplay: 'flex',\n\talignItems: 'center',\n\tpadding: '5px'\n};\n\nvar containerStyle = {\n\tmargin: '0 -5px'\n};\n\nmodule.exports = createReactClass({\n\n\tpropTypes: {\n\t\tdata: PropTypes.array,\n\t\tselected: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n\t\tonRender: PropTypes.func,\n\t\tonChange: PropTypes.func,\n\t\tonUpdate: PropTypes.func.isRequired\n\t},\n\n\tgetInitialState() {\n\t\treturn {\n\t\t\tcurrentValue: null\n\t\t};\n\t},\n\n\tUNSAFE_componentWillMount() {\n\t\tvar me = this, selected = me.props.selected;\n\n\t\tme.setState({\n\t\t\tcurrentValue: selected\n\t\t});\n\t},\n\n\tcomponentDidMount() {\n\t\tvar me = this;\n\n\t\tif (me.props.onRender && me.hasData())\n\t\t\tme.props.onRender();\n\t},\n\n\tcomponentDidUpdate() {\n\t\tvar me = this, props = me.props;\n\n\t\tprops.onUpdate();\n\t},\n\n\thasData() {\n\t\tvar me = this, data = me.props.data;\n\n\t\treturn data !== null && data.length > 0;\n\t},\n\n\tsetSelected(selected) {\n\t\tvar me = this;\n\n\t\tme.setState({currentValue: selected});\n\t},\n\n\treset() {\n\t\tvar me = this;\n\n\t\tme.props.onChange(null);\n\t\tme.setState({ currentValue: null });\n\t},\n\n\thandleChange(value) {\n\t\tvar me = this;\n\n\t\tme.props.onChange(value[0]);\n\t\tme.setState({ currentValue: value[0] });\n\t},\n\n\tgetChildren() {\n\t\tvar me = this;\n\n\t\treturn me.props.data.map(current => {\n\t\t\tlet id = \"type\" + current.code;\n\t\t\tvar deadline;\n\n\t\t\tif ( current.deadline ) {\n\t\t\t\tdeadline = parseInt(current.deadline, 10);\n\t\t\t}\n\n\t\t\treturn (\n\t\t\t\t<SelectableItem key={id} oid={current.code.toString()}>\n\t\t\t\t\t<div className=\"listsItem\" style={itemContainerStyle}>\n\t\t\t\t\t\t<div style={imageStyle}>\n\t\t\t\t\t\t\t<ImageLabel\n\t\t\t\t\t\t\t\tcenter={false}\n\t\t\t\t\t\t\t\ttitle={current.title}\n\t\t\t\t\t\t\t\ttext={current.text}\n\t\t\t\t\t\t\t\tsrc={current.src}\n\t\t\t\t\t\t\t\twidth=\"16px\"\n\t\t\t\t\t\t\t\theight=\"16px\"\n\t\t\t\t\t\t\t\tstringBreak\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<Badge backgroundColor={deadline}>{current.count}</Badge>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</SelectableItem>\n\t\t\t);\n\t\t});\n\t},\n\n\trender() {\n\t\tvar me = this;\n\n\t\treturn (\n\t\t\t<div style={containerStyle}>\n\t\t\t\t<SelectableList \n\t\t\t\t\tref={el => me._list = el} \n\t\t\t\t\tmultiSelect={false} \n\t\t\t\t\tvalue={[me.state.currentValue]} \n\t\t\t\t\tonChange={me.handleChange}>{me.getChildren()}\n\t\t\t\t</SelectableList>\n\t\t\t</div>\n\t\t);\n\t}\n});", "\n      import API from \"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./TypeFilter.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\noptions.insert = insertFn.bind(null, \"head\");\noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./TypeFilter.css\";\n       export default content && content.locals ? content.locals : undefined;\n"], "names": ["PropTypes", "require", "React", "createReactClass", "AsyncLoading", "reactRedux", "Provider", "easyGrid", "Connector", "TempList", "easy", "module", "me", "event", "selectedType", "window", "type", "asyncData", "isFirstEnter", "activeClass", "data", "SelectableList", "SelectableItem", "ImageLabel", "Badge", "imageStyle", "itemContainerStyle", "containerStyle", "selected", "props", "value", "current", "id", "deadline", "parseInt", "el"], "mappings": ";;;;;;;;;;;;AAAA;AACwH;AACtB;AAClG,8BAA8B,mFAA2B,CAAC,8FAAwC;AAClG;AACA;AACA;AACA,CAAC;AACD;AACA,6DAAe,uBAAuB,EAAC;;;;;ACTvC;AAAA;AAAA,IAAIA,YAAYC,mBAAOA,CAAC,uDAAY;AACpC,IAAIC,QAAQD,mBAAOA,CAAC,oBAAO;AAC3B,IAAIE,mBAAmBF,mBAAOA,CAAC,8CAAoB;AACnD,IAAIG,eAAeH,mBAAOA,CAAC,+IAAiE;AAC5F,IAAII,aAAaJ,mBAAOA,CAAC,4DAAa;AACtC,IAAIK,WAAWD,WAAW,QAAQ;AAClC,IAAIE,WAAWN,mBAAOA,CAAC,gHAAkD;AACzE,IAAIO,YAAYP,mBAAOA,CAAC,4BAAW;AACnC,IAAIQ,WAAWR,mBAAOA,CAAC,mEAAgB;AAEvC,IAAIS,OAAOH,SAAS;IACnB,UAAU;AACX;AAEAN,mBAAOA,CAAC,mEAAkB;AAC1BA,mBAAOA,CAAC,8DAA4B;AAEpCU,cAAc,GAAGR,iBAAiB;IACjC,WAAW;QACV,SAASH,UAAU,MAAM,CAAC,UAAU;QACpC,UAAUA,UAAU,MAAM;QAC1B,cAAcA,UAAU,MAAM;QAC9B,UAAUA,UAAU,IAAI;QACxB,qBAAqBA,UAAU,IAAI;QACnC,gBAAgBA,UAAU,IAAI;IAC/B;IAEA;QACC,OAAO;YACN,cAAc;QACf;IACD;IAEA;QACC,IAAIY,KAAK,IAAI;QAEb,OAAO;YACN,aAAaA,GAAG,KAAK,CAAC,YAAY;YAClC,cAAc;QACf;IACD;IAEA;QACC,IAAIA,KAAK,IAAI;QAEbA,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,QAAQ;QAC3C,IAAKA,GAAG,KAAK,CAAC,mBAAmB,EAAG;YACnCA,GAAG,KAAK,CAAC,mBAAmB,CAACA,GAAG,iBAAiB;QAClD;IACD;IAEA,4BAAkBC,KAAK;QACtB,IAAID,KAAK,IAAI;QAEbA,GAAG,QAAQ,CAAC;YAAC,aAAaC,MAAM,KAAK;QAAA;QACrCD,GAAG,MAAM;IACV;IAEA;QACC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,QAAQ,CAAC;IACtD;IAEA;QACC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;IAC5D;IAEA,iBAAOE,YAAY;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAACA;IACnE;IAEA;QACC,IAAIC,OAAO,MAAM,EAAE;YAClBA,OAAO,MAAM,CAAC;QACf;IACD;IAEA,uBAAaC,IAAI;QAChB,IAAIJ,KAAK,IAAI;QAEbA,GAAG,QAAQ,CAAC;YAAC,cAAc;QAAK;QAChCA,GAAG,KAAK,CAAC,cAAc,CAACI,MAAMJ,GAAG,KAAK,CAAC,YAAY;IACpD;IAEA,sBAAYK,SAAS;QACpB,IAAIL,KAAK,IAAI,EACZM,eAAeN,GAAG,KAAK,CAAC,YAAY,EACpCE,eAAiBI,eAAiBN,GAAG,KAAK,CAAC,QAAQ,GAAG;QAEvD,IAAI,CAACK,UAAU,OAAO,EAAE;YACvB,OAAO;QACR;QAEA,qBACC,oBAACR;YACA,UAAUK;YACV,MAAMG,UAAU,OAAO;YACvB,KAAI;YACJ,UAAUL,GAAG,KAAK,CAAC,QAAQ;YAC3B,UAAUA,GAAG,YAAY;YACzB,UAAUA,GAAG,QAAQ;;IAExB;IAEA;QACC,IAAIO,cAAc,IAAI,CAAC,KAAK,CAAC,WAAW,EACvCC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAACD,YAAY;QAEvC,OAAOX,UAAU,aAAa,CAACY,KAAK,GAAG,EAAE;YAAEA,MAAAA;QAAK;IACjD;IAEA;QACC,IAAIR,KAAK,IAAI;QAEb,qBACC,oBAACN;YAAS,OAAOI,KAAK,KAAK;yBAC1B,oBAACN;YACA,KAAI;YACJ,KAAI;YACJ;YACA,QAAQQ,GAAG,YAAY;YACvB,aAAaA,GAAG,WAAW;;IAI/B;iBA3GM;AA4GP;;;;;AC7HA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAIZ,YAAYC,mBAAOA,CAAC,uDAAY;AACpC,IAAIC,QAAQD,mBAAOA,CAAC,oBAAO;AAE3B,IAAIE,mBAAmBF,mBAAOA,CAAC,8CAAoB;AAEnD,IAAIoB,iBAAiBpB,mBAAOA,CAAC,iJAAoE;AACjG,IAAIqB,iBAAiBrB,mBAAOA,CAAC,iJAAoE;AACjG,IAAIsB,aAAatB,mBAAOA,CAAC,qHAAsD;AAC/E,IAAIuB,QAAQvB,mBAAOA,CAAC,uGAA+C;AAEnE,IAAIwB,aAAa;IAChB,MAAM;IACN,QAAQ;AACT;AAEA,IAAIC,qBAAqB;IACxB,SAAS;IACT,YAAY;IACZ,SAAS;AACV;AAEA,IAAIC,iBAAiB;IACpB,QAAQ;AACT;AAEAhB,cAAc,GAAGR,iBAAiB;IAEjC,WAAW;QACV,MAAMH,UAAU,KAAK;QACrB,UAAUA,UAAU,SAAS,CAAC;YAACA,UAAU,MAAM;YAAEA,UAAU,MAAM;SAAC;QAClE,UAAUA,UAAU,IAAI;QACxB,UAAUA,UAAU,IAAI;QACxB,UAAUA,UAAU,IAAI,CAAC,UAAU;IACpC;IAEA;QACC,OAAO;YACN,cAAc;QACf;IACD;IAEA;QACC,IAAIY,KAAK,IAAI,EAAEgB,WAAWhB,GAAG,KAAK,CAAC,QAAQ;QAE3CA,GAAG,QAAQ,CAAC;YACX,cAAcgB;QACf;IACD;IAEA;QACC,IAAIhB,KAAK,IAAI;QAEb,IAAIA,GAAG,KAAK,CAAC,QAAQ,IAAIA,GAAG,OAAO,IAClCA,GAAG,KAAK,CAAC,QAAQ;IACnB;IAEA;QACC,IAAIA,KAAK,IAAI,EAAEiB,QAAQjB,GAAG,KAAK;QAE/BiB,MAAM,QAAQ;IACf;IAEA;QACC,IAAIjB,KAAK,IAAI,EAAEQ,OAAOR,GAAG,KAAK,CAAC,IAAI;QAEnC,OAAOQ,SAAS,QAAQA,KAAK,MAAM,GAAG;IACvC;IAEA,sBAAYQ,QAAQ;QACnB,IAAIhB,KAAK,IAAI;QAEbA,GAAG,QAAQ,CAAC;YAAC,cAAcgB;QAAQ;IACpC;IAEA;QACC,IAAIhB,KAAK,IAAI;QAEbA,GAAG,KAAK,CAAC,QAAQ,CAAC;QAClBA,GAAG,QAAQ,CAAC;YAAE,cAAc;QAAK;IAClC;IAEA,uBAAakB,KAAK;QACjB,IAAIlB,KAAK,IAAI;QAEbA,GAAG,KAAK,CAAC,QAAQ,CAACkB,KAAK,CAAC,EAAE;QAC1BlB,GAAG,QAAQ,CAAC;YAAE,cAAckB,KAAK,CAAC,EAAE;QAAC;IACtC;IAEA;QACC,IAAIlB,KAAK,IAAI;QAEb,OAAOA,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAACmB,SAAAA;YACxB,IAAIC,KAAK,SAASD,QAAQ,IAAI;YAC9B,IAAIE;YAEJ,IAAKF,QAAQ,QAAQ,EAAG;gBACvBE,WAAWC,SAASH,QAAQ,QAAQ,EAAE;YACvC;YAEA,qBACC,oBAACT;gBAAe,KAAKU;gBAAI,KAAKD,QAAQ,IAAI,CAAC,QAAQ;6BAClD,oBAAC;gBAAI,WAAU;gBAAY,OAAOL;6BACjC,oBAAC;gBAAI,OAAOD;6BACX,oBAACF;gBACA,QAAQ;gBACR,OAAOQ,QAAQ,KAAK;gBACpB,MAAMA,QAAQ,IAAI;gBAClB,KAAKA,QAAQ,GAAG;gBAChB,OAAM;gBACN,QAAO;gBACP;+BAGF,oBAAC,2BACA,oBAACP;gBAAM,iBAAiBS;eAAWF,QAAQ,KAAK;QAKrD;IACD;IAEA;QACC,IAAInB,KAAK,IAAI;QAEb,qBACC,oBAAC;YAAI,OAAOe;yBACX,oBAACN;YACA,KAAKc,SAAAA;uBAAMvB,GAAG,KAAK,GAAGuB;;YACtB,aAAa;YACb,OAAO;gBAACvB,GAAG,KAAK,CAAC,YAAY;aAAC;YAC9B,UAAUA,GAAG,YAAY;WAAGA,GAAG,WAAW;IAI9C;iBA9GM;AA+GP;;;;;;;;;;;;;;;;;;;;;;;;ACvIA,MAAwG;AACxG,MAA8F;AAC9F,MAAqG;AACrG,MAAwH;AACxH,MAAiH;AACjH,MAAiH;AACjH,MAA6I;AAC7I;AACA;;AAEA;;AAEA,4BAA4B,qGAAmB;AAC/C,wBAAwB,kHAAa;AACrC,iBAAiB,uGAAa;AAC9B,iBAAiB,+FAAM;AACvB,6BAA6B,sGAAkB;;AAE/C,aAAa,0GAAG,CAAC,mHAAO;;;;AAIuF;AAC/G,OAAO,6DAAe,mHAAO,IAAI,0HAAc,GAAG,0HAAc,YAAY,EAAC"}