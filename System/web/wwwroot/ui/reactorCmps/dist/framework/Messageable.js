define(["Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","Storage","tokens!reactorCmps/tokens/general","react"], function(__WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_Storage__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_react__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/Messageable"], {
"../reactor/src/helpers/Messageable.js": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.string.substr.js */ "../node_modules/core-js/modules/es.string.substr.js");
__webpack_require__(/*! core-js/modules/es.object.keys.js */ "../node_modules/core-js/modules/es.object.keys.js");
__webpack_require__(/*! core-js/modules/es.date.to-string.js */ "../node_modules/core-js/modules/es.date.to-string.js");
__webpack_require__(/*! core-js/modules/es.date.now.js */ "../node_modules/core-js/modules/es.date.now.js");
__webpack_require__(/*! core-js/modules/es.string.search.js */ "../node_modules/core-js/modules/es.string.search.js");
__webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
__webpack_require__(/*! core-js/modules/es.array.splice.js */ "../node_modules/core-js/modules/es.array.splice.js");
__webpack_require__(/*! core-js/modules/web.timers.js */ "../node_modules/core-js/modules/web.timers.js");
__webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
var Storage = __webpack_require__(/*! Storage */ "Storage");
var util = __webpack_require__(/*! reactor/src/helpers/util.js */ "../reactor/src/helpers/util.js");
var registered = false;
function Messageable(msgID) {
    var me = this;
    if (!msgID) {
        /* eslint-disable */ console.error("You have to give an ID to a messageable");
    /* eslint-enable */ }
    me.isIE = util.isIE();
    me.msgID = msgID;
}
Messageable.prototype = {
    listeners: [],
    handleStorageEvent: function handleStorageEvent(evt) {
        var me = this, key = evt.key, msgObject, receivedMsg, count, listener, newValue = evt.newValue;
        var subKey, magicStr = "112233-";
        // Gambiarra para forçar a maravilha do IE 11 em windows 8 a sincronizar // FRAM-5183
        /* istanbul ignore next - depende de browser */ if (me.isIE) {
            subKey = key.substr(0, 7);
        }
        if (subKey && subKey === magicStr) {
            key = key.substr(7);
            newValue = localStorage.getItem(key);
        }
        try {
            msgObject = JSON.parse(newValue);
        } catch (e) {
            return;
        }
        if (msgObject && msgObject.msg) {
            receivedMsg = msgObject.msg;
            for(count = 0; count < me.listeners.length; count++){
                listener = me.listeners[count];
                if (listener.msgID === msgObject.msgID && listener.msg === receivedMsg) {
                    listener.cb(msgObject.params);
                } else if (receivedMsg === "pong") {
                    listener.lastPong = msgObject.date;
                }
            }
        }
    },
    addListener: function addListener(msg, cb) {
        var me = this;
        me.listeners.push({
            msgID: me.msgID,
            msg: msg,
            cb: cb
        });
        me.clearOldListeners();
        me.registerStorageEvent();
    },
    clearOldListeners: function clearOldListeners() {
        var me = this, listeners = me.listeners, listener, count, lastPongLimit = Date.now() - 90000, list = Storage.list(), prop;
        for(prop in list){
            if (prop.search("msgID-") > -1 || prop === "genericChecklistData") {
                Storage.remove(prop);
            }
        }
        for(count = listeners.length - 1; count >= 0; count--){
            listener = listeners[count];
            if (listener.lastPong < lastPongLimit) {
                me.listeners.splice(count, 1);
                Storage.remove(listener.msgID);
            }
        }
    },
    ping: function ping() {
        var me = this, listeners = me.listeners, listener, count;
        for(count = 0; count < listeners.length; count++){
            listener = listeners[count];
            Storage.set(listener.msgID, "ping");
        }
        // waits for windows to answer
        setTimeout(me.clearOldListeners.bind(me), 15000);
    },
    registerStorageEvent: function registerStorageEvent() {
        var me = this;
        if (!registered) {
            window.addEventListener("storage", me.handleStorageEvent.bind(me));
            setInterval(me.ping.bind(me), 600000);
            registered = true;
        }
    }
};
module.exports = Messageable;


}),
"./src/framework/components/Messageable.js": (function (module, __unused_webpack_exports, __webpack_require__) {
module.exports = __webpack_require__(/*! reactor/src/helpers/Messageable */ "../reactor/src/helpers/Messageable.js");


}),
"Storage": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Storage__;

}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/components/Messageable.js"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=Messageable.js.map