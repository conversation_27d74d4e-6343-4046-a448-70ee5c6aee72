{"version": 3, "file": "framework/ProfileView.js", "sources": ["webpack://watch1749037385376/./src/framework/components/mobile/Profile/ProfileView.scss", "webpack://watch1749037385376/../reactor2/src/Mobile/components/Templates/Leaf/Leaf.jsx", "webpack://watch1749037385376/./src/framework/components/mobile/Profile/LicenseSelector.jsx", "webpack://watch1749037385376/./src/framework/components/mobile/Profile/LocationFacade.js", "webpack://watch1749037385376/./src/framework/components/mobile/Profile/ProfileView.jsx", "webpack://watch1749037385376/./src/workspace/components/Common/LicenseKeysAlert/LicenseKeysRequest.js", "webpack://watch1749037385376/./src/framework/components/mobile/Profile/ProfileView.scss?c6bb"], "sourcesContent": ["// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `@charset \"UTF-8\";\n/* Aplica uma transição com uma velocidade constante do início ao fim. */\n/* Começa a animação devagar, acelera no meio e desacelera no final. */\n/* Começa a animação devagar e acelera ao longo do tempo. */\n/* Começa a animação rapidamente e desacelera no final. */\n/* Aplica uma aceleração e desaceleração moderada no início e no final da animação. */\n/* Define o tamanho de fonte de 10px. */\n/* Define o tamanho de fonte de 11px. */\n/* Define o tamanho de fonte de 12px. */\n/* Define o tamanho de fonte de 13px. */\n/* Define o tamanho de fonte de 14px. */\n/* Define o tamanho de fonte de 16px. */\n/* Define o tamanho de fonte de 20px. */\n/* Define o tamanho de fonte de 24px. */\n/* A família de fontes é definida como arial, \"Helvetica Neue\", e sans-serif. */\n/* O peso de fonte definido como bold */\n/* O peso de fonte definido como normal */\n/* Define o tamanho de fonte de 32px. */\n/* Define o tamanho de fonte de 36px. */\n/* Define a altura da linha como 1. */\n/* Define a altura da linha como 1.25 */\n/* Define a altura da linha como 1.5 */\n/* Aplica um raio de borda de 3px. */\n/* Aplica um espaçamento de 4px. */\n/* Aplica um espaçamento de 8px, que é o dobro de Spacing1. */\n/* Aplica um espaçamento de 12px, que é o triplo de Spacing1. */\n/* Aplica um espaçamento de 16px, que é o quádruplo de Spacing1. */\n/* Aplica um espaçamento de 20px, que é o quíntuplo de Spacing1. */\n/* Aplica um espaçamento de 24px, que é o sêxtuplo de Spacing1. */\n/* Aplica um espaçamento de 28px, que é o sétuplo de Spacing1. */\n/* Aplica um espaçamento de 32px, que é o octuplo de Spacing1. */\n/* Aplica um tamanho de 16px, definido como Spacing4. */\n/* Aplica um tamanho de 24px, definido como Spacing6. */\n/* Aplica um tamanho de 28px, definido como Spacing7. */\n/* Aplica um tamanho de 32px, definido como Spacing8. */\n/* Aplica um tamanho de 36px, que é o nono múltiplo de \\$pacing1. */\n/* Aplica um espaçamento de 4px entre itens, definido como Spacing1. */\n/* Aplica um espaçamento de 8px entre itens, definido como Spacing2. */\n/* Aplica um espaçamento de 12px entre itens, definido como Spacing3. */\n/* Aplica um espaçamento de 16px entre itens, definido como Spacing4. */\n/* Aplica um espaçamento de 20px entre itens, definido como Spacing5. */\n/* Aplica um espaçamento de 32px entre itens, definido como Spacing8. */\n/* Aplica um espaçamento de 12px entre itens em uma linha, definido como StackMedium. */\n/* Aplica um espaçamento de 20px entre itens em uma linha, definido como StackContent. */\n/* Aplica um espaçamento de 20px para cabeçalhos de nível 1, definido como StackContent. */\n/* Aplica um espaçamento de 16px para cabeçalhos de nível 2, definido como StackLarge. */\n/* Aplica um espaçamento de 12px para cabeçalhos de nível 3, definido como StackMedium. */\n/* Aplica um inset de 8px, definido como Spacing2. */\n/* Aplica um inset de 12px, definido como Spacing3. */\n/* Aplica um inset de 16px, definido como Spacing4. */\n/* Aplica um inset de 20px, definido como Spacing5. */\n/* Aplica um espaçamento de 4px para espaçamentos inline, definido como Spacing1. */\n/* Aplica um espaçamento de 8px para espaçamentos inline, definido como Spacing2. */\n/* Aplica um espaçamento de 16px para espaçamentos inline em relacionamentos, definido como Spacing4. */\n/* Aplica um espaçamento de 32px para espaçamentos inline grandes, definido como Spacing8. */\n/* Aplica uma transição rápida com duração de 0.15 segundos. */\n/* Aplica uma transição normal com duração de 0.25 segundos. */\n/* Aplica uma transição mais longa com duração de 0.35 segundos. */\n/* Aplica uma transição lenta com duração de 1 segundo. */\n.profileContentWrapper > button > span {\n  color: #f32c40;\n  font-weight: 500;\n  text-transform: uppercase;\n}`, \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "var PropTypes = require('prop-types');\nvar React = require('react');\n\nvar createReactClass = require('create-react-class');\n\nvar styles = {\n\tbackgroundColor: 'white',\n\theight: '100%',\n\tdisplay: \"flex\",\n\tflexDirection: \"column\",\n\tappearance: \"none\"\n};\n\nmodule.exports = createReactClass({\n\tdisplayName: \"Mobile/components/Templates/Leaf/Leaf\",\n\n\tpropTypes: {\n\t\tchildren: PropTypes.oneOfType([\n\t\t\tPropTypes.arrayOf(PropTypes.node),\n\t\t\tPropTypes.node\n\t\t])\n\t},\n\n\trender() {\n\t\treturn (\n\t\t\t<div className=\"rctLeaf\" {...this.props} style={this.props.skipStyles ? null : styles}>\n\t\t\t\t{this.props.children}\n\t\t\t</div>\n\t\t);\n\t}\n});", "import React, { useState, useCallback, useMemo } from \"react\";\nimport Utils from \"Utils\";\nimport Storage from \"Storage\";\nimport useTokenManager from \"reactor2/src/Atomic/components/Helpers/Language/useTokenManager\";\nimport DropdownButton from \"reactor2/src/Atomic/components/Orgs/Dropdown/DropdownButton\";\nimport MenuItem from \"reactor2/src/Atomic/components/Atoms/MenuItem/MenuItem\";\nimport Icon from \"reactor2/src/Atomic/components/Atoms/Icon/Icon\";\nimport * as styleVariables from \"reactor2/src/Styles/tokens/styleVariables.ecss\";\nimport { getLicenseKeys, changeLicense } from \"reactorCmps/src/workspace/components/Common/LicenseKeysAlert/LicenseKeysRequest\";\nimport location from \"reactorCmps/src/framework/components/mobile/Profile/LocationFacade\";\n\nconst licenseDropdownButtonStyle = {\n\tmarginBottom: styleVariables.spacingSpacing8\n};\n\nconst selectedLicenseIconStyle = {\n\tmarginRight: styleVariables.spacingSpacing2\n};\n\nconst LICENSE_ICON = \"seicon-key\";\n\nconst reloadPage = () => {\n\tconst { origin, pathname } = location.get();\n\tconst mobile = Utils.getURLParameter(\"mobile\");\n\tconst mobileParam = mobile ? `&mobile=${mobile}` : \"\";\n\n\tlocation.set(`${origin}${pathname}?refreshInfo=true${mobileParam}`);\n};\n\nconst LicenseSelector = () => {\n\tconst getToken = useTokenManager();\n\tconst [loading, setLoading] = useState(true);\n\tconst [licenses, setLicenses] = useState([]);\n\tconst [error, setError] = useState();\n\n\tconst handleDropdownButtonClick = useCallback(() => {\n\t\t!licenses.length && getLicenseKeys().then((data) => {\n\t\t\tsetLicenses(data);\n\t\t\tsetLoading(false);\n\t\t});\n\t}, [licenses]);\n\n\tconst handleLicenseClick = useCallback((id) => {\n\t\tsetLoading(true);\n\t\tchangeLicense(id).then(() => {\n\t\t\tStorage.remove(\"pendencyProcessed\");\n\t\t\treloadPage();\n\t\t}).catch((err) => {\n\t\t\tsetError(Utils.decodeHtmlString(err.message));\n\t\t\tsetLoading(false);\n\t\t});\n\t}, []);\n\n\tconst memoizedContent = useMemo(() => {\n\t\tif (error)\n\t\t\treturn <span data-test-selector={\"LicenseSelectorError\"}>{error}</span>;\n\n\t\treturn licenses.map(({ id, checked, text }) => (\n\t\t\t<MenuItem\n\t\t\t\teventKey={id}\n\t\t\t\tkey={id}\n\t\t\t\tselected={checked}\n\t\t\t\tonClick={() => !checked && handleLicenseClick(id)}\n\t\t\t>\n\t\t\t\t{ checked && <Icon icon={LICENSE_ICON} style={selectedLicenseIconStyle} /> }\n\t\t\t\t{ text }\n\t\t\t</MenuItem>\n\t\t));\n\t}, [error, licenses, handleLicenseClick]);\n\n\treturn (\n\t\t<div style={licenseDropdownButtonStyle}>\n\t\t\t<DropdownButton\n\t\t\t\ticon={LICENSE_ICON}\n\t\t\t\ttitle={getToken(\"213119\").toUpperCase()}\n\t\t\t\tmenuTitle={getToken(\"213119\")}\n\t\t\t\tonClick={handleDropdownButtonClick}\n\t\t\t\tloading={loading}\n\t\t\t\tnoCaret\n\t\t\t>\n\t\t\t\t{memoizedContent}\n\t\t\t</DropdownButton>\n\t\t</div>\n\t);\n};\n\nLicenseSelector.displayName = \"framework/components/mobile/Profile/LicenseSelector\";\n\nexport default LicenseSelector;", "/* istanbul ignore file */\nexport default {\n\tget: () => window.location,\n\tset: (url) => window.location = url\n};", "import React, { useCallback, useMemo, useState } from \"react\";\nimport { Provider } from \"react-redux\";\nimport Utils from \"Utils\";\nimport WorkspaceInfo from \"WorkspaceInfo\";\nimport useTokenManager from \"reactor2/src/Atomic/components/Helpers/Language/useTokenManager\";\nimport Leaf from \"reactor2/src/Mobile/components/Templates/Leaf/Leaf\";\nimport MobileHeader from \"reactor2/src/Mobile/components/Orgs/Header/MobileHeader\";\nimport Store from \"reactor2/src/store/store\";\nimport { SIZE_BIG } from \"reactor2/src/constants/sizeConstants\";\nimport UserView from \"reactor2/src/Atomic/components/Mols/UserView/UserView\";\nimport Button from \"reactor2/src/Atomic/components/Atoms/Button/Button\";\nimport Link from \"reactor2/src/Atomic/components/Atoms/Link/Link\";\nimport * as styleVariables from \"reactor2/src/Styles/tokens/styleVariables.ecss\";\nimport LicenseSelector from \"reactorCmps/src/framework/components/mobile/Profile/LicenseSelector\";\nimport \"reactorCmps/src/framework/components/mobile/Profile/ProfileView.scss\";\n\nconst headerStyle = {\n\twidth: \"100%\"\n};\n\nconst containerStyle = {\n\tposition: \"relative\",\n\theight: \"100%\",\n\tbackground: styleVariables.colorsSolidBlue7,\n\tcolor: styleVariables.colorsSolidGray1,\n\toverflowY: \"auto\"\n};\n\nconst contentWrapperStyle = {\n\tposition: \"absolute\",\n\ttop: 0,\n\twidth: \"100%\",\n\tdisplay: \"flex\",\n\tflexDirection: \"column\",\n\talignItems: \"center\"\n};\n\nconst backgroundImageStyle = {\n\twidth: \"100%\",\n\theight: \"100%\",\n\tbackgroundSize: \"100% 100%\",\n\tfilter: \"blur(15px)\",\n\topacity: \".2\"\n};\n\nconst profilePictureWrapperStyle = {\n\tmarginTop: styleVariables.spacingSpacing6,\n\tmarginBottom: styleVariables.spacingSpacing4\n};\n\nconst commomTextStyle = {\n\tlineHeight: styleVariables.fontLineHeightReset,\n\tletterSpacing: \".02em\",\n\tmarginBottom: styleVariables.spacingSpacing2\n};\n\nconst userNameStyle = {\n\t...commomTextStyle,\n\tfontSize: styleVariables.fontFontXL\n};\n\nconst departmentStyle = {\n\t...commomTextStyle,\n\tfontSize: 18,\n\tmarginBottom: styleVariables.spacingSpacing8\n};\n\nconst docLinkStyle = {\n\tcolor: styleVariables.colorsWhite,\n\tfontSize: styleVariables.fontFontSM,\n\tmarginTop: styleVariables.spacingSpacing8\n};\n\nconst LOGOFF_ICON = \"seicon-mobile-logoff\";\nconst LOGOFF_ICON_COLOR = styleVariables.colorsSolidRed;\nconst DOCS_ICON = \"seicon-help-circle-alt\";\n\nconst DOWNLOAD_LINK = \"https://documents.softexpert.com/{0}/mobile/mo001\";\nconst DOCS_LANGUAGE_KEYS = {\n\t\"pt-br\": \"pt_BR\",\n\t\"es\": \"es_ES\",\n\t\"default\": \"en_US\"\n};\n\nconst getDocDownloadLink = () => {\n\tconst key = DOCS_LANGUAGE_KEYS[WorkspaceInfo.getDefaultLanguage()] || DOCS_LANGUAGE_KEYS.default;\n\n\treturn DOWNLOAD_LINK.seformat(key);\n};\n\nconst openDocDownloadLink = event => Utils.sendBlankLinkEventToWebview(event.target.href, event);\n\nconst ProfileView = () => {\n\tconst getToken = useTokenManager();\n\tconst [leaving, setLeaving] = useState(false);\n\n\tconst handleCloseProfileLeaf = useCallback(() => {\n\t\tcurl(\"Mobile/MobileUIApp\", MobileUIApp => {\n\t\t\tMobileUIApp.destroyCurrentLeaf();\n\t\t});\n\t}, []);\n\n\tconst handleLogoff = useCallback(() => {\n\t\tcurl(\"SessionManager\", SessionManager => {\n\t\t\tUtils.amplitudeTrack(\"MOB - Fez logout\");\n\t\t\tSessionManager.logout(undefined, undefined, false, undefined, \"mobile-profile-logout\");\n\t\t});\n\n\t\tsetLeaving(true);\n\t}, []);\n\n\tconst memoizedBackground = useMemo(() => {\n\t\tconst styleWithImage = {\n\t\t\t...backgroundImageStyle,\n\t\t\tbackgroundImage: `url(${WorkspaceInfo.getProfilePicture()})`\n\t\t};\n\n\t\treturn <div style={styleWithImage} data-test-selector={\"ProfileViewBackground\"} />;\n\t}, []);\n\n\tconst memoizedProfilePicture = useMemo(() => (\n\t\t<div style={profilePictureWrapperStyle}>\n\t\t\t<Provider store={Store}>\n\t\t\t\t<UserView\n\t\t\t\t\tdetails={false}\n\t\t\t\t\tcdUser={WorkspaceInfo.getCDUser()}\n\t\t\t\t\thideText\n\t\t\t\t\tsize={SIZE_BIG}\n\t\t\t\t/>\n\t\t\t</Provider>\n\t\t</div>\n\t), []);\n\n\tconst memoizedLogoffButton = useMemo(() => (\n\t\t<Button\n\t\t\ticon={LOGOFF_ICON}\n\t\t\ticonColor={LOGOFF_ICON_COLOR}\n\t\t\tloading={leaving}\n\t\t\tonClick={handleLogoff}\n\t\t>\n\t\t\t{getToken(\"100004\")}\n\t\t</Button>\n\t), [getToken, leaving, handleLogoff]);\n\n\treturn (\n\t\t<Leaf>\n\t\t\t<MobileHeader hasBackBtn fnBack={handleCloseProfileLeaf}>\n\t\t\t\t<div style={headerStyle} />\n\t\t\t</MobileHeader>\n\t\t\t<div style={containerStyle}>\n\t\t\t\t{memoizedBackground}\n\t\t\t\t<div style={contentWrapperStyle} className=\"profileContentWrapper\">\n\t\t\t\t\t{memoizedProfilePicture}\n\t\t\t\t\t<h5 style={userNameStyle}>{WorkspaceInfo.getUserName()}</h5>\n\t\t\t\t\t<h5 style={departmentStyle}>{WorkspaceInfo.getDepartment()}</h5>\n\t\t\t\t\t<LicenseSelector />\n\t\t\t\t\t{memoizedLogoffButton}\n\t\t\t\t\t<Link style={docLinkStyle} icon={DOCS_ICON} href={getDocDownloadLink()} onClick={openDocDownloadLink}>\n\t\t\t\t\t\t{getToken(\"109009\")}\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</Leaf>\n\t);\n};\n\nProfileView.displayName = \"framework/components/mobile/Profile/ProfileView\";\n\nexport default ProfileView;", "import Connector from 'Connector';\n\nexport function getLicenseKeys() {\n\treturn new Promise((resolve, reject) => {\n\t\tConnector.callLogic(\n\t\t\t'COLicenseKeyLogic/getMenuChangeKeyLogin',\n\t\t\tundefined,\n\t\t\t{\n\t\t\t\tsuccess: (response) => {\n\t\t\t\t\t/* istanbul ignore else */\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tresolve(response.results);\n\t\t\t\t\t}\n\n\t\t\t\t\treject();\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\t});\n}\n\nexport function changeLicense(key, logout) {\n\treturn new Promise((resolve, reject) => {\n\t\tConnector.callLogic(\n\t\t\t\"COLicenseKeyLogic/changeLicense\",\n\t\t\t{\n\t\t\t\tjson: '[{\"outherLicenseKey\":\"' + key + '\"}, {\"logout\":\"' + logout + '\"}]'\n\t\t\t},\n\t\t\t{\n\t\t\t\tsuccess: function(res) {\n\t\t\t\t\tif (res.success === true) {\n\t\t\t\t\t\tresolve();\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// Troca para licenca NAMED_USER com usuario logado abre confirmacao para logout\n\t\t\t\t\t\tif (res.termCode === 301135) {\n\t\t\t\t\t\t\treject({ message: res.message, key });\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\treject({ message: res.message });\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\t});\n}", "\n      import API from \"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[2]!./ProfileView.scss\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\noptions.insert = insertFn.bind(null, \"head\");\noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[2]!./ProfileView.scss\";\n       export default content && content.locals ? content.locals : undefined;\n"], "names": ["PropTypes", "require", "React", "createReactClass", "styles", "module", "useState", "useCallback", "useMemo", "Utils", "Storage", "useTokenManager", "DropdownButton", "MenuItem", "Icon", "styleVariables", "getLicenseKeys", "changeLicense", "location", "licenseDropdownButtonStyle", "selectedLicenseIconStyle", "LICENSE_ICON", "reloadPage", "_location_get", "origin", "pathname", "mobile", "mobileParam", "LicenseSelector", "getToken", "_useState", "loading", "setLoading", "_useState1", "licenses", "setLicenses", "_useState2", "error", "setError", "handleDropdownButtonClick", "data", "handleLicenseClick", "id", "err", "memoized<PERSON><PERSON><PERSON>", "checked", "text", "window", "url", "Provider", "WorkspaceInfo", "Leaf", "MobileHeader", "Store", "SIZE_BIG", "UserView", "<PERSON><PERSON>", "Link", "headerStyle", "containerStyle", "contentWrapperStyle", "backgroundImageStyle", "profilePictureWrapperStyle", "commomTextStyle", "userNameStyle", "departmentStyle", "docLinkStyle", "LOGOFF_ICON", "LOGOFF_ICON_COLOR", "DOCS_ICON", "DOWNLOAD_LINK", "DOCS_LANGUAGE_KEYS", "getDocDownloadLink", "key", "openDocDownloadLink", "event", "ProfileView", "leaving", "setLeaving", "handleCloseProfileLeaf", "curl", "MobileUIApp", "handle<PERSON>ogoff", "Session<PERSON>anager", "undefined", "memoizedBackground", "styleWithImage", "memoizedProfilePicture", "memoized<PERSON><PERSON>ffButton", "Connector", "Promise", "resolve", "reject", "response", "logout", "success", "res"], "mappings": ";;;;;;;;;;;;AAAA;AAC8H;AACtB;AACxG,8BAA8B,mFAA2B,CAAC,8FAAwC;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,6DAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtEvC,IAAIA,YAAYC,mBAAOA,CAAC,uDAAY;AACpC,IAAIC,QAAQD,mBAAOA,CAAC,oBAAO;AAE3B,IAAIE,mBAAmBF,mBAAOA,CAAC,8CAAoB;AAEnD,IAAIG,SAAS;IACZ,iBAAiB;IACjB,QAAQ;IACR,SAAS;IACT,eAAe;IACf,YAAY;AACb;AAEAC,cAAc,GAAGF,iBAAiB;IACjC,aAAa;IAEb,WAAW;QACV,UAAUH,UAAU,SAAS,CAAC;YAC7BA,UAAU,OAAO,CAACA,UAAU,IAAI;YAChCA,UAAU,IAAI;SACd;IACF;IAEA;QACC,qBACC,oBAAC;YAAI,WAAU;WAAc,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,OAAOI;YAC7E,IAAI,CAAC,KAAK,CAAC,QAAQ;IAGvB;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0D+B;AAAA;AAxF+B;AACpC;AACI;AACgE;AACL;AACX;AACZ;AACe;AAC+C;AACtC;AAE1F,IAAMe,6BAA6B;IAClC,cAAcJ,2FAA8B;AAC7C;AAEA,IAAMK,2BAA2B;IAChC,aAAaL,2FAA8B;AAC5C;AAEA,IAAMM,eAAe;AAErB,IAAMC,aAAa;IAClB,IAA6BC,gBAAAA,+GAAY,IAAjCC,SAAqBD,cAArBC,QAAQC,WAAaF,cAAbE;IAChB,IAAMC,SAASjB,4DAAqB,CAAC;IACrC,IAAMkB,cAAcD,SAAU,WAAiB,OAAPA,UAAW;IAEnDR,+GAAY,CAAE,GAAWO,OAATD,QAAqCG,OAA5BF,UAAS,qBAA+B,OAAZE;AACtD;AAEA,IAAMC,kBAAkB;IACvB,IAAMC,WAAWlB,2GAAeA;IAChC,IAA8BmB,6BAAAA,+CAAQA,CAAC,WAAhCC,UAAuBD,cAAdE,aAAcF;IAC9B,IAAgCG,8BAAAA,+CAAQA,CAAC,EAAE,OAApCC,WAAyBD,eAAfE,cAAeF;IAChC,IAA0BG,8BAAAA,+CAAQA,QAA3BC,QAAmBD,eAAZE,WAAYF;IAE1B,IAAMG,4BAA4BhC,kDAAWA,CAAC;QAC7C,CAAC2B,SAAS,MAAM,IAAIlB,gIAAcA,GAAG,IAAI,CAAC,SAACwB;YAC1CL,YAAYK;YACZR,WAAW;QACZ;IACD,GAAG;QAACE;KAAS;IAEb,IAAMO,qBAAqBlC,kDAAWA,CAAC,SAACmC;QACvCV,WAAW;QACXf,+HAAaA,CAACyB,IAAI,IAAI,CAAC;YACtBhC,qDAAc,CAAC;YACfY;QACD,EAAG,SAAK,CAAC,SAACqB;YACTL,SAAS7B,6DAAsB,CAACkC,IAAI,OAAO;YAC3CX,WAAW;QACZ;IACD,GAAG,EAAE;IAEL,IAAMY,kBAAkBpC,8CAAOA,CAAC;QAC/B,IAAI6B,OACH,qBAAO,2DAAC;YAAK,sBAAoB;WAAyBA;QAE3D,OAAOH,SAAS,GAAG,CAAC;gBAAGQ,WAAAA,IAAIG,gBAAAA,SAASC,aAAAA;iCACnC,2DAACjC,+FAAQA;gBACR,UAAU6B;gBACV,KAAKA;gBACL,UAAUG;gBACV,SAAS;2BAAM,CAACA,WAAWJ,mBAAmBC;;eAE5CG,yBAAW,2DAAC/B,uFAAIA;gBAAC,MAAMO;gBAAc,OAAOD;gBAC5C0B;;IAGL,GAAG;QAACT;QAAOH;QAAUO;KAAmB;IAExC,qBACC,2DAAC;QAAI,OAAOtB;qBACX,2DAACP,oGAAcA;QACd,MAAMS;QACN,OAAOQ,SAAS,UAAU,WAAW;QACrC,WAAWA,SAAS;QACpB,SAASU;QACT,SAASR;QACT;OAECa;AAIL;AAEAhB,gBAAgB,WAAW,GAAG;AAE9B,6DAAeA,eAAeA,EAAC;;;;;;;;;;ACxF/B,wBAAwB,GACxB,6DAAe;IACd,KAAK;eAAMmB,OAAO,QAAQ;;IAC1B,KAAK,SAACC;eAAQD,OAAO,QAAQ,GAAGC;;AACjC,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoKyB;AAxKmC;AACvB;AACb;AACgB;AACoD;AACxB;AACa;AACtC;AACmB;AACa;AACL;AACN;AACe;AACiB;AACpB;AAE9E,IAAMU,cAAc;IACnB,OAAO;AACR;AAEA,IAAMC,iBAAiB;IACtB,UAAU;IACV,QAAQ;IACR,YAAY5C,6FAA+B;IAC3C,OAAOA,6FAA+B;IACtC,WAAW;AACZ;AAEA,IAAM6C,sBAAsB;IAC3B,UAAU;IACV,KAAK;IACL,OAAO;IACP,SAAS;IACT,eAAe;IACf,YAAY;AACb;AAEA,IAAMC,uBAAuB;IAC5B,OAAO;IACP,QAAQ;IACR,gBAAgB;IAChB,QAAQ;IACR,SAAS;AACV;AAEA,IAAMC,6BAA6B;IAClC,WAAW/C,4FAA8B;IACzC,cAAcA,4FAA8B;AAC7C;AAEA,IAAMgD,kBAAkB;IACvB,YAAYhD,gGAAkC;IAC9C,eAAe;IACf,cAAcA,4FAA8B;AAC7C;AAEA,IAAMiD,gBAAgB,wCAClBD;IACH,UAAUhD,uFAAyB;;AAGpC,IAAMkD,kBAAkB,wCACpBF;IACH,UAAU;IACV,cAAchD,4FAA8B;;AAG7C,IAAMmD,eAAe;IACpB,OAAOnD,wFAA0B;IACjC,UAAUA,uFAAyB;IACnC,WAAWA,4FAA8B;AAC1C;AAEA,IAAMoD,cAAc;AACpB,IAAMC,oBAAoBrD,2FAA6B;AACvD,IAAMsD,YAAY;AAElB,IAAMC,gBAAgB;AACtB,IAAMC,qBAAqB;IAC1B,SAAS;IACT,IAAM;IACN,WAAW;AACZ;AAEA,IAAMC,qBAAqB;IAC1B,IAAMC,MAAMF,kBAAkB,CAACrB,uEAAgC,GAAG,IAAIqB,kBAAmB,WAAO;IAEhG,OAAOD,cAAc,QAAQ,CAACG;AAC/B;AAEA,IAAMC,sBAAsBC,SAAAA;WAASlE,wEAAiC,CAACkE,MAAM,MAAM,CAAC,IAAI,EAAEA;;AAE1F,IAAMC,cAAc;IACnB,IAAM/C,WAAWlB,2GAAeA;IAChC,IAA8BmB,6BAAAA,+CAAQA,CAAC,YAAhC+C,UAAuB/C,cAAdgD,aAAchD;IAE9B,IAAMiD,yBAAyBxE,kDAAWA,CAAC;QAC1CyE,KAAK,sBAAsBC,SAAAA;YAC1BA,YAAY,kBAAkB;QAC/B;IACD,GAAG,EAAE;IAEL,IAAMC,eAAe3E,kDAAWA,CAAC;QAChCyE,KAAK,kBAAkBG,SAAAA;YACtB1E,2DAAoB,CAAC;YACrB0E,eAAe,MAAM,CAACC,WAAWA,WAAW,OAAOA,WAAW;QAC/D;QAEAN,WAAW;IACZ,GAAG,EAAE;IAEL,IAAMO,qBAAqB7E,8CAAOA,CAAC;QAClC,IAAM8E,iBAAiB,wCACnBzB;YACH,iBAAkB,OAAwC,OAAlCX,sEAA+B,IAAG;;QAG3D,qBAAO,2DAAC;YAAI,OAAOoC;YAAgB,sBAAoB;;IACxD,GAAG,EAAE;IAEL,IAAMC,yBAAyB/E,8CAAOA,CAAC;6BACtC,2DAAC;YAAI,OAAOsD;yBACX,2DAACb,iDAAQA;YAAC,OAAOI,iEAAKA;yBACrB,2DAACE,+FAAQA;YACR,SAAS;YACT,QAAQL,8DAAuB;YAC/B;YACA,MAAMI,0EAAQA;;OAIf,EAAE;IAEL,IAAMkC,uBAAuBhF,8CAAOA,CAAC;6BACpC,2DAACgD,4FAAMA;YACN,MAAMW;YACN,WAAWC;YACX,SAASS;YACT,SAASK;WAERrD,SAAS;OAET;QAACA;QAAUgD;QAASK;KAAa;IAEpC,qBACC,2DAAC/B,2FAAIA,sBACJ,2DAACC,gGAAYA;QAAC;QAAW,QAAQ2B;qBAChC,2DAAC;QAAI,OAAOrB;uBAEb,2DAAC;QAAI,OAAOC;OACV0B,kCACD,2DAAC;QAAI,OAAOzB;QAAqB,WAAU;OACzC2B,sCACD,2DAAC;QAAG,OAAOvB;OAAgBd,gEAAyB,mBACpD,2DAAC;QAAG,OAAOe;OAAkBf,kEAA2B,mBACxD,2DAACtB,4GAAeA,SACf4D,oCACD,2DAAC/B,wFAAIA;QAAC,OAAOS;QAAc,MAAMG;QAAW,MAAMG;QAAsB,SAASE;OAC/E7C,SAAS;AAMhB;AAEA+C,YAAY,WAAW,GAAG;AAE1B,6DAAeA,WAAWA,EAAC;;;;;;;;;;;;;;;AC5H1B;AA5CiC;AAE3B,SAAS5D;IACf,OAAO,IAAI0E,QAAQ,SAACC,SAASC;QAC5BH,0DAAmB,CAClB,2CACAL,WACA;YACC,SAAS,SAACS;gBACT,wBAAwB,GACxB,IAAIA,SAAS,OAAO,EAAE;oBACrBF,QAAQE,SAAS,OAAO;gBACzB;gBAEAD;YACD;QACD;IAEF;AACD;AAEO,SAAS3E,cAAcwD,GAAG,EAAEqB,MAAM;IACxC,OAAO,IAAIJ,QAAQ,SAACC,SAASC;QAC5BH,0DAAmB,CAClB,mCACA;YACC,MAAM,2BAA2BhB,MAAM,oBAAoBqB,SAAS;QACrE,GACA;YACCC,SAAS,SAATA,QAAkBC,GAAG;gBACpB,IAAIA,IAAI,OAAO,KAAK,MAAM;oBACzBL;gBACD,OAAO;oBACN,gFAAgF;oBAChF,IAAIK,IAAI,QAAQ,KAAK,QAAQ;wBAC5BJ,OAAO;4BAAE,SAASI,IAAI,OAAO;4BAAEvB,KAAAA;wBAAI;oBACpC,OAAO;wBACNmB,OAAO;4BAAE,SAASI,IAAI,OAAO;wBAAC;oBAC/B;gBACD;YACD;QACD;IAEF;AACD;;;;;;;;;;;;;;;;;;;;;;;;AC3CA,MAA8G;AAC9G,MAAoG;AACpG,MAA2G;AAC3G,MAA8H;AAC9H,MAAuH;AACvH,MAAuH;AACvH,MAAwO;AACxO;AACA;;AAEA;;AAEA,4BAA4B,qGAAmB;AAC/C,wBAAwB,kHAAa;AACrC,iBAAiB,uGAAa;AAC9B,iBAAiB,+FAAM;AACvB,6BAA6B,sGAAkB;;AAE/C,aAAa,0GAAG,CAAC,kLAAO;;;;AAIkL;AAC1M,OAAO,6DAAe,kLAAO,IAAI,yLAAc,GAAG,yLAAc,YAAY,EAAC"}