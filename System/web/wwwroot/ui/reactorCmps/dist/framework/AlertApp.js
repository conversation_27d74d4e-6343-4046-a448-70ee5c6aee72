define(["react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","Storage","tokens!reactorCmps/tokens/general","create-react-class"], function(__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_Storage__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_create_react_class__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/AlertApp"], {
"./src/framework/components/mobile/AlertAppAndroid.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.object.assign.js */ "../node_modules/core-js/modules/es.object.assign.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var RenderInBody = __webpack_require__(/*! reactor/src/Atomic/components/Helpers/RenderInBody */ "../reactor/src/Atomic/components/Helpers/RenderInBody.jsx");
var Image = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Image/Image */ "../reactor/src/Atomic/components/Atoms/Image/Image.jsx");
var Utils = __webpack_require__(/*! Utils */ "Utils");
var Storage = __webpack_require__(/*! Storage */ "Storage");
var overlayStyle = {
    position: 'absolute',
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    width: '100%',
    top: 0,
    left: 0,
    backgroundImage: 'linear-gradient(#3a4291, #2e7daa)'
};
var downloadBtn = {
    border: 'none',
    outline: 'none',
    backgroundColor: 'white',
    color: '#2e7daa',
    fontSize: 20,
    borderRadius: 4,
    padding: '8px 35px',
    marginRight: '10%'
};
var cancelBtn = {
    fontSize: 20,
    textDecoration: 'none',
    marginRight: '5%',
    marginLeft: '10%',
    color: 'white'
};
var logoStyle = {
    alignSelf: 'center',
    paddingTop: 10
};
var wrapperStyle = {
    paddingBottom: 10,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%'
};
var messageStyle = {
    fontSize: 25,
    paddingTop: 20
};
var secondMessageStyle = {
    fontSize: 20,
    textAlign: 'center'
};
__webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");
module.exports = createReactClass({
    displayName: 'framework/components/mobile/AlertAppAndroid',
    getInitialState: function getInitialState() {
        return {
            show: true
        };
    },
    openSESuiteOnPlayStore: function openSESuiteOnPlayStore() {
        var me = this;
        Storage.set("AlertAppTrack", "Clicou em Baixar");
        window.location.href = "https://play.app.goo.gl/?link=https://play.google.com/store/apps/details?id=com.sesuite2";
        me.setState({
            show: false
        });
    },
    notNowClick: function notNowClick() {
        Storage.set("AlertAppTrack", "Clicou em Agora não");
        this.setState({
            show: false
        });
    },
    getImageLogo: function getImageLogo() {
        return Utils.getSystemUrl() + '/ui/desktop/lite/resources/images/suiteLogos/sesuite-hive-white.svg';
    },
    getImageApp: function getImageApp() {
        return Utils.getSystemUrl() + '/ui/desktop/lite/resources/images/mobile-screen-alert.png';
    },
    render: function render() {
        var me = this, messageContainerStyle = Object.assign({
            flexDirection: 'column',
            color: 'white'
        }, wrapperStyle);
        if (me.state.show) {
            return /*#__PURE__*/ React.createElement(RenderInBody, null, /*#__PURE__*/ React.createElement("div", {
                style: overlayStyle
            }, /*#__PURE__*/ React.createElement(Image, {
                size: "large",
                style: logoStyle,
                src: me.getImageLogo(),
                center: true
            }), /*#__PURE__*/ React.createElement("div", {
                style: messageContainerStyle
            }, /*#__PURE__*/ React.createElement("span", {
                style: messageStyle
            }, SE.t(304007)), /*#__PURE__*/ React.createElement("span", {
                style: secondMessageStyle
            }, SE.t(304008)), /*#__PURE__*/ React.createElement(Image, {
                style: logoStyle,
                height: "300px",
                src: me.getImageApp()
            })), /*#__PURE__*/ React.createElement("div", {
                style: wrapperStyle
            }, /*#__PURE__*/ React.createElement("a", {
                style: cancelBtn,
                onClick: me.notNowClick
            }, SE.t(303956)), /*#__PURE__*/ React.createElement("button", {
                style: downloadBtn,
                onClick: me.openSESuiteOnPlayStore
            }, SE.t(303955)))));
        }
        return null;
    }
});


}),
"Storage": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Storage__;

}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/components/mobile/AlertAppAndroid.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=AlertApp.js.map