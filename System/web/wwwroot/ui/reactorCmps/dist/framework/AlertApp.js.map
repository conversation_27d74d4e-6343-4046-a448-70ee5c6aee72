{"version": 3, "file": "framework/AlertApp.js", "sources": ["webpack://watch1749037385376/./src/framework/components/mobile/AlertAppAndroid.jsx"], "sourcesContent": ["var React = require('react');\nvar createReactClass = require('create-react-class');\nvar RenderInBody = require('reactor/src/Atomic/components/Helpers/RenderInBody');\nvar Image = require('reactor/src/Atomic/components/Atoms/Image/Image');\nvar Utils = require('Utils');\nvar Storage = require('Storage');\n\nvar overlayStyle = {\n\tposition: 'absolute',\n\tdisplay: 'flex',\n\tflexDirection: 'column',\n\theight: '100%',\n\twidth: '100%',\n\ttop: 0,\n\tleft: 0,\n\tbackgroundImage: 'linear-gradient(#3a4291, #2e7daa)'\n};\n\nvar downloadBtn = {\n\tborder: 'none',\n\toutline: 'none',\n\tbackgroundColor: 'white',\n\tcolor: '#2e7daa',\n\tfontSize: 20,\n\tborderRadius: 4,\n\tpadding: '8px 35px',\n\tmarginRight: '10%'\n};\n\nvar cancelBtn = {\n\tfontSize: 20,\n\ttextDecoration: 'none',\n\tmarginRight: '5%',\n\tmarginLeft: '10%',\n\tcolor: 'white'\n};\n\nvar logoStyle = {\n\talignSelf: 'center',\n\tpaddingTop: 10\n};\n\nvar wrapperStyle = {\n\tpaddingBottom: 10,\n\tdisplay: 'flex',\n\talignItems: 'center',\n\tjustifyContent: 'center',\n\twidth: '100%'\n};\n\nvar messageStyle = {\n\tfontSize: 25,\n\tpaddingTop: 20\n};\n\nvar secondMessageStyle = {\n\tfontSize: 20,\n\ttextAlign: 'center'\n};\n\nrequire(\"reactorCmps/tokens/general\");\n\nmodule.exports = createReactClass({\n\tdisplayName: 'framework/components/mobile/AlertAppAndroid',\n\n\tgetInitialState: function() {\n\t\treturn {\n\t\t\tshow: true\n\t\t};\n\t},\n\n\topenSESuiteOnPlayStore: function() {\n\t\tvar me = this;\n\n\t\tStorage.set(\"AlertAppTrack\", \"Clicou em Baixar\");\n\n\t\twindow.location.href = \"https://play.app.goo.gl/?link=https://play.google.com/store/apps/details?id=com.sesuite2\";\n\n\t\tme.setState({\n\t\t\tshow: false\n\t\t});\n\t},\n\n\tnotNowClick: function() {\n\t\tStorage.set(\"AlertAppTrack\", \"Clicou em Agora não\");\n\n\t\tthis.setState({\n\t\t\tshow: false\n\t\t});\n\t},\n\n\tgetImageLogo: function() {\n\t\treturn Utils.getSystemUrl() + '/ui/desktop/lite/resources/images/suiteLogos/sesuite-hive-white.svg';\n\t},\n\n\tgetImageApp: function() {\n\t\treturn Utils.getSystemUrl() + '/ui/desktop/lite/resources/images/mobile-screen-alert.png';\n\t},\n\n\trender: function() {\n\t\tvar me = this,\n\t\t\tmessageContainerStyle = Object.assign(\n\t\t\t\t{\n\t\t\t\t\tflexDirection: 'column',\n\t\t\t\t\tcolor: 'white'\n\t\t\t\t},\n\t\t\t\twrapperStyle\n\t\t\t);\n\n\t\tif (me.state.show) {\n\t\t\treturn (\n\t\t\t\t<RenderInBody>\n\t\t\t\t\t<div style={overlayStyle}>\n\t\t\t\t\t\t<Image size=\"large\" style={logoStyle} src={me.getImageLogo()} center />\n\t\t\t\t\t\t<div style={messageContainerStyle}>\n\t\t\t\t\t\t\t<span style={messageStyle}>{SE.t(304007)}</span>\n\t\t\t\t\t\t\t<span style={secondMessageStyle}>{SE.t(304008)}</span>\n\t\t\t\t\t\t\t<Image style={logoStyle} height=\"300px\" src={me.getImageApp()}/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div style={wrapperStyle}>\n\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\tstyle={cancelBtn}\n\t\t\t\t\t\t\t\tonClick={me.notNowClick}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{SE.t(303956)}\n\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\tstyle={downloadBtn}\n\t\t\t\t\t\t\t\tonClick={me.openSESuiteOnPlayStore}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{SE.t(303955)}\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</RenderInBody>\n\t\t\t);\n\t\t}\n\n\t\treturn null;\n\t}\n});"], "names": ["React", "require", "createReactClass", "RenderInBody", "Image", "Utils", "Storage", "overlayStyle", "downloadBtn", "cancelBtn", "logoStyle", "wrapperStyle", "messageStyle", "secondMessageStyle", "module", "getInitialState", "openSESuiteOnPlayStore", "me", "window", "notNowClick", "getImageLogo", "getImageApp", "render", "messageContainerStyle", "Object", "SE"], "mappings": ";;;AAAA;AAAA,IAAIA,QAAQC,mBAAOA,CAAC,oBAAO;AAC3B,IAAIC,mBAAmBD,mBAAOA,CAAC,8CAAoB;AACnD,IAAIE,eAAeF,mBAAOA,CAAC,qHAAoD;AAC/E,IAAIG,QAAQH,mBAAOA,CAAC,+GAAiD;AACrE,IAAII,QAAQJ,mBAAOA,CAAC,oBAAO;AAC3B,IAAIK,UAAUL,mBAAOA,CAAC,wBAAS;AAE/B,IAAIM,eAAe;IAClB,UAAU;IACV,SAAS;IACT,eAAe;IACf,QAAQ;IACR,OAAO;IACP,KAAK;IACL,MAAM;IACN,iBAAiB;AAClB;AAEA,IAAIC,cAAc;IACjB,QAAQ;IACR,SAAS;IACT,iBAAiB;IACjB,OAAO;IACP,UAAU;IACV,cAAc;IACd,SAAS;IACT,aAAa;AACd;AAEA,IAAIC,YAAY;IACf,UAAU;IACV,gBAAgB;IAChB,aAAa;IACb,YAAY;IACZ,OAAO;AACR;AAEA,IAAIC,YAAY;IACf,WAAW;IACX,YAAY;AACb;AAEA,IAAIC,eAAe;IAClB,eAAe;IACf,SAAS;IACT,YAAY;IACZ,gBAAgB;IAChB,OAAO;AACR;AAEA,IAAIC,eAAe;IAClB,UAAU;IACV,YAAY;AACb;AAEA,IAAIC,qBAAqB;IACxB,UAAU;IACV,WAAW;AACZ;AAEAZ,mBAAOA,CAAC,8DAA4B;AAEpCa,cAAc,GAAGZ,iBAAiB;IACjC,aAAa;IAEba,iBAAiB,SAAjBA;QACC,OAAO;YACN,MAAM;QACP;IACD;IAEAC,wBAAwB,SAAxBA;QACC,IAAIC,KAAK,IAAI;QAEbX,QAAQ,GAAG,CAAC,iBAAiB;QAE7BY,OAAO,QAAQ,CAAC,IAAI,GAAG;QAEvBD,GAAG,QAAQ,CAAC;YACX,MAAM;QACP;IACD;IAEAE,aAAa,SAAbA;QACCb,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,IAAI,CAAC,QAAQ,CAAC;YACb,MAAM;QACP;IACD;IAEAc,cAAc,SAAdA;QACC,OAAOf,MAAM,YAAY,KAAK;IAC/B;IAEAgB,aAAa,SAAbA;QACC,OAAOhB,MAAM,YAAY,KAAK;IAC/B;IAEAiB,QAAQ,SAARA;QACC,IAAIL,KAAK,IAAI,EACZM,wBAAwBC,OAAO,MAAM,CACpC;YACC,eAAe;YACf,OAAO;QACR,GACAb;QAGF,IAAIM,GAAG,KAAK,CAAC,IAAI,EAAE;YAClB,qBACC,oBAACd,kCACA,oBAAC;gBAAI,OAAOI;6BACX,oBAACH;gBAAM,MAAK;gBAAQ,OAAOM;gBAAW,KAAKO,GAAG,YAAY;gBAAI;8BAC9D,oBAAC;gBAAI,OAAOM;6BACX,oBAAC;gBAAK,OAAOX;eAAea,GAAG,CAAC,CAAC,wBACjC,oBAAC;gBAAK,OAAOZ;eAAqBY,GAAG,CAAC,CAAC,wBACvC,oBAACrB;gBAAM,OAAOM;gBAAW,QAAO;gBAAQ,KAAKO,GAAG,WAAW;+BAE5D,oBAAC;gBAAI,OAAON;6BACX,oBAAC;gBACA,OAAOF;gBACP,SAASQ,GAAG,WAAW;eAEtBQ,GAAG,CAAC,CAAC,wBAEP,oBAAC;gBACA,OAAOjB;gBACP,SAASS,GAAG,sBAAsB;eAEjCQ,GAAG,CAAC,CAAC;QAMZ;QAEA,OAAO;IACR;AACD"}