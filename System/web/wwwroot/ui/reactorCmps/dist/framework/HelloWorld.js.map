{"version": 3, "file": "framework/HelloWorld.js", "sources": ["webpack://watch1749037385376/./src/framework/docsExamples/HelloWorld.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport Connector from \"Connector\";\n\nexport default () => {\n\tconst [ content, setContent ] = useState(\"\");\n\n\tuseEffect(() => {\n\t\tConnector.callMachete(\"examples/framework/hello-world\", {\n\t\t\tsuccess: content => setContent(content)\n\t\t});\n\t}, [setContent]);\n\n\treturn (\n\t\t<span>{content}</span>\n\t);\n};"], "names": ["React", "useEffect", "useState", "Connector", "_useState", "content", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAmD;AACjB;AAElC,yBAAe;IACd,IAAgCI,6BAAAA,+CAAQA,CAAC,SAAjCC,UAAwBD,cAAfE,aAAeF;IAEhCH,gDAASA,CAAC;QACTE,4DAAqB,CAAC,kCAAkC;YACvD,SAASE,SAAAA;uBAAWC,WAAWD;;QAChC;IACD,GAAG;QAACC;KAAW;IAEf,qBACC,2DAAC,cAAMD;AAET,EAAE"}