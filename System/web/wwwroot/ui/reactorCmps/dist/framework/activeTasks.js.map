{"version": 3, "file": "framework/activeTasks.js", "sources": ["webpack://watch1749037385376/./src/asset/collection/InventoryTaskListCollection.jsx", "webpack://watch1749037385376/./src/asset/collection/MaintenanceTaskCollection.jsx", "webpack://watch1749037385376/./src/audit/mobile/Tasks/collection/AuditExecutionCollection.js", "webpack://watch1749037385376/./src/document/collection/DocumentTasksCollection.js", "webpack://watch1749037385376/./src/framework/components/mobile/Tasks/Helpers/activeTasks.js", "webpack://watch1749037385376/./src/framework/components/mobile/Tasks/Helpers/collectionSearch.js", "webpack://watch1749037385376/./src/framework/components/mobile/Tasks/Helpers/tasks.js", "webpack://watch1749037385376/./src/project/mobile/Tasks/collection/ProjectTaskExecution.js", "webpack://watch1749037385376/./src/timecontrol/mobile/Tasks/collection/ListActionExecution.js", "webpack://watch1749037385376/./src/workflow/collections/TaskCollection.js", "webpack://watch1749037385376/./src/workforce/mobile/Tasks/collection/TaskExecution.js"], "sourcesContent": ["var createCollection = require('SG2/collection/Factory');\n\nmodule.exports = createCollection({\n\tmodel: {\n\t\tprimaryKey: \"cdinventory\",\n\n\t\tproxyCfg: {\n\t\t\troute: \"asset/rest/inventoryTaskList.php\"\n\t\t}\n\t},\n\tcollection: {\n\t\tdefaultOrder: {}\n\t}\n});", "var createCollection = require('SG2/collection/Factory');\n\nmodule.exports = createCollection({\n\tmodel: {\n\t\tprimaryKey: \"cdgenactivity\",\n\t\tproxyCfg: {\n\t\t\troute: \"/maintenance/rest/maintenanceTaskList.php\"\n\t\t}\n\t},\n\tcollection: {\n\t\tdefaultOrder: {}\n\t}\n});", "var createCollection = require('SG2/collection/Factory');\n\nmodule.exports = createCollection({\n\tmodel: {\n\t\tprimaryKey: \"cdaudit\",\n\t\tproxyCfg: {\n\t\t\troute: \"/audit/rpc/collection/auditExecutionTaskList.php\"\n\t\t}\n\t},\n\tcollection: {\n\t\tdefaultOrder: {}\n\t}\n});", "var createCollection = require('SG2/collection/Factory');\n\nmodule.exports = function(defaultOrder) {\n\treturn createCollection({\n\t\tmodel: {\n\t\t\tprimaryKey: 'cddocument',\n\t\t\t\n\t\t\tproxyCfg: {\n\t\t\t\troute: '/document/request/document_pendency_collection.php'\n\t\t\t}\n\t\t},\n\t\tcollection: {\n\t\t\tdefaultOrder,\n\t\t\tquantity: 10\n\t\t}\n\t});\n};", "var Connector = require(\"Connector\");\nvar when = require(\"when\");\nvar tasks = require('reactorCmps/src/framework/components/mobile/Tasks/Helpers/tasks.js');\nvar workspaceInfo = require('WorkspaceInfo');\nvar collectionSearch = require('reactorCmps/src/framework/components/mobile/Tasks/Helpers/collectionSearch');\n\nfunction mapFromComponent(component, activeModules) {\n\treturn component.tasks.map(function(item) {\n\t\tvar name = activeModules[item.code];\n\n\t\tif (name) {\n\t\t\treturn {\n\t\t\t\tname: item.text,\n\t\t\t\tcount: item.count,\n\t\t\t\tcode: item.code,\n\t\t\t\tstatus: item.status,\n\t\t\t\tcomponentName: component.text,\n\t\t\t\troute: name,\n\t\t\t\toid: item.oid\n\t\t\t};\n\t\t}\n\n\t\treturn false;\n\t}).filter(function(item) {\n\t\t// remove falses\n\t\treturn item;\n\t});\n}\n\nmodule.exports = {\n\n\tgetTasks: function() {\n\t\tvar license = workspaceInfo.getUserProfile(),\n\t\t\tJSON = `[[`, defaultTasks = {};\n\n\t\tlicense = license ? license.toLowerCase() : 'manager';\n\n\t\ttasks.forEach(function(task) {\n\t\t\tdefaultTasks[task.codes[license]] = {};\n\n\t\t\tJSON += `{` + task.codes[license] + `:[]},`;\n\n\t\t\tdefaultTasks[task.codes[license]].tasks = task.tasks;\n\t\t\tdefaultTasks[task.codes[license]].component = task.component;\n\t\t});\n\n\t\tJSON += `],{\"Boolean\":\"false\"}]`;\n\n\t\treturn {\n\t\t\tdefaultActives: defaultTasks,\n\t\t\tcodes: JSON\n\t\t};\n\t},\n\n\tfilter: function(pendencies, actives) {\n\t\tvar executions = [], res = [], count = 0, item, valueToConcat, i;\n\n\t\tif (pendencies && pendencies[0] && pendencies[0].executions) {\n\t\t\texecutions = pendencies[0].executions;\n\n\t\t\tfor (; count < executions.length; count++) {\n\t\t\t\titem = executions[count];\n\n\t\t\t\tif (actives[item.code] && item.tasks) {\n\t\t\t\t\tvalueToConcat = mapFromComponent(item, actives[item.code]);\n\n\t\t\t\t\tfor (i in valueToConcat) {\n\t\t\t\t\t\tvalueToConcat[i].codeByLicense = item.code;\n\t\t\t\t\t}\n\n\t\t\t\t\tres = res.concat(valueToConcat);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn res;\n\t},\n\n\tgetParamsFromTaskObj: function(taskObj) {\n\n\t\tif (!taskObj) return \"\";\n\n\t\treturn Object.keys(taskObj).reduce(function(query, key, index, array) {\n\t\t\treturn query + key + (index !== array.length - 1 ? ',' : '');\n\t\t}, \"\");\n\t},\n\n\tgetStaticTaskList: function() {\n\t\tvar me = this,\n\t\t\ttaskObj = me.getTasks().defaultActives;\n\n\t\treturn Promise.all([me.getPermissionLookupObj(taskObj), collectionSearch.countTasks()]).then(me.getValidTaskObj.bind(me, taskObj));\n\t},\n\n\tgetPermissionLookupObj: function(taskObj) {\n\t\tvar me = this,\n\t\t\tdeferred = when.defer(),\n\t\t\tparams = me.getParamsFromTaskObj(taskObj);\n\n\t\tConnector.callBaseclass('commons/rest/check_menu_permissions.php?check=' + params, {\n\t\t\tsuccess: function(res) {\n\t\t\t\tif (res.success) {\n\t\t\t\t\tdeferred.resolve(res.results && res.results[0]);\n\t\t\t\t} else {\n\t\t\t\t\tdeferred.reject();\n\t\t\t\t}\n\t\t\t},\n\t\t\terror: function() {\n\t\t\t\tdeferred.reject();\n\t\t\t}\n\t\t});\n\n\t\treturn deferred.promise;\n\t},\n\n\tgetValidTaskObj: function(taskObj, lookupObj) {\n\t\tconst [permissionObj, countObj] = lookupObj;\n\n\t\treturn Object.keys(taskObj).reduce(function(result, codeByLicense) {\n\t\t\tvar task = taskObj[codeByLicense];\n\n\t\t\tif (!permissionObj[codeByLicense]) return result;\n\n\t\t\treturn result.concat(\n\t\t\t\tObject.keys(task.tasks).reduce(function(innerResult, taskCode) {\n\t\t\t\t\tif (countObj[`${task.component}_${taskCode}`] === 0) return innerResult;\n\n\t\t\t\t\treturn innerResult.concat(\n\t\t\t\t\t\tObject.assign({}, task.tasks[taskCode], {\n\t\t\t\t\t\t\tcodeByLicense: codeByLicense,\n\t\t\t\t\t\t\toid: ['execution', codeByLicense, taskCode].join(','),\n\t\t\t\t\t\t\tquantity: countObj[`${task.component}_${taskCode}`]\n\t\t\t\t\t\t})\n\t\t\t\t\t);\n\t\t\t\t}, []));\n\t\t}, []);\n\t}\n};", "/* istanbul ignore file - collection search for tasks */\nimport ProjectTasksCollection from \"reactorCmps/src/project/mobile/Tasks/collection/ProjectTaskExecution\";\nimport DocumentTasksCollection from \"reactorCmps/src/document/collection/DocumentTasksCollection\";\nimport AuditTasksCollection from \"reactorCmps/src/audit/mobile/Tasks/collection/AuditExecutionCollection\";\nimport WorkflowTasksCollection from \"reactorCmps/src/workflow/collections/TaskCollection\";\nimport InventoryTasksCollection from \"reactorCmps/src/asset/collection/InventoryTaskListCollection\";\nimport MaintenanceTasksCollection from \"reactorCmps/src/asset/collection/MaintenanceTaskCollection\";\nimport TimeControlTasksCollection from \"reactorCmps/src/timecontrol/mobile/Tasks/collection/ListActionExecution\";\nimport WorkforceTasksCollection from \"reactorCmps/src/workforce/mobile/Tasks/collection/TaskExecution\";\n\nconst TASKS = {\n\tPROJECT_EXECUTION: 'PROJECT_' + 5,\n\tAUDIT_EXECUTION: 'AUDIT_' + 1,\n\tWORKFLOW_EXECUTION: 'WORKFLOW_' + 1,\n\tASSET_INVENTORY_EXECUTION: 'ASSET_' + 302160,\n\tTIMECONTROL_EXECUTION: 'TIMECONTROL_' + 1,\n\tWORKFORCE_TASK_EXECUTION: 'WORKFORCE_' + 4,\n\tDOCUMENT_REVISION: 'DOCUMENT_' + 50,\n\tDOCUMENT_ACKNOWLEDGMENT: 'DOCUMENT_' + 62,\n\tMAINTENANCE_EXECUTION: 'MAINTENANCE_' + 3\n};\n\nconst countCollection = (collection, resolve) => {\n\tcollection.count().then((size) => {\n\t\tresolve(size);\n\t});\n};\n\nexport const countTasks = async() => {\n\tconst projectCollection = new ProjectTasksCollection();\n\tconst documentCollection = new (DocumentTasksCollection({ NMTITLE: 'ASC' }))();\n\tconst auditCollection = new AuditTasksCollection();\n\tconst workflowCollection = new WorkflowTasksCollection();\n\tconst inventoryCollection = new InventoryTasksCollection();\n\tconst maintenanceCollection = new MaintenanceTasksCollection();\n\tconst timeControlCollection = new TimeControlTasksCollection();\n\tconst workforceCollection = new WorkforceTasksCollection();\n\n\tconst collections = [\n\t\tprojectCollection,\n\t\tauditCollection,\n\t\tworkflowCollection,\n\t\tinventoryCollection,\n\t\ttimeControlCollection,\n\t\tworkforceCollection\n\t];\n\n\tconst quantityPromises = collections.map((collection) => {\n\t\tcollection.quantity = 1;\n\t\treturn new Promise((resolve) => {\n\t\t\tcollection.search().then(() => {\n\t\t\t\tcountCollection(collection, resolve);\n\t\t\t});\n\t\t});\n\t});\n\n\tdocumentCollection.quantity = 1;\n\tquantityPromises.push(...[\n\t\tnew Promise((resolve) => {\n\t\t\tdocumentCollection.search({ pendency: 'document_revision_phase' }).then(() => {\n\t\t\t\tcountCollection(documentCollection, resolve);\n\t\t\t});\n\t\t}),\n\t\tnew Promise((resolve) => {\n\t\t\tdocumentCollection.search({ pendency: 'document_know_publishing' }).then(() => {\n\t\t\t\tcountCollection(documentCollection, resolve);\n\t\t\t});\n\t\t})\n\t]);\n\n\tmaintenanceCollection.quantity = 1;\n\tquantityPromises.push(\n\t\tnew Promise((resolve) => {\n\t\t\tmaintenanceCollection.search({ cdprod: 126 }).then(() => {\n\t\t\t\tcountCollection(maintenanceCollection, resolve);\n\t\t\t});\n\t\t})\n\t);\n\n\tconst quantity = await Promise.all(quantityPromises);\n\tconst tasksValue = Object.values(TASKS);\n\n\treturn tasksValue.reduce((tasks, task, index) => {\n\t\treturn { ...tasks, [task]: quantity[index] };\n\t}, {});\n};\n", "module.exports = [\n\t{\n\t\tcomponent: 'ASSET',\n\t\tcodes: {\n\t\t\tmanager: '109',\n\t\t\tstaff: '118',\n\t\t\tbasic: '110',\n\t\t\tview: '232'\n\t\t},\n\t\ttasks: {\n\t\t\t302160: {\n\t\t\t\tname: 302160,\n\t\t\t\tcomponentName: 113688,\n\t\t\t\troute: \"asset/execute\"\n\t\t\t}\n\t\t}\n\t},\n\t{\n\t\tcomponent: 'WORKFORCE',\n\t\tcodes: {\n\t\t\tmanager: '205',\n\t\t\tstaff: '206',\n\t\t\tbasic: '207',\n\t\t\tview: '254'\n\t\t},\n\t\ttasks: {\n\t\t\t4: {\n\t\t\t\tname: 112441,\n\t\t\t\tcomponentName: 202568,\n\t\t\t\troute: \"workforce/executeTask\"\n\t\t\t}\n\t\t}\n\t},\n\t{\n\t\tcomponent: 'DOCUMENT',\n\t\tcodes: {\n\t\t\tmanager: '73',\n\t\t\tstaff: '88',\n\t\t\tbasic: '87',\n\t\t\tview: '240'\n\t\t},\n\t\ttasks: {\n\t\t\t// 50, 62 = cdPendency\n\t\t\t50: {\n\t\t\t\tname: 113305,\n\t\t\t\tcomponentName: 103814,\n\t\t\t\troute: \"document/execute\"\n\t\t\t},\n\t\t\t62: {\n\t\t\t\tname: 200248,\n\t\t\t\tcomponentName: 103814,\n\t\t\t\troute: \"document/knowledge\"\n\t\t\t}\n\t\t}\n\t},\n\t{\n\t\tcomponent: 'MAINTENANCE',\n\t\tcodes: {\n\t\t\tmanager: '126',\n\t\t\tstaff: '128',\n\t\t\tbasic: '127',\n\t\t\tview: '238'\n\t\t},\n\t\ttasks: {\n\t\t\t3: {\n\t\t\t\tname: 112441,\n\t\t\t\tcomponentName: 100289,\n\t\t\t\troute: \"maintenance/execute\"\n\t\t\t}\n\t\t}\n\t},\n\t{\n\t\tcomponent: 'PROJECT',\n\t\tcodes: {\n\t\t\tmanager: '41',\n\t\t\tstaff: '42',\n\t\t\tbasic: '43',\n\t\t\tview: '256'\n\t\t},\n\t\ttasks: {\n\t\t\t5: {\n\t\t\t\tname: 112441,\n\t\t\t\tcomponentName: 102175,\n\t\t\t\troute: \"project/executeTask\"\n\t\t\t}\n\t\t}\n\t},\n\t{\n\t\tcomponent: 'WORKFLOW',\n\t\tcodes: {\n\t\t\tmanager: '104',\n\t\t\tstaff: '105',\n\t\t\tbasic: '106',\n\t\t\tview: '258'\n\t\t},\n\t\ttasks: {\n\t\t\t// 1 = cdPendency\n\t\t\t1: {\n\t\t\t\tname: 112441,\n\t\t\t\tcomponentName: 108546,\n\t\t\t\troute: \"workflow/execute\"\n\t\t\t}\n\t\t}\n\n\t},\n\t{\n\t\tcomponent: 'AUDIT',\n\t\tcodes: {\n\t\t\tmanager: '90',\n\t\t\tstaff: '91',\n\t\t\tbasic: '92',\n\t\t\tview: '255'\n\t\t},\n\t\ttasks: {\n\t\t\t1: {\n\t\t\t\tname: 103953,\n\t\t\t\tcomponentName: 102208,\n\t\t\t\troute: \"audit/execute\"\n\t\t\t}\n\t\t}\n\t},\n\t{\n\t\tcomponent: 'TIMECONTROL',\n\t\tcodes: {\n\t\t\tmanager: '174',\n\t\t\tstaff: '175',\n\t\t\tbasic: '176',\n\t\t\tview: '259'\n\t\t},\n\t\ttasks: {\n\t\t\t1: {\n\t\t\t\tname: 210797,\n\t\t\t\tcomponentName: 100475,\n\t\t\t\troute: \"timecontrol/execute\"\n\t\t\t}\n\t\t}\n\t}\n];", "var createCollection = require('SG2/collection/Factory');\n\nmodule.exports = createCollection({\n\tmodel: {\n\t\tprimaryKey: \"cdtask\",\n\t\tproxyCfg: {\n\t\t\troute: \"/project/rest/projectTaskExecutionPendencyList.php\"\n\t\t}\n\t},\n\tcollection: {\n\t\tdefaultOrder: {}\n\t}\n});", "var createCollection = require('SG2/collection/Factory');\n\nmodule.exports = createCollection({\n\tmodel: {\n\t\tprimaryKey: \"cdgenactivity\",\n\t\tproxyCfg: {\n\t\t\troute: \"/timecontrol/rest/mobile/tasks.php\"\n\t\t}\n\t},\n\tcollection: {\n\t\tdefaultOrder: {}\n\t}\n});", "var createCollection = require('SG2/collection/Factory');\n\nmodule.exports = createCollection({\n\tmodel: {\n\t\tprimaryKey: \"idtask\",\n\n\t\tproxyCfg: {\n\t\t\troute: \"workflow/request/pendency.php\"\n\t\t}\n\t},\n\n\tcollection: {\n\t\tsilent: true,\n\n\t\tdefaultOrder: {\n\t\t\tid: \"ASC\"\n\t\t},\n\n\t\tquantity: 10\n\t}\n});", "var createCollection = require('SG2/collection/Factory');\n\nmodule.exports = createCollection({\n\tmodel: {\n\t\tprimaryKey: \"cdtask\",\n\t\tproxyCfg: {\n\t\t\troute: \"/workforce/rest/mobile/taskExecutionPendencyList.php\"\n\t\t}\n\t},\n\tcollection: {\n\t\tdefaultOrder: {}\n\t}\n});"], "names": ["createCollection", "require", "module", "defaultOrder", "Connector", "when", "tasks", "workspaceInfo", "collectionSearch", "mapFromComponent", "component", "activeModules", "item", "name", "getTasks", "license", "JSON", "defaultTasks", "task", "filter", "pendencies", "actives", "executions", "res", "count", "valueToConcat", "i", "getParamsFromTaskObj", "taskObj", "Object", "query", "key", "index", "array", "getStaticTaskList", "me", "Promise", "getPermissionLookupObj", "deferred", "params", "success", "error", "getValidTaskObj", "lookupObj", "_lookupObj", "permissionObj", "count<PERSON>b<PERSON>", "result", "codeByLicense", "innerResult", "taskCode", "ProjectTasksCollection", "DocumentTasksCollection", "AuditTasksCollection", "WorkflowTasksCollection", "InventoryTasksCollection", "MaintenanceTasksCollection", "TimeControlTasksCollection", "WorkforceTasksCollection", "TASKS", "countCollection", "collection", "resolve", "size", "countTasks", "_quantityPromises", "projectCollection", "documentCollection", "auditCollection", "workflowCollection", "inventoryCollection", "maintenanceCollection", "timeControlCollection", "workforceCollection", "collections", "quantityPromises", "quantity", "tasksValue"], "mappings": ";;;AAAA,IAAIA,mBAAmBC,mBAAOA,CAAC,sDAAwB;AAEvDC,cAAc,GAAGF,iBAAiB;IACjC,OAAO;QACN,YAAY;QAEZ,UAAU;YACT,OAAO;QACR;IACD;IACA,YAAY;QACX,cAAc,CAAC;IAChB;AACD;;;;;ACbA,IAAIA,mBAAmBC,mBAAOA,CAAC,sDAAwB;AAEvDC,cAAc,GAAGF,iBAAiB;IACjC,OAAO;QACN,YAAY;QACZ,UAAU;YACT,OAAO;QACR;IACD;IACA,YAAY;QACX,cAAc,CAAC;IAChB;AACD;;;;;ACZA,IAAIA,mBAAmBC,mBAAOA,CAAC,sDAAwB;AAEvDC,cAAc,GAAGF,iBAAiB;IACjC,OAAO;QACN,YAAY;QACZ,UAAU;YACT,OAAO;QACR;IACD;IACA,YAAY;QACX,cAAc,CAAC;IAChB;AACD;;;;;ACZA,IAAIA,mBAAmBC,mBAAOA,CAAC,sDAAwB;AAEvDC,cAAc,GAAG,SAASC,YAAY;IACrC,OAAOH,iBAAiB;QACvB,OAAO;YACN,YAAY;YAEZ,UAAU;gBACT,OAAO;YACR;QACD;QACA,YAAY;YACXG,cAAAA;YACA,UAAU;QACX;IACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAIC,YAAYH,mBAAOA,CAAC,4BAAW;AACnC,IAAII,OAAOJ,mBAAOA,CAAC,kBAAM;AACzB,IAAIK,QAAQL,mBAAOA,CAAC,oIAAoE;AACxF,IAAIM,gBAAgBN,mBAAOA,CAAC,oCAAe;AAC3C,IAAIO,mBAAmBP,mBAAOA,CAAC,uJAA4E;AAE3G,SAASQ,iBAAiBC,SAAS,EAAEC,aAAa;IACjD,OAAOD,UAAU,KAAK,CAAC,GAAG,CAAC,SAASE,IAAI;QACvC,IAAIC,OAAOF,aAAa,CAACC,KAAK,IAAI,CAAC;QAEnC,IAAIC,MAAM;YACT,OAAO;gBACN,MAAMD,KAAK,IAAI;gBACf,OAAOA,KAAK,KAAK;gBACjB,MAAMA,KAAK,IAAI;gBACf,QAAQA,KAAK,MAAM;gBACnB,eAAeF,UAAU,IAAI;gBAC7B,OAAOG;gBACP,KAAKD,KAAK,GAAG;YACd;QACD;QAEA,OAAO;IACR,GAAG,MAAM,CAAC,SAASA,IAAI;QACtB,gBAAgB;QAChB,OAAOA;IACR;AACD;AAEAV,cAAc,GAAG;IAEhBY,UAAU,SAAVA;QACC,IAAIC,UAAUR,cAAc,cAAc,IACzCS,OAAQ,MAAKC,eAAe,CAAC;QAE9BF,UAAUA,UAAUA,QAAQ,WAAW,KAAK;QAE5CT,MAAM,OAAO,CAAC,SAASY,IAAI;YAC1BD,YAAY,CAACC,KAAK,KAAK,CAACH,QAAQ,CAAC,GAAG,CAAC;YAErCC,QAAS,MAAKE,KAAK,KAAK,CAACH,QAAQ,GAAI;YAErCE,YAAY,CAACC,KAAK,KAAK,CAACH,QAAQ,CAAC,CAAC,KAAK,GAAGG,KAAK,KAAK;YACpDD,YAAY,CAACC,KAAK,KAAK,CAACH,QAAQ,CAAC,CAAC,SAAS,GAAGG,KAAK,SAAS;QAC7D;QAEAF,QAAS;QAET,OAAO;YACN,gBAAgBC;YAChB,OAAOD;QACR;IACD;IAEAG,QAAQ,SAARA,OAAiBC,UAAU,EAAEC,OAAO;QACnC,IAAIC,aAAa,EAAE,EAAEC,MAAM,EAAE,EAAEC,QAAQ,GAAGZ,MAAMa,eAAeC;QAE/D,IAAIN,cAAcA,UAAU,CAAC,EAAE,IAAIA,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE;YAC5DE,aAAaF,UAAU,CAAC,EAAE,CAAC,UAAU;YAErC,MAAOI,QAAQF,WAAW,MAAM,EAAEE,QAAS;gBAC1CZ,OAAOU,UAAU,CAACE,MAAM;gBAExB,IAAIH,OAAO,CAACT,KAAK,IAAI,CAAC,IAAIA,KAAK,KAAK,EAAE;oBACrCa,gBAAgBhB,iBAAiBG,MAAMS,OAAO,CAACT,KAAK,IAAI,CAAC;oBAEzD,IAAKc,KAAKD,cAAe;wBACxBA,aAAa,CAACC,EAAE,CAAC,aAAa,GAAGd,KAAK,IAAI;oBAC3C;oBAEAW,MAAMA,IAAI,MAAM,CAACE;gBAClB;YACD;QACD;QAEA,OAAOF;IACR;IAEAI,sBAAsB,SAAtBA,qBAA+BC,OAAO;QAErC,IAAI,CAACA,SAAS,OAAO;QAErB,OAAOC,OAAO,IAAI,CAACD,SAAS,MAAM,CAAC,SAASE,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK;YACnE,OAAOH,QAAQC,MAAOC,CAAAA,UAAUC,MAAM,MAAM,GAAG,IAAI,MAAM,EAAC;QAC3D,GAAG;IACJ;IAEAC,mBAAmB,SAAnBA;QACC,IAAIC,KAAK,IAAI,EACZP,UAAUO,GAAG,QAAQ,GAAG,cAAc;QAEvC,OAAOC,QAAQ,GAAG,CAAC;YAACD,GAAG,sBAAsB,CAACP;YAAUpB,iBAAiB,UAAU;SAAG,EAAE,IAAI,CAAC2B,GAAG,eAAe,CAAC,IAAI,CAACA,IAAIP;IAC1H;IAEAS,wBAAwB,SAAxBA,uBAAiCT,OAAO;QACvC,IAAIO,KAAK,IAAI,EACZG,WAAWjC,KAAK,KAAK,IACrBkC,SAASJ,GAAG,oBAAoB,CAACP;QAElCxB,UAAU,aAAa,CAAC,mDAAmDmC,QAAQ;YAClFC,SAAS,SAATA,QAAkBjB,GAAG;gBACpB,IAAIA,IAAI,OAAO,EAAE;oBAChBe,SAAS,OAAO,CAACf,IAAI,OAAO,IAAIA,IAAI,OAAO,CAAC,EAAE;gBAC/C,OAAO;oBACNe,SAAS,MAAM;gBAChB;YACD;YACAG,OAAO,SAAPA;gBACCH,SAAS,MAAM;YAChB;QACD;QAEA,OAAOA,SAAS,OAAO;IACxB;IAEAI,iBAAiB,SAAjBA,gBAA0Bd,OAAO,EAAEe,SAAS;QAC3C,IAAkCC,8BAAAA,eAA3BC,gBAA2BD,eAAZE,WAAYF;QAElC,OAAOf,OAAO,IAAI,CAACD,SAAS,MAAM,CAAC,SAASmB,MAAM,EAAEC,aAAa;YAChE,IAAI9B,OAAOU,OAAO,CAACoB,cAAc;YAEjC,IAAI,CAACH,aAAa,CAACG,cAAc,EAAE,OAAOD;YAE1C,OAAOA,OAAO,MAAM,CACnBlB,OAAO,IAAI,CAACX,KAAK,KAAK,EAAE,MAAM,CAAC,SAAS+B,WAAW,EAAEC,QAAQ;gBAC5D,IAAIJ,QAAQ,CAAE,GAAoBI,OAAlBhC,KAAK,SAAS,EAAC,KAAY,OAATgC,UAAW,KAAK,GAAG,OAAOD;gBAE5D,OAAOA,YAAY,MAAM,CACxBpB,OAAO,MAAM,CAAC,CAAC,GAAGX,KAAK,KAAK,CAACgC,SAAS,EAAE;oBACvC,eAAeF;oBACf,KAAK;wBAAC;wBAAaA;wBAAeE;qBAAS,CAAC,IAAI,CAAC;oBACjD,UAAUJ,QAAQ,CAAE,GAAoBI,OAAlBhC,KAAK,SAAS,EAAC,KAAY,OAATgC,UAAW;gBACpD;YAEF,GAAG,EAAE;QACP,GAAG,EAAE;IACN;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzIA,sDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AApFwG;AACR;AACQ;AAChB;AACU;AACA;AACa;AACV;AAEvG,IAAMS,QAAQ;IACb,mBAAmB,aAAa;IAChC,iBAAiB,WAAW;IAC5B,oBAAoB,cAAc;IAClC,2BAA2B,WAAW;IACtC,uBAAuB,iBAAiB;IACxC,0BAA0B,eAAe;IACzC,mBAAmB,cAAc;IACjC,yBAAyB,cAAc;IACvC,uBAAuB,iBAAiB;AACzC;AAEA,IAAMC,kBAAkB,SAACC,YAAYC;IACpCD,WAAW,KAAK,GAAG,IAAI,CAAC,SAACE;QACxBD,QAAQC;IACT;AACD;AAEO,IAAMC;eAAa;YA6BzBC,mBA5BMC,mBACAC,oBACAC,iBACAC,oBACAC,qBACAC,uBACAC,uBACAC,qBAEAC,aASAC,kBAgCAC,UACAC;;;;oBAnDAX,oBAAoB,IAAIf,6GAAsBA;oBAC9CgB,qBAAqB,IAAKf,CAAAA,mGAAuBA,CAAC;wBAAE,SAAS;oBAAM,EAAC;oBACpEgB,kBAAkB,IAAIf,gHAAoBA;oBAC1CgB,qBAAqB,IAAIf,6FAAuBA;oBAChDgB,sBAAsB,IAAIf,sGAAwBA;oBAClDgB,wBAAwB,IAAIf,oGAA0BA;oBACtDgB,wBAAwB,IAAIf,iHAA0BA;oBACtDgB,sBAAsB,IAAIf,yGAAwBA;oBAElDgB;wBACLR;wBACAE;wBACAC;wBACAC;wBACAE;wBACAC;;oBAGKE,mBAAmBD,YAAY,GAAG,CAAC,SAACb;wBACzCA,WAAW,QAAQ,GAAG;wBACtB,OAAO,IAAIzB,QAAQ,SAAC0B;4BACnBD,WAAW,MAAM,GAAG,IAAI,CAAC;gCACxBD,gBAAgBC,YAAYC;4BAC7B;wBACD;oBACD;oBAEAK,mBAAmB,QAAQ,GAAG;oBAC9BF,CAAAA,oBAAAA,kBAAiB,IAAI,OAArBA;wBACC,IAAI7B,QAAQ,SAAC0B;4BACZK,mBAAmB,MAAM,CAAC;gCAAE,UAAU;4BAA0B,GAAG,IAAI,CAAC;gCACvEP,gBAAgBO,oBAAoBL;4BACrC;wBACD;wBACA,IAAI1B,QAAQ,SAAC0B;4BACZK,mBAAmB,MAAM,CAAC;gCAAE,UAAU;4BAA2B,GAAG,IAAI,CAAC;gCACxEP,gBAAgBO,oBAAoBL;4BACrC;wBACD;;oBAGDS,sBAAsB,QAAQ,GAAG;oBACjCI,iBAAiB,IAAI,CACpB,IAAIvC,QAAQ,SAAC0B;wBACZS,sBAAsB,MAAM,CAAC;4BAAE,QAAQ;wBAAI,GAAG,IAAI,CAAC;4BAClDX,gBAAgBW,uBAAuBT;wBACxC;oBACD;oBAGgB;;wBAAM1B,QAAQ,GAAG,CAACuC;;;oBAA7BC,WAAW;oBACXC,aAAahD,OAAO,MAAM,CAAC8B;oBAEjC;;wBAAOkB,WAAW,MAAM,CAAC,SAACvE,OAAOY,MAAMc;4BACtC,OAAO,wCAAK1B,QAAO,qBAACY,MAAO0D,QAAQ,CAAC5C,MAAM;wBAC3C,GAAG,CAAC;;;;IACL;oBAzDagC;;;IAyDX;;;;;ACrFF9D,cAAc,GAAG;IAChB;QACC,WAAW;QACX,OAAO;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,MAAM;QACP;QACA,OAAO;YACN,QAAQ;gBACP,MAAM;gBACN,eAAe;gBACf,OAAO;YACR;QACD;IACD;IACA;QACC,WAAW;QACX,OAAO;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,MAAM;QACP;QACA,OAAO;YACN,GAAG;gBACF,MAAM;gBACN,eAAe;gBACf,OAAO;YACR;QACD;IACD;IACA;QACC,WAAW;QACX,OAAO;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,MAAM;QACP;QACA,OAAO;YACN,sBAAsB;YACtB,IAAI;gBACH,MAAM;gBACN,eAAe;gBACf,OAAO;YACR;YACA,IAAI;gBACH,MAAM;gBACN,eAAe;gBACf,OAAO;YACR;QACD;IACD;IACA;QACC,WAAW;QACX,OAAO;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,MAAM;QACP;QACA,OAAO;YACN,GAAG;gBACF,MAAM;gBACN,eAAe;gBACf,OAAO;YACR;QACD;IACD;IACA;QACC,WAAW;QACX,OAAO;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,MAAM;QACP;QACA,OAAO;YACN,GAAG;gBACF,MAAM;gBACN,eAAe;gBACf,OAAO;YACR;QACD;IACD;IACA;QACC,WAAW;QACX,OAAO;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,MAAM;QACP;QACA,OAAO;YACN,iBAAiB;YACjB,GAAG;gBACF,MAAM;gBACN,eAAe;gBACf,OAAO;YACR;QACD;IAED;IACA;QACC,WAAW;QACX,OAAO;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,MAAM;QACP;QACA,OAAO;YACN,GAAG;gBACF,MAAM;gBACN,eAAe;gBACf,OAAO;YACR;QACD;IACD;IACA;QACC,WAAW;QACX,OAAO;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,MAAM;QACP;QACA,OAAO;YACN,GAAG;gBACF,MAAM;gBACN,eAAe;gBACf,OAAO;YACR;QACD;IACD;CACA;;;;;ACzID,IAAIF,mBAAmBC,mBAAOA,CAAC,sDAAwB;AAEvDC,cAAc,GAAGF,iBAAiB;IACjC,OAAO;QACN,YAAY;QACZ,UAAU;YACT,OAAO;QACR;IACD;IACA,YAAY;QACX,cAAc,CAAC;IAChB;AACD;;;;;ACZA,IAAIA,mBAAmBC,mBAAOA,CAAC,sDAAwB;AAEvDC,cAAc,GAAGF,iBAAiB;IACjC,OAAO;QACN,YAAY;QACZ,UAAU;YACT,OAAO;QACR;IACD;IACA,YAAY;QACX,cAAc,CAAC;IAChB;AACD;;;;;ACZA,IAAIA,mBAAmBC,mBAAOA,CAAC,sDAAwB;AAEvDC,cAAc,GAAGF,iBAAiB;IACjC,OAAO;QACN,YAAY;QAEZ,UAAU;YACT,OAAO;QACR;IACD;IAEA,YAAY;QACX,QAAQ;QAER,cAAc;YACb,IAAI;QACL;QAEA,UAAU;IACX;AACD;;;;;ACpBA,IAAIA,mBAAmBC,mBAAOA,CAAC,sDAAwB;AAEvDC,cAAc,GAAGF,iBAAiB;IACjC,OAAO;QACN,YAAY;QACZ,UAAU;YACT,OAAO;QACR;IACD;IACA,YAAY;QACX,cAAc,CAAC;IAChB;AACD"}