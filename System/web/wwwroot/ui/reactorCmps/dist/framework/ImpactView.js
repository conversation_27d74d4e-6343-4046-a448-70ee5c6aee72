define(["tokens!reactorCmps/tokens/general","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","create-react-class"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_create_react_class__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/ImpactView"], {
"./impact/app.js": (function (__unused_webpack_module, exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.array.sort.js */ "../node_modules/core-js/modules/es.array.sort.js");
__webpack_require__(/*! core-js/modules/es.array.for-each.js */ "../node_modules/core-js/modules/es.array.for-each.js");
__webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
__webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
__webpack_require__(/*! core-js/modules/es.string.split.js */ "../node_modules/core-js/modules/es.string.split.js");
__webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
exports.stats = null;
exports.mapModules = null;
exports.mapChunks = null;
function load(stats) {
    stats.assets.sort(function(a, b) {
        return b.size - a.size;
    });
    stats.modules.sort(function(a, b) {
        return a.id - b.id;
    });
    var mapModules = {};
    var mapModulesIdent = {};
    var mapModulesUid = {};
    stats.modules.forEach(function(module, idx) {
        mapModules[module.id] = module;
        mapModulesIdent["$" + module.identifier] = module;
        mapModulesUid[module.uid = idx] = module;
        module.dependencies = [];
    });
    var mapChunks = {};
    stats.chunks.forEach(function(chunk) {
        mapChunks[chunk.id] = chunk;
        chunk.children = [];
    });
    stats.modules.forEach(function(module) {
        module.reasons.forEach(function(reason) {
            var m = mapModulesIdent["$" + reason.moduleIdentifier];
            if (!m) return;
            reason.moduleUid = m.uid;
            m.dependencies.push({
                type: reason.type,
                moduleId: module.id,
                moduleUid: module.uid,
                module: module.name,
                userRequest: reason.userRequest,
                loc: reason.loc
            });
        });
        module.issuerUid = mapModulesIdent["$" + module.issuer] && mapModulesIdent["$" + module.issuer].uid;
        (function setTimestamp(module) {
            if (typeof module.timestamp === "number") return module.timestamp;
            if (!module.profile) return;
            var factory = module.profile.factory || 0;
            var building = module.profile.building || 0;
            module.time = factory + building;
            if (!module.issuer) return module.timestamp = module.time;
            var issuer = mapModulesIdent["$" + module.issuer];
            if (!issuer) return module.timestamp = NaN;
            setTimestamp(issuer);
            module.timestamp = issuer.timestamp + module.time;
        })(module);
    });
    stats.chunks.forEach(function(chunk) {
        chunk.parents.forEach(function(parent) {
            var c = mapChunks[parent];
            c.children.push(chunk.id);
        });
        chunk.origins.forEach(function(origin) {
            var m = mapModulesIdent["$" + origin.moduleIdentifier];
            if (!m) return;
            origin.moduleUid = m.uid;
        });
    });
    stats.modules.forEach(function(module) {
        module.dependencies.sort(function(a, b) {
            if (!a.loc && !b.loc) return 0;
            if (!a.loc) return 1;
            if (!b.loc) return -1;
            a = a.loc.split(/[:-]/);
            b = b.loc.split(/[:-]/);
            if (+a[0] < +b[0]) return -1;
            if (+a[0] > +b[0]) return 1;
            if (+a[1] < +b[1]) return -1;
            if (+a[1] > +b[1]) return 1;
            return 0;
        });
    });
    var maxLength = 0;
    stats.assets.forEach(function(a) {
        if (a.name.length > maxLength) maxLength = a.name.length;
    });
    stats.assets.forEach(function(a) {
        a.normalizedName = a.name;
        while(a.normalizedName.length < maxLength)a.normalizedName = " " + a.normalizedName;
    });
    stats.assets.sort(function(a, b) {
        a = a.normalizedName;
        b = b.normalizedName;
        return a < b ? -1 : 1;
    });
    exports.stats = stats;
    exports.mapChunks = mapChunks;
    exports.mapModules = mapModules;
    exports.mapModulesUid = mapModulesUid;
    exports.mapModulesIdent = mapModulesIdent;
}
exports.load = load;


}),
"./impact/helpers.js": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.array.index-of.js */ "../node_modules/core-js/modules/es.array.index-of.js");
module.exports = {
    getColor: function getColor(module1, impact, colors) {
        if (module1.reasons.length === 0) {
            return colors.entry;
        } else if (impact.indexOf(module1.id) >= 0) {
            return colors.impact;
        }
        return "#ccc";
    }
};


}),
"./impact/modules.js": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.array.for-each.js */ "../node_modules/core-js/modules/es.array.for-each.js");
__webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
__webpack_require__(/*! core-js/modules/es.array.filter.js */ "../node_modules/core-js/modules/es.array.filter.js");
__webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
__webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
__webpack_require__(/*! core-js/modules/es.array.find.js */ "../node_modules/core-js/modules/es.array.find.js");
module.exports.go = function(element, colors, onActive) {
    var app = __webpack_require__(/*! ./app */ "./impact/app.js");
    var helpers = __webpack_require__(/*! ./helpers */ "./impact/helpers.js");
    var nodes = [];
    var edges = [];
    var moduleCount = app.stats.modules.length;
    var maxTimestamp = 0;
    var maxSize = 0;
    app.stats.modules.forEach(function(module1, idx) {
        if (module1.size > maxSize) maxSize = module1.size;
        if (module1.timestamp > maxTimestamp) maxTimestamp = module1.timestamp;
    });
    app.stats.modules.forEach(function(module1, idx) {
        var done = {};
        var uniqueReasons = module1.reasons.filter(function(reason) {
            var parent = reason.module;
            if (done["$" + parent]) return false;
            done["$" + parent] = true;
            return true;
        });
        var uid = module1.uid;
        nodes.push({
            id: "module" + uid,
            uid: uid,
            moduleUid: uid,
            moduleId: module1.id,
            module: module1,
            type: "webpack",
            size: module1.size + 1,
            label: "[" + module1.id + "] " + module1.name,
            shortLabel: "" + module1.id,
            x: Math.cos(idx / moduleCount * Math.PI * 2) * Math.sqrt(uniqueReasons.length + 1) * Math.sqrt(moduleCount),
            y: Math.sin(idx / moduleCount * Math.PI * 2) * Math.sqrt(uniqueReasons.length + 1) * Math.sqrt(moduleCount),
            originalColor: colors.nodeDefault,
            color: helpers.getColor(module1, app.stats.impact, colors)
        });
        uniqueReasons.forEach(function(reason) {
            var parentIdent = reason.moduleIdentifier;
            var parentModule = app.mapModulesIdent["$" + parentIdent];
            if (!parentModule) return;
            var weight = 1;
            var async = false;
            edges.push({
                id: "edge" + module1.uid + "-" + +parentModule.uid,
                sourceModuleUid: parentModule.uid,
                sourceModule: parentModule,
                source: "module" + parentModule.uid,
                targetModule: module1,
                targetModuleUid: module1.uid,
                target: "module" + module1.uid,
                arrow: "target",
                type: "arrow",
                lineDash: [
                    5
                ],
                originalColor: colors.edgeDefault,
                color: colors.edgeDefault,
                size: weight,
                weight: weight
            });
        });
    });
    sigma.canvas.labels.webpack = function(node, context, settings) {
        var old = node.label;
        if (node.shortLabel) node.label = node.shortLabel;
        sigma.canvas.labels.def(node, context, settings);
        node.label = old;
    };
    sigma.canvas.edges.dashedArrow = function(edge, source, target, context, settings) {
        if (!context.getLineDash || !context.setLineDash) return sigma.canvas.edges.array(edge, source, target, context, settings);
        var old = context.getLineDash();
        context.setLineDash(edge.lineDash || [
            5,
            5
        ]);
        sigma.canvas.edges.arrow(edge, source, target, context, settings);
        context.setLineDash(old);
    };
    var s = new sigma({
        graph: {
            nodes: nodes,
            edges: edges
        },
        renderer: {
            type: "canvas",
            container: element
        },
        settings: {
            edgeColor: "target",
            maxNodeSize: 4,
            minNodeSize: 4,
            maxEdgeSize: 2,
            minEdgeSize: 0.05
        }
    });
    s.bind("clickNode", function(e) {
        var mod = e.data.node.module;
        if (mod.reasons.length === 0) {
            setActiveModule(mod.id);
        } else {
            reset();
        }
    });
    s.refresh();
    s.startForceAtlas2({
        edgeWeightInfluence: 0.5,
        adjustSizes: true,
        iterationsPerRender: 25,
        slowDown: 0.00000001
    });
    s.renderers[0].resize();
    var alreadyGreen = {};
    function setDepsAsGreen(parentId, depId) {
        if (alreadyGreen[parentId + depId]) {
            return;
        }
        alreadyGreen[parentId + depId] = true;
        s.graph.edges().forEach(function(e) {
            if (e.sourceModule.id === parentId && e.targetModule.id === depId) {
                e.color = colors.edgeActive;
                e.targetModule.dependencies.forEach(function(dep) {
                    setDepsAsGreen(depId, dep.moduleId);
                });
            }
            s.graph.nodes().forEach(function(n) {
                if (n.moduleId === depId && n.color !== colors.entry && n.color !== colors.impact) {
                    n.color = colors.edgeActive;
                    if (!n.module.bkpLabel) n.module.bkpLabel = n.shortLabel;
                    n.shortLabel = n.label;
                }
            });
        });
    }
    var reset = function reset() {
        alreadyGreen = {};
        s.graph.edges().forEach(function(e) {
            if (e.color === colors.edgeActive) {
                e.color = colors.edgeDefault;
            }
        });
        s.graph.nodes().forEach(function(n) {
            if (n.color !== colors.entry && n.color !== colors.impact) {
                n.color = colors.edgeDefault;
            }
            if (n.module.bkpLabel) {
                n.shortLabel = n.module.bkpLabel;
            }
        });
    };
    var setActiveModule = function setActiveModule(id) {
        var activeModule = app.stats.modules.find(function(mod) {
            return mod.id === id;
        });
        reset();
        onActive(id);
        activeModule.dependencies.forEach(function(dep) {
            setDepsAsGreen(id, dep.moduleId);
        });
        s.refresh();
    };
    return {
        setActiveModule: setActiveModule
    };
};


}),
"./src/framework/ImpactView/ImpactView.js": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js") /* eslint-enable */ ;
__webpack_require__(/*! core-js/modules/es.string.split.js */ "../node_modules/core-js/modules/es.string.split.js");
__webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
__webpack_require__(/*! core-js/modules/es.string.replace.js */ "../node_modules/core-js/modules/es.string.replace.js");
__webpack_require__(/*! core-js/modules/es.array.iterator.js */ "../node_modules/core-js/modules/es.array.iterator.js");
__webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ "../node_modules/core-js/modules/web.dom-collections.iterator.js");
__webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
__webpack_require__(/*! core-js/modules/es.array.index-of.js */ "../node_modules/core-js/modules/es.array.index-of.js");
__webpack_require__(/*! core-js/modules/es.array.join.js */ "../node_modules/core-js/modules/es.array.join.js");
__webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
__webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
__webpack_require__(/*! core-js/modules/es.symbol.js */ "../node_modules/core-js/modules/es.symbol.js");
__webpack_require__(/*! core-js/modules/es.symbol.description.js */ "../node_modules/core-js/modules/es.symbol.description.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var Badge = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Badge */ "../reactor/src/Atomic/components/Atoms/Badge.jsx");
var Utils = __webpack_require__(/*! Utils */ "Utils");
var Panel = __webpack_require__(/*! reactor/src/Atomic/components/Orgs/Panel/Panel */ "../reactor/src/Atomic/components/Orgs/Panel/Panel.jsx");
var Button = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Button/Button */ "../reactor/src/Atomic/components/Atoms/Button/Button.jsx");
var smooothScroll = __webpack_require__(/*! smoothscroll-polyfill */ "../node_modules/smoothscroll-polyfill/dist/smoothscroll.js");
var styleVariables = __webpack_require__(/*! reactor/src/helpers/styleVariables */ "../reactor/src/helpers/styleVariables.js");
var app = __webpack_require__(/*! ../../../impact/app */ "./impact/app.js");
var modules = __webpack_require__(/*! ../../../impact/modules */ "./impact/modules.js");
var colors = {
    edgeDefault: styleVariables.divGray,
    edgeActive: styleVariables.successGreen,
    nodeDefault: styleVariables.placeholderGrey,
    entry: styleVariables.dangerRed,
    impact: styleVariables.netBlue
};
var centerStyle = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    margin: "10px 0"
};
var entryStyle = {
    cursor: "pointer"
};
var sigmaStyle = {
    marginTop: 50,
    height: 500,
    width: "100%",
    borderBottom: "1px  solid black"
};
var separator = /*#__PURE__*/ React.createElement("span", {
    style: {
        margin: 5
    }
});
/* eslint-disable */ var isReactor = Utils.getURLParameter("reactor") != null;
smooothScroll.polyfill();
/* istanbul ignore next */ module.exports = createReactClass({
    getInitialState: function getInitialState() {
        return {
            impactJson: {
                modules: []
            },
            showall: false
        };
    },
    UNSAFE_componentWillMount: function UNSAFE_componentWillMount() {
        var me = this;
        var impactPath = "reactorCmps/impact.json";
        if (isReactor) {
            impactPath = "reactor/dist/impact.json";
        }
        curl("json!" + impactPath, function(json) {
            window.impactJson = json;
            try {
                me.setState({
                    impactJson: json
                });
            } catch (e) {
                console.error(e);
            }
        }).next();
    },
    componentDidUpdate: function componentDidUpdate() {
        var me = this;
        curl("js!wwwroot/ui/reactorCmps/impact/sigma.min.js", function() {
            try {
                if (!me.graph) {
                    app.load(me.state.impactJson);
                    me.graph = modules.go(me.refs.sigmaTarget, colors, me.onActiveEntry);
                }
            } catch (e) {
                console.error(e);
            }
        });
    },
    onActiveEntry: function onActiveEntry(id) {
        console.log("Entry ativa: " + id);
        document.documentElement.scroll({
            top: 0,
            left: 0,
            behavior: 'smooth'
        });
    },
    activeEntry: function activeEntry(id) {
        var me = this;
        console.log("Ativando " + id);
        me.graph.setActiveModule(id);
    },
    getLegend: function getLegend() {
        return /*#__PURE__*/ React.createElement("div", {
            style: centerStyle
        }, [
            /*#__PURE__*/ React.createElement(Badge, {
                backgroundColor: colors.entry
            }, "Entry"),
            separator,
            /*#__PURE__*/ React.createElement(Badge, {
                backgroundColor: colors.impact,
                color: styleVariables.textGrey
            }, "Modificado"),
            separator,
            /*#__PURE__*/ React.createElement(Badge, {
                backgroundColor: colors.edgeDefault,
                color: styleVariables.textGrey
            }, "Dependência")
        ].map(function(item, index) {
            return React.cloneElement(item, {
                key: index
            });
        }));
    },
    findEntryByModuleName: function findEntryByModuleName(moduleName) {
        var me = this;
        if (isReactor) {
            return {
                url: "/se/ui/reactor/dist/docs/docs.html?reactCmp=" + moduleName.split("examples/")[1].replace(/\//g, '-').replace('.jsx', '').replace('.js', '')
            };
        }
        moduleName = moduleName.replace("./src/", "").replace('.jsx', '').replace('.js', '');
        for(var key in me.state.impactJson.entries){
            var entry = me.state.impactJson.entries[key];
            if (entry.source && entry.source.indexOf(moduleName) >= 0) {
                entry.key = key;
                return entry;
            }
        }
    },
    getUrl: function getUrl(entry) {
        entry.args = entry.args || [];
        if (entry.url) {
            return Utils.getSystemUrl(true) + entry.url;
        } else {
            return Utils.getBaseclassUrl() + "/ui/reactor/dist/reactor-dev.html?" + entry.args.join("&") + (entry.args.length > 0 ? "&" : "") + "reactCmp=reactorCmps/" + entry.key;
        }
    },
    getEntryPoint: function getEntryPoint(module1) {
        var me = this, shortName = module1.name.replace("./src/", "");
        var entry = me.findEntryByModuleName(module1.name);
        return /*#__PURE__*/ React.createElement("div", {
            style: {
                marginTop: 20,
                marginLeft: 50,
                marginRight: 50
            },
            key: module1.id
        }, /*#__PURE__*/ React.createElement(Panel, {
            Header: entry.mapped || isReactor ? /*#__PURE__*/ React.createElement("a", {
                href: me.getUrl(entry),
                target: "_blank"
            }, shortName) : /*#__PURE__*/ React.createElement("span", null, /*#__PURE__*/ React.createElement("span", {
                target: "_blank"
            }, shortName), /*#__PURE__*/ React.createElement("span", null, "  "), /*#__PURE__*/ React.createElement("a", {
                target: "_blank",
                href: me.getUrl(entry)
            }, "não mapeada :(")),
            rightHeaderContent: /*#__PURE__*/ React.createElement(Button, {
                onClick: me.activeEntry.bind(me, module1.id),
                size: "medium"
            }, "Ativar")
        }, entry.description === null ? "--" : entry.description));
        return /*#__PURE__*/ React.createElement("div", {
            key: module1.id
        }, /*#__PURE__*/ React.createElement(Badge, {
            style: entryStyle,
            onClick: me.activeEntry.bind(me, module1.id)
        }, module1.name));
    },
    getAllEntries: function getAllEntries() {
        var me = this, state = me.state, entries = [];
        for(var key in state.impactJson.entries){
            var entry = state.impactJson.entries[key];
            entry.key = key;
            entries.push(/*#__PURE__*/ React.createElement("div", {
                style: {
                    marginTop: 20,
                    marginLeft: 50,
                    marginRight: 50
                },
                key: key
            }, /*#__PURE__*/ React.createElement(Panel, null, /*#__PURE__*/ React.createElement("a", {
                href: me.getUrl(entry),
                target: "_blank"
            }, key), entry.mapped || isReactor ? null : /*#__PURE__*/ React.createElement("span", null, "não mapeado"), entry.description ? /*#__PURE__*/ React.createElement("div", null, entry.description) : null)));
        }
        return entries;
    },
    render: function render() {
        var me = this, state = me.state;
        return /*#__PURE__*/ React.createElement("div", {
            style: {
                height: "100%",
                width: "100%"
            }
        }, /*#__PURE__*/ React.createElement("div", {
            id: "sigmaTarget",
            ref: "sigmaTarget",
            style: sigmaStyle
        }), /*#__PURE__*/ React.createElement("div", null, me.getLegend(), /*#__PURE__*/ React.createElement("div", null, !state.showAll && state.impactJson.modules ? state.impactJson.modules.map(function(module1) {
            if (module1.reasons.length === 0) {
                return me.getEntryPoint(module1);
            }
        }) : null, state.showAll && state.impactJson.entries ? me.getAllEntries() : null), isReactor ? null : /*#__PURE__*/ React.createElement("div", {
            style: {
                float: "right",
                margin: 50
            }
        }, /*#__PURE__*/ React.createElement(Button, {
            onClick: function onClick() {
                me.setState({
                    showAll: !state.showAll
                });
            },
            size: "medium"
        }, "Exibir todos os entryPoints"))));
    },
    displayName: "exports"
});


}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/ImpactView/ImpactView.js"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=ImpactView.js.map