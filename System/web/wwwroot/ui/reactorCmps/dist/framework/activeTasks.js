define(["Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","WorkspaceInfo","tokens!reactorCmps/tokens/general","SG2/collection/Factory","Connector","when"], function(__WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_WorkspaceInfo__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_SG2_collection_Factory__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEBPACK_EXTERNAL_MODULE_when__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/activeTasks"], {
"./src/asset/collection/InventoryTaskListCollection.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: "cdinventory",
        proxyCfg: {
            route: "asset/rest/inventoryTaskList.php"
        }
    },
    collection: {
        defaultOrder: {}
    }
});


}),
"./src/asset/collection/MaintenanceTaskCollection.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: "cdgenactivity",
        proxyCfg: {
            route: "/maintenance/rest/maintenanceTaskList.php"
        }
    },
    collection: {
        defaultOrder: {}
    }
});


}),
"./src/audit/mobile/Tasks/collection/AuditExecutionCollection.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: "cdaudit",
        proxyCfg: {
            route: "/audit/rpc/collection/auditExecutionTaskList.php"
        }
    },
    collection: {
        defaultOrder: {}
    }
});


}),
"./src/document/collection/DocumentTasksCollection.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = function(defaultOrder) {
    return createCollection({
        model: {
            primaryKey: 'cddocument',
            proxyCfg: {
                route: '/document/request/document_pendency_collection.php'
            }
        },
        collection: {
            defaultOrder: defaultOrder,
            quantity: 10
        }
    });
};


}),
"./src/framework/components/mobile/Tasks/Helpers/activeTasks.js": (function (module, __unused_webpack_exports, __webpack_require__) {
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
__webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
__webpack_require__(/*! core-js/modules/es.array.filter.js */ "../node_modules/core-js/modules/es.array.filter.js");
__webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
__webpack_require__(/*! core-js/modules/es.array.for-each.js */ "../node_modules/core-js/modules/es.array.for-each.js");
__webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
__webpack_require__(/*! core-js/modules/es.object.keys.js */ "../node_modules/core-js/modules/es.object.keys.js");
__webpack_require__(/*! core-js/modules/es.array.reduce.js */ "../node_modules/core-js/modules/es.array.reduce.js");
__webpack_require__(/*! core-js/modules/es.array.iterator.js */ "../node_modules/core-js/modules/es.array.iterator.js");
__webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ "../node_modules/core-js/modules/web.dom-collections.iterator.js");
__webpack_require__(/*! core-js/modules/es.string.iterator.js */ "../node_modules/core-js/modules/es.string.iterator.js");
__webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
__webpack_require__(/*! core-js/modules/es.object.assign.js */ "../node_modules/core-js/modules/es.object.assign.js");
__webpack_require__(/*! core-js/modules/es.array.join.js */ "../node_modules/core-js/modules/es.array.join.js");
var Connector = __webpack_require__(/*! Connector */ "Connector");
var when = __webpack_require__(/*! when */ "when");
var tasks = __webpack_require__(/*! reactorCmps/src/framework/components/mobile/Tasks/Helpers/tasks.js */ "./src/framework/components/mobile/Tasks/Helpers/tasks.js");
var workspaceInfo = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
var collectionSearch = __webpack_require__(/*! reactorCmps/src/framework/components/mobile/Tasks/Helpers/collectionSearch */ "./src/framework/components/mobile/Tasks/Helpers/collectionSearch.js");
function mapFromComponent(component, activeModules) {
    return component.tasks.map(function(item) {
        var name = activeModules[item.code];
        if (name) {
            return {
                name: item.text,
                count: item.count,
                code: item.code,
                status: item.status,
                componentName: component.text,
                route: name,
                oid: item.oid
            };
        }
        return false;
    }).filter(function(item) {
        // remove falses
        return item;
    });
}
module.exports = {
    getTasks: function getTasks() {
        var license = workspaceInfo.getUserProfile(), JSON = "[[", defaultTasks = {};
        license = license ? license.toLowerCase() : 'manager';
        tasks.forEach(function(task) {
            defaultTasks[task.codes[license]] = {};
            JSON += "{" + task.codes[license] + ":[]},";
            defaultTasks[task.codes[license]].tasks = task.tasks;
            defaultTasks[task.codes[license]].component = task.component;
        });
        JSON += '],{"Boolean":"false"}]';
        return {
            defaultActives: defaultTasks,
            codes: JSON
        };
    },
    filter: function filter(pendencies, actives) {
        var executions = [], res = [], count = 0, item, valueToConcat, i;
        if (pendencies && pendencies[0] && pendencies[0].executions) {
            executions = pendencies[0].executions;
            for(; count < executions.length; count++){
                item = executions[count];
                if (actives[item.code] && item.tasks) {
                    valueToConcat = mapFromComponent(item, actives[item.code]);
                    for(i in valueToConcat){
                        valueToConcat[i].codeByLicense = item.code;
                    }
                    res = res.concat(valueToConcat);
                }
            }
        }
        return res;
    },
    getParamsFromTaskObj: function getParamsFromTaskObj(taskObj) {
        if (!taskObj) return "";
        return Object.keys(taskObj).reduce(function(query, key, index, array) {
            return query + key + (index !== array.length - 1 ? ',' : '');
        }, "");
    },
    getStaticTaskList: function getStaticTaskList() {
        var me = this, taskObj = me.getTasks().defaultActives;
        return Promise.all([
            me.getPermissionLookupObj(taskObj),
            collectionSearch.countTasks()
        ]).then(me.getValidTaskObj.bind(me, taskObj));
    },
    getPermissionLookupObj: function getPermissionLookupObj(taskObj) {
        var me = this, deferred = when.defer(), params = me.getParamsFromTaskObj(taskObj);
        Connector.callBaseclass('commons/rest/check_menu_permissions.php?check=' + params, {
            success: function success(res) {
                if (res.success) {
                    deferred.resolve(res.results && res.results[0]);
                } else {
                    deferred.reject();
                }
            },
            error: function error() {
                deferred.reject();
            }
        });
        return deferred.promise;
    },
    getValidTaskObj: function getValidTaskObj(taskObj, lookupObj) {
        var _lookupObj = _sliced_to_array(lookupObj, 2), permissionObj = _lookupObj[0], countObj = _lookupObj[1];
        return Object.keys(taskObj).reduce(function(result, codeByLicense) {
            var task = taskObj[codeByLicense];
            if (!permissionObj[codeByLicense]) return result;
            return result.concat(Object.keys(task.tasks).reduce(function(innerResult, taskCode) {
                if (countObj["".concat(task.component, "_").concat(taskCode)] === 0) return innerResult;
                return innerResult.concat(Object.assign({}, task.tasks[taskCode], {
                    codeByLicense: codeByLicense,
                    oid: [
                        'execution',
                        codeByLicense,
                        taskCode
                    ].join(','),
                    quantity: countObj["".concat(task.component, "_").concat(taskCode)]
                }));
            }, []));
        }, []);
    }
};


}),
"./src/framework/components/mobile/Tasks/Helpers/collectionSearch.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  countTasks: function() { return countTasks; }
});
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_string_search_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.string.search.js */ "../node_modules/core-js/modules/es.string.search.js");
/* ESM import */var core_js_modules_es_string_search_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_search_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ "../node_modules/core-js/modules/es.array.iterator.js");
/* ESM import */var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ "../node_modules/core-js/modules/web.dom-collections.iterator.js");
/* ESM import */var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ "../node_modules/core-js/modules/es.string.iterator.js");
/* ESM import */var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var core_js_modules_es_object_values_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.object.values.js */ "../node_modules/core-js/modules/es.object.values.js");
/* ESM import */var core_js_modules_es_object_values_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_values_js__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var core_js_modules_es_array_reduce_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.array.reduce.js */ "../node_modules/core-js/modules/es.array.reduce.js");
/* ESM import */var core_js_modules_es_array_reduce_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_reduce_js__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var reactorCmps_src_project_mobile_Tasks_collection_ProjectTaskExecution__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! reactorCmps/src/project/mobile/Tasks/collection/ProjectTaskExecution */ "./src/project/mobile/Tasks/collection/ProjectTaskExecution.js");
/* ESM import */var reactorCmps_src_project_mobile_Tasks_collection_ProjectTaskExecution__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_src_project_mobile_Tasks_collection_ProjectTaskExecution__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var reactorCmps_src_document_collection_DocumentTasksCollection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactorCmps/src/document/collection/DocumentTasksCollection */ "./src/document/collection/DocumentTasksCollection.js");
/* ESM import */var reactorCmps_src_document_collection_DocumentTasksCollection__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_src_document_collection_DocumentTasksCollection__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var reactorCmps_src_audit_mobile_Tasks_collection_AuditExecutionCollection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactorCmps/src/audit/mobile/Tasks/collection/AuditExecutionCollection */ "./src/audit/mobile/Tasks/collection/AuditExecutionCollection.js");
/* ESM import */var reactorCmps_src_audit_mobile_Tasks_collection_AuditExecutionCollection__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_src_audit_mobile_Tasks_collection_AuditExecutionCollection__WEBPACK_IMPORTED_MODULE_11__);
/* ESM import */var reactorCmps_src_workflow_collections_TaskCollection__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! reactorCmps/src/workflow/collections/TaskCollection */ "./src/workflow/collections/TaskCollection.js");
/* ESM import */var reactorCmps_src_workflow_collections_TaskCollection__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_src_workflow_collections_TaskCollection__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var reactorCmps_src_asset_collection_InventoryTaskListCollection__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactorCmps/src/asset/collection/InventoryTaskListCollection */ "./src/asset/collection/InventoryTaskListCollection.jsx");
/* ESM import */var reactorCmps_src_asset_collection_InventoryTaskListCollection__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_src_asset_collection_InventoryTaskListCollection__WEBPACK_IMPORTED_MODULE_13__);
/* ESM import */var reactorCmps_src_asset_collection_MaintenanceTaskCollection__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactorCmps/src/asset/collection/MaintenanceTaskCollection */ "./src/asset/collection/MaintenanceTaskCollection.jsx");
/* ESM import */var reactorCmps_src_asset_collection_MaintenanceTaskCollection__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_src_asset_collection_MaintenanceTaskCollection__WEBPACK_IMPORTED_MODULE_14__);
/* ESM import */var reactorCmps_src_timecontrol_mobile_Tasks_collection_ListActionExecution__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactorCmps/src/timecontrol/mobile/Tasks/collection/ListActionExecution */ "./src/timecontrol/mobile/Tasks/collection/ListActionExecution.js");
/* ESM import */var reactorCmps_src_timecontrol_mobile_Tasks_collection_ListActionExecution__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_src_timecontrol_mobile_Tasks_collection_ListActionExecution__WEBPACK_IMPORTED_MODULE_15__);
/* ESM import */var reactorCmps_src_workforce_mobile_Tasks_collection_TaskExecution__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! reactorCmps/src/workforce/mobile/Tasks/collection/TaskExecution */ "./src/workforce/mobile/Tasks/collection/TaskExecution.js");
/* ESM import */var reactorCmps_src_workforce_mobile_Tasks_collection_TaskExecution__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_src_workforce_mobile_Tasks_collection_TaskExecution__WEBPACK_IMPORTED_MODULE_16__);
/* istanbul ignore file - collection search for tasks */ function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
    try {
        var info = gen[key](arg);
        var value = info.value;
    } catch (error) {
        reject(error);
        return;
    }
    if (info.done) {
        resolve(value);
    } else {
        Promise.resolve(value).then(_next, _throw);
    }
}
function _async_to_generator(fn) {
    return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
            var gen = fn.apply(self, args);
            function _next(value) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
            }
            function _throw(err) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
            }
            _next(undefined);
        });
    };
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _ts_generator(thisArg, body) {
    var f, y, t, g, _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    };
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}

















var TASKS = {
    PROJECT_EXECUTION: 'PROJECT_' + 5,
    AUDIT_EXECUTION: 'AUDIT_' + 1,
    WORKFLOW_EXECUTION: 'WORKFLOW_' + 1,
    ASSET_INVENTORY_EXECUTION: 'ASSET_' + 302160,
    TIMECONTROL_EXECUTION: 'TIMECONTROL_' + 1,
    WORKFORCE_TASK_EXECUTION: 'WORKFORCE_' + 4,
    DOCUMENT_REVISION: 'DOCUMENT_' + 50,
    DOCUMENT_ACKNOWLEDGMENT: 'DOCUMENT_' + 62,
    MAINTENANCE_EXECUTION: 'MAINTENANCE_' + 3
};
var countCollection = function(collection, resolve) {
    collection.count().then(function(size) {
        resolve(size);
    });
};
var countTasks = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function() {
        var _quantityPromises, projectCollection, documentCollection, auditCollection, workflowCollection, inventoryCollection, maintenanceCollection, timeControlCollection, workforceCollection, collections, quantityPromises, quantity, tasksValue;
        return _ts_generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    projectCollection = new (reactorCmps_src_project_mobile_Tasks_collection_ProjectTaskExecution__WEBPACK_IMPORTED_MODULE_9___default())();
                    documentCollection = new (reactorCmps_src_document_collection_DocumentTasksCollection__WEBPACK_IMPORTED_MODULE_10___default()({
                        NMTITLE: 'ASC'
                    }))();
                    auditCollection = new (reactorCmps_src_audit_mobile_Tasks_collection_AuditExecutionCollection__WEBPACK_IMPORTED_MODULE_11___default())();
                    workflowCollection = new (reactorCmps_src_workflow_collections_TaskCollection__WEBPACK_IMPORTED_MODULE_12___default())();
                    inventoryCollection = new (reactorCmps_src_asset_collection_InventoryTaskListCollection__WEBPACK_IMPORTED_MODULE_13___default())();
                    maintenanceCollection = new (reactorCmps_src_asset_collection_MaintenanceTaskCollection__WEBPACK_IMPORTED_MODULE_14___default())();
                    timeControlCollection = new (reactorCmps_src_timecontrol_mobile_Tasks_collection_ListActionExecution__WEBPACK_IMPORTED_MODULE_15___default())();
                    workforceCollection = new (reactorCmps_src_workforce_mobile_Tasks_collection_TaskExecution__WEBPACK_IMPORTED_MODULE_16___default())();
                    collections = [
                        projectCollection,
                        auditCollection,
                        workflowCollection,
                        inventoryCollection,
                        timeControlCollection,
                        workforceCollection
                    ];
                    quantityPromises = collections.map(function(collection) {
                        collection.quantity = 1;
                        return new Promise(function(resolve) {
                            collection.search().then(function() {
                                countCollection(collection, resolve);
                            });
                        });
                    });
                    documentCollection.quantity = 1;
                    (_quantityPromises = quantityPromises).push.apply(_quantityPromises, [
                        new Promise(function(resolve) {
                            documentCollection.search({
                                pendency: 'document_revision_phase'
                            }).then(function() {
                                countCollection(documentCollection, resolve);
                            });
                        }),
                        new Promise(function(resolve) {
                            documentCollection.search({
                                pendency: 'document_know_publishing'
                            }).then(function() {
                                countCollection(documentCollection, resolve);
                            });
                        })
                    ]);
                    maintenanceCollection.quantity = 1;
                    quantityPromises.push(new Promise(function(resolve) {
                        maintenanceCollection.search({
                            cdprod: 126
                        }).then(function() {
                            countCollection(maintenanceCollection, resolve);
                        });
                    }));
                    return [
                        4,
                        Promise.all(quantityPromises)
                    ];
                case 1:
                    quantity = _state.sent();
                    tasksValue = Object.values(TASKS);
                    return [
                        2,
                        tasksValue.reduce(function(tasks, task, index) {
                            return _object_spread_props(_object_spread({}, tasks), _define_property({}, task, quantity[index]));
                        }, {})
                    ];
            }
        });
    });
    return function countTasks() {
        return _ref.apply(this, arguments);
    };
}();


}),
"./src/framework/components/mobile/Tasks/Helpers/tasks.js": (function (module) {
module.exports = [
    {
        component: 'ASSET',
        codes: {
            manager: '109',
            staff: '118',
            basic: '110',
            view: '232'
        },
        tasks: {
            302160: {
                name: 302160,
                componentName: 113688,
                route: "asset/execute"
            }
        }
    },
    {
        component: 'WORKFORCE',
        codes: {
            manager: '205',
            staff: '206',
            basic: '207',
            view: '254'
        },
        tasks: {
            4: {
                name: 112441,
                componentName: 202568,
                route: "workforce/executeTask"
            }
        }
    },
    {
        component: 'DOCUMENT',
        codes: {
            manager: '73',
            staff: '88',
            basic: '87',
            view: '240'
        },
        tasks: {
            // 50, 62 = cdPendency
            50: {
                name: 113305,
                componentName: 103814,
                route: "document/execute"
            },
            62: {
                name: 200248,
                componentName: 103814,
                route: "document/knowledge"
            }
        }
    },
    {
        component: 'MAINTENANCE',
        codes: {
            manager: '126',
            staff: '128',
            basic: '127',
            view: '238'
        },
        tasks: {
            3: {
                name: 112441,
                componentName: 100289,
                route: "maintenance/execute"
            }
        }
    },
    {
        component: 'PROJECT',
        codes: {
            manager: '41',
            staff: '42',
            basic: '43',
            view: '256'
        },
        tasks: {
            5: {
                name: 112441,
                componentName: 102175,
                route: "project/executeTask"
            }
        }
    },
    {
        component: 'WORKFLOW',
        codes: {
            manager: '104',
            staff: '105',
            basic: '106',
            view: '258'
        },
        tasks: {
            // 1 = cdPendency
            1: {
                name: 112441,
                componentName: 108546,
                route: "workflow/execute"
            }
        }
    },
    {
        component: 'AUDIT',
        codes: {
            manager: '90',
            staff: '91',
            basic: '92',
            view: '255'
        },
        tasks: {
            1: {
                name: 103953,
                componentName: 102208,
                route: "audit/execute"
            }
        }
    },
    {
        component: 'TIMECONTROL',
        codes: {
            manager: '174',
            staff: '175',
            basic: '176',
            view: '259'
        },
        tasks: {
            1: {
                name: 210797,
                componentName: 100475,
                route: "timecontrol/execute"
            }
        }
    }
];


}),
"./src/project/mobile/Tasks/collection/ProjectTaskExecution.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: "cdtask",
        proxyCfg: {
            route: "/project/rest/projectTaskExecutionPendencyList.php"
        }
    },
    collection: {
        defaultOrder: {}
    }
});


}),
"./src/timecontrol/mobile/Tasks/collection/ListActionExecution.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: "cdgenactivity",
        proxyCfg: {
            route: "/timecontrol/rest/mobile/tasks.php"
        }
    },
    collection: {
        defaultOrder: {}
    }
});


}),
"./src/workflow/collections/TaskCollection.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: "idtask",
        proxyCfg: {
            route: "workflow/request/pendency.php"
        }
    },
    collection: {
        silent: true,
        defaultOrder: {
            id: "ASC"
        },
        quantity: 10
    }
});


}),
"./src/workforce/mobile/Tasks/collection/TaskExecution.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: "cdtask",
        proxyCfg: {
            route: "/workforce/rest/mobile/taskExecutionPendencyList.php"
        }
    },
    collection: {
        defaultOrder: {}
    }
});


}),
"Connector": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"SG2/collection/Factory": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_SG2_collection_Factory__;

}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"WorkspaceInfo": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_WorkspaceInfo__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"when": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_when__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/components/mobile/Tasks/Helpers/activeTasks.js"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=activeTasks.js.map