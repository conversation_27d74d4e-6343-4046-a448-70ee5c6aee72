"use strict";
define(["react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","tokens!reactorCmps/tokens/general","create-react-class","Connector","suite-storage","when"], function(__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEBPACK_EXTERNAL_MODULE_suite_storage__, __WEBPA<PERSON><PERSON>_EXTERNAL_MODULE_when__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/TCGTextArea"], {
"./src/framework/components/TCG/TCGTextArea.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactorCmps_tokens_general__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");
/* ESM import */var reactorCmps_tokens_general__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_tokens_general__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactor2_src_Atomic_components_Mols_TextArea_TCGTextArea__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactor2/src/Atomic/components/Mols/TextArea/TCGTextArea */ "../reactor2/src/Atomic/components/Mols/TextArea/TCGTextArea.jsx");


/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reactor2_src_Atomic_components_Mols_TextArea_TCGTextArea__WEBPACK_IMPORTED_MODULE_1__["default"]);


}),
"Connector": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"Utils": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"when": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_when__;

}),
"reactorCmps/tokens/general": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/components/TCG/TCGTextArea.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=TCGTextArea.js.map