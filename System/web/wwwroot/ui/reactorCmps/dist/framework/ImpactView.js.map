{"version": 3, "file": "framework/ImpactView.js", "sources": ["webpack://watch1749037385376/./impact/app.js", "webpack://watch1749037385376/./impact/helpers.js", "webpack://watch1749037385376/./impact/modules.js", "webpack://watch1749037385376/./src/framework/ImpactView/ImpactView.js"], "sourcesContent": ["exports.stats = null;\nexports.mapModules = null;\nexports.mapChunks = null;\n\nfunction load(stats) {\n\tstats.assets.sort(function(a, b) {\n\t\treturn b.size - a.size;\n\t});\n\tstats.modules.sort(function(a, b) {\n\t\treturn a.id - b.id;\n\t});\n\tvar mapModules = {};\n\tvar mapModulesIdent = {};\n\tvar mapModulesUid = {};\n\tstats.modules.forEach(function(module, idx) {\n\t\tmapModules[module.id] = module;\n\t\tmapModulesIdent[\"$\"+module.identifier] = module;\n\t\tmapModulesUid[module.uid = idx] = module;\n\t\tmodule.dependencies = [];\n\t});\n\tvar mapChunks = {};\n\tstats.chunks.forEach(function(chunk) {\n\t\tmapChunks[chunk.id] = chunk;\n\t\tchunk.children = [];\n\t});\n\tstats.modules.forEach(function(module) {\n\t\tmodule.reasons.forEach(function(reason) {\n\t\t\tvar m = mapModulesIdent[\"$\"+reason.moduleIdentifier];\n\t\t\tif(!m) return;\n\t\t\treason.moduleUid = m.uid;\n\t\t\tm.dependencies.push({\n\t\t\t\ttype: reason.type,\n\t\t\t\tmoduleId: module.id,\n\t\t\t\tmoduleUid: module.uid,\n\t\t\t\tmodule: module.name,\n\t\t\t\tuserRequest: reason.userRequest,\n\t\t\t\tloc: reason.loc\n\t\t\t});\n\t\t});\n\t\tmodule.issuerUid = mapModulesIdent[\"$\"+module.issuer] && mapModulesIdent[\"$\"+module.issuer].uid;\n\t\t(function setTimestamp(module) {\n\t\t\tif(typeof module.timestamp === \"number\") return module.timestamp;\n\t\t\tif(!module.profile) return;\n\t\t\tvar factory = module.profile.factory || 0;\n\t\t\tvar building = module.profile.building || 0;\n\t\t\tmodule.time = factory + building;\n\t\t\tif(!module.issuer) return module.timestamp = module.time;\n\t\t\tvar issuer = mapModulesIdent[\"$\"+module.issuer];\n\t\t\tif(!issuer) return module.timestamp = NaN;\n\t\t\tsetTimestamp(issuer);\n\t\t\tmodule.timestamp = issuer.timestamp + module.time;\n\t\t}(module));\n\t});\n\tstats.chunks.forEach(function(chunk) {\n\t\tchunk.parents.forEach(function(parent) {\n\t\t\tvar c = mapChunks[parent];\n\t\t\tc.children.push(chunk.id);\n\t\t});\n\t\tchunk.origins.forEach(function(origin) {\n\t\t\tvar m = mapModulesIdent[\"$\"+origin.moduleIdentifier];\n\t\t\tif(!m) return;\n\t\t\torigin.moduleUid = m.uid;\n\t\t});\n\t});\n\tstats.modules.forEach(function(module) {\n\t\tmodule.dependencies.sort(function(a, b) {\n\t\t\tif(!a.loc && !b.loc) return 0;\n\t\t\tif(!a.loc) return 1;\n\t\t\tif(!b.loc) return -1;\n\t\t\ta = a.loc.split(/[:-]/);\n\t\t\tb = b.loc.split(/[:-]/);\n\t\t\tif(+a[0] < +b[0]) return -1;\n\t\t\tif(+a[0] > +b[0]) return 1;\n\t\t\tif(+a[1] < +b[1]) return -1;\n\t\t\tif(+a[1] > +b[1]) return 1;\n\t\t\treturn 0;\n\t\t});\n\t});\n\tvar maxLength = 0;\n\tstats.assets.forEach(function(a) {\n\t\tif(a.name.length > maxLength) maxLength = a.name.length;\n\t});\n\tstats.assets.forEach(function(a) {\n\t\ta.normalizedName = a.name;\n\t\twhile(a.normalizedName.length < maxLength)\n\t\t\ta.normalizedName = \" \" + a.normalizedName;\n\t});\n\tstats.assets.sort(function(a, b) {\n\t\ta = a.normalizedName;\n\t\tb = b.normalizedName;\n\t\treturn a < b ? -1 : 1;\n\t});\n\texports.stats = stats;\n\texports.mapChunks = mapChunks;\n\texports.mapModules = mapModules;\n\texports.mapModulesUid = mapModulesUid;\n\texports.mapModulesIdent = mapModulesIdent;\n}\n\nexports.load = load;", "module.exports = {\n\tgetColor: function(module, impact, colors) {\n\t\tif (module.reasons.length === 0) {\n\t\t\treturn colors.entry;\n\t\t} else if (impact.indexOf(module.id) >= 0) {\n\t\t\treturn colors.impact;\n\t\t}\n\t\treturn \"#ccc\";\n\t}\n};", "module.exports.go = function(element, colors, onActive) {\n\tvar app = require(\"./app\");\n\tvar helpers = require(\"./helpers\");\n\n\tvar nodes = [];\n\tvar edges = [];\n\tvar moduleCount = app.stats.modules.length;\n\tvar maxTimestamp = 0;\n\tvar maxSize = 0;\n\n\tapp.stats.modules.forEach(function(module, idx) {\n\t\tif (module.size > maxSize) maxSize = module.size;\n\t\tif (module.timestamp > maxTimestamp) maxTimestamp = module.timestamp;\n\t});\n\tapp.stats.modules.forEach(function(module, idx) {\n\t\tvar done = {};\n\t\tvar uniqueReasons = module.reasons.filter(function(reason) {\n\t\t\tvar parent = reason.module;\n\t\t\tif (done[\"$\" + parent]) return false;\n\t\t\tdone[\"$\" + parent] = true;\n\t\t\treturn true;\n\t\t});\n\t\tvar uid = module.uid;\n\t\tnodes.push({\n\t\t\tid: \"module\" + uid,\n\t\t\tuid: uid,\n\t\t\tmoduleUid: uid,\n\t\t\tmoduleId: module.id,\n\t\t\tmodule: module,\n\t\t\ttype: \"webpack\",\n\t\t\tsize: module.size + 1,\n\t\t\tlabel: \"[\" + module.id + \"] \" + module.name,\n\t\t\tshortLabel: \"\" + module.id,\n\t\t\tx: Math.cos(idx / moduleCount * Math.PI * 2) * Math.sqrt(uniqueReasons.length + 1) * Math.sqrt(moduleCount),\n\t\t\ty: Math.sin(idx / moduleCount * Math.PI * 2) * Math.sqrt(uniqueReasons.length + 1) * Math.sqrt(moduleCount),\n\t\t\toriginalColor: colors.nodeDefault,\n\t\t\tcolor: helpers.getColor(module, app.stats.impact, colors)\n\t\t});\n\t\tuniqueReasons.forEach(function(reason) {\n\t\t\tvar parentIdent = reason.moduleIdentifier;\n\t\t\tvar parentModule = app.mapModulesIdent[\"$\" + parentIdent];\n\t\t\tif (!parentModule) return;\n\t\t\tvar weight = 1;\n\t\t\tvar async = false;\n\t\t\tedges.push({\n\t\t\t\tid: \"edge\" + module.uid + \"-\" + +parentModule.uid,\n\t\t\t\tsourceModuleUid: parentModule.uid,\n\t\t\t\tsourceModule: parentModule,\n\t\t\t\tsource: \"module\" + parentModule.uid,\n\t\t\t\ttargetModule: module,\n\t\t\t\ttargetModuleUid: module.uid,\n\t\t\t\ttarget: \"module\" + module.uid,\n\t\t\t\tarrow: \"target\",\n\t\t\t\ttype: \"arrow\",\n\t\t\t\tlineDash: [5],\n\t\t\t\toriginalColor: colors.edgeDefault,\n\t\t\t\tcolor: colors.edgeDefault,\n\t\t\t\tsize: weight,\n\t\t\t\tweight: weight\n\t\t\t});\n\t\t});\n\t});\n\n\n\tsigma.canvas.labels.webpack = function(node, context, settings) {\n\t\tvar old = node.label;\n\t\tif (node.shortLabel) node.label = node.shortLabel;\n\t\tsigma.canvas.labels.def(node, context, settings);\n\t\tnode.label = old;\n\t};\n\n\tsigma.canvas.edges.dashedArrow = function(edge, source, target, context, settings) {\n\t\tif (!context.getLineDash || !context.setLineDash) return sigma.canvas.edges.array(edge, source, target, context, settings);\n\t\tvar old = context.getLineDash();\n\t\tcontext.setLineDash(edge.lineDash || [5, 5]);\n\t\tsigma.canvas.edges.arrow(edge, source, target, context, settings);\n\t\tcontext.setLineDash(old);\n\t};\n\n\tvar s = new sigma({\n\t\tgraph: {\n\t\t\tnodes: nodes,\n\t\t\tedges: edges\n\t\t},\n\t\trenderer: {\n\t\t\ttype: \"canvas\",\n\t\t\tcontainer: element\n\t\t},\n\t\tsettings: {\n\t\t\tedgeColor: \"target\",\n\t\t\tmaxNodeSize: 4,\n\t\t\tminNodeSize: 4,\n\t\t\tmaxEdgeSize: 2,\n\t\t\tminEdgeSize: 0.05\n\t\t}\n\t});\n\n\ts.bind(\"clickNode\", function(e) {\n\t\tvar mod = e.data.node.module;\n\n\t\tif (mod.reasons.length === 0) {\n\t\t\tsetActiveModule(mod.id);\n\t\t} else {\n\t\t\treset();\n\t\t}\n\t});\n\n\ts.refresh();\n\n\ts.startForceAtlas2({\n\t\tedgeWeightInfluence: 0.5,\n\t\tadjustSizes: true,\n\t\titerationsPerRender: 25,\n\t\tslowDown: 0.00000001\n\t});\n\n\ts.renderers[0].resize();\n\n\n\tvar alreadyGreen = {};\n\n\tfunction setDepsAsGreen(parentId, depId) {\n\t\tif (alreadyGreen[parentId + depId]) {\n\t\t\treturn;\n\t\t}\n\n\t\talreadyGreen[parentId + depId] = true;\n\n\t\ts.graph.edges().forEach(function(e) {\n\t\t\tif (e.sourceModule.id === parentId && e.targetModule.id === depId) {\n\t\t\t\te.color = colors.edgeActive;\n\n\t\t\t\te.targetModule.dependencies.forEach(function(dep) {\n\t\t\t\t\tsetDepsAsGreen(depId, dep.moduleId);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\ts.graph.nodes().forEach(function(n) {\n\t\t\t\tif (n.moduleId === depId && n.color !== colors.entry && n.color !== colors.impact) {\n\t\t\t\t\tn.color = colors.edgeActive;\n\t\t\t\t\tif (!n.module.bkpLabel) n.module.bkpLabel = n.shortLabel;\n\t\t\t\t\tn.shortLabel = n.label;\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t}\n\n\tvar reset = function() {\n\t\talreadyGreen = {};\n\n\t\ts.graph.edges().forEach(function(e) {\n\t\t\tif (e.color === colors.edgeActive) {\n\t\t\t\te.color = colors.edgeDefault;\n\t\t\t}\n\t\t});\n\n\t\ts.graph.nodes().forEach(function(n) {\n\t\t\tif (n.color !== colors.entry && n.color !== colors.impact) {\n\t\t\t\tn.color = colors.edgeDefault;\n\t\t\t}\n\n\t\t\tif (n.module.bkpLabel) {\n\t\t\t\tn.shortLabel = n.module.bkpLabel;\n\t\t\t}\n\t\t});\n\t}\n\n\tvar setActiveModule = function(id) {\n\t\tvar activeModule = app.stats.modules.find(function(mod) {\n\t\t\treturn mod.id === id;\n\t\t});\n\n\t\treset();\n\n\t\tonActive(id);\n\n\t\tactiveModule.dependencies.forEach(function(dep) {\n\t\t\tsetDepsAsGreen(id, dep.moduleId);\n\t\t});\n\n\t\ts.refresh();\n\t};\n\n\treturn {\n\t\tsetActiveModule: setActiveModule\n\t};\n}", "var React = require('react');\nvar createReactClass = require('create-react-class');\nvar Badge = require('reactor/src/Atomic/components/Atoms/Badge');\nvar Utils = require('Utils');\nvar Panel = require('reactor/src/Atomic/components/Orgs/Panel/Panel');\nvar Button = require('reactor/src/Atomic/components/Atoms/Button/Button');\nvar smooothScroll = require('smoothscroll-polyfill');\n\nvar styleVariables = require('reactor/src/helpers/styleVariables');\n\nvar app = require(\"../../../impact/app\");\nvar modules = require(\"../../../impact/modules\");\n\nvar colors = {\n\tedgeDefault: styleVariables.divGray,\n\tedgeActive: styleVariables.successGreen,\n\tnodeDefault: styleVariables.placeholderGrey,\n\tentry: styleVariables.dangerRed,\n\timpact: styleVariables.netBlue\n};\n\nvar centerStyle = {\n\tdisplay: \"flex\",\n\tjustifyContent: \"center\",\n\talignItems: \"center\",\n\tflexDirection: \"row\",\n\tmargin: \"10px 0\"\n};\n\nvar entryStyle = {\n\tcursor: \"pointer\"\n};\n\nvar sigmaStyle = {\n\tmarginTop: 50,\n\theight: 500,\n\twidth: \"100%\",\n\tborderBottom: \"1px  solid black\"\n};\n\nvar separator = (<span style={{margin: 5}}></span>);\n\n/* eslint-disable */\nvar isReactor = Utils.getURLParameter(\"reactor\") != null;\n\nsmooothScroll.polyfill();\n\n/* istanbul ignore next */\nmodule.exports = createReactClass({\n\tgetInitialState: function() {\n\t\treturn {\n\t\t\timpactJson: {\n\t\t\t\tmodules: []\n\t\t\t},\n\t\t\tshowall: false\n\t\t};\n\t},\n\n\tUNSAFE_componentWillMount: function() {\n\t\tvar me = this;\n\n\t\tvar impactPath = \"reactorCmps/impact.json\";\n\n\t\tif (isReactor) {\n\t\t\timpactPath = \"reactor/dist/impact.json\";\n\t\t}\n\n\t\tcurl(\"json!\" + impactPath, function(json) {\n\t\t\twindow.impactJson = json;\n\n\t\t\ttry {\n\t\t\t\tme.setState({\n\t\t\t\t\timpactJson: json\n\t\t\t\t});\n\t\t\t} catch(e) {\n\t\t\t\tconsole.error(e);\n\t\t\t}\n\t\t}).next();\n\t},\n\n\tcomponentDidUpdate: function() {\n\t\tvar me = this;\n\n\t\tcurl(\"js!wwwroot/ui/reactorCmps/impact/sigma.min.js\", function() {\n\t\t\ttry {\n\t\t\t\tif (!me.graph) {\n\t\t\t\t\tapp.load(me.state.impactJson);\n\n\t\t\t\t\tme.graph = modules.go(me.refs.sigmaTarget, colors, me.onActiveEntry);\n\t\t\t\t}\n\t\t\t} catch(e) {\n\t\t\t\tconsole.error(e);\n\t\t\t}\n\t\t});\n\n\t},\n\n\tonActiveEntry: function(id) {\n\t\tconsole.log(\"Entry ativa: \" + id);\n\n\t\tdocument.documentElement.scroll({\n\t\t\ttop: 0,\n\t\t\tleft: 0,\n\t\t\tbehavior: 'smooth'\n\t\t});\n\t},\n\n\tactiveEntry: function(id) {\n\t\tvar me = this;\n\n\t\tconsole.log(\"Ativando \" + id);\n\n\t\tme.graph.setActiveModule(id);\n\t},\n\n\tgetLegend: function() {\n\t\treturn (\n\t\t\t<div style={centerStyle}>\n\t\t\t\t{[\n\t\t\t\t\t<Badge backgroundColor={colors.entry}>{\"Entry\"}</Badge>,\n\t\t\t\t\tseparator,\n\t\t\t\t\t<Badge backgroundColor={colors.impact}\n\t\t\t\t\t\t   color={styleVariables.textGrey}>{\"Modificado\"}</Badge>,\n\t\t\t\t\tseparator,\n\t\t\t\t\t<Badge backgroundColor={colors.edgeDefault}\n\t\t\t\t\t\t   color={styleVariables.textGrey}>{\"Dependência\"}</Badge>\n\t\t\t\t].map(function(item, index) {\n\t\t\t\t\treturn React.cloneElement(item, {key: index});\n\t\t\t\t})}\n\t\t\t</div>\n\t\t);\n\t},\n\n\tfindEntryByModuleName: function(moduleName) {\n\t\tvar me = this;\n\n\t\tif (isReactor) {\n\t\t\treturn {\n\t\t\t\turl: \"/se/ui/reactor/dist/docs/docs.html?reactCmp=\" + moduleName.split(\"examples/\")[1].replace(/\\//g, '-').replace('.jsx', '').replace('.js', '')\n\t\t\t};\n\t\t}\n\n\t\tmoduleName = moduleName.replace(\"./src/\", \"\").replace('.jsx', '').replace('.js', '');\n\n\t\tfor (var key in me.state.impactJson.entries) {\n\t\t\tvar entry = me.state.impactJson.entries[key];\n\n\t\t\tif (entry.source && entry.source.indexOf(moduleName) >= 0) {\n\t\t\t\tentry.key = key;\n\n\t\t\t\treturn entry;\n\t\t\t}\n\t\t}\n\t},\n\n\tgetUrl: function(entry) {\n\t\tentry.args = entry.args || [];\n\n\t\tif (entry.url) {\n\t\t\treturn Utils.getSystemUrl(true) + entry.url;\n\t\t} else {\n\t\t\treturn Utils.getBaseclassUrl() + \"/ui/reactor/dist/reactor-dev.html?\" + entry.args.join(\"&\") + (entry.args.length > 0 ? \"&\" : \"\") + \"reactCmp=reactorCmps/\" + entry.key;\n\t\t}\n\t},\n\n\tgetEntryPoint: function(module) {\n\t\tvar me = this,\n\t\t\tshortName = module.name.replace(\"./src/\", \"\");\n\n\t\tvar entry = me.findEntryByModuleName(module.name);\n\n\t\treturn (\n\t\t\t<div\n\t\t\t\tstyle={{marginTop: 20, marginLeft: 50, marginRight: 50}}\n\t\t\t\tkey={module.id}\n\t\t\t>\n\t\t\t\t<Panel\n\t\t\t\t\tHeader={\n\t\t\t\t\t\t(entry.mapped || isReactor) ?\n\t\t\t\t\t\t\t(<a href={me.getUrl(entry)} target=\"_blank\">{shortName}</a>) :\n\t\t\t\t\t\t\t(<span>\n\t\t\t\t\t\t\t\t<span target=\"_blank\">{shortName}</span><span>  </span><a target=\"_blank\" href={me.getUrl(entry)}>{\"não mapeada :(\"}</a>\n\t\t\t\t\t\t\t</span>)\n\t\t\t\t\t}\n\t\t\t\t\trightHeaderContent={\n\t\t\t\t\t\t<Button onClick={me.activeEntry.bind(me, module.id)} size=\"medium\">{\"Ativar\"}</Button>\n\t\t\t\t\t}\n\t\t\t\t>\n\t\t\t\t\t{entry.description === null ? \"--\" : entry.description}\n\t\t\t\t</Panel>\n\t\t\t</div>\n\t\t);\n\n\t\treturn (\n\t\t\t<div key={module.id}>\n\t\t\t\t<Badge\n\t\t\t\t\tstyle={entryStyle}\n\t\t\t\t\tonClick={me.activeEntry.bind(me, module.id)}\n\t\t\t\t>\n\t\t\t\t\t{module.name}\n\t\t\t\t</Badge>\n\t\t\t</div>\n\t\t);\n\t},\n\n\tgetAllEntries: function() {\n\t\tvar me = this, state = me.state, entries = [];\n\n\t\tfor (var key in state.impactJson.entries) {\n\t\t\tvar entry = state.impactJson.entries[key];\n\n\t\t\tentry.key = key;\n\n\t\t\tentries.push(\n\t\t\t\t<div\n\t\t\t\t\tstyle={{marginTop: 20, marginLeft: 50, marginRight: 50}}\n\t\t\t\t\tkey={key}\n\t\t\t\t>\n\t\t\t\t\t<Panel>\n\t\t\t\t\t\t<a href={me.getUrl(entry)} target=\"_blank\">{key}</a>{(entry.mapped || isReactor) ? null : <span>{\"não mapeado\"}</span>}\n\t\t\t\t\t\t{entry.description ? (<div>\n\t\t\t\t\t\t\t{entry.description}\n\t\t\t\t\t\t</div>) : null }\n\t\t\t\t\t</Panel>\n\t\t\t\t</div>\n\t\t\t);\n\n\t\t}\n\n\t\treturn entries;\n\t},\n\n\trender: function() {\n\t\tvar me = this, state = me.state;\n\n\t\treturn (\n\t\t\t<div style={{height: \"100%\", width: \"100%\"}}>\n\t\t\t\t<div id=\"sigmaTarget\" ref=\"sigmaTarget\" style={sigmaStyle}/>\n\t\t\t\t<div>\n\t\t\t\t\t{me.getLegend()}\n\t\t\t\t\t<div>\n\t\t\t\t\t\t{!state.showAll && state.impactJson.modules ? state.impactJson.modules.map(function(module) {\n\t\t\t\t\t\t\tif (module.reasons.length === 0) {\n\t\t\t\t\t\t\t\treturn me.getEntryPoint(module);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}) : null}\n\t\t\t\t\t\t{state.showAll && state.impactJson.entries ? me.getAllEntries() : null}\n\t\t\t\t\t</div>\n\t\t\t\t\t{isReactor ?\n\t\t\t\t\t\tnull :\n\t\t\t\t\t\t(<div style={{float: \"right\", margin: 50}}>\n\t\t\t\t\t\t\t<Button onClick={function() { me.setState({showAll: !state.showAll}); }} size=\"medium\">{\"Exibir todos os entryPoints\"}</Button>\n\t\t\t\t\t\t</div>)\n\t\t\t\t\t}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n});\n/* eslint-enable */"], "names": ["exports", "load", "stats", "a", "b", "mapModules", "mapModulesIdent", "mapModulesUid", "module", "idx", "mapChunks", "chunk", "reason", "m", "setTimestamp", "factory", "building", "issuer", "NaN", "parent", "c", "origin", "max<PERSON><PERSON><PERSON>", "getColor", "module1", "impact", "colors", "element", "onActive", "app", "require", "helpers", "nodes", "edges", "moduleCount", "maxTimestamp", "maxSize", "done", "uniqueReasons", "uid", "Math", "parentIdent", "parentModule", "weight", "async", "sigma", "node", "context", "settings", "old", "edge", "source", "target", "s", "e", "mod", "setActiveModule", "reset", "alreadyGreen", "setDepsAsGreen", "parentId", "depId", "dep", "n", "id", "activeModule", "React", "createReactClass", "Badge", "Utils", "Panel", "<PERSON><PERSON>", "smooothScroll", "styleVariables", "modules", "centerStyle", "entryStyle", "sigmaStyle", "separator", "isReactor", "getInitialState", "UNSAFE_componentWillMount", "me", "impactPath", "curl", "json", "window", "console", "componentDidUpdate", "onActiveEntry", "document", "activeEntry", "getLegend", "item", "index", "findEntryByModuleName", "moduleName", "key", "entry", "getUrl", "getEntryPoint", "shortName", "getAllEntries", "state", "entries", "render", "onClick"], "mappings": ";;;AAAAA,mBAAAA,CAAAA,0FAAAA;AAAAA,mBAAAA,CAAAA,kGAAAA;AAAAA,mBAAAA,CAAAA,sGAAAA;AAAAA,mBAAAA,CAAAA,gGAAAA;AAAAA,mBAAAA,CAAAA,8FAAAA;AAAAA,mBAAAA,CAAAA,4FAAAA;AAAAA,aAAa,GAAG;AAChBA,kBAAkB,GAAG;AACrBA,iBAAiB,GAAG;AAEpB,SAASC,KAAKC,KAAK;IAClBA,MAAM,MAAM,CAAC,IAAI,CAAC,SAASC,CAAC,EAAEC,CAAC;QAC9B,OAAOA,EAAE,IAAI,GAAGD,EAAE,IAAI;IACvB;IACAD,MAAM,OAAO,CAAC,IAAI,CAAC,SAASC,CAAC,EAAEC,CAAC;QAC/B,OAAOD,EAAE,EAAE,GAAGC,EAAE,EAAE;IACnB;IACA,IAAIC,aAAa,CAAC;IAClB,IAAIC,kBAAkB,CAAC;IACvB,IAAIC,gBAAgB,CAAC;IACrBL,MAAM,OAAO,CAAC,OAAO,CAAC,SAASM,MAAM,EAAEC,GAAG;QACzCJ,UAAU,CAACG,OAAO,EAAE,CAAC,GAAGA;QACxBF,eAAe,CAAC,MAAIE,OAAO,UAAU,CAAC,GAAGA;QACzCD,aAAa,CAACC,OAAO,GAAG,GAAGC,IAAI,GAAGD;QAClCA,OAAO,YAAY,GAAG,EAAE;IACzB;IACA,IAAIE,YAAY,CAAC;IACjBR,MAAM,MAAM,CAAC,OAAO,CAAC,SAASS,KAAK;QAClCD,SAAS,CAACC,MAAM,EAAE,CAAC,GAAGA;QACtBA,MAAM,QAAQ,GAAG,EAAE;IACpB;IACAT,MAAM,OAAO,CAAC,OAAO,CAAC,SAASM,MAAM;QACpCA,OAAO,OAAO,CAAC,OAAO,CAAC,SAASI,MAAM;YACrC,IAAIC,IAAIP,eAAe,CAAC,MAAIM,OAAO,gBAAgB,CAAC;YACpD,IAAG,CAACC,GAAG;YACPD,OAAO,SAAS,GAAGC,EAAE,GAAG;YACxBA,EAAE,YAAY,CAAC,IAAI,CAAC;gBACnB,MAAMD,OAAO,IAAI;gBACjB,UAAUJ,OAAO,EAAE;gBACnB,WAAWA,OAAO,GAAG;gBACrB,QAAQA,OAAO,IAAI;gBACnB,aAAaI,OAAO,WAAW;gBAC/B,KAAKA,OAAO,GAAG;YAChB;QACD;QACAJ,OAAO,SAAS,GAAGF,eAAe,CAAC,MAAIE,OAAO,MAAM,CAAC,IAAIF,eAAe,CAAC,MAAIE,OAAO,MAAM,CAAC,CAAC,GAAG;QAC9F,UAASM,aAAaN,MAAM;YAC5B,IAAG,OAAOA,OAAO,SAAS,KAAK,UAAU,OAAOA,OAAO,SAAS;YAChE,IAAG,CAACA,OAAO,OAAO,EAAE;YACpB,IAAIO,UAAUP,OAAO,OAAO,CAAC,OAAO,IAAI;YACxC,IAAIQ,WAAWR,OAAO,OAAO,CAAC,QAAQ,IAAI;YAC1CA,OAAO,IAAI,GAAGO,UAAUC;YACxB,IAAG,CAACR,OAAO,MAAM,EAAE,OAAOA,OAAO,SAAS,GAAGA,OAAO,IAAI;YACxD,IAAIS,SAASX,eAAe,CAAC,MAAIE,OAAO,MAAM,CAAC;YAC/C,IAAG,CAACS,QAAQ,OAAOT,OAAO,SAAS,GAAGU;YACtCJ,aAAaG;YACbT,OAAO,SAAS,GAAGS,OAAO,SAAS,GAAGT,OAAO,IAAI;QAClD,GAAEA;IACH;IACAN,MAAM,MAAM,CAAC,OAAO,CAAC,SAASS,KAAK;QAClCA,MAAM,OAAO,CAAC,OAAO,CAAC,SAASQ,MAAM;YACpC,IAAIC,IAAIV,SAAS,CAACS,OAAO;YACzBC,EAAE,QAAQ,CAAC,IAAI,CAACT,MAAM,EAAE;QACzB;QACAA,MAAM,OAAO,CAAC,OAAO,CAAC,SAASU,MAAM;YACpC,IAAIR,IAAIP,eAAe,CAAC,MAAIe,OAAO,gBAAgB,CAAC;YACpD,IAAG,CAACR,GAAG;YACPQ,OAAO,SAAS,GAAGR,EAAE,GAAG;QACzB;IACD;IACAX,MAAM,OAAO,CAAC,OAAO,CAAC,SAASM,MAAM;QACpCA,OAAO,YAAY,CAAC,IAAI,CAAC,SAASL,CAAC,EAAEC,CAAC;YACrC,IAAG,CAACD,EAAE,GAAG,IAAI,CAACC,EAAE,GAAG,EAAE,OAAO;YAC5B,IAAG,CAACD,EAAE,GAAG,EAAE,OAAO;YAClB,IAAG,CAACC,EAAE,GAAG,EAAE,OAAO,CAAC;YACnBD,IAAIA,EAAE,GAAG,CAAC,KAAK,CAAC;YAChBC,IAAIA,EAAE,GAAG,CAAC,KAAK,CAAC;YAChB,IAAG,CAACD,CAAC,CAAC,EAAE,GAAG,CAACC,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC;YAC1B,IAAG,CAACD,CAAC,CAAC,EAAE,GAAG,CAACC,CAAC,CAAC,EAAE,EAAE,OAAO;YACzB,IAAG,CAACD,CAAC,CAAC,EAAE,GAAG,CAACC,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC;YAC1B,IAAG,CAACD,CAAC,CAAC,EAAE,GAAG,CAACC,CAAC,CAAC,EAAE,EAAE,OAAO;YACzB,OAAO;QACR;IACD;IACA,IAAIkB,YAAY;IAChBpB,MAAM,MAAM,CAAC,OAAO,CAAC,SAASC,CAAC;QAC9B,IAAGA,EAAE,IAAI,CAAC,MAAM,GAAGmB,WAAWA,YAAYnB,EAAE,IAAI,CAAC,MAAM;IACxD;IACAD,MAAM,MAAM,CAAC,OAAO,CAAC,SAASC,CAAC;QAC9BA,EAAE,cAAc,GAAGA,EAAE,IAAI;QACzB,MAAMA,EAAE,cAAc,CAAC,MAAM,GAAGmB,UAC/BnB,EAAE,cAAc,GAAG,MAAMA,EAAE,cAAc;IAC3C;IACAD,MAAM,MAAM,CAAC,IAAI,CAAC,SAASC,CAAC,EAAEC,CAAC;QAC9BD,IAAIA,EAAE,cAAc;QACpBC,IAAIA,EAAE,cAAc;QACpB,OAAOD,IAAIC,IAAI,CAAC,IAAI;IACrB;IACAJ,aAAa,GAAGE;IAChBF,iBAAiB,GAAGU;IACpBV,kBAAkB,GAAGK;IACrBL,qBAAqB,GAAGO;IACxBP,uBAAuB,GAAGM;AAC3B;AAEAN,YAAY,GAAGC;;;;;ACnGfO,mBAAAA,CAAAA,kGAAAA;AAAAA,cAAc,GAAG;IAChBe,UAAU,SAAVA,SAAmBC,OAAM,EAAEC,MAAM,EAAEC,MAAM;QACxC,IAAIF,QAAO,OAAO,CAAC,MAAM,KAAK,GAAG;YAChC,OAAOE,OAAO,KAAK;QACpB,OAAO,IAAID,OAAO,OAAO,CAACD,QAAO,EAAE,KAAK,GAAG;YAC1C,OAAOE,OAAO,MAAM;QACrB;QACA,OAAO;IACR;AACD;;;;;ACTAlB,mBAAAA,CAAAA,kGAAAA;AAAAA,mBAAAA,CAAAA,sGAAAA;AAAAA,mBAAAA,CAAAA,8FAAAA;AAAAA,mBAAAA,CAAAA,gGAAAA;AAAAA,mBAAAA,CAAAA,gGAAAA;AAAAA,mBAAAA,CAAAA,0FAAAA;AAAAA,iBAAiB,GAAG,SAASmB,OAAO,EAAED,MAAM,EAAEE,QAAQ;IACrD,IAAIC,MAAMC,mBAAOA,CAAC,8BAAO;IACzB,IAAIC,UAAUD,mBAAOA,CAAC,sCAAW;IAEjC,IAAIE,QAAQ,EAAE;IACd,IAAIC,QAAQ,EAAE;IACd,IAAIC,cAAcL,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM;IAC1C,IAAIM,eAAe;IACnB,IAAIC,UAAU;IAEdP,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAASL,OAAM,EAAEf,GAAG;QAC7C,IAAIe,QAAO,IAAI,GAAGY,SAASA,UAAUZ,QAAO,IAAI;QAChD,IAAIA,QAAO,SAAS,GAAGW,cAAcA,eAAeX,QAAO,SAAS;IACrE;IACAK,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAASL,OAAM,EAAEf,GAAG;QAC7C,IAAI4B,OAAO,CAAC;QACZ,IAAIC,gBAAgBd,QAAO,OAAO,CAAC,MAAM,CAAC,SAASZ,MAAM;YACxD,IAAIO,SAASP,OAAO,MAAM;YAC1B,IAAIyB,IAAI,CAAC,MAAMlB,OAAO,EAAE,OAAO;YAC/BkB,IAAI,CAAC,MAAMlB,OAAO,GAAG;YACrB,OAAO;QACR;QACA,IAAIoB,MAAMf,QAAO,GAAG;QACpBQ,MAAM,IAAI,CAAC;YACV,IAAI,WAAWO;YACf,KAAKA;YACL,WAAWA;YACX,UAAUf,QAAO,EAAE;YACnB,QAAQA;YACR,MAAM;YACN,MAAMA,QAAO,IAAI,GAAG;YACpB,OAAO,MAAMA,QAAO,EAAE,GAAG,OAAOA,QAAO,IAAI;YAC3C,YAAY,KAAKA,QAAO,EAAE;YAC1B,GAAGgB,KAAK,GAAG,CAAC/B,MAAMyB,cAAcM,KAAK,EAAE,GAAG,KAAKA,KAAK,IAAI,CAACF,cAAc,MAAM,GAAG,KAAKE,KAAK,IAAI,CAACN;YAC/F,GAAGM,KAAK,GAAG,CAAC/B,MAAMyB,cAAcM,KAAK,EAAE,GAAG,KAAKA,KAAK,IAAI,CAACF,cAAc,MAAM,GAAG,KAAKE,KAAK,IAAI,CAACN;YAC/F,eAAeR,OAAO,WAAW;YACjC,OAAOK,QAAQ,QAAQ,CAACP,SAAQK,IAAI,KAAK,CAAC,MAAM,EAAEH;QACnD;QACAY,cAAc,OAAO,CAAC,SAAS1B,MAAM;YACpC,IAAI6B,cAAc7B,OAAO,gBAAgB;YACzC,IAAI8B,eAAeb,IAAI,eAAe,CAAC,MAAMY,YAAY;YACzD,IAAI,CAACC,cAAc;YACnB,IAAIC,SAAS;YACb,IAAIC,QAAQ;YACZX,MAAM,IAAI,CAAC;gBACV,IAAI,SAAST,QAAO,GAAG,GAAG,MAAM,CAACkB,aAAa,GAAG;gBACjD,iBAAiBA,aAAa,GAAG;gBACjC,cAAcA;gBACd,QAAQ,WAAWA,aAAa,GAAG;gBACnC,cAAclB;gBACd,iBAAiBA,QAAO,GAAG;gBAC3B,QAAQ,WAAWA,QAAO,GAAG;gBAC7B,OAAO;gBACP,MAAM;gBACN,UAAU;oBAAC;iBAAE;gBACb,eAAeE,OAAO,WAAW;gBACjC,OAAOA,OAAO,WAAW;gBACzB,MAAMiB;gBACN,QAAQA;YACT;QACD;IACD;IAGAE,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,SAASC,IAAI,EAAEC,OAAO,EAAEC,QAAQ;QAC7D,IAAIC,MAAMH,KAAK,KAAK;QACpB,IAAIA,KAAK,UAAU,EAAEA,KAAK,KAAK,GAAGA,KAAK,UAAU;QACjDD,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,CAACC,MAAMC,SAASC;QACvCF,KAAK,KAAK,GAAGG;IACd;IAEAJ,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,SAASK,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEL,OAAO,EAAEC,QAAQ;QAChF,IAAI,CAACD,QAAQ,WAAW,IAAI,CAACA,QAAQ,WAAW,EAAE,OAAOF,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,CAACK,MAAMC,QAAQC,QAAQL,SAASC;QACjH,IAAIC,MAAMF,QAAQ,WAAW;QAC7BA,QAAQ,WAAW,CAACG,KAAK,QAAQ,IAAI;YAAC;YAAG;SAAE;QAC3CL,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,CAACK,MAAMC,QAAQC,QAAQL,SAASC;QACxDD,QAAQ,WAAW,CAACE;IACrB;IAEA,IAAII,IAAI,IAAIR,MAAM;QACjB,OAAO;YACN,OAAOb;YACP,OAAOC;QACR;QACA,UAAU;YACT,MAAM;YACN,WAAWN;QACZ;QACA,UAAU;YACT,WAAW;YACX,aAAa;YACb,aAAa;YACb,aAAa;YACb,aAAa;QACd;IACD;IAEA0B,EAAE,IAAI,CAAC,aAAa,SAASC,CAAC;QAC7B,IAAIC,MAAMD,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;QAE5B,IAAIC,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG;YAC7BC,gBAAgBD,IAAI,EAAE;QACvB,OAAO;YACNE;QACD;IACD;IAEAJ,EAAE,OAAO;IAETA,EAAE,gBAAgB,CAAC;QAClB,qBAAqB;QACrB,aAAa;QACb,qBAAqB;QACrB,UAAU;IACX;IAEAA,EAAE,SAAS,CAAC,EAAE,CAAC,MAAM;IAGrB,IAAIK,eAAe,CAAC;IAEpB,SAASC,eAAeC,QAAQ,EAAEC,KAAK;QACtC,IAAIH,YAAY,CAACE,WAAWC,MAAM,EAAE;YACnC;QACD;QAEAH,YAAY,CAACE,WAAWC,MAAM,GAAG;QAEjCR,EAAE,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,SAASC,CAAC;YACjC,IAAIA,EAAE,YAAY,CAAC,EAAE,KAAKM,YAAYN,EAAE,YAAY,CAAC,EAAE,KAAKO,OAAO;gBAClEP,EAAE,KAAK,GAAG5B,OAAO,UAAU;gBAE3B4B,EAAE,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,SAASQ,GAAG;oBAC/CH,eAAeE,OAAOC,IAAI,QAAQ;gBACnC;YACD;YAEAT,EAAE,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,SAASU,CAAC;gBACjC,IAAIA,EAAE,QAAQ,KAAKF,SAASE,EAAE,KAAK,KAAKrC,OAAO,KAAK,IAAIqC,EAAE,KAAK,KAAKrC,OAAO,MAAM,EAAE;oBAClFqC,EAAE,KAAK,GAAGrC,OAAO,UAAU;oBAC3B,IAAI,CAACqC,EAAE,MAAM,CAAC,QAAQ,EAAEA,EAAE,MAAM,CAAC,QAAQ,GAAGA,EAAE,UAAU;oBACxDA,EAAE,UAAU,GAAGA,EAAE,KAAK;gBACvB;YACD;QACD;IACD;IAEA,IAAIN,QAAQ;QACXC,eAAe,CAAC;QAEhBL,EAAE,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,SAASC,CAAC;YACjC,IAAIA,EAAE,KAAK,KAAK5B,OAAO,UAAU,EAAE;gBAClC4B,EAAE,KAAK,GAAG5B,OAAO,WAAW;YAC7B;QACD;QAEA2B,EAAE,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,SAASU,CAAC;YACjC,IAAIA,EAAE,KAAK,KAAKrC,OAAO,KAAK,IAAIqC,EAAE,KAAK,KAAKrC,OAAO,MAAM,EAAE;gBAC1DqC,EAAE,KAAK,GAAGrC,OAAO,WAAW;YAC7B;YAEA,IAAIqC,EAAE,MAAM,CAAC,QAAQ,EAAE;gBACtBA,EAAE,UAAU,GAAGA,EAAE,MAAM,CAAC,QAAQ;YACjC;QACD;IACD;IAEA,IAAIP,kBAAkB,yBAASQ,EAAE;QAChC,IAAIC,eAAepC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS0B,GAAG;YACrD,OAAOA,IAAI,EAAE,KAAKS;QACnB;QAEAP;QAEA7B,SAASoC;QAETC,aAAa,YAAY,CAAC,OAAO,CAAC,SAASH,GAAG;YAC7CH,eAAeK,IAAIF,IAAI,QAAQ;QAChC;QAEAT,EAAE,OAAO;IACV;IAEA,OAAO;QACN,iBAAiBG;IAClB;AACD;;;;;AC1LA,8GAmQA,iBAAiB;AAnQjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAIU,QAAQpC,mBAAOA,CAAC,oBAAO;AAC3B,IAAIqC,mBAAmBrC,mBAAOA,CAAC,8CAAoB;AACnD,IAAIsC,QAAQtC,mBAAOA,CAAC,mGAA2C;AAC/D,IAAIuC,QAAQvC,mBAAOA,CAAC,oBAAO;AAC3B,IAAIwC,QAAQxC,mBAAOA,CAAC,6GAAgD;AACpE,IAAIyC,SAASzC,mBAAOA,CAAC,mHAAmD;AACxE,IAAI0C,gBAAgB1C,mBAAOA,CAAC,yFAAuB;AAEnD,IAAI2C,iBAAiB3C,mBAAOA,CAAC,oFAAoC;AAEjE,IAAID,MAAMC,mBAAOA,CAAC,4CAAqB;AACvC,IAAI4C,UAAU5C,mBAAOA,CAAC,oDAAyB;AAE/C,IAAIJ,SAAS;IACZ,aAAa+C,eAAe,OAAO;IACnC,YAAYA,eAAe,YAAY;IACvC,aAAaA,eAAe,eAAe;IAC3C,OAAOA,eAAe,SAAS;IAC/B,QAAQA,eAAe,OAAO;AAC/B;AAEA,IAAIE,cAAc;IACjB,SAAS;IACT,gBAAgB;IAChB,YAAY;IACZ,eAAe;IACf,QAAQ;AACT;AAEA,IAAIC,aAAa;IAChB,QAAQ;AACT;AAEA,IAAIC,aAAa;IAChB,WAAW;IACX,QAAQ;IACR,OAAO;IACP,cAAc;AACf;AAEA,IAAIC,0BAAa,oBAAC;IAAK,OAAO;QAAC,QAAQ;IAAC;;AAExC,kBAAkB,GAClB,IAAIC,YAAYV,MAAM,eAAe,CAAC,cAAc;AAEpDG,cAAc,QAAQ;AAEtB,wBAAwB,GACxBhE,cAAc,GAAG2D,iBAAiB;IACjCa,iBAAiB,SAAjBA;QACC,OAAO;YACN,YAAY;gBACX,SAAS,EAAE;YACZ;YACA,SAAS;QACV;IACD;IAEAC,2BAA2B,SAA3BA;QACC,IAAIC,KAAK,IAAI;QAEb,IAAIC,aAAa;QAEjB,IAAIJ,WAAW;YACdI,aAAa;QACd;QAEAC,KAAK,UAAUD,YAAY,SAASE,IAAI;YACvCC,OAAO,UAAU,GAAGD;YAEpB,IAAI;gBACHH,GAAG,QAAQ,CAAC;oBACX,YAAYG;gBACb;YACD,EAAE,OAAM/B,GAAG;gBACViC,QAAQ,KAAK,CAACjC;YACf;QACD,GAAG,IAAI;IACR;IAEAkC,oBAAoB,SAApBA;QACC,IAAIN,KAAK,IAAI;QAEbE,KAAK,iDAAiD;YACrD,IAAI;gBACH,IAAI,CAACF,GAAG,KAAK,EAAE;oBACdrD,IAAI,IAAI,CAACqD,GAAG,KAAK,CAAC,UAAU;oBAE5BA,GAAG,KAAK,GAAGR,QAAQ,EAAE,CAACQ,GAAG,IAAI,CAAC,WAAW,EAAExD,QAAQwD,GAAG,aAAa;gBACpE;YACD,EAAE,OAAM5B,GAAG;gBACViC,QAAQ,KAAK,CAACjC;YACf;QACD;IAED;IAEAmC,eAAe,SAAfA,cAAwBzB,EAAE;QACzBuB,QAAQ,GAAG,CAAC,kBAAkBvB;QAE9B0B,SAAS,eAAe,CAAC,MAAM,CAAC;YAC/B,KAAK;YACL,MAAM;YACN,UAAU;QACX;IACD;IAEAC,aAAa,SAAbA,YAAsB3B,EAAE;QACvB,IAAIkB,KAAK,IAAI;QAEbK,QAAQ,GAAG,CAAC,cAAcvB;QAE1BkB,GAAG,KAAK,CAAC,eAAe,CAAClB;IAC1B;IAEA4B,WAAW,SAAXA;QACC,qBACC,oBAAC;YAAI,OAAOjB;WACV;0BACA,oBAACP;gBAAM,iBAAiB1C,OAAO,KAAK;eAAG;YACvCoD;0BACA,oBAACV;gBAAM,iBAAiB1C,OAAO,MAAM;gBACjC,OAAO+C,eAAe,QAAQ;eAAG;YACrCK;0BACA,oBAACV;gBAAM,iBAAiB1C,OAAO,WAAW;gBACtC,OAAO+C,eAAe,QAAQ;eAAG;SACrC,CAAC,GAAG,CAAC,SAASoB,IAAI,EAAEC,KAAK;YACzB,OAAO5B,MAAM,YAAY,CAAC2B,MAAM;gBAAC,KAAKC;YAAK;QAC5C;IAGH;IAEAC,uBAAuB,SAAvBA,sBAAgCC,UAAU;QACzC,IAAId,KAAK,IAAI;QAEb,IAAIH,WAAW;YACd,OAAO;gBACN,KAAK,iDAAiDiB,WAAW,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,OAAO;YAC/I;QACD;QAEAA,aAAaA,WAAW,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,OAAO;QAEjF,IAAK,IAAIC,OAAOf,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAE;YAC5C,IAAIgB,QAAQhB,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAACe,IAAI;YAE5C,IAAIC,MAAM,MAAM,IAAIA,MAAM,MAAM,CAAC,OAAO,CAACF,eAAe,GAAG;gBAC1DE,MAAM,GAAG,GAAGD;gBAEZ,OAAOC;YACR;QACD;IACD;IAEAC,QAAQ,SAARA,OAAiBD,KAAK;QACrBA,MAAM,IAAI,GAAGA,MAAM,IAAI,IAAI,EAAE;QAE7B,IAAIA,MAAM,GAAG,EAAE;YACd,OAAO7B,MAAM,YAAY,CAAC,QAAQ6B,MAAM,GAAG;QAC5C,OAAO;YACN,OAAO7B,MAAM,eAAe,KAAK,uCAAuC6B,MAAM,IAAI,CAAC,IAAI,CAAC,OAAQA,CAAAA,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAC,IAAK,0BAA0BA,MAAM,GAAG;QACxK;IACD;IAEAE,eAAe,SAAfA,cAAwB5E,OAAM;QAC7B,IAAI0D,KAAK,IAAI,EACZmB,YAAY7E,QAAO,IAAI,CAAC,OAAO,CAAC,UAAU;QAE3C,IAAI0E,QAAQhB,GAAG,qBAAqB,CAAC1D,QAAO,IAAI;QAEhD,qBACC,oBAAC;YACA,OAAO;gBAAC,WAAW;gBAAI,YAAY;gBAAI,aAAa;YAAE;YACtD,KAAKA,QAAO,EAAE;yBAEd,oBAAC8C;YACA,QACE4B,MAAM,MAAM,IAAInB,0BACf,oBAAC;gBAAE,MAAMG,GAAG,MAAM,CAACgB;gBAAQ,QAAO;eAAUG,2BAC5C,oBAAC,4BACD,oBAAC;gBAAK,QAAO;eAAUA,0BAAiB,oBAAC,cAAK,qBAAS,oBAAC;gBAAE,QAAO;gBAAS,MAAMnB,GAAG,MAAM,CAACgB;eAAS;YAGtG,kCACC,oBAAC3B;gBAAO,SAASW,GAAG,WAAW,CAAC,IAAI,CAACA,IAAI1D,QAAO,EAAE;gBAAG,MAAK;eAAU;WAGpE0E,MAAM,WAAW,KAAK,OAAO,OAAOA,MAAM,WAAW;QAKzD,qBACC,oBAAC;YAAI,KAAK1E,QAAO,EAAE;yBAClB,oBAAC4C;YACA,OAAOQ;YACP,SAASM,GAAG,WAAW,CAAC,IAAI,CAACA,IAAI1D,QAAO,EAAE;WAEzCA,QAAO,IAAI;IAIhB;IAEA8E,eAAe,SAAfA;QACC,IAAIpB,KAAK,IAAI,EAAEqB,QAAQrB,GAAG,KAAK,EAAEsB,UAAU,EAAE;QAE7C,IAAK,IAAIP,OAAOM,MAAM,UAAU,CAAC,OAAO,CAAE;YACzC,IAAIL,QAAQK,MAAM,UAAU,CAAC,OAAO,CAACN,IAAI;YAEzCC,MAAM,GAAG,GAAGD;YAEZO,QAAQ,IAAI,eACX,oBAAC;gBACA,OAAO;oBAAC,WAAW;oBAAI,YAAY;oBAAI,aAAa;gBAAE;gBACtD,KAAKP;6BAEL,oBAAC3B,2BACA,oBAAC;gBAAE,MAAMY,GAAG,MAAM,CAACgB;gBAAQ,QAAO;eAAUD,MAAUC,MAAM,MAAM,IAAInB,YAAa,qBAAO,oBAAC,cAAM,gBAChGmB,MAAM,WAAW,iBAAI,oBAAC,aACrBA,MAAM,WAAW,IACT;QAKd;QAEA,OAAOM;IACR;IAEAC,QAAQ,SAARA;QACC,IAAIvB,KAAK,IAAI,EAAEqB,QAAQrB,GAAG,KAAK;QAE/B,qBACC,oBAAC;YAAI,OAAO;gBAAC,QAAQ;gBAAQ,OAAO;YAAM;yBACzC,oBAAC;YAAI,IAAG;YAAc,KAAI;YAAc,OAAOL;0BAC/C,oBAAC,aACCK,GAAG,SAAS,kBACb,oBAAC,aACC,CAACqB,MAAM,OAAO,IAAIA,MAAM,UAAU,CAAC,OAAO,GAAGA,MAAM,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS/E,OAAM;YACzF,IAAIA,QAAO,OAAO,CAAC,MAAM,KAAK,GAAG;gBAChC,OAAO0D,GAAG,aAAa,CAAC1D;YACzB;QACD,KAAK,MACJ+E,MAAM,OAAO,IAAIA,MAAM,UAAU,CAAC,OAAO,GAAGrB,GAAG,aAAa,KAAK,OAElEH,YACA,qBACC,oBAAC;YAAI,OAAO;gBAAC,OAAO;gBAAS,QAAQ;YAAE;yBACvC,oBAACR;YAAOmC,SAAS,SAATA;gBAAsBxB,GAAG,QAAQ,CAAC;oBAAC,SAAS,CAACqB,MAAM,OAAO;gBAAA;YAAI;YAAG,MAAK;WAAU;IAM9F;iBAjNM;AAkNP"}