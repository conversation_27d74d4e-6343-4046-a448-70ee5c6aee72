define(["Storage","tokens!reactorCmps/tokens/general","when","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","WorkspaceInfo","SG2/collection/Factory","create-react-class","Connector","suite-storage"], function(__WEBPACK_EXTERNAL_MODULE_Storage__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_when__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEB<PERSON>CK_EXTERNAL_MODULE_WorkspaceInfo__, __WEBPACK_EXTERNAL_MODULE_SG2_collection_Factory__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEBPACK_EXTERNAL_MODULE_suite_storage__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/ProfileView"], {
"../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[2]!./src/framework/components/mobile/Profile/ProfileView.scss": (function (module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "../node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../../../node_modules/css-loader/dist/runtime/api.js */ "../node_modules/css-loader/dist/runtime/api.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, `@charset "UTF-8";
/* Aplica uma transição com uma velocidade constante do início ao fim. */
/* Começa a animação devagar, acelera no meio e desacelera no final. */
/* Começa a animação devagar e acelera ao longo do tempo. */
/* Começa a animação rapidamente e desacelera no final. */
/* Aplica uma aceleração e desaceleração moderada no início e no final da animação. */
/* Define o tamanho de fonte de 10px. */
/* Define o tamanho de fonte de 11px. */
/* Define o tamanho de fonte de 12px. */
/* Define o tamanho de fonte de 13px. */
/* Define o tamanho de fonte de 14px. */
/* Define o tamanho de fonte de 16px. */
/* Define o tamanho de fonte de 20px. */
/* Define o tamanho de fonte de 24px. */
/* A família de fontes é definida como arial, "Helvetica Neue", e sans-serif. */
/* O peso de fonte definido como bold */
/* O peso de fonte definido como normal */
/* Define o tamanho de fonte de 32px. */
/* Define o tamanho de fonte de 36px. */
/* Define a altura da linha como 1. */
/* Define a altura da linha como 1.25 */
/* Define a altura da linha como 1.5 */
/* Aplica um raio de borda de 3px. */
/* Aplica um espaçamento de 4px. */
/* Aplica um espaçamento de 8px, que é o dobro de Spacing1. */
/* Aplica um espaçamento de 12px, que é o triplo de Spacing1. */
/* Aplica um espaçamento de 16px, que é o quádruplo de Spacing1. */
/* Aplica um espaçamento de 20px, que é o quíntuplo de Spacing1. */
/* Aplica um espaçamento de 24px, que é o sêxtuplo de Spacing1. */
/* Aplica um espaçamento de 28px, que é o sétuplo de Spacing1. */
/* Aplica um espaçamento de 32px, que é o octuplo de Spacing1. */
/* Aplica um tamanho de 16px, definido como Spacing4. */
/* Aplica um tamanho de 24px, definido como Spacing6. */
/* Aplica um tamanho de 28px, definido como Spacing7. */
/* Aplica um tamanho de 32px, definido como Spacing8. */
/* Aplica um tamanho de 36px, que é o nono múltiplo de \$pacing1. */
/* Aplica um espaçamento de 4px entre itens, definido como Spacing1. */
/* Aplica um espaçamento de 8px entre itens, definido como Spacing2. */
/* Aplica um espaçamento de 12px entre itens, definido como Spacing3. */
/* Aplica um espaçamento de 16px entre itens, definido como Spacing4. */
/* Aplica um espaçamento de 20px entre itens, definido como Spacing5. */
/* Aplica um espaçamento de 32px entre itens, definido como Spacing8. */
/* Aplica um espaçamento de 12px entre itens em uma linha, definido como StackMedium. */
/* Aplica um espaçamento de 20px entre itens em uma linha, definido como StackContent. */
/* Aplica um espaçamento de 20px para cabeçalhos de nível 1, definido como StackContent. */
/* Aplica um espaçamento de 16px para cabeçalhos de nível 2, definido como StackLarge. */
/* Aplica um espaçamento de 12px para cabeçalhos de nível 3, definido como StackMedium. */
/* Aplica um inset de 8px, definido como Spacing2. */
/* Aplica um inset de 12px, definido como Spacing3. */
/* Aplica um inset de 16px, definido como Spacing4. */
/* Aplica um inset de 20px, definido como Spacing5. */
/* Aplica um espaçamento de 4px para espaçamentos inline, definido como Spacing1. */
/* Aplica um espaçamento de 8px para espaçamentos inline, definido como Spacing2. */
/* Aplica um espaçamento de 16px para espaçamentos inline em relacionamentos, definido como Spacing4. */
/* Aplica um espaçamento de 32px para espaçamentos inline grandes, definido como Spacing8. */
/* Aplica uma transição rápida com duração de 0.15 segundos. */
/* Aplica uma transição normal com duração de 0.25 segundos. */
/* Aplica uma transição mais longa com duração de 0.35 segundos. */
/* Aplica uma transição lenta com duração de 1 segundo. */
.profileContentWrapper > button > span {
  color: #f32c40;
  font-weight: 500;
  text-transform: uppercase;
}`, ""]);
// Exports
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);


}),
"../reactor2/src/Mobile/components/Templates/Leaf/Leaf.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var styles = {
    backgroundColor: 'white',
    height: '100%',
    display: "flex",
    flexDirection: "column",
    appearance: "none"
};
module.exports = createReactClass({
    displayName: "Mobile/components/Templates/Leaf/Leaf",
    propTypes: {
        children: PropTypes.oneOfType([
            PropTypes.arrayOf(PropTypes.node),
            PropTypes.node
        ])
    },
    render: function() {
        return /*#__PURE__*/ React.createElement("div", _object_spread_props(_object_spread({
            className: "rctLeaf"
        }, this.props), {
            style: this.props.skipStyles ? null : styles
        }), this.props.children);
    }
});


}),
"./src/framework/components/mobile/Profile/LicenseSelector.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var Storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! Storage */ "Storage");
/* ESM import */var Storage__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(Storage__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/useTokenManager */ "../reactor2/src/Atomic/components/Helpers/Language/useTokenManager.js");
/* ESM import */var reactor2_src_Atomic_components_Orgs_Dropdown_DropdownButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactor2/src/Atomic/components/Orgs/Dropdown/DropdownButton */ "../reactor2/src/Atomic/components/Orgs/Dropdown/DropdownButton.jsx");
/* ESM import */var reactor2_src_Atomic_components_Orgs_Dropdown_DropdownButton__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Orgs_Dropdown_DropdownButton__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_MenuItem_MenuItem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/MenuItem/MenuItem */ "../reactor2/src/Atomic/components/Atoms/MenuItem/MenuItem.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_MenuItem_MenuItem__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_MenuItem_MenuItem__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Icon_Icon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Icon/Icon */ "../reactor2/src/Atomic/components/Atoms/Icon/Icon.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Icon_Icon__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Icon_Icon__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! reactor2/src/Styles/tokens/styleVariables.ecss */ "../reactor2/src/Styles/tokens/styleVariables.ecss");
/* ESM import */var reactorCmps_src_workspace_components_Common_LicenseKeysAlert_LicenseKeysRequest__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactorCmps/src/workspace/components/Common/LicenseKeysAlert/LicenseKeysRequest */ "./src/workspace/components/Common/LicenseKeysAlert/LicenseKeysRequest.js");
/* ESM import */var reactorCmps_src_framework_components_mobile_Profile_LocationFacade__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactorCmps/src/framework/components/mobile/Profile/LocationFacade */ "./src/framework/components/mobile/Profile/LocationFacade.js");
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}












var licenseDropdownButtonStyle = {
    marginBottom: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_9__.spacingSpacing8
};
var selectedLicenseIconStyle = {
    marginRight: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_9__.spacingSpacing2
};
var LICENSE_ICON = "seicon-key";
var reloadPage = function() {
    var _location_get = reactorCmps_src_framework_components_mobile_Profile_LocationFacade__WEBPACK_IMPORTED_MODULE_11__["default"].get(), origin = _location_get.origin, pathname = _location_get.pathname;
    var mobile = Utils__WEBPACK_IMPORTED_MODULE_3___default().getURLParameter("mobile");
    var mobileParam = mobile ? "&mobile=".concat(mobile) : "";
    reactorCmps_src_framework_components_mobile_Profile_LocationFacade__WEBPACK_IMPORTED_MODULE_11__["default"].set("".concat(origin).concat(pathname, "?refreshInfo=true").concat(mobileParam));
};
var LicenseSelector = function() {
    var getToken = (0,reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_5__["default"])();
    var _useState = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true), 2), loading = _useState[0], setLoading = _useState[1];
    var _useState1 = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]), 2), licenses = _useState1[0], setLicenses = _useState1[1];
    var _useState2 = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), 2), error = _useState2[0], setError = _useState2[1];
    var handleDropdownButtonClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {
        !licenses.length && (0,reactorCmps_src_workspace_components_Common_LicenseKeysAlert_LicenseKeysRequest__WEBPACK_IMPORTED_MODULE_10__.getLicenseKeys)().then(function(data) {
            setLicenses(data);
            setLoading(false);
        });
    }, [
        licenses
    ]);
    var handleLicenseClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(id) {
        setLoading(true);
        (0,reactorCmps_src_workspace_components_Common_LicenseKeysAlert_LicenseKeysRequest__WEBPACK_IMPORTED_MODULE_10__.changeLicense)(id).then(function() {
            Storage__WEBPACK_IMPORTED_MODULE_4___default().remove("pendencyProcessed");
            reloadPage();
        })["catch"](function(err) {
            setError(Utils__WEBPACK_IMPORTED_MODULE_3___default().decodeHtmlString(err.message));
            setLoading(false);
        });
    }, []);
    var memoizedContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {
        if (error) return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement("span", {
            "data-test-selector": "LicenseSelectorError"
        }, error);
        return licenses.map(function(param) {
            var id = param.id, checked = param.checked, text = param.text;
            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Atomic_components_Atoms_MenuItem_MenuItem__WEBPACK_IMPORTED_MODULE_7___default()), {
                eventKey: id,
                key: id,
                selected: checked,
                onClick: function() {
                    return !checked && handleLicenseClick(id);
                }
            }, checked && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Atomic_components_Atoms_Icon_Icon__WEBPACK_IMPORTED_MODULE_8___default()), {
                icon: LICENSE_ICON,
                style: selectedLicenseIconStyle
            }), text);
        });
    }, [
        error,
        licenses,
        handleLicenseClick
    ]);
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement("div", {
        style: licenseDropdownButtonStyle
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Atomic_components_Orgs_Dropdown_DropdownButton__WEBPACK_IMPORTED_MODULE_6___default()), {
        icon: LICENSE_ICON,
        title: getToken("213119").toUpperCase(),
        menuTitle: getToken("213119"),
        onClick: handleDropdownButtonClick,
        loading: loading,
        noCaret: true
    }, memoizedContent));
};
LicenseSelector.displayName = "framework/components/mobile/Profile/LicenseSelector";
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LicenseSelector);


}),
"./src/framework/components/mobile/Profile/LocationFacade.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* istanbul ignore file */ /* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    get: function() {
        return window.location;
    },
    set: function(url) {
        return window.location = url;
    }
});


}),
"./src/framework/components/mobile/Profile/ProfileView.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ "../node_modules/react-redux/es/index.js");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/useTokenManager */ "../reactor2/src/Atomic/components/Helpers/Language/useTokenManager.js");
/* ESM import */var reactor2_src_Mobile_components_Templates_Leaf_Leaf__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactor2/src/Mobile/components/Templates/Leaf/Leaf */ "../reactor2/src/Mobile/components/Templates/Leaf/Leaf.jsx");
/* ESM import */var reactor2_src_Mobile_components_Templates_Leaf_Leaf__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Mobile_components_Templates_Leaf_Leaf__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var reactor2_src_Mobile_components_Orgs_Header_MobileHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactor2/src/Mobile/components/Orgs/Header/MobileHeader */ "../reactor2/src/Mobile/components/Orgs/Header/MobileHeader.jsx");
/* ESM import */var reactor2_src_Mobile_components_Orgs_Header_MobileHeader__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Mobile_components_Orgs_Header_MobileHeader__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var reactor2_src_store_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactor2/src/store/store */ "../reactor2/src/store/store.js");
/* ESM import */var reactor2_src_store_store__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_store_store__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var reactor2_src_constants_sizeConstants__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! reactor2/src/constants/sizeConstants */ "../reactor2/src/constants/sizeConstants.js");
/* ESM import */var reactor2_src_Atomic_components_Mols_UserView_UserView__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactor2/src/Atomic/components/Mols/UserView/UserView */ "../reactor2/src/Atomic/components/Mols/UserView/UserView.js");
/* ESM import */var reactor2_src_Atomic_components_Mols_UserView_UserView__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Mols_UserView_UserView__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Button/Button */ "../reactor2/src/Atomic/components/Atoms/Button/Button.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_11__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Link/Link */ "../reactor2/src/Atomic/components/Atoms/Link/Link.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactor2/src/Styles/tokens/styleVariables.ecss */ "../reactor2/src/Styles/tokens/styleVariables.ecss");
/* ESM import */var reactorCmps_src_framework_components_mobile_Profile_LicenseSelector__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactorCmps/src/framework/components/mobile/Profile/LicenseSelector */ "./src/framework/components/mobile/Profile/LicenseSelector.jsx");
/* ESM import */var reactorCmps_src_framework_components_mobile_Profile_ProfileView_scss__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactorCmps/src/framework/components/mobile/Profile/ProfileView.scss */ "./src/framework/components/mobile/Profile/ProfileView.scss");
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
















var headerStyle = {
    width: "100%"
};
var containerStyle = {
    position: "relative",
    height: "100%",
    background: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.colorsSolidBlue7,
    color: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.colorsSolidGray1,
    overflowY: "auto"
};
var contentWrapperStyle = {
    position: "absolute",
    top: 0,
    width: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center"
};
var backgroundImageStyle = {
    width: "100%",
    height: "100%",
    backgroundSize: "100% 100%",
    filter: "blur(15px)",
    opacity: ".2"
};
var profilePictureWrapperStyle = {
    marginTop: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.spacingSpacing6,
    marginBottom: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.spacingSpacing4
};
var commomTextStyle = {
    lineHeight: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.fontLineHeightReset,
    letterSpacing: ".02em",
    marginBottom: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.spacingSpacing2
};
var userNameStyle = _object_spread_props(_object_spread({}, commomTextStyle), {
    fontSize: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.fontFontXL
});
var departmentStyle = _object_spread_props(_object_spread({}, commomTextStyle), {
    fontSize: 18,
    marginBottom: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.spacingSpacing8
});
var docLinkStyle = {
    color: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.colorsWhite,
    fontSize: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.fontFontSM,
    marginTop: reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.spacingSpacing8
};
var LOGOFF_ICON = "seicon-mobile-logoff";
var LOGOFF_ICON_COLOR = reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.colorsSolidRed;
var DOCS_ICON = "seicon-help-circle-alt";
var DOWNLOAD_LINK = "https://documents.softexpert.com/{0}/mobile/mo001";
var DOCS_LANGUAGE_KEYS = {
    "pt-br": "pt_BR",
    es: "es_ES",
    "default": "en_US"
};
var getDocDownloadLink = function() {
    var key = DOCS_LANGUAGE_KEYS[WorkspaceInfo__WEBPACK_IMPORTED_MODULE_4___default().getDefaultLanguage()] || DOCS_LANGUAGE_KEYS["default"];
    return DOWNLOAD_LINK.seformat(key);
};
var openDocDownloadLink = function(event) {
    return Utils__WEBPACK_IMPORTED_MODULE_3___default().sendBlankLinkEventToWebview(event.target.href, event);
};
var ProfileView = function() {
    var getToken = (0,reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_5__["default"])();
    var _useState = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), leaving = _useState[0], setLeaving = _useState[1];
    var handleCloseProfileLeaf = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {
        curl("Mobile/MobileUIApp", function(MobileUIApp) {
            MobileUIApp.destroyCurrentLeaf();
        });
    }, []);
    var handleLogoff = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {
        curl("SessionManager", function(SessionManager) {
            Utils__WEBPACK_IMPORTED_MODULE_3___default().amplitudeTrack("MOB - Fez logout");
            SessionManager.logout(undefined, undefined, false, undefined, "mobile-profile-logout");
        });
        setLeaving(true);
    }, []);
    var memoizedBackground = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {
        var styleWithImage = _object_spread_props(_object_spread({}, backgroundImageStyle), {
            backgroundImage: "url(".concat(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_4___default().getProfilePicture(), ")")
        });
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
            style: styleWithImage,
            "data-test-selector": "ProfileViewBackground"
        });
    }, []);
    var memoizedProfilePicture = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
            style: profilePictureWrapperStyle
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(react_redux__WEBPACK_IMPORTED_MODULE_2__.Provider, {
            store: (reactor2_src_store_store__WEBPACK_IMPORTED_MODULE_8___default())
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Mols_UserView_UserView__WEBPACK_IMPORTED_MODULE_10___default()), {
            details: false,
            cdUser: WorkspaceInfo__WEBPACK_IMPORTED_MODULE_4___default().getCDUser(),
            hideText: true,
            size: reactor2_src_constants_sizeConstants__WEBPACK_IMPORTED_MODULE_9__.SIZE_BIG
        })));
    }, []);
    var memoizedLogoffButton = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_11___default()), {
            icon: LOGOFF_ICON,
            iconColor: LOGOFF_ICON_COLOR,
            loading: leaving,
            onClick: handleLogoff
        }, getToken("100004"));
    }, [
        getToken,
        leaving,
        handleLogoff
    ]);
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Mobile_components_Templates_Leaf_Leaf__WEBPACK_IMPORTED_MODULE_6___default()), null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Mobile_components_Orgs_Header_MobileHeader__WEBPACK_IMPORTED_MODULE_7___default()), {
        hasBackBtn: true,
        fnBack: handleCloseProfileLeaf
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
        style: headerStyle
    })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
        style: containerStyle
    }, memoizedBackground, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
        style: contentWrapperStyle,
        className: "profileContentWrapper"
    }, memoizedProfilePicture, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("h5", {
        style: userNameStyle
    }, WorkspaceInfo__WEBPACK_IMPORTED_MODULE_4___default().getUserName()), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("h5", {
        style: departmentStyle
    }, WorkspaceInfo__WEBPACK_IMPORTED_MODULE_4___default().getDepartment()), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(reactorCmps_src_framework_components_mobile_Profile_LicenseSelector__WEBPACK_IMPORTED_MODULE_14__["default"], null), memoizedLogoffButton, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_12___default()), {
        style: docLinkStyle,
        icon: DOCS_ICON,
        href: getDocDownloadLink(),
        onClick: openDocDownloadLink
    }, getToken("109009")))));
};
ProfileView.displayName = "framework/components/mobile/Profile/ProfileView";
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfileView);


}),
"./src/workspace/components/Common/LicenseKeysAlert/LicenseKeysRequest.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  changeLicense: function() { return changeLicense; },
  getLicenseKeys: function() { return getLicenseKeys; }
});
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! Connector */ "Connector");
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(Connector__WEBPACK_IMPORTED_MODULE_1__);


function getLicenseKeys() {
    return new Promise(function(resolve, reject) {
        Connector__WEBPACK_IMPORTED_MODULE_1___default().callLogic('COLicenseKeyLogic/getMenuChangeKeyLogin', undefined, {
            success: function(response) {
                /* istanbul ignore else */ if (response.success) {
                    resolve(response.results);
                }
                reject();
            }
        });
    });
}
function changeLicense(key, logout) {
    return new Promise(function(resolve, reject) {
        Connector__WEBPACK_IMPORTED_MODULE_1___default().callLogic("COLicenseKeyLogic/changeLicense", {
            json: '[{"outherLicenseKey":"' + key + '"}, {"logout":"' + logout + '"}]'
        }, {
            success: function success(res) {
                if (res.success === true) {
                    resolve();
                } else {
                    // Troca para licenca NAMED_USER com usuario logado abre confirmacao para logout
                    if (res.termCode === 301135) {
                        reject({
                            message: res.message,
                            key: key
                        });
                    } else {
                        reject({
                            message: res.message
                        });
                    }
                }
            }
        });
    });
}


}),
"./src/framework/components/mobile/Profile/ProfileView.scss": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js */ "../node_modules/style-loader/dist/runtime/styleDomAPI.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/insertBySelector.js */ "../node_modules/style-loader/dist/runtime/insertBySelector.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js */ "../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js */ "../node_modules/style-loader/dist/runtime/insertStyleElement.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js */ "../node_modules/style-loader/dist/runtime/styleTagTransform.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_3_use_1_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_3_use_2_ProfileView_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! !!../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[2]!./ProfileView.scss */ "../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[2]!./src/framework/components/mobile/Profile/ProfileView.scss");

      
      
      
      
      
      
      
      
      

var options = {};

options.styleTagTransform = (_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default());
options.setAttributes = (_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default());
options.insert = _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default().bind(null, "head");
options.domAPI = (_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default());
options.insertStyleElement = (_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default());

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_3_use_1_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_3_use_2_ProfileView_scss__WEBPACK_IMPORTED_MODULE_6__["default"], options);




       /* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_3_use_1_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_3_use_2_ProfileView_scss__WEBPACK_IMPORTED_MODULE_6__["default"] && _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_3_use_1_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_3_use_2_ProfileView_scss__WEBPACK_IMPORTED_MODULE_6__["default"].locals ? _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_3_use_1_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_3_use_2_ProfileView_scss__WEBPACK_IMPORTED_MODULE_6__["default"].locals : undefined);


}),
"Connector": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"SG2/collection/Factory": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_SG2_collection_Factory__;

}),
"Storage": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Storage__;

}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"WorkspaceInfo": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_WorkspaceInfo__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"when": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_when__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/components/mobile/Profile/ProfileView.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=ProfileView.js.map