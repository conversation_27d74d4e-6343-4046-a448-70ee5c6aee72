{"version": 3, "file": "framework/OldRichText.js", "sources": ["webpack://watch1749037385376/./src/framework/components/OldRichText.jsx"], "sourcesContent": ["var PropTypes = require('prop-types');\nvar React = require(\"react\");\nvar createReactClass = require('create-react-class');\nvar when = require('when');\nvar RichText = require(\"reactor/src/Form/components/Atoms/RichText\");\nvar AsyncLoading = require(\"reactor/src/Atomic/components/Helpers/AsyncLoading/AsyncLoading\");\nvar reactRedux = require(\"react-redux\");\nvar Provider = reactRedux.Provider;\nvar easyGrid = require(\"reactor/src/FlexGrid/components/Helpers/EasyGrid\");\nvar Connector = require(\"Connector\");\n\nvar easy = easyGrid({\n\tgridName: \"RichText\"\n});\n\nvar containerStyle = {\n\theight: \"100%\"\n};\n\nrequire(\"reactorCmps/tokens/general\");\n\nmodule.exports = createReactClass({\n\tpropTypes: {\n\t\toid: PropTypes.string.isRequired,\n\t\tgetFromOid: PropTypes.string,\n\t\theight: PropTypes.string\n\t},\n\n\tgetInitialState() {\n\t\tvar hasOid = Boolean(this.props.oid || this.props.getFromOid);\n\n\t\treturn {\n\t\t\tcontentLoaded: !hasOid\n\t\t};\n\t},\n\n\tcomponentDidMount() {\n\t\tvar me = this;\n\n\t\tme.refs.async.getWrappedInstance().loadData(me.props.forceLoadData);\n\n\t\tif (this.state.contentLoaded) {\n\t\t\tthis.handleContentLoad();\n\t\t}\n\t},\n\n\tcomponentDidUpdate(_prevProps, prevState) {\n\t\tif (!prevState.contentLoaded && this.state.contentLoaded) {\n\t\t\tthis.handleContentLoad();\n\t\t}\n\t},\n\n\thandleContentLoad() {\n\t\tthis.props.onContentLoad && this.props.onContentLoad();\n\t},\n\n\tgetChildren(asyncData) {\n\t\tvar me = this, html = \"\";\n\n\t\tif (asyncData.results && asyncData.results[0]) {\n\t\t\thtml = asyncData.results[0].html;\n\t\t}\n\n\t\treturn (\n\t\t\t<RichText\n\t\t\t\t{...me.props}>\n\t\t\t\t{html}\n\t\t\t</RichText>\n\t\t);\n\t},\n\n\tretrieveData() {\n\t\tvar me = this,\n\t\t\tprops = me.props,\n\t\t\tmethod = \"find\",\n\t\t\toid = props.oid;\n\n\t\tif (props.getFromOid) {\n\t\t\tmethod = \"copyInMemory\";\n\t\t\toid = props.getFromOid;\n\t\t}\n\n\t\tif (!oid) {\n\t\t\treturn when.resolve('');\n\t\t}\n\n\t\treturn Connector.callLogic2(\"RichTextResourceLogic/\" + method, {\n\t\t\tjson: '[{\"String\": \"' + oid + '\"}]'\n\t\t}, {\n\t\t\tsuccess: me.handleRetrievedData\n\t\t});\n\t},\n\n\thandleRetrievedData: function(data) {\n\t\tvar me = this, props = me.props;\n\n\t\tif (data.results[0]) {\n\t\t\tme.setState({ contentLoaded: true });\n\t\t\tprops.onChange && props.onChange(data.results[0].html);\n\t\t}\n\t},\n\n\trender() {\n\t\tvar me = this;\n\n\t\treturn (\n\t\t\t<Provider store={easy.Store}>\n\t\t\t\t<AsyncLoading\n\t\t\t\t\tref=\"async\"\n\t\t\t\t\toid={\"richText\" + me.props.oid}\n\t\t\t\t\tfireLoad\n\t\t\t\t\tloadFn={me.retrieveData}\n\t\t\t\t\tgetChildren={me.getChildren}\n\t\t\t\t\tcontainerStyle={containerStyle}\n\t\t\t\t/>\n\t\t\t</Provider>\n\t\t);\n\t}\n});"], "names": ["PropTypes", "require", "React", "createReactClass", "when", "RichText", "AsyncLoading", "reactRedux", "Provider", "easyGrid", "Connector", "easy", "containerStyle", "module", "hasOid", "Boolean", "me", "_prevProps", "prevState", "asyncData", "html", "props", "method", "oid", "handleRetrievedData", "data"], "mappings": ";;;AAAA,IAAIA,YAAYC,mBAAOA,CAAC,uDAAY;AACpC,IAAIC,QAAQD,mBAAOA,CAAC,oBAAO;AAC3B,IAAIE,mBAAmBF,mBAAOA,CAAC,8CAAoB;AACnD,IAAIG,OAAOH,mBAAOA,CAAC,kBAAM;AACzB,IAAII,WAAWJ,mBAAOA,CAAC,qGAA4C;AACnE,IAAIK,eAAeL,mBAAOA,CAAC,+IAAiE;AAC5F,IAAIM,aAAaN,mBAAOA,CAAC,4DAAa;AACtC,IAAIO,WAAWD,WAAW,QAAQ;AAClC,IAAIE,WAAWR,mBAAOA,CAAC,gHAAkD;AACzE,IAAIS,YAAYT,mBAAOA,CAAC,4BAAW;AAEnC,IAAIU,OAAOF,SAAS;IACnB,UAAU;AACX;AAEA,IAAIG,iBAAiB;IACpB,QAAQ;AACT;AAEAX,mBAAOA,CAAC,8DAA4B;AAEpCY,cAAc,GAAGV,iBAAiB;IACjC,WAAW;QACV,KAAKH,UAAU,MAAM,CAAC,UAAU;QAChC,YAAYA,UAAU,MAAM;QAC5B,QAAQA,UAAU,MAAM;IACzB;IAEA;QACC,IAAIc,SAASC,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU;QAE5D,OAAO;YACN,eAAe,CAACD;QACjB;IACD;IAEA;QACC,IAAIE,KAAK,IAAI;QAEbA,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,QAAQ,CAACA,GAAG,KAAK,CAAC,aAAa;QAElE,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;YAC7B,IAAI,CAAC,iBAAiB;QACvB;IACD;IAEA,6BAAmBC,UAAU,EAAEC,SAAS;QACvC,IAAI,CAACA,UAAU,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;YACzD,IAAI,CAAC,iBAAiB;QACvB;IACD;IAEA;QACC,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa;IACrD;IAEA,sBAAYC,SAAS;QACpB,IAAIH,KAAK,IAAI,EAAEI,OAAO;QAEtB,IAAID,UAAU,OAAO,IAAIA,UAAU,OAAO,CAAC,EAAE,EAAE;YAC9CC,OAAOD,UAAU,OAAO,CAAC,EAAE,CAAC,IAAI;QACjC;QAEA,qBACC,oBAACd,UACIW,GAAG,KAAK,EACXI;IAGJ;IAEA;QACC,IAAIJ,KAAK,IAAI,EACZK,QAAQL,GAAG,KAAK,EAChBM,SAAS,QACTC,MAAMF,MAAM,GAAG;QAEhB,IAAIA,MAAM,UAAU,EAAE;YACrBC,SAAS;YACTC,MAAMF,MAAM,UAAU;QACvB;QAEA,IAAI,CAACE,KAAK;YACT,OAAOnB,KAAK,OAAO,CAAC;QACrB;QAEA,OAAOM,UAAU,UAAU,CAAC,2BAA2BY,QAAQ;YAC9D,MAAM,kBAAkBC,MAAM;QAC/B,GAAG;YACF,SAASP,GAAG,mBAAmB;QAChC;IACD;IAEAQ,qBAAqB,SAArBA,oBAA8BC,IAAI;QACjC,IAAIT,KAAK,IAAI,EAAEK,QAAQL,GAAG,KAAK;QAE/B,IAAIS,KAAK,OAAO,CAAC,EAAE,EAAE;YACpBT,GAAG,QAAQ,CAAC;gBAAE,eAAe;YAAK;YAClCK,MAAM,QAAQ,IAAIA,MAAM,QAAQ,CAACI,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI;QACtD;IACD;IAEA;QACC,IAAIT,KAAK,IAAI;QAEb,qBACC,oBAACR;YAAS,OAAOG,KAAK,KAAK;yBAC1B,oBAACL;YACA,KAAI;YACJ,KAAK,aAAaU,GAAG,KAAK,CAAC,GAAG;YAC9B;YACA,QAAQA,GAAG,YAAY;YACvB,aAAaA,GAAG,WAAW;YAC3B,gBAAgBJ;;IAIpB;iBAhGM;AAiGP"}