define(["react","react-dom","jquery","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","async!ExtensionLoader","tokens!reactorCmps/tokens/general","suite-storage","when"], function(__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_jquery__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_extensions__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_suite_storage__, __WEBPACK_EXTERNAL_MODULE_when__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/ReactLoader"], {
"./dist/TokenProductMapping.js": (function (module) {
module.exports = {
    "apqp/ControlPlan": 11,
    "document/RelationshipView": 21,
    "fmea/FmeaStructure": 8
};


}),
"./src/extension/sdk/ExtensionBootstrap.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  initialize: function() { return initialize; }
});
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/web.timers.js */ "../node_modules/core-js/modules/web.timers.js");
/* ESM import */var core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_promise_finally_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.promise.finally.js */ "../node_modules/core-js/modules/es.promise.finally.js");
/* ESM import */var core_js_modules_es_promise_finally_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_promise_finally_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.map.js */ "../node_modules/core-js/modules/es.map.js");
/* ESM import */var core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_map_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ "../node_modules/core-js/modules/es.array.iterator.js");
/* ESM import */var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ "../node_modules/core-js/modules/web.dom-collections.iterator.js");
/* ESM import */var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ "../node_modules/core-js/modules/es.string.iterator.js");
/* ESM import */var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var core_js_modules_es_array_is_array_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.array.is-array.js */ "../node_modules/core-js/modules/es.array.is-array.js");
/* ESM import */var core_js_modules_es_array_is_array_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_is_array_js__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var core_js_modules_es_array_for_each_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.array.for-each.js */ "../node_modules/core-js/modules/es.array.for-each.js");
/* ESM import */var core_js_modules_es_array_for_each_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_for_each_js__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ "../node_modules/core-js/modules/es.symbol.js");
/* ESM import */var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ "../node_modules/core-js/modules/es.symbol.description.js");
/* ESM import */var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_11__);
/* ESM import */var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.symbol.iterator.js */ "../node_modules/core-js/modules/es.symbol.iterator.js");
/* ESM import */var core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_iterator_js__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_13__);
/* ESM import */var core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! core-js/modules/es.object.assign.js */ "../node_modules/core-js/modules/es.object.assign.js");
/* ESM import */var core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_14__);
/* ESM import */var core_js_modules_es_object_create_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! core-js/modules/es.object.create.js */ "../node_modules/core-js/modules/es.object.create.js");
/* ESM import */var core_js_modules_es_object_create_js__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_create_js__WEBPACK_IMPORTED_MODULE_15__);
/* ESM import */var core_js_modules_es_object_from_entries_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! core-js/modules/es.object.from-entries.js */ "../node_modules/core-js/modules/es.object.from-entries.js");
/* ESM import */var core_js_modules_es_object_from_entries_js__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_from_entries_js__WEBPACK_IMPORTED_MODULE_16__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_17__);
/* ESM import */var reactorCmps_src_extension_sdk_ExtensionContext__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! reactorCmps/src/extension/sdk/ExtensionContext */ "./src/extension/sdk/ExtensionContext.js");
/* ESM import */var reactorCmps_src_extension_sdk_ExtensionErrors__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! reactorCmps/src/extension/sdk/ExtensionErrors */ "./src/extension/sdk/ExtensionErrors.js");
/* ESM import */var extensions__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! extensions */ "extensions");
/* ESM import */var extensions__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(extensions__WEBPACK_IMPORTED_MODULE_20__);
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
    try {
        var info = gen[key](arg);
        var value = info.value;
    } catch (error) {
        reject(error);
        return;
    }
    if (info.done) {
        resolve(value);
    } else {
        Promise.resolve(value).then(_next, _throw);
    }
}
function _async_to_generator(fn) {
    return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
            var gen = fn.apply(self, args);
            function _next(value) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
            }
            function _throw(err) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
            }
            _next(undefined);
        });
    };
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _ts_generator(thisArg, body) {
    var f, y, t, g, _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    };
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}





















/* istanbul ignore file */ var isExtensionActive = function(props, extension) {
    return new Promise(function(resolve, reject) {
        var currentTarget = Promise.resolve();
        if (extension.target.custom) {
            currentTarget = Promise.resolve(extension.target.custom(props));
        }
        var timer = setTimeout(function() {
            return reject((0,reactorCmps_src_extension_sdk_ExtensionErrors__WEBPACK_IMPORTED_MODULE_19__.BootstrapLoadTimeout)(extension));
        }, 5000);
        currentTarget.then(resolve)["catch"](reject)["finally"](function() {
            return clearTimeout(timer);
        });
    });
};
var initialize = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function(EntryComponent) {
        var props, extensions, activeExtensions, loadedExtensions, e;
        var _arguments = arguments;
        return _ts_generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    props = _arguments.length > 1 && _arguments[1] !== void 0 ? _arguments[1] : {};
                    extensions = new Map();
                    _state.label = 1;
                case 1:
                    _state.trys.push([
                        1,
                        4,
                        ,
                        5
                    ]);
                    if (!Array.isArray((extensions__WEBPACK_IMPORTED_MODULE_20___default()))) throw (0,reactorCmps_src_extension_sdk_ExtensionErrors__WEBPACK_IMPORTED_MODULE_19__.BootstrapUnsuportedEntryType)((extensions__WEBPACK_IMPORTED_MODULE_20___default()));
                    return [
                        4,
                        Promise.all(extensions__WEBPACK_IMPORTED_MODULE_20___default().map(function(extension) {
                            return isExtensionActive(props, extension);
                        }))
                    ];
                case 2:
                    activeExtensions = _state.sent();
                    return [
                        4,
                        Promise.all(activeExtensions.map(function(shouldLoad, index) {
                            return shouldLoad ? (extensions__WEBPACK_IMPORTED_MODULE_20___default())[index].load() : Promise.resolve(null);
                        }))
                    ];
                case 3:
                    loadedExtensions = _state.sent();
                    loadedExtensions.forEach(function(loaded, index) {
                        if (loaded) {
                            var extensor = loaded["default"] || loaded;
                            var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;
                            try {
                                for(var _iterator = (extensions__WEBPACK_IMPORTED_MODULE_20___default())[index].extensionPoints[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){
                                    var point = _step.value;
                                    if (extensions.has(point.name)) {
                                        extensions.get(point.name).push(extensor[point.name]);
                                    } else {
                                        extensions.set(point.name, [
                                            extensor[point.name]
                                        ]);
                                    }
                                }
                            } catch (err) {
                                _didIteratorError = true;
                                _iteratorError = err;
                            } finally{
                                try {
                                    if (!_iteratorNormalCompletion && _iterator["return"] != null) {
                                        _iterator["return"]();
                                    }
                                } finally{
                                    if (_didIteratorError) {
                                        throw _iteratorError;
                                    }
                                }
                            }
                        }
                    });
                    if (extensions.size) {
                        return [
                            2,
                            Object.assign(Object.create(EntryComponent), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_17___default().forwardRef(function DecoratedWithExtensionProvider(_props, ref) {
                                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_17___default().createElement(reactorCmps_src_extension_sdk_ExtensionContext__WEBPACK_IMPORTED_MODULE_18__["default"].Provider, {
                                    value: Object.fromEntries(extensions)
                                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_17___default().createElement(EntryComponent, _object_spread_props(_object_spread({}, _props), {
                                    ref: ref
                                })));
                            }))
                        ];
                    }
                    return [
                        3,
                        5
                    ];
                case 4:
                    e = _state.sent();
                    /* eslint-disable */ console.error(e);
                    return [
                        3,
                        5
                    ];
                case 5:
                    return [
                        2,
                        EntryComponent
                    ];
            }
        });
    });
    return function initialize(EntryComponent) {
        return _ref.apply(this, arguments);
    };
}();


}),
"./src/extension/sdk/ExtensionContext.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext("NjJyZWFjdG9yQ21wcy9zcmMvZXh0ZW5zaW9uL3Nkay9FeHRlbnNpb25Db250ZXh0Lmpze30=", {}));


}),
"./src/extension/sdk/ExtensionErrors.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  BootstrapLoadTimeout: function() { return BootstrapLoadTimeout; },
  BootstrapUnsuportedEntryType: function() { return BootstrapUnsuportedEntryType; },
  ExtensionNameNotDefined: function() { return ExtensionNameNotDefined; },
  ExtensionPointUnusedExports: function() { return ExtensionPointUnusedExports; },
  PointNameConflict: function() { return PointNameConflict; },
  PointOverrideIncompatible: function() { return PointOverrideIncompatible; },
  PointOverrideNotDefinedProperty: function() { return PointOverrideNotDefinedProperty; },
  RegisterComponentNotDefined: function() { return RegisterComponentNotDefined; },
  RegisterNotDefinedInOverride: function() { return RegisterNotDefinedInOverride; },
  RegisterOverrideNotDefined: function() { return RegisterOverrideNotDefined; },
  RegisterOverridesNotDefined: function() { return RegisterOverridesNotDefined; },
  RegisterPointNotDefined: function() { return RegisterPointNotDefined; }
});
/* ESM import */var core_js_modules_es_error_cause_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.error.cause.js */ "../node_modules/core-js/modules/es.error.cause.js");
/* ESM import */var core_js_modules_es_error_cause_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_error_cause_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.error.to-string.js */ "../node_modules/core-js/modules/es.error.to-string.js");
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.join.js */ "../node_modules/core-js/modules/es.array.join.js");
/* ESM import */var core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_join_js__WEBPACK_IMPORTED_MODULE_5__);
/* istanbul ignore file */ /* eslint-disable */ function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}






var PointNameConflict = function(point) {
    return new Error("Existe um conflito de 'name' no ponto '".concat(point.name, "' verifique as defini\xe7\xf5es em reactorCmps/src/extension/points"));
};
var PointOverrideNotDefinedProperty = function(point, property) {
    return new Error("Voc\xea est\xe1 retornando a propriedade '".concat(property, "' n\xe3o definida no 'overrides' do ponto de extens\xe3o '").concat(point.name, "'. Defina essa propriedade no 'overrides' usando a documenta\xe7\xe3o: /framework-docs/Reactor/reactor-sdk"));
};
var PointOverrideIncompatible = function(point, overrideName, invalidMsg) {
    return new Error("".concat(invalidMsg, ": A valida\xe7\xe3o da propriedade '").concat(overrideName, "' do ponto de extens\xe3o '").concat(point.name, "' falhou! Verifique se sua implementa\xe7\xe3o est\xe1 de acordo com o ponto em 'reactorCmps/src/extension/points'"));
};
var ExtensionNameNotDefined = function() {
    return new Error("A propriedade 'name' \xe9 obrigat\xf3ria na defini\xe7\xe3o da customiza\xe7\xe3o. Verifique o arquivo 'reactorCmps/custom_srv/entry.js'");
};
var ExtensionPointUnusedExports = function(entry, exportedPointName) {
    return new Error("O export '".concat(exportedPointName, "' n\xe3o possue um ponto de extens\xe3o definido no 'custom_srv/entry.js'. Cheque o arquivo definido na propriedade 'load' do 'custom_srv/entry.js'. Os exports possiveis s\xe3o: '").concat(entry.extensionPoints.map(function(point) {
        return point.name;
    }).join("','"), "'"));
};
var BootstrapLoadTimeout = function(extension) {
    return "A customiza\xe7\xe3o '".concat(extension.name, "' n\xe3o foi carregada por demorar mais de 5s para inicializar! Verifique o arquivo entry.js da pasta reactorCmps/custom_srv.");
};
var BootstrapUnsuportedEntryType = function(entries) {
    return new Error("O export default do arquivo entry.js da pasta reactorCmps/custom_srv precisa ser um Array e foi recebido um: ".concat(typeof entries === "undefined" ? "undefined" : _type_of(entries)));
};
var RegisterPointNotDefined = function() {
    return new Error("O primeiro argumento do 'register' é obrigatorio e deve conter a definição do ponto de extensão importada de 'reactorCmps/src/extension/points'");
};
var RegisterOverrideNotDefined = function(point) {
    return new Error("O ponto de extens\xe3o '".concat(point.name, "' definido em 'reactorCmps/src/extension/points' deve definir um 'overrides', veja a documenta\xe7\xe3o: /framework-docs/Reactor/reactor-sdk"));
};
var RegisterComponentNotDefined = function() {
    return new Error("O argumento 'Component' do 'register' é obrigatorio e deve conter o componente que receberá as props customizadas!");
};
var RegisterOverridesNotDefined = function() {
    return new Error("A propriedade 'overrides' é obrigatoria e deve conter valores padrão para que o componente funcione sem customizações!");
};
var RegisterNotDefinedInOverride = function(point, property) {
    return new Error("A propriedade '".concat(property, "' n\xe3o existe no 'overrides' do ponto de extens\xe3o '").concat(point.name, "'. Defina o contrato corretamente seguindo a documenta\xe7\xe3o: /framework-docs/Reactor/reactor-sdk"));
};


}),
"./src/framework/helpers/ReactLoader.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  load: function() { return load; },
  unmount: function() { return unmount; },
  unmountAll: function() { return unmountAll; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/web.timers.js */ "../node_modules/core-js/modules/web.timers.js");
/* ESM import */var core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.slice.js */ "../node_modules/core-js/modules/es.array.slice.js");
/* ESM import */var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_array_find_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.find.js */ "../node_modules/core-js/modules/es.array.find.js");
/* ESM import */var core_js_modules_es_array_find_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_find_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_es_array_some_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.array.some.js */ "../node_modules/core-js/modules/es.array.some.js");
/* ESM import */var core_js_modules_es_array_some_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_some_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.object.assign.js */ "../node_modules/core-js/modules/es.object.assign.js");
/* ESM import */var core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_assign_js__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var react_dom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-dom */ "react-dom");
/* ESM import */var react_dom__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var jquery__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jquery */ "jquery");
/* ESM import */var jquery__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(jquery__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var when__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! when */ "when");
/* ESM import */var when__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(when__WEBPACK_IMPORTED_MODULE_11__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactor2/src/Styles/tokens/styleVariables.ecss */ "../reactor2/src/Styles/tokens/styleVariables.ecss");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_TokenContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/TokenContext */ "../reactor2/src/Atomic/components/Helpers/Language/TokenContext.js");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_TokenContext__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Helpers_Language_TokenContext__WEBPACK_IMPORTED_MODULE_14__);
/* ESM import */var reactorCmps_src_framework_helpers_TokenProductMapping__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactorCmps/src/framework/helpers/TokenProductMapping */ "./src/framework/helpers/TokenProductMapping.js");
/* ESM import */var reactorCmps_src_framework_helpers_TokenProductMapping__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_src_framework_helpers_TokenProductMapping__WEBPACK_IMPORTED_MODULE_15__);
/* ESM import */var reactorCmps_src_extension_sdk_ExtensionBootstrap__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! reactorCmps/src/extension/sdk/ExtensionBootstrap */ "./src/extension/sdk/ExtensionBootstrap.js");
/* ESM import */var reactor2_src_helpers_TestIdLoader_TestIdLoader__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! reactor2/src/helpers/_TestIdLoader/TestIdLoader */ "../reactor2/src/helpers/_TestIdLoader/TestIdLoader.js");
/* ESM import */var reactor2_src_helpers_DefaultFrameTestGen_DefaultFrameTestGen__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! reactor2/src/helpers/_DefaultFrameTestGen/DefaultFrameTestGen */ "../reactor2/src/helpers/_DefaultFrameTestGen/DefaultFrameTestGen.js");
/* ESM import */var reactor2_src_helpers_util__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! reactor2/src/helpers/util */ "../reactor2/src/helpers/util.js");
/* ESM import */var reactor2_src_helpers_util__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_helpers_util__WEBPACK_IMPORTED_MODULE_19__);




















var REACTOR_CMPS_PATH = "reactorCmps/";
var ROOT_ATTRIBUTE = "data-reactroot";
var ROOT_ATTRIBUTE_SELECTOR = "[".concat(ROOT_ATTRIBUTE, "]");
var TASKCENTER_ATTRIBUTE = "data-taskcenter";
var TASK_CENTER_ENTRY = "workspace/TaskCenter";
var resolve = function(deferred, instance, immediateResolve, target) {
    var domInstance = (0,react_dom__WEBPACK_IMPORTED_MODULE_9__.findDOMNode)(instance);
    if (!domInstance && !immediateResolve) {
        return setTimeout(resolve.bind(null, deferred, instance, immediateResolve, target), 50);
    }
    target.setAttribute(ROOT_ATTRIBUTE, true);
    return deferred.resolve(instance);
};
var unmount = function(target) {
    if (!target) return false;
    if (target.hasAttribute(ROOT_ATTRIBUTE)) {
        target.removeAttribute(ROOT_ATTRIBUTE);
        if (target.hasAttribute(TASKCENTER_ATTRIBUTE)) {
            target.removeAttribute(TASKCENTER_ATTRIBUTE);
        }
        return (0,react_dom__WEBPACK_IMPORTED_MODULE_9__.unmountComponentAtNode)(target);
    }
    return false;
};
var unmountAll = function(target) {
    var roots = [].slice.call(jquery__WEBPACK_IMPORTED_MODULE_10___default()(target).find(ROOT_ATTRIBUTE_SELECTOR)).concat(target);
    return roots.some(function(current) {
        return unmount(current);
    });
};
var createElementWithInstance = function(rctEntry, props) {
    var componentType = rctEntry.component || rctEntry.reactCmp || rctEntry;
    var defaultProps = Object.assign({}, props);
    var instance, immediate = true;
    if (componentType.prototype && componentType.prototype.isReactComponent) {
        defaultProps.ref = function(ref) {
            return instance = ref;
        };
        immediate = false;
    }
    return {
        immediate: immediate,
        element: /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_8__.createElement)(componentType, defaultProps),
        getInstance: function() {
            return instance;
        }
    };
};
(0,reactor2_src_helpers_TestIdLoader_TestIdLoader__WEBPACK_IMPORTED_MODULE_17__.setupTestIdLoader)();
(0,reactor2_src_helpers_DefaultFrameTestGen_DefaultFrameTestGen__WEBPACK_IMPORTED_MODULE_18__.setupDefaultFrameTest)();
var load = function(entry, target, props, immediateResolve) {
    var deferred = when__WEBPACK_IMPORTED_MODULE_11___default().defer();
    if (reactor2_src_helpers_util__WEBPACK_IMPORTED_MODULE_19___default().isTaskCenter()) {
        document.body.setAttribute(TASKCENTER_ATTRIBUTE, "true");
    } else {
        document.body.removeAttribute(TASKCENTER_ATTRIBUTE);
    }
    if (entry === TASK_CENTER_ENTRY) {
        target.setAttribute(TASKCENTER_ATTRIBUTE, true);
    }
    unmount(target);
    curl(REACTOR_CMPS_PATH + entry, function(rctEntry) {
        var deferredModule = when__WEBPACK_IMPORTED_MODULE_11___default().defer();
        /* istanbul ignore next */ if (!target) {
            return;
        } else if (reactor2_src_helpers_util__WEBPACK_IMPORTED_MODULE_19___default().isTaskCenter()) {
            __webpack_require__.e(/*! import() */ "vendors-reactor2_src_Styles_fonts_newFontFamily_mcss").then(__webpack_require__.t.bind(__webpack_require__, /*! reactor2/src/Styles/fonts/newFontFamily.mcss */ "../reactor2/src/Styles/fonts/newFontFamily.mcss", 23)).then(function(module) {
                deferredModule.resolve(module);
            });
            target.style.fontFamily = "Roboto";
        } else if (!Utils__WEBPACK_IMPORTED_MODULE_12___default().getIsMobile()) {
            target.style.fontFamily = reactor2_src_Styles_tokens_styleVariables_ecss__WEBPACK_IMPORTED_MODULE_13__.fontFontFamily;
        }
        (0,reactorCmps_src_extension_sdk_ExtensionBootstrap__WEBPACK_IMPORTED_MODULE_16__.initialize)(rctEntry, props).then(function(decorated) {
            var _createElementWithInstance = createElementWithInstance(decorated, props), element = _createElementWithInstance.element, getInstance = _createElementWithInstance.getInstance, immediate = _createElementWithInstance.immediate;
            var cdproduct = props && props.cdProduct || (reactorCmps_src_framework_helpers_TokenProductMapping__WEBPACK_IMPORTED_MODULE_15___default())[entry];
            if (cdproduct !== undefined) {
                element = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_8__.createElement)((reactor2_src_Atomic_components_Helpers_Language_TokenContext__WEBPACK_IMPORTED_MODULE_14___default().Provider), {
                    value: cdproduct
                }, element);
            }
            (0,react_dom__WEBPACK_IMPORTED_MODULE_9__.render)(element, target, function() {
                return resolve(deferred, getInstance(), immediateResolve !== undefined ? immediateResolve : immediate, target);
            });
        });
    }, function() {
        deferred.reject();
    });
    return deferred.promise;
};


}),
"./src/framework/helpers/TokenProductMapping.js": (function (module, __unused_webpack_exports, __webpack_require__) {
module.exports = __webpack_require__(/*! reactorCmps/dist/TokenProductMapping */ "./dist/TokenProductMapping.js");


}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"jquery": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_jquery__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"when": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_when__;

}),
"extensions": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_extensions__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/helpers/ReactLoader.js"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=ReactLoader.js.map