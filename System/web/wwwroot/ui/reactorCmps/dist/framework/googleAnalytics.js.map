{"version": 3, "file": "framework/googleAnalytics.js", "sources": ["webpack://watch1749037385376/../reactor2/src/helpers/googleAnalytics.js", "webpack://watch1749037385376/./src/framework/libs/googleAnalytics.js"], "sourcesContent": ["module.exports = {\n\n\tsetPage: function(shortcut, term) {\n\t\tif (!window.ga) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// TODO pegar um termo em inglês para passar para aqui\n\t\tif (shortcut && term && typeof(term) === 'number') {\n\t\t\tga('set', {\n\t\t\t\ttitle: shortcut.toUpperCase() + ':' + SE.t(term)\n\t\t\t});\n\t\t}\n\n\t\tga('send', 'pageview');\n\n\t\treturn true;\n\t},\n\n\tcreate: function(code, shortcut, term) {\n\t\tif (window.ga) {\n\t\t\tthis.setPage(shortcut, term);\n\t\t\treturn true;\n\t\t} else if (!code) {\n\t\t\treturn false;\n\t\t}\n\n\n\t\t// Can't lintzar this line\n\t\t/* eslint-disable */\n\t\t(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){\n\t\t\t(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),\n\t\t\tm=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)\n\t\t})(window,document,'script','https://www.google-analytics.com/analytics.js','ga');\n\t\t/* eslint-enable */\n\n\t\tga('create', code, 'auto');\n\t\tthis.setPage(shortcut, term);\n\n\t\treturn true;\n\t},\n\n\tsendEvent: function(...args) {\n\t\tif (!window.ga) {\n\t\t\treturn false;\n\t\t}\n\t\tga('send', 'event', ...args);\n\n\t\treturn true;\n\t},\n\n\tisTracking: function() {\n\t\treturn window.ga !== undefined;\n\t},\n\n\tdestroy: function() {\n\t\twindow.ga = undefined;\n\n\t\treturn true;\n\t}\n};", "import googleAnalytics from 'reactor2/src/helpers/googleAnalytics.js';\n\nexport default googleAnalytics;"], "names": ["module", "setPage", "shortcut", "term", "window", "ga", "SE", "create", "code", "i", "s", "o", "g", "r", "a", "m", "arguments", "Date", "document", "sendEvent", "_key", "args", "isTracking", "undefined", "destroy", "googleAnalytics"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAA,mBAAAA,CAAAA,kGAAAA;AAAAA,mBAAAA,CAAAA,8FAAAA;AAAAA,cAAc,GAAG;IAEhBC,SAAS,SAATA,QAAkBC,QAAQ,EAAEC,IAAI;QAC/B,IAAI,CAACC,OAAO,EAAE,EAAE;YACf,OAAO;QACR;QAEA,sDAAsD;QACtD,IAAIF,YAAYC,QAAQ,OAAOA,SAAU,UAAU;YAClDE,GAAG,OAAO;gBACT,OAAOH,SAAS,WAAW,KAAK,MAAMI,GAAG,CAAC,CAACH;YAC5C;QACD;QAEAE,GAAG,QAAQ;QAEX,OAAO;IACR;IAEAE,QAAQ,SAARA,OAAiBC,IAAI,EAAEN,QAAQ,EAAEC,IAAI;QACpC,IAAIC,OAAO,EAAE,EAAE;YACd,IAAI,CAAC,OAAO,CAACF,UAAUC;YACvB,OAAO;QACR,OAAO,IAAI,CAACK,MAAM;YACjB,OAAO;QACR;QAGA,0BAA0B;QAC1B,kBAAkB,GACjB,UAASC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC;YAAEN,CAAC,CAAC,wBAAwB,GAACI;YAAEJ,CAAC,CAACI,EAAE,GAACJ,CAAC,CAACI,EAAE,IAAE;gBAC/DJ,CAAAA,CAAC,CAACI,EAAE,CAAC,CAAC,GAACJ,CAAC,CAACI,EAAE,CAAC,CAAC,IAAE,EAAC,EAAG,IAAI,CAACG;YAAU,GAAEP,CAAC,CAACI,EAAE,CAAC,CAAC,GAAC,IAAE,IAAII;YAAOH,IAAEJ,EAAE,aAAa,CAACC,IAC3EI,IAAEL,EAAE,oBAAoB,CAACC,EAAE,CAAC,EAAE;YAACG,EAAE,KAAK,GAAC;YAAEA,EAAE,GAAG,GAACF;YAAEG,EAAE,UAAU,CAAC,YAAY,CAACD,GAAEC;QAC9E,GAAGX,QAAOc,UAAS,UAAS,iDAAgD;QAC5E,iBAAiB,GAEjBb,GAAG,UAAUG,MAAM;QACnB,IAAI,CAAC,OAAO,CAACN,UAAUC;QAEvB,OAAO;IACR;IAEAgB,WAAW,SAAXA;QAAoBC,IAAAA,IAAAA,OAAAA,UAAAA,QAAGC,OAAHD,UAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA;YAAGC,KAAHD,QAAAA,SAAAA,CAAAA,KAAO;;QAC1B,IAAI,CAAChB,OAAO,EAAE,EAAE;YACf,OAAO;QACR;QACAC,SAAAA,KAAAA,GAAAA;YAAG;YAAQ;SAAiB,CAA5BA,OAAoB,qBAAGgB;QAEvB,OAAO;IACR;IAEAC,YAAY,SAAZA;QACC,OAAOlB,OAAO,EAAE,KAAKmB;IACtB;IAEAC,SAAS,SAATA;QACCpB,OAAO,EAAE,GAAGmB;QAEZ,OAAO;IACR;AACD;;;;;;;;;;;;AC5DsE;AAEtE,6DAAeE,gFAAeA,EAAC"}