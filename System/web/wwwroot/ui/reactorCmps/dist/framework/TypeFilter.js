define(["tokens!reactorCmps/tokens/general","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","create-react-class","Connector","suite-storage"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEBPACK_EXTERNAL_MODULE_suite_storage__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/TypeFilter"], {
"../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./src/framework/components/TypeFilter.css": (function (module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "../node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../node_modules/css-loader/dist/runtime/api.js */ "../node_modules/css-loader/dist/runtime/api.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, `.newTypeFilterExtCmp [role="presentation"] {
	overflow: visible;
}`, ""]);
// Exports
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);


}),
"./src/framework/components/TypeFilter.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.string.search.js */ "../node_modules/core-js/modules/es.string.search.js");
__webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var AsyncLoading = __webpack_require__(/*! reactor/src/Atomic/components/Helpers/AsyncLoading/AsyncLoading */ "../reactor/src/Atomic/components/Helpers/AsyncLoading/AsyncLoading.jsx");
var reactRedux = __webpack_require__(/*! react-redux */ "../node_modules/react-redux/es/index.js");
var Provider = reactRedux.Provider;
var easyGrid = __webpack_require__(/*! reactor/src/FlexGrid/components/Helpers/EasyGrid */ "../reactor/src/FlexGrid/components/Helpers/EasyGrid.js");
var Connector = __webpack_require__(/*! Connector */ "Connector");
var TempList = __webpack_require__(/*! ./TypeListTemp */ "./src/framework/components/TypeListTemp.jsx");
var easy = easyGrid({
    gridName: "typeFilter"
});
__webpack_require__(/*! ./TypeFilter.css */ "./src/framework/components/TypeFilter.css");
__webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");
module.exports = createReactClass({
    propTypes: {
        classes: PropTypes.object.isRequired,
        selected: PropTypes.string,
        currentClass: PropTypes.number,
        onRender: PropTypes.func,
        classLoaderListener: PropTypes.func,
        changeListener: PropTypes.func
    },
    getDefaultProps: function() {
        return {
            currentClass: 1
        };
    },
    getInitialState: function() {
        var me = this;
        return {
            activeClass: me.props.currentClass,
            isFirstEnter: true
        };
    },
    componentDidMount: function() {
        var me = this;
        me.refs.async.getWrappedInstance().loadData();
        if (me.props.classLoaderListener) {
            me.props.classLoaderListener(me.changeClassLoader);
        }
    },
    changeClassLoader: function(event) {
        var me = this;
        me.setState({
            activeClass: event.value
        });
        me.reload();
    },
    reload: function() {
        return this.refs.async.getWrappedInstance().loadData(true);
    },
    clear: function() {
        return this.refs.async.getWrappedInstance().refs.list.reset();
    },
    select: function(selectedType) {
        return this.refs.async.getWrappedInstance().refs.list.setSelected(selectedType);
    },
    onUpdate: function() {
        if (window.search) {
            window.search('full');
        }
    },
    onChangeType: function(type) {
        var me = this;
        me.setState({
            isFirstEnter: false
        });
        me.props.changeListener(type, me.state.currentClass);
    },
    getChildren: function(asyncData) {
        var me = this, isFirstEnter = me.state.isFirstEnter, selectedType = isFirstEnter ? me.props.selected : '';
        if (!asyncData.results) {
            return null;
        }
        return /*#__PURE__*/ React.createElement(TempList, {
            selected: selectedType,
            data: asyncData.results,
            ref: "list",
            onRender: me.props.onRender,
            onChange: me.onChangeType,
            onUpdate: me.onUpdate
        });
    },
    retrieveData: function() {
        var activeClass = this.state.activeClass, data = this.props.classes[activeClass];
        return Connector.callBaseclass(data.url, {
            data: data
        });
    },
    render: function() {
        var me = this;
        return /*#__PURE__*/ React.createElement(Provider, {
            store: easy.Store
        }, /*#__PURE__*/ React.createElement(AsyncLoading, {
            ref: "async",
            oid: "typeFilter",
            fireLoad: true,
            loadFn: me.retrieveData,
            getChildren: me.getChildren
        }));
    },
    displayName: "exports"
});


}),
"./src/framework/components/TypeListTemp.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
__webpack_require__(/*! core-js/modules/es.parse-int.js */ "../node_modules/core-js/modules/es.parse-int.js");
__webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
__webpack_require__(/*! core-js/modules/es.error.to-string.js */ "../node_modules/core-js/modules/es.error.to-string.js");
__webpack_require__(/*! core-js/modules/es.date.to-string.js */ "../node_modules/core-js/modules/es.date.to-string.js");
__webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ "../node_modules/core-js/modules/es.regexp.to-string.js");
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var SelectableList = __webpack_require__(/*! reactor/src/Form/components/Mols/SelectableList/SelectableList.jsx */ "../reactor/src/Form/components/Mols/SelectableList/SelectableList.jsx");
var SelectableItem = __webpack_require__(/*! reactor/src/Form/components/Mols/SelectableList/SelectableItem.jsx */ "../reactor/src/Form/components/Mols/SelectableList/SelectableItem.jsx");
var ImageLabel = __webpack_require__(/*! reactor/src/Atomic/components/Mols/ImageTextView.jsx */ "../reactor/src/Atomic/components/Mols/ImageTextView.jsx");
var Badge = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Badge.jsx */ "../reactor/src/Atomic/components/Atoms/Badge.jsx");
var imageStyle = {
    flex: '1 1 auto',
    msFlex: '1 1 auto'
};
var itemContainerStyle = {
    display: 'flex',
    alignItems: 'center',
    padding: '5px'
};
var containerStyle = {
    margin: '0 -5px'
};
module.exports = createReactClass({
    propTypes: {
        data: PropTypes.array,
        selected: PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.number
        ]),
        onRender: PropTypes.func,
        onChange: PropTypes.func,
        onUpdate: PropTypes.func.isRequired
    },
    getInitialState: function() {
        return {
            currentValue: null
        };
    },
    UNSAFE_componentWillMount: function() {
        var me = this, selected = me.props.selected;
        me.setState({
            currentValue: selected
        });
    },
    componentDidMount: function() {
        var me = this;
        if (me.props.onRender && me.hasData()) me.props.onRender();
    },
    componentDidUpdate: function() {
        var me = this, props = me.props;
        props.onUpdate();
    },
    hasData: function() {
        var me = this, data = me.props.data;
        return data !== null && data.length > 0;
    },
    setSelected: function(selected) {
        var me = this;
        me.setState({
            currentValue: selected
        });
    },
    reset: function() {
        var me = this;
        me.props.onChange(null);
        me.setState({
            currentValue: null
        });
    },
    handleChange: function(value) {
        var me = this;
        me.props.onChange(value[0]);
        me.setState({
            currentValue: value[0]
        });
    },
    getChildren: function() {
        var me = this;
        return me.props.data.map(function(current) {
            var id = "type" + current.code;
            var deadline;
            if (current.deadline) {
                deadline = parseInt(current.deadline, 10);
            }
            return /*#__PURE__*/ React.createElement(SelectableItem, {
                key: id,
                oid: current.code.toString()
            }, /*#__PURE__*/ React.createElement("div", {
                className: "listsItem",
                style: itemContainerStyle
            }, /*#__PURE__*/ React.createElement("div", {
                style: imageStyle
            }, /*#__PURE__*/ React.createElement(ImageLabel, {
                center: false,
                title: current.title,
                text: current.text,
                src: current.src,
                width: "16px",
                height: "16px",
                stringBreak: true
            })), /*#__PURE__*/ React.createElement("div", null, /*#__PURE__*/ React.createElement(Badge, {
                backgroundColor: deadline
            }, current.count))));
        });
    },
    render: function() {
        var me = this;
        return /*#__PURE__*/ React.createElement("div", {
            style: containerStyle
        }, /*#__PURE__*/ React.createElement(SelectableList, {
            ref: function(el) {
                return me._list = el;
            },
            multiSelect: false,
            value: [
                me.state.currentValue
            ],
            onChange: me.handleChange
        }, me.getChildren()));
    },
    displayName: "exports"
});


}),
"./src/framework/components/TypeFilter.css": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js */ "../node_modules/style-loader/dist/runtime/styleDomAPI.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/insertBySelector.js */ "../node_modules/style-loader/dist/runtime/insertBySelector.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js */ "../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js */ "../node_modules/style-loader/dist/runtime/insertStyleElement.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js */ "../node_modules/style-loader/dist/runtime/styleTagTransform.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_TypeFilter_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! !!../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./TypeFilter.css */ "../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./src/framework/components/TypeFilter.css");

      
      
      
      
      
      
      
      
      

var options = {};

options.styleTagTransform = (_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default());
options.setAttributes = (_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default());
options.insert = _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default().bind(null, "head");
options.domAPI = (_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default());
options.insertStyleElement = (_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default());

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_TypeFilter_css__WEBPACK_IMPORTED_MODULE_6__["default"], options);




       /* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_TypeFilter_css__WEBPACK_IMPORTED_MODULE_6__["default"] && _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_TypeFilter_css__WEBPACK_IMPORTED_MODULE_6__["default"].locals ? _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_TypeFilter_css__WEBPACK_IMPORTED_MODULE_6__["default"].locals : undefined);


}),
"Connector": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/components/TypeFilter.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=TypeFilter.js.map