define(["Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","tokens!reactorCmps/tokens/general"], function(__WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/googleAnalytics"], {
"../reactor2/src/helpers/googleAnalytics.js": (function (module, __unused_webpack_exports, __webpack_require__) {
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_without_holes(arr) {
    if (Array.isArray(arr)) return _array_like_to_array(arr);
}
function _iterable_to_array(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _non_iterable_spread() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _to_consumable_array(arr) {
    return _array_without_holes(arr) || _iterable_to_array(arr) || _unsupported_iterable_to_array(arr) || _non_iterable_spread();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
__webpack_require__(/*! core-js/modules/es.date.to-string.js */ "../node_modules/core-js/modules/es.date.to-string.js");
__webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
module.exports = {
    setPage: function setPage(shortcut, term) {
        if (!window.ga) {
            return false;
        }
        // TODO pegar um termo em inglês para passar para aqui
        if (shortcut && term && typeof term === 'number') {
            ga('set', {
                title: shortcut.toUpperCase() + ':' + SE.t(term)
            });
        }
        ga('send', 'pageview');
        return true;
    },
    create: function create(code, shortcut, term) {
        if (window.ga) {
            this.setPage(shortcut, term);
            return true;
        } else if (!code) {
            return false;
        }
        // Can't lintzar this line
        /* eslint-disable */ (function(i, s, o, g, r, a, m) {
            i['GoogleAnalyticsObject'] = r;
            i[r] = i[r] || function() {
                (i[r].q = i[r].q || []).push(arguments);
            }, i[r].l = 1 * new Date();
            a = s.createElement(o), m = s.getElementsByTagName(o)[0];
            a.async = 1;
            a.src = g;
            m.parentNode.insertBefore(a, m);
        })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
        /* eslint-enable */ ga('create', code, 'auto');
        this.setPage(shortcut, term);
        return true;
    },
    sendEvent: function sendEvent() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        if (!window.ga) {
            return false;
        }
        ga.apply(void 0, [
            'send',
            'event'
        ].concat(_to_consumable_array(args)));
        return true;
    },
    isTracking: function isTracking() {
        return window.ga !== undefined;
    },
    destroy: function destroy() {
        window.ga = undefined;
        return true;
    }
};


}),
"./src/framework/libs/googleAnalytics.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_helpers_googleAnalytics_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/helpers/googleAnalytics.js */ "../reactor2/src/helpers/googleAnalytics.js");
/* ESM import */var reactor2_src_helpers_googleAnalytics_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_helpers_googleAnalytics_js__WEBPACK_IMPORTED_MODULE_0__);

/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((reactor2_src_helpers_googleAnalytics_js__WEBPACK_IMPORTED_MODULE_0___default()));


}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/libs/googleAnalytics.js"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=googleAnalytics.js.map