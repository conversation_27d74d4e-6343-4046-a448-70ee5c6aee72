"use strict";
define(["Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","when","tokens!reactorCmps/tokens/general","react"], function(__WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_when__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_react__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/promiseHelper"], {
"./src/framework/helpers/promiseHelper.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  createCancellablePromise: function() { return /* reexport safe */ reactor2_src_helpers_promiseHelper__WEBPACK_IMPORTED_MODULE_0__.createCancellablePromise; },
  createPromiseManager: function() { return /* reexport safe */ reactor2_src_helpers_promiseHelper__WEBPACK_IMPORTED_MODULE_0__.createPromiseManager; },
  defaultCatchHandler: function() { return /* reexport safe */ reactor2_src_helpers_promiseHelper__WEBPACK_IMPORTED_MODULE_0__.defaultCatchHandler; },
  delayedLoadingNotifier: function() { return /* reexport safe */ reactor2_src_helpers_promiseHelper__WEBPACK_IMPORTED_MODULE_0__.delayedLoadingNotifier; },
  usePromiseManager: function() { return /* reexport safe */ reactor2_src_helpers_promiseHelper__WEBPACK_IMPORTED_MODULE_0__.usePromiseManager; }
});
/* ESM import */var reactor2_src_helpers_promiseHelper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/helpers/promiseHelper */ "../reactor2/src/helpers/promiseHelper.js");



}),
"Utils": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"watch1749037385376": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"when": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_when__;

}),
"reactorCmps/tokens/general": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/helpers/promiseHelper.js"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=promiseHelper.js.map