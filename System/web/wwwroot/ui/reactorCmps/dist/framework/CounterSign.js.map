{"version": 3, "file": "framework/CounterSign.js", "sources": ["webpack://watch1749037385376/../node_modules/formik/dist/formik.esm.js", "webpack://watch1749037385376/../reactor2/src/Form/_adapters/formik/adaptation.js", "webpack://watch1749037385376/../reactor2/src/Form/_adapters/formik/formik.js", "webpack://watch1749037385376/./src/framework/components/CounterSign.jsx"], "sourcesContent": ["import deepmerge from 'deepmerge';\nimport isPlainObject from 'lodash-es/isPlainObject';\nimport cloneDeep from 'lodash-es/cloneDeep';\nimport { createContext, useContext, Children, useRef, useEffect, useState, useCallback, useMemo, useImperativeHandle, createElement, useLayoutEffect, forwardRef, Component } from 'react';\nimport isEqual from 'react-fast-compare';\nimport invariant from 'tiny-warning';\nimport clone from 'lodash-es/clone';\nimport toPath from 'lodash-es/toPath';\nimport hoistNonReactStatics from 'hoist-non-react-statics';\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nvar FormikContext = /*#__PURE__*/createContext(\"MTY2Mm5vZGVfbW9kdWxlcy9mb3JtaWsvZGlzdC9mb3JtaWsuZXNtLmpzdW5kZWZpbmVk\", undefined);\nFormikContext.displayName = 'FormikContext';\nvar FormikProvider = FormikContext.Provider;\nvar FormikConsumer = FormikContext.Consumer;\nfunction useFormikContext() {\n  var formik = useContext(FormikContext);\n  !!!formik ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Formik context is undefined, please verify you are calling useFormikContext() as child of a <Formik> component.\") : invariant(false) : void 0;\n  return formik;\n}\n\n/** @private is the value an empty array? */\n\nvar isEmptyArray = function isEmptyArray(value) {\n  return Array.isArray(value) && value.length === 0;\n};\n/** @private is the given object a Function? */\n\nvar isFunction = function isFunction(obj) {\n  return typeof obj === 'function';\n};\n/** @private is the given object an Object? */\n\nvar isObject = function isObject(obj) {\n  return obj !== null && typeof obj === 'object';\n};\n/** @private is the given object an integer? */\n\nvar isInteger = function isInteger(obj) {\n  return String(Math.floor(Number(obj))) === obj;\n};\n/** @private is the given object a string? */\n\nvar isString = function isString(obj) {\n  return Object.prototype.toString.call(obj) === '[object String]';\n};\n/** @private is the given object a NaN? */\n// eslint-disable-next-line no-self-compare\n\nvar isNaN$1 = function isNaN(obj) {\n  return obj !== obj;\n};\n/** @private Does a React component have exactly 0 children? */\n\nvar isEmptyChildren = function isEmptyChildren(children) {\n  return Children.count(children) === 0;\n};\n/** @private is the given object/value a promise? */\n\nvar isPromise = function isPromise(value) {\n  return isObject(value) && isFunction(value.then);\n};\n/** @private is the given object/value a type of synthetic event? */\n\nvar isInputEvent = function isInputEvent(value) {\n  return value && isObject(value) && isObject(value.target);\n};\n/**\r\n * Same as document.activeElement but wraps in a try-catch block. In IE it is\r\n * not safe to call document.activeElement if there is nothing focused.\r\n *\r\n * The activeElement will be null only if the document or document body is not\r\n * yet defined.\r\n *\r\n * @param {?Document} doc Defaults to current document.\r\n * @return {Element | null}\r\n * @see https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/core/dom/getActiveElement.js\r\n */\n\nfunction getActiveElement(doc) {\n  doc = doc || (typeof document !== 'undefined' ? document : undefined);\n\n  if (typeof doc === 'undefined') {\n    return null;\n  }\n\n  try {\n    return doc.activeElement || doc.body;\n  } catch (e) {\n    return doc.body;\n  }\n}\n/**\r\n * Deeply get a value from an object via its path.\r\n */\n\nfunction getIn(obj, key, def, p) {\n  if (p === void 0) {\n    p = 0;\n  }\n\n  var path = toPath(key);\n\n  while (obj && p < path.length) {\n    obj = obj[path[p++]];\n  } // check if path is not in the end\n\n\n  if (p !== path.length && !obj) {\n    return def;\n  }\n\n  return obj === undefined ? def : obj;\n}\n/**\r\n * Deeply set a value from in object via it's path. If the value at `path`\r\n * has changed, return a shallow copy of obj with `value` set at `path`.\r\n * If `value` has not changed, return the original `obj`.\r\n *\r\n * Existing objects / arrays along `path` are also shallow copied. Sibling\r\n * objects along path retain the same internal js reference. Since new\r\n * objects / arrays are only created along `path`, we can test if anything\r\n * changed in a nested structure by comparing the object's reference in\r\n * the old and new object, similar to how russian doll cache invalidation\r\n * works.\r\n *\r\n * In earlier versions of this function, which used cloneDeep, there were\r\n * issues whereby settings a nested value would mutate the parent\r\n * instead of creating a new object. `clone` avoids that bug making a\r\n * shallow copy of the objects along the update path\r\n * so no object is mutated in place.\r\n *\r\n * Before changing this function, please read through the following\r\n * discussions.\r\n *\r\n * @see https://github.com/developit/linkstate\r\n * @see https://github.com/jaredpalmer/formik/pull/123\r\n */\n\nfunction setIn(obj, path, value) {\n  var res = clone(obj); // this keeps inheritance when obj is a class\n\n  var resVal = res;\n  var i = 0;\n  var pathArray = toPath(path);\n\n  for (; i < pathArray.length - 1; i++) {\n    var currentPath = pathArray[i];\n    var currentObj = getIn(obj, pathArray.slice(0, i + 1));\n\n    if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {\n      resVal = resVal[currentPath] = clone(currentObj);\n    } else {\n      var nextPath = pathArray[i + 1];\n      resVal = resVal[currentPath] = isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};\n    }\n  } // Return original object if new value is the same as current\n\n\n  if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {\n    return obj;\n  }\n\n  if (value === undefined) {\n    delete resVal[pathArray[i]];\n  } else {\n    resVal[pathArray[i]] = value;\n  } // If the path array has a single element, the loop did not run.\n  // Deleting on `resVal` had no effect in this scenario, so we delete on the result instead.\n\n\n  if (i === 0 && value === undefined) {\n    delete res[pathArray[i]];\n  }\n\n  return res;\n}\n/**\r\n * Recursively a set the same value for all keys and arrays nested object, cloning\r\n * @param object\r\n * @param value\r\n * @param visited\r\n * @param response\r\n */\n\nfunction setNestedObjectValues(object, value, visited, response) {\n  if (visited === void 0) {\n    visited = new WeakMap();\n  }\n\n  if (response === void 0) {\n    response = {};\n  }\n\n  for (var _i = 0, _Object$keys = Object.keys(object); _i < _Object$keys.length; _i++) {\n    var k = _Object$keys[_i];\n    var val = object[k];\n\n    if (isObject(val)) {\n      if (!visited.get(val)) {\n        visited.set(val, true); // In order to keep array values consistent for both dot path  and\n        // bracket syntax, we need to check if this is an array so that\n        // this will output  { friends: [true] } and not { friends: { \"0\": true } }\n\n        response[k] = Array.isArray(val) ? [] : {};\n        setNestedObjectValues(val, value, visited, response[k]);\n      }\n    } else {\n      response[k] = value;\n    }\n  }\n\n  return response;\n}\n\nfunction formikReducer(state, msg) {\n  switch (msg.type) {\n    case 'SET_VALUES':\n      return _extends({}, state, {\n        values: msg.payload\n      });\n\n    case 'SET_TOUCHED':\n      return _extends({}, state, {\n        touched: msg.payload\n      });\n\n    case 'SET_ERRORS':\n      if (isEqual(state.errors, msg.payload)) {\n        return state;\n      }\n\n      return _extends({}, state, {\n        errors: msg.payload\n      });\n\n    case 'SET_STATUS':\n      return _extends({}, state, {\n        status: msg.payload\n      });\n\n    case 'SET_ISSUBMITTING':\n      return _extends({}, state, {\n        isSubmitting: msg.payload\n      });\n\n    case 'SET_ISVALIDATING':\n      return _extends({}, state, {\n        isValidating: msg.payload\n      });\n\n    case 'SET_FIELD_VALUE':\n      return _extends({}, state, {\n        values: setIn(state.values, msg.payload.field, msg.payload.value)\n      });\n\n    case 'SET_FIELD_TOUCHED':\n      return _extends({}, state, {\n        touched: setIn(state.touched, msg.payload.field, msg.payload.value)\n      });\n\n    case 'SET_FIELD_ERROR':\n      return _extends({}, state, {\n        errors: setIn(state.errors, msg.payload.field, msg.payload.value)\n      });\n\n    case 'RESET_FORM':\n      return _extends({}, state, msg.payload);\n\n    case 'SET_FORMIK_STATE':\n      return msg.payload(state);\n\n    case 'SUBMIT_ATTEMPT':\n      return _extends({}, state, {\n        touched: setNestedObjectValues(state.values, true),\n        isSubmitting: true,\n        submitCount: state.submitCount + 1\n      });\n\n    case 'SUBMIT_FAILURE':\n      return _extends({}, state, {\n        isSubmitting: false\n      });\n\n    case 'SUBMIT_SUCCESS':\n      return _extends({}, state, {\n        isSubmitting: false\n      });\n\n    default:\n      return state;\n  }\n} // Initial empty states // objects\n\n\nvar emptyErrors = {};\nvar emptyTouched = {};\nfunction useFormik(_ref) {\n  var _ref$validateOnChange = _ref.validateOnChange,\n      validateOnChange = _ref$validateOnChange === void 0 ? true : _ref$validateOnChange,\n      _ref$validateOnBlur = _ref.validateOnBlur,\n      validateOnBlur = _ref$validateOnBlur === void 0 ? true : _ref$validateOnBlur,\n      _ref$validateOnMount = _ref.validateOnMount,\n      validateOnMount = _ref$validateOnMount === void 0 ? false : _ref$validateOnMount,\n      isInitialValid = _ref.isInitialValid,\n      _ref$enableReinitiali = _ref.enableReinitialize,\n      enableReinitialize = _ref$enableReinitiali === void 0 ? false : _ref$enableReinitiali,\n      onSubmit = _ref.onSubmit,\n      rest = _objectWithoutPropertiesLoose(_ref, [\"validateOnChange\", \"validateOnBlur\", \"validateOnMount\", \"isInitialValid\", \"enableReinitialize\", \"onSubmit\"]);\n\n  var props = _extends({\n    validateOnChange: validateOnChange,\n    validateOnBlur: validateOnBlur,\n    validateOnMount: validateOnMount,\n    onSubmit: onSubmit\n  }, rest);\n\n  var initialValues = useRef(props.initialValues);\n  var initialErrors = useRef(props.initialErrors || emptyErrors);\n  var initialTouched = useRef(props.initialTouched || emptyTouched);\n  var initialStatus = useRef(props.initialStatus);\n  var isMounted = useRef(false);\n  var fieldRegistry = useRef({});\n\n  if (process.env.NODE_ENV !== \"production\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(function () {\n      !(typeof isInitialValid === 'undefined') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isInitialValid has been deprecated and will be removed in future versions of Formik. Please use initialErrors or validateOnMount instead.') : invariant(false) : void 0; // eslint-disable-next-line\n    }, []);\n  }\n\n  useEffect(function () {\n    isMounted.current = true;\n    return function () {\n      isMounted.current = false;\n    };\n  }, []);\n\n  var _React$useState = useState(0),\n      setIteration = _React$useState[1];\n\n  var stateRef = useRef({\n    values: cloneDeep(props.initialValues),\n    errors: cloneDeep(props.initialErrors) || emptyErrors,\n    touched: cloneDeep(props.initialTouched) || emptyTouched,\n    status: cloneDeep(props.initialStatus),\n    isSubmitting: false,\n    isValidating: false,\n    submitCount: 0\n  });\n  var state = stateRef.current;\n  var dispatch = useCallback(function (action) {\n    var prev = stateRef.current;\n    stateRef.current = formikReducer(prev, action); // force rerender\n\n    if (prev !== stateRef.current) setIteration(function (x) {\n      return x + 1;\n    });\n  }, []);\n  var runValidateHandler = useCallback(function (values, field) {\n    return new Promise(function (resolve, reject) {\n      var maybePromisedErrors = props.validate(values, field);\n\n      if (maybePromisedErrors == null) {\n        // use loose null check here on purpose\n        resolve(emptyErrors);\n      } else if (isPromise(maybePromisedErrors)) {\n        maybePromisedErrors.then(function (errors) {\n          resolve(errors || emptyErrors);\n        }, function (actualException) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\"Warning: An unhandled error was caught during validation in <Formik validate />\", actualException);\n          }\n\n          reject(actualException);\n        });\n      } else {\n        resolve(maybePromisedErrors);\n      }\n    });\n  }, [props.validate]);\n  /**\r\n   * Run validation against a Yup schema and optionally run a function if successful\r\n   */\n\n  var runValidationSchema = useCallback(function (values, field) {\n    var validationSchema = props.validationSchema;\n    var schema = isFunction(validationSchema) ? validationSchema(field) : validationSchema;\n    var promise = field && schema.validateAt ? schema.validateAt(field, values) : validateYupSchema(values, schema);\n    return new Promise(function (resolve, reject) {\n      promise.then(function () {\n        resolve(emptyErrors);\n      }, function (err) {\n        // Yup will throw a validation error if validation fails. We catch those and\n        // resolve them into Formik errors. We can sniff if something is a Yup error\n        // by checking error.name.\n        // @see https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n        if (err.name === 'ValidationError') {\n          resolve(yupToFormErrors(err));\n        } else {\n          // We throw any other errors\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\"Warning: An unhandled error was caught during validation in <Formik validationSchema />\", err);\n          }\n\n          reject(err);\n        }\n      });\n    });\n  }, [props.validationSchema]);\n  var runSingleFieldLevelValidation = useCallback(function (field, value) {\n    return new Promise(function (resolve) {\n      return resolve(fieldRegistry.current[field].validate(value));\n    });\n  }, []);\n  var runFieldLevelValidations = useCallback(function (values) {\n    var fieldKeysWithValidation = Object.keys(fieldRegistry.current).filter(function (f) {\n      return isFunction(fieldRegistry.current[f].validate);\n    }); // Construct an array with all of the field validation functions\n\n    var fieldValidations = fieldKeysWithValidation.length > 0 ? fieldKeysWithValidation.map(function (f) {\n      return runSingleFieldLevelValidation(f, getIn(values, f));\n    }) : [Promise.resolve('DO_NOT_DELETE_YOU_WILL_BE_FIRED')]; // use special case ;)\n\n    return Promise.all(fieldValidations).then(function (fieldErrorsList) {\n      return fieldErrorsList.reduce(function (prev, curr, index) {\n        if (curr === 'DO_NOT_DELETE_YOU_WILL_BE_FIRED') {\n          return prev;\n        }\n\n        if (curr) {\n          prev = setIn(prev, fieldKeysWithValidation[index], curr);\n        }\n\n        return prev;\n      }, {});\n    });\n  }, [runSingleFieldLevelValidation]); // Run all validations and return the result\n\n  var runAllValidations = useCallback(function (values) {\n    return Promise.all([runFieldLevelValidations(values), props.validationSchema ? runValidationSchema(values) : {}, props.validate ? runValidateHandler(values) : {}]).then(function (_ref2) {\n      var fieldErrors = _ref2[0],\n          schemaErrors = _ref2[1],\n          validateErrors = _ref2[2];\n      var combinedErrors = deepmerge.all([fieldErrors, schemaErrors, validateErrors], {\n        arrayMerge: arrayMerge\n      });\n      return combinedErrors;\n    });\n  }, [props.validate, props.validationSchema, runFieldLevelValidations, runValidateHandler, runValidationSchema]); // Run all validations methods and update state accordingly\n\n  var validateFormWithHighPriority = useEventCallback(function (values) {\n    if (values === void 0) {\n      values = state.values;\n    }\n\n    dispatch({\n      type: 'SET_ISVALIDATING',\n      payload: true\n    });\n    return runAllValidations(values).then(function (combinedErrors) {\n      if (!!isMounted.current) {\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: false\n        });\n        dispatch({\n          type: 'SET_ERRORS',\n          payload: combinedErrors\n        });\n      }\n\n      return combinedErrors;\n    });\n  });\n  useEffect(function () {\n    if (validateOnMount && isMounted.current === true && isEqual(initialValues.current, props.initialValues)) {\n      validateFormWithHighPriority(initialValues.current);\n    }\n  }, [validateOnMount, validateFormWithHighPriority]);\n  var resetForm = useCallback(function (nextState) {\n    var values = nextState && nextState.values ? nextState.values : initialValues.current;\n    var errors = nextState && nextState.errors ? nextState.errors : initialErrors.current ? initialErrors.current : props.initialErrors || {};\n    var touched = nextState && nextState.touched ? nextState.touched : initialTouched.current ? initialTouched.current : props.initialTouched || {};\n    var status = nextState && nextState.status ? nextState.status : initialStatus.current ? initialStatus.current : props.initialStatus;\n    initialValues.current = values;\n    initialErrors.current = errors;\n    initialTouched.current = touched;\n    initialStatus.current = status;\n\n    var dispatchFn = function dispatchFn() {\n      dispatch({\n        type: 'RESET_FORM',\n        payload: {\n          isSubmitting: !!nextState && !!nextState.isSubmitting,\n          errors: errors,\n          touched: touched,\n          status: status,\n          values: values,\n          isValidating: !!nextState && !!nextState.isValidating,\n          submitCount: !!nextState && !!nextState.submitCount && typeof nextState.submitCount === 'number' ? nextState.submitCount : 0\n        }\n      });\n    };\n\n    if (props.onReset) {\n      var maybePromisedOnReset = props.onReset(state.values, imperativeMethods);\n\n      if (isPromise(maybePromisedOnReset)) {\n        maybePromisedOnReset.then(dispatchFn);\n      } else {\n        dispatchFn();\n      }\n    } else {\n      dispatchFn();\n    }\n  }, [props.initialErrors, props.initialStatus, props.initialTouched, props.onReset]);\n  useEffect(function () {\n    if (isMounted.current === true && !isEqual(initialValues.current, props.initialValues)) {\n      if (enableReinitialize) {\n        initialValues.current = props.initialValues;\n        resetForm();\n\n        if (validateOnMount) {\n          validateFormWithHighPriority(initialValues.current);\n        }\n      }\n    }\n  }, [enableReinitialize, props.initialValues, resetForm, validateOnMount, validateFormWithHighPriority]);\n  useEffect(function () {\n    if (enableReinitialize && isMounted.current === true && !isEqual(initialErrors.current, props.initialErrors)) {\n      initialErrors.current = props.initialErrors || emptyErrors;\n      dispatch({\n        type: 'SET_ERRORS',\n        payload: props.initialErrors || emptyErrors\n      });\n    }\n  }, [enableReinitialize, props.initialErrors]);\n  useEffect(function () {\n    if (enableReinitialize && isMounted.current === true && !isEqual(initialTouched.current, props.initialTouched)) {\n      initialTouched.current = props.initialTouched || emptyTouched;\n      dispatch({\n        type: 'SET_TOUCHED',\n        payload: props.initialTouched || emptyTouched\n      });\n    }\n  }, [enableReinitialize, props.initialTouched]);\n  useEffect(function () {\n    if (enableReinitialize && isMounted.current === true && !isEqual(initialStatus.current, props.initialStatus)) {\n      initialStatus.current = props.initialStatus;\n      dispatch({\n        type: 'SET_STATUS',\n        payload: props.initialStatus\n      });\n    }\n  }, [enableReinitialize, props.initialStatus, props.initialTouched]);\n  var validateField = useEventCallback(function (name) {\n    // This will efficiently validate a single field by avoiding state\n    // changes if the validation function is synchronous. It's different from\n    // what is called when using validateForm.\n    if (fieldRegistry.current[name] && isFunction(fieldRegistry.current[name].validate)) {\n      var value = getIn(state.values, name);\n      var maybePromise = fieldRegistry.current[name].validate(value);\n\n      if (isPromise(maybePromise)) {\n        // Only flip isValidating if the function is async.\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: true\n        });\n        return maybePromise.then(function (x) {\n          return x;\n        }).then(function (error) {\n          dispatch({\n            type: 'SET_FIELD_ERROR',\n            payload: {\n              field: name,\n              value: error\n            }\n          });\n          dispatch({\n            type: 'SET_ISVALIDATING',\n            payload: false\n          });\n        });\n      } else {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: maybePromise\n          }\n        });\n        return Promise.resolve(maybePromise);\n      }\n    } else if (props.validationSchema) {\n      dispatch({\n        type: 'SET_ISVALIDATING',\n        payload: true\n      });\n      return runValidationSchema(state.values, name).then(function (x) {\n        return x;\n      }).then(function (error) {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: getIn(error, name)\n          }\n        });\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: false\n        });\n      });\n    }\n\n    return Promise.resolve();\n  });\n  var registerField = useCallback(function (name, _ref3) {\n    var validate = _ref3.validate;\n    fieldRegistry.current[name] = {\n      validate: validate\n    };\n  }, []);\n  var unregisterField = useCallback(function (name) {\n    delete fieldRegistry.current[name];\n  }, []);\n  var setTouched = useEventCallback(function (touched, shouldValidate) {\n    dispatch({\n      type: 'SET_TOUCHED',\n      payload: touched\n    });\n    var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n  });\n  var setErrors = useCallback(function (errors) {\n    dispatch({\n      type: 'SET_ERRORS',\n      payload: errors\n    });\n  }, []);\n  var setValues = useEventCallback(function (values, shouldValidate) {\n    var resolvedValues = isFunction(values) ? values(state.values) : values;\n    dispatch({\n      type: 'SET_VALUES',\n      payload: resolvedValues\n    });\n    var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(resolvedValues) : Promise.resolve();\n  });\n  var setFieldError = useCallback(function (field, value) {\n    dispatch({\n      type: 'SET_FIELD_ERROR',\n      payload: {\n        field: field,\n        value: value\n      }\n    });\n  }, []);\n  var setFieldValue = useEventCallback(function (field, value, shouldValidate) {\n    dispatch({\n      type: 'SET_FIELD_VALUE',\n      payload: {\n        field: field,\n        value: value\n      }\n    });\n    var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(setIn(state.values, field, value)) : Promise.resolve();\n  });\n  var executeChange = useCallback(function (eventOrTextValue, maybePath) {\n    // By default, assume that the first argument is a string. This allows us to use\n    // handleChange with React Native and React Native Web's onChangeText prop which\n    // provides just the value of the input.\n    var field = maybePath;\n    var val = eventOrTextValue;\n    var parsed; // If the first argument is not a string though, it has to be a synthetic React Event (or a fake one),\n    // so we handle like we would a normal HTML change event.\n\n    if (!isString(eventOrTextValue)) {\n      // If we can, persist the event\n      // @see https://reactjs.org/docs/events.html#event-pooling\n      if (eventOrTextValue.persist) {\n        eventOrTextValue.persist();\n      }\n\n      var target = eventOrTextValue.target ? eventOrTextValue.target : eventOrTextValue.currentTarget;\n      var type = target.type,\n          name = target.name,\n          id = target.id,\n          value = target.value,\n          checked = target.checked,\n          outerHTML = target.outerHTML,\n          options = target.options,\n          multiple = target.multiple;\n      field = maybePath ? maybePath : name ? name : id;\n\n      if (!field && process.env.NODE_ENV !== \"production\") {\n        warnAboutMissingIdentifier({\n          htmlContent: outerHTML,\n          documentationAnchorLink: 'handlechange-e-reactchangeeventany--void',\n          handlerName: 'handleChange'\n        });\n      }\n\n      val = /number|range/.test(type) ? (parsed = parseFloat(value), isNaN(parsed) ? '' : parsed) : /checkbox/.test(type) // checkboxes\n      ? getValueForCheckbox(getIn(state.values, field), checked, value) : options && multiple // <select multiple>\n      ? getSelectedValues(options) : value;\n    }\n\n    if (field) {\n      // Set form fields by name\n      setFieldValue(field, val);\n    }\n  }, [setFieldValue, state.values]);\n  var handleChange = useEventCallback(function (eventOrPath) {\n    if (isString(eventOrPath)) {\n      return function (event) {\n        return executeChange(event, eventOrPath);\n      };\n    } else {\n      executeChange(eventOrPath);\n    }\n  });\n  var setFieldTouched = useEventCallback(function (field, touched, shouldValidate) {\n    if (touched === void 0) {\n      touched = true;\n    }\n\n    dispatch({\n      type: 'SET_FIELD_TOUCHED',\n      payload: {\n        field: field,\n        value: touched\n      }\n    });\n    var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n  });\n  var executeBlur = useCallback(function (e, path) {\n    if (e.persist) {\n      e.persist();\n    }\n\n    var _e$target = e.target,\n        name = _e$target.name,\n        id = _e$target.id,\n        outerHTML = _e$target.outerHTML;\n    var field = path ? path : name ? name : id;\n\n    if (!field && process.env.NODE_ENV !== \"production\") {\n      warnAboutMissingIdentifier({\n        htmlContent: outerHTML,\n        documentationAnchorLink: 'handleblur-e-any--void',\n        handlerName: 'handleBlur'\n      });\n    }\n\n    setFieldTouched(field, true);\n  }, [setFieldTouched]);\n  var handleBlur = useEventCallback(function (eventOrString) {\n    if (isString(eventOrString)) {\n      return function (event) {\n        return executeBlur(event, eventOrString);\n      };\n    } else {\n      executeBlur(eventOrString);\n    }\n  });\n  var setFormikState = useCallback(function (stateOrCb) {\n    if (isFunction(stateOrCb)) {\n      dispatch({\n        type: 'SET_FORMIK_STATE',\n        payload: stateOrCb\n      });\n    } else {\n      dispatch({\n        type: 'SET_FORMIK_STATE',\n        payload: function payload() {\n          return stateOrCb;\n        }\n      });\n    }\n  }, []);\n  var setStatus = useCallback(function (status) {\n    dispatch({\n      type: 'SET_STATUS',\n      payload: status\n    });\n  }, []);\n  var setSubmitting = useCallback(function (isSubmitting) {\n    dispatch({\n      type: 'SET_ISSUBMITTING',\n      payload: isSubmitting\n    });\n  }, []);\n  var submitForm = useEventCallback(function () {\n    dispatch({\n      type: 'SUBMIT_ATTEMPT'\n    });\n    return validateFormWithHighPriority().then(function (combinedErrors) {\n      // In case an error was thrown and passed to the resolved Promise,\n      // `combinedErrors` can be an instance of an Error. We need to check\n      // that and abort the submit.\n      // If we don't do that, calling `Object.keys(new Error())` yields an\n      // empty array, which causes the validation to pass and the form\n      // to be submitted.\n      var isInstanceOfError = combinedErrors instanceof Error;\n      var isActuallyValid = !isInstanceOfError && Object.keys(combinedErrors).length === 0;\n\n      if (isActuallyValid) {\n        // Proceed with submit...\n        //\n        // To respect sync submit fns, we can't simply wrap executeSubmit in a promise and\n        // _always_ dispatch SUBMIT_SUCCESS because isSubmitting would then always be false.\n        // This would be fine in simple cases, but make it impossible to disable submit\n        // buttons where people use callbacks or promises as side effects (which is basically\n        // all of v1 Formik code). Instead, recall that we are inside of a promise chain already,\n        //  so we can try/catch executeSubmit(), if it returns undefined, then just bail.\n        // If there are errors, throw em. Otherwise, wrap executeSubmit in a promise and handle\n        // cleanup of isSubmitting on behalf of the consumer.\n        var promiseOrUndefined;\n\n        try {\n          promiseOrUndefined = executeSubmit(); // Bail if it's sync, consumer is responsible for cleaning up\n          // via setSubmitting(false)\n\n          if (promiseOrUndefined === undefined) {\n            return;\n          }\n        } catch (error) {\n          throw error;\n        }\n\n        return Promise.resolve(promiseOrUndefined).then(function (result) {\n          if (!!isMounted.current) {\n            dispatch({\n              type: 'SUBMIT_SUCCESS'\n            });\n          }\n\n          return result;\n        })[\"catch\"](function (_errors) {\n          if (!!isMounted.current) {\n            dispatch({\n              type: 'SUBMIT_FAILURE'\n            }); // This is a legit error rejected by the onSubmit fn\n            // so we don't want to break the promise chain\n\n            throw _errors;\n          }\n        });\n      } else if (!!isMounted.current) {\n        // ^^^ Make sure Formik is still mounted before updating state\n        dispatch({\n          type: 'SUBMIT_FAILURE'\n        }); // throw combinedErrors;\n\n        if (isInstanceOfError) {\n          throw combinedErrors;\n        }\n      }\n\n      return;\n    });\n  });\n  var handleSubmit = useEventCallback(function (e) {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    } // Warn if form submission is triggered by a <button> without a\n    // specified `type` attribute during development. This mitigates\n    // a common gotcha in forms with both reset and submit buttons,\n    // where the dev forgets to add type=\"button\" to the reset button.\n\n\n    if (process.env.NODE_ENV !== \"production\" && typeof document !== 'undefined') {\n      // Safely get the active element (works with IE)\n      var activeElement = getActiveElement();\n\n      if (activeElement !== null && activeElement instanceof HTMLButtonElement) {\n        !(activeElement.attributes && activeElement.attributes.getNamedItem('type')) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You submitted a Formik form using a button with an unspecified `type` attribute.  Most browsers default button elements to `type=\"submit\"`. If this is not a submit button, please add `type=\"button\"`.') : invariant(false) : void 0;\n      }\n    }\n\n    submitForm()[\"catch\"](function (reason) {\n      console.warn(\"Warning: An unhandled error was caught from submitForm()\", reason);\n    });\n  });\n  var imperativeMethods = {\n    resetForm: resetForm,\n    validateForm: validateFormWithHighPriority,\n    validateField: validateField,\n    setErrors: setErrors,\n    setFieldError: setFieldError,\n    setFieldTouched: setFieldTouched,\n    setFieldValue: setFieldValue,\n    setStatus: setStatus,\n    setSubmitting: setSubmitting,\n    setTouched: setTouched,\n    setValues: setValues,\n    setFormikState: setFormikState,\n    submitForm: submitForm\n  };\n  var executeSubmit = useEventCallback(function () {\n    return onSubmit(state.values, imperativeMethods);\n  });\n  var handleReset = useEventCallback(function (e) {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    }\n\n    resetForm();\n  });\n  var getFieldMeta = useCallback(function (name) {\n    return {\n      value: getIn(state.values, name),\n      error: getIn(state.errors, name),\n      touched: !!getIn(state.touched, name),\n      initialValue: getIn(initialValues.current, name),\n      initialTouched: !!getIn(initialTouched.current, name),\n      initialError: getIn(initialErrors.current, name)\n    };\n  }, [state.errors, state.touched, state.values]);\n  var getFieldHelpers = useCallback(function (name) {\n    return {\n      setValue: function setValue(value, shouldValidate) {\n        return setFieldValue(name, value, shouldValidate);\n      },\n      setTouched: function setTouched(value, shouldValidate) {\n        return setFieldTouched(name, value, shouldValidate);\n      },\n      setError: function setError(value) {\n        return setFieldError(name, value);\n      }\n    };\n  }, [setFieldValue, setFieldTouched, setFieldError]);\n  var getFieldProps = useCallback(function (nameOrOptions) {\n    var isAnObject = isObject(nameOrOptions);\n    var name = isAnObject ? nameOrOptions.name : nameOrOptions;\n    var valueState = getIn(state.values, name);\n    var field = {\n      name: name,\n      value: valueState,\n      onChange: handleChange,\n      onBlur: handleBlur\n    };\n\n    if (isAnObject) {\n      var type = nameOrOptions.type,\n          valueProp = nameOrOptions.value,\n          is = nameOrOptions.as,\n          multiple = nameOrOptions.multiple;\n\n      if (type === 'checkbox') {\n        if (valueProp === undefined) {\n          field.checked = !!valueState;\n        } else {\n          field.checked = !!(Array.isArray(valueState) && ~valueState.indexOf(valueProp));\n          field.value = valueProp;\n        }\n      } else if (type === 'radio') {\n        field.checked = valueState === valueProp;\n        field.value = valueProp;\n      } else if (is === 'select' && multiple) {\n        field.value = field.value || [];\n        field.multiple = true;\n      }\n    }\n\n    return field;\n  }, [handleBlur, handleChange, state.values]);\n  var dirty = useMemo(function () {\n    return !isEqual(initialValues.current, state.values);\n  }, [initialValues.current, state.values]);\n  var isValid = useMemo(function () {\n    return typeof isInitialValid !== 'undefined' ? dirty ? state.errors && Object.keys(state.errors).length === 0 : isInitialValid !== false && isFunction(isInitialValid) ? isInitialValid(props) : isInitialValid : state.errors && Object.keys(state.errors).length === 0;\n  }, [isInitialValid, dirty, state.errors, props]);\n\n  var ctx = _extends({}, state, {\n    initialValues: initialValues.current,\n    initialErrors: initialErrors.current,\n    initialTouched: initialTouched.current,\n    initialStatus: initialStatus.current,\n    handleBlur: handleBlur,\n    handleChange: handleChange,\n    handleReset: handleReset,\n    handleSubmit: handleSubmit,\n    resetForm: resetForm,\n    setErrors: setErrors,\n    setFormikState: setFormikState,\n    setFieldTouched: setFieldTouched,\n    setFieldValue: setFieldValue,\n    setFieldError: setFieldError,\n    setStatus: setStatus,\n    setSubmitting: setSubmitting,\n    setTouched: setTouched,\n    setValues: setValues,\n    submitForm: submitForm,\n    validateForm: validateFormWithHighPriority,\n    validateField: validateField,\n    isValid: isValid,\n    dirty: dirty,\n    unregisterField: unregisterField,\n    registerField: registerField,\n    getFieldProps: getFieldProps,\n    getFieldMeta: getFieldMeta,\n    getFieldHelpers: getFieldHelpers,\n    validateOnBlur: validateOnBlur,\n    validateOnChange: validateOnChange,\n    validateOnMount: validateOnMount\n  });\n\n  return ctx;\n}\nfunction Formik(props) {\n  var formikbag = useFormik(props);\n  var component = props.component,\n      children = props.children,\n      render = props.render,\n      innerRef = props.innerRef; // This allows folks to pass a ref to <Formik />\n\n  useImperativeHandle(innerRef, function () {\n    return formikbag;\n  });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(function () {\n      !!props.render ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"<Formik render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Formik render={(props) => ...} /> with <Formik>{(props) => ...}</Formik>\") : invariant(false) : void 0; // eslint-disable-next-line\n    }, []);\n  }\n\n  return createElement(FormikProvider, {\n    value: formikbag\n  }, component ? createElement(component, formikbag) : render ? render(formikbag) : children // children come last, always called\n  ? isFunction(children) ? children(formikbag) : !isEmptyChildren(children) ? Children.only(children) : null : null);\n}\n\nfunction warnAboutMissingIdentifier(_ref4) {\n  var htmlContent = _ref4.htmlContent,\n      documentationAnchorLink = _ref4.documentationAnchorLink,\n      handlerName = _ref4.handlerName;\n  console.warn(\"Warning: Formik called `\" + handlerName + \"`, but you forgot to pass an `id` or `name` attribute to your input:\\n    \" + htmlContent + \"\\n    Formik cannot determine which value to update. For more info see https://formik.org/docs/api/formik#\" + documentationAnchorLink + \"\\n  \");\n}\n/**\r\n * Transform Yup ValidationError to a more usable object\r\n */\n\n\nfunction yupToFormErrors(yupError) {\n  var errors = {};\n\n  if (yupError.inner) {\n    if (yupError.inner.length === 0) {\n      return setIn(errors, yupError.path, yupError.message);\n    }\n\n    for (var _iterator = yupError.inner, _isArray = Array.isArray(_iterator), _i = 0, _iterator = _isArray ? _iterator : _iterator[Symbol.iterator]();;) {\n      var _ref5;\n\n      if (_isArray) {\n        if (_i >= _iterator.length) break;\n        _ref5 = _iterator[_i++];\n      } else {\n        _i = _iterator.next();\n        if (_i.done) break;\n        _ref5 = _i.value;\n      }\n\n      var err = _ref5;\n\n      if (!getIn(errors, err.path)) {\n        errors = setIn(errors, err.path, err.message);\n      }\n    }\n  }\n\n  return errors;\n}\n/**\r\n * Validate a yup schema.\r\n */\n\nfunction validateYupSchema(values, schema, sync, context) {\n  if (sync === void 0) {\n    sync = false;\n  }\n\n  var normalizedValues = prepareDataForValidation(values);\n  return schema[sync ? 'validateSync' : 'validate'](normalizedValues, {\n    abortEarly: false,\n    context: context || normalizedValues\n  });\n}\n/**\r\n * Recursively prepare values.\r\n */\n\nfunction prepareDataForValidation(values) {\n  var data = Array.isArray(values) ? [] : {};\n\n  for (var k in values) {\n    if (Object.prototype.hasOwnProperty.call(values, k)) {\n      var key = String(k);\n\n      if (Array.isArray(values[key]) === true) {\n        data[key] = values[key].map(function (value) {\n          if (Array.isArray(value) === true || isPlainObject(value)) {\n            return prepareDataForValidation(value);\n          } else {\n            return value !== '' ? value : undefined;\n          }\n        });\n      } else if (isPlainObject(values[key])) {\n        data[key] = prepareDataForValidation(values[key]);\n      } else {\n        data[key] = values[key] !== '' ? values[key] : undefined;\n      }\n    }\n  }\n\n  return data;\n}\n/**\r\n * deepmerge array merging algorithm\r\n * https://github.com/KyleAMathews/deepmerge#combine-array\r\n */\n\nfunction arrayMerge(target, source, options) {\n  var destination = target.slice();\n  source.forEach(function merge(e, i) {\n    if (typeof destination[i] === 'undefined') {\n      var cloneRequested = options.clone !== false;\n      var shouldClone = cloneRequested && options.isMergeableObject(e);\n      destination[i] = shouldClone ? deepmerge(Array.isArray(e) ? [] : {}, e, options) : e;\n    } else if (options.isMergeableObject(e)) {\n      destination[i] = deepmerge(target[i], e, options);\n    } else if (target.indexOf(e) === -1) {\n      destination.push(e);\n    }\n  });\n  return destination;\n}\n/** Return multi select values based on an array of options */\n\n\nfunction getSelectedValues(options) {\n  return Array.from(options).filter(function (el) {\n    return el.selected;\n  }).map(function (el) {\n    return el.value;\n  });\n}\n/** Return the next value for a checkbox */\n\n\nfunction getValueForCheckbox(currentValue, checked, valueProp) {\n  // If the current value was a boolean, return a boolean\n  if (typeof currentValue === 'boolean') {\n    return Boolean(checked);\n  } // If the currentValue was not a boolean we want to return an array\n\n\n  var currentArrayOfValues = [];\n  var isValueInArray = false;\n  var index = -1;\n\n  if (!Array.isArray(currentValue)) {\n    // eslint-disable-next-line eqeqeq\n    if (!valueProp || valueProp == 'true' || valueProp == 'false') {\n      return Boolean(checked);\n    }\n  } else {\n    // If the current value is already an array, use it\n    currentArrayOfValues = currentValue;\n    index = currentValue.indexOf(valueProp);\n    isValueInArray = index >= 0;\n  } // If the checkbox was checked and the value is not already present in the aray we want to add the new value to the array of values\n\n\n  if (checked && valueProp && !isValueInArray) {\n    return currentArrayOfValues.concat(valueProp);\n  } // If the checkbox was unchecked and the value is not in the array, simply return the already existing array of values\n\n\n  if (!isValueInArray) {\n    return currentArrayOfValues;\n  } // If the checkbox was unchecked and the value is in the array, remove the value and return the array\n\n\n  return currentArrayOfValues.slice(0, index).concat(currentArrayOfValues.slice(index + 1));\n} // React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\n// @see https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n\n\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;\n\nfunction useEventCallback(fn) {\n  var ref = useRef(fn); // we copy a ref to the callback scoped to the current state/props on each render\n\n  useIsomorphicLayoutEffect(function () {\n    ref.current = fn;\n  });\n  return useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return ref.current.apply(void 0, args);\n  }, []);\n}\n\nfunction useField(propsOrFieldName) {\n  var formik = useFormikContext();\n  var getFieldProps = formik.getFieldProps,\n      getFieldMeta = formik.getFieldMeta,\n      getFieldHelpers = formik.getFieldHelpers,\n      registerField = formik.registerField,\n      unregisterField = formik.unregisterField;\n  var isAnObject = isObject(propsOrFieldName); // Normalize propsOrFieldName to FieldHookConfig<Val>\n\n  var props = isAnObject ? propsOrFieldName : {\n    name: propsOrFieldName\n  };\n  var fieldName = props.name,\n      validateFn = props.validate;\n  useEffect(function () {\n    if (fieldName) {\n      registerField(fieldName, {\n        validate: validateFn\n      });\n    }\n\n    return function () {\n      if (fieldName) {\n        unregisterField(fieldName);\n      }\n    };\n  }, [registerField, unregisterField, fieldName, validateFn]);\n\n  if (process.env.NODE_ENV !== \"production\") {\n    !formik ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'useField() / <Field /> must be used underneath a <Formik> component or withFormik() higher order component') : invariant(false) : void 0;\n  }\n\n  !fieldName ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Invalid field name. Either pass `useField` a string or an object containing a `name` key.') : invariant(false) : void 0;\n  var fieldHelpers = useMemo(function () {\n    return getFieldHelpers(fieldName);\n  }, [getFieldHelpers, fieldName]);\n  return [getFieldProps(props), getFieldMeta(fieldName), fieldHelpers];\n}\nfunction Field(_ref) {\n  var validate = _ref.validate,\n      name = _ref.name,\n      render = _ref.render,\n      children = _ref.children,\n      is = _ref.as,\n      component = _ref.component,\n      className = _ref.className,\n      props = _objectWithoutPropertiesLoose(_ref, [\"validate\", \"name\", \"render\", \"children\", \"as\", \"component\", \"className\"]);\n\n  var _useFormikContext = useFormikContext(),\n      formik = _objectWithoutPropertiesLoose(_useFormikContext, [\"validate\", \"validationSchema\"]);\n\n  if (process.env.NODE_ENV !== \"production\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(function () {\n      !!render ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"<Field render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Field name=\\\"\" + name + \"\\\" render={({field, form}) => ...} /> with <Field name=\\\"\" + name + \"\\\">{({field, form, meta}) => ...}</Field>\") : invariant(false) : void 0;\n      !!(is && children && isFunction(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <Field as> and <Field children> as a function in the same <Field> component; <Field as> will be ignored.') : invariant(false) : void 0;\n      !!(component && children && isFunction(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <Field component> and <Field children> as a function in the same <Field> component; <Field component> will be ignored.') : invariant(false) : void 0;\n      !!(render && children && !isEmptyChildren(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <Field render> and <Field children> in the same <Field> component; <Field children> will be ignored') : invariant(false) : void 0; // eslint-disable-next-line\n    }, []);\n  } // Register field and field-level validation with parent <Formik>\n\n\n  var registerField = formik.registerField,\n      unregisterField = formik.unregisterField;\n  useEffect(function () {\n    registerField(name, {\n      validate: validate\n    });\n    return function () {\n      unregisterField(name);\n    };\n  }, [registerField, unregisterField, name, validate]);\n  var field = formik.getFieldProps(_extends({\n    name: name\n  }, props));\n  var meta = formik.getFieldMeta(name);\n  var legacyBag = {\n    field: field,\n    form: formik\n  };\n\n  if (render) {\n    return render(_extends({}, legacyBag, {\n      meta: meta\n    }));\n  }\n\n  if (isFunction(children)) {\n    return children(_extends({}, legacyBag, {\n      meta: meta\n    }));\n  }\n\n  if (component) {\n    // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n    if (typeof component === 'string') {\n      var innerRef = props.innerRef,\n          rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n      return createElement(component, _extends({\n        ref: innerRef\n      }, field, rest, {\n        className: className\n      }), children);\n    } // We don't pass `meta` for backwards compat\n\n\n    return createElement(component, _extends({\n      field: field,\n      form: formik\n    }, props, {\n      className: className\n    }), children);\n  } // default to input here so we can check for both `as` and `children` above\n\n\n  var asElement = is || 'input';\n\n  if (typeof asElement === 'string') {\n    var _innerRef = props.innerRef,\n        _rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n    return createElement(asElement, _extends({\n      ref: _innerRef\n    }, field, _rest, {\n      className: className\n    }), children);\n  }\n\n  return createElement(asElement, _extends({}, field, props, {\n    className: className\n  }), children);\n}\n\nvar Form = /*#__PURE__*/forwardRef(function (props, ref) {\n  // iOS needs an \"action\" attribute for nice input: https://stackoverflow.com/a/39485162/406725\n  // We default the action to \"#\" in case the preventDefault fails (just updates the URL hash)\n  var action = props.action,\n      rest = _objectWithoutPropertiesLoose(props, [\"action\"]);\n\n  var _action = action != null ? action : '#';\n\n  var _useFormikContext = useFormikContext(),\n      handleReset = _useFormikContext.handleReset,\n      handleSubmit = _useFormikContext.handleSubmit;\n\n  return createElement(\"form\", _extends({\n    onSubmit: handleSubmit,\n    ref: ref,\n    onReset: handleReset,\n    action: _action\n  }, rest));\n});\nForm.displayName = 'Form';\n\n/**\r\n * A public higher-order component to access the imperative API\r\n */\n\nfunction withFormik(_ref) {\n  var _ref$mapPropsToValues = _ref.mapPropsToValues,\n      mapPropsToValues = _ref$mapPropsToValues === void 0 ? function (vanillaProps) {\n    var val = {};\n\n    for (var k in vanillaProps) {\n      if (vanillaProps.hasOwnProperty(k) && typeof vanillaProps[k] !== 'function') {\n        // @todo TypeScript fix\n        val[k] = vanillaProps[k];\n      }\n    }\n\n    return val;\n  } : _ref$mapPropsToValues,\n      config = _objectWithoutPropertiesLoose(_ref, [\"mapPropsToValues\"]);\n\n  return function createFormik(Component$1) {\n    var componentDisplayName = Component$1.displayName || Component$1.name || Component$1.constructor && Component$1.constructor.name || 'Component';\n    /**\r\n     * We need to use closures here for to provide the wrapped component's props to\r\n     * the respective withFormik config methods.\r\n     */\n\n    var C = /*#__PURE__*/function (_React$Component) {\n      _inheritsLoose(C, _React$Component);\n\n      function C() {\n        var _this;\n\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n\n        _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n        _this.validate = function (values) {\n          return config.validate(values, _this.props);\n        };\n\n        _this.validationSchema = function () {\n          return isFunction(config.validationSchema) ? config.validationSchema(_this.props) : config.validationSchema;\n        };\n\n        _this.handleSubmit = function (values, actions) {\n          return config.handleSubmit(values, _extends({}, actions, {\n            props: _this.props\n          }));\n        };\n\n        _this.renderFormComponent = function (formikProps) {\n          return createElement(Component$1, _extends({}, _this.props, formikProps));\n        };\n\n        return _this;\n      }\n\n      var _proto = C.prototype;\n\n      _proto.render = function render() {\n        var _this$props = this.props,\n            props = _objectWithoutPropertiesLoose(_this$props, [\"children\"]);\n\n        return createElement(Formik, _extends({}, props, config, {\n          validate: config.validate && this.validate,\n          validationSchema: config.validationSchema && this.validationSchema,\n          initialValues: mapPropsToValues(this.props),\n          initialStatus: config.mapPropsToStatus && config.mapPropsToStatus(this.props),\n          initialErrors: config.mapPropsToErrors && config.mapPropsToErrors(this.props),\n          initialTouched: config.mapPropsToTouched && config.mapPropsToTouched(this.props),\n          onSubmit: this.handleSubmit,\n          children: this.renderFormComponent\n        }));\n      };\n\n      return C;\n    }(Component);\n\n    C.displayName = \"WithFormik(\" + componentDisplayName + \")\";\n    return hoistNonReactStatics(C, Component$1 // cast type to ComponentClass (even if SFC)\n    );\n  };\n}\n\n/**\r\n * Connect any component to Formik context, and inject as a prop called `formik`;\r\n * @param Comp React Component\r\n */\n\nfunction connect(Comp) {\n  var C = function C(props) {\n    return createElement(FormikConsumer, null, function (formik) {\n      !!!formik ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Formik context is undefined, please verify you are rendering <Form>, <Field>, <FastField>, <FieldArray>, or your custom context-using component as a child of a <Formik> component. Component name: \" + Comp.name) : invariant(false) : void 0;\n      return createElement(Comp, _extends({}, props, {\n        formik: formik\n      }));\n    });\n  };\n\n  var componentDisplayName = Comp.displayName || Comp.name || Comp.constructor && Comp.constructor.name || 'Component'; // Assign Comp to C.WrappedComponent so we can access the inner component in tests\n  // For example, <Field.WrappedComponent /> gets us <FieldInner/>\n\n  C.WrappedComponent = Comp;\n  C.displayName = \"FormikConnect(\" + componentDisplayName + \")\";\n  return hoistNonReactStatics(C, Comp // cast type to ComponentClass (even if SFC)\n  );\n}\n\n/**\r\n * Some array helpers!\r\n */\n\nvar move = function move(array, from, to) {\n  var copy = copyArrayLike(array);\n  var value = copy[from];\n  copy.splice(from, 1);\n  copy.splice(to, 0, value);\n  return copy;\n};\nvar swap = function swap(arrayLike, indexA, indexB) {\n  var copy = copyArrayLike(arrayLike);\n  var a = copy[indexA];\n  copy[indexA] = copy[indexB];\n  copy[indexB] = a;\n  return copy;\n};\nvar insert = function insert(arrayLike, index, value) {\n  var copy = copyArrayLike(arrayLike);\n  copy.splice(index, 0, value);\n  return copy;\n};\nvar replace = function replace(arrayLike, index, value) {\n  var copy = copyArrayLike(arrayLike);\n  copy[index] = value;\n  return copy;\n};\n\nvar copyArrayLike = function copyArrayLike(arrayLike) {\n  if (!arrayLike) {\n    return [];\n  } else if (Array.isArray(arrayLike)) {\n    return [].concat(arrayLike);\n  } else {\n    var maxIndex = Object.keys(arrayLike).map(function (key) {\n      return parseInt(key);\n    }).reduce(function (max, el) {\n      return el > max ? el : max;\n    }, 0);\n    return Array.from(_extends({}, arrayLike, {\n      length: maxIndex + 1\n    }));\n  }\n};\n\nvar createAlterationHandler = function createAlterationHandler(alteration, defaultFunction) {\n  var fn = typeof alteration === 'function' ? alteration : defaultFunction;\n  return function (data) {\n    if (Array.isArray(data) || isObject(data)) {\n      var clone = copyArrayLike(data);\n      return fn(clone);\n    } // This can be assumed to be a primitive, which\n    // is a case for top level validation errors\n\n\n    return data;\n  };\n};\n\nvar FieldArrayInner = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(FieldArrayInner, _React$Component);\n\n  function FieldArrayInner(props) {\n    var _this;\n\n    _this = _React$Component.call(this, props) || this; // We need TypeScript generics on these, so we'll bind them in the constructor\n    // @todo Fix TS 3.2.1\n\n    _this.updateArrayField = function (fn, alterTouched, alterErrors) {\n      var _this$props = _this.props,\n          name = _this$props.name,\n          setFormikState = _this$props.formik.setFormikState;\n      setFormikState(function (prevState) {\n        var updateErrors = createAlterationHandler(alterErrors, fn);\n        var updateTouched = createAlterationHandler(alterTouched, fn); // values fn should be executed before updateErrors and updateTouched,\n        // otherwise it causes an error with unshift.\n\n        var values = setIn(prevState.values, name, fn(getIn(prevState.values, name)));\n        var fieldError = alterErrors ? updateErrors(getIn(prevState.errors, name)) : undefined;\n        var fieldTouched = alterTouched ? updateTouched(getIn(prevState.touched, name)) : undefined;\n\n        if (isEmptyArray(fieldError)) {\n          fieldError = undefined;\n        }\n\n        if (isEmptyArray(fieldTouched)) {\n          fieldTouched = undefined;\n        }\n\n        return _extends({}, prevState, {\n          values: values,\n          errors: alterErrors ? setIn(prevState.errors, name, fieldError) : prevState.errors,\n          touched: alterTouched ? setIn(prevState.touched, name, fieldTouched) : prevState.touched\n        });\n      });\n    };\n\n    _this.push = function (value) {\n      return _this.updateArrayField(function (arrayLike) {\n        return [].concat(copyArrayLike(arrayLike), [cloneDeep(value)]);\n      }, false, false);\n    };\n\n    _this.handlePush = function (value) {\n      return function () {\n        return _this.push(value);\n      };\n    };\n\n    _this.swap = function (indexA, indexB) {\n      return _this.updateArrayField(function (array) {\n        return swap(array, indexA, indexB);\n      }, true, true);\n    };\n\n    _this.handleSwap = function (indexA, indexB) {\n      return function () {\n        return _this.swap(indexA, indexB);\n      };\n    };\n\n    _this.move = function (from, to) {\n      return _this.updateArrayField(function (array) {\n        return move(array, from, to);\n      }, true, true);\n    };\n\n    _this.handleMove = function (from, to) {\n      return function () {\n        return _this.move(from, to);\n      };\n    };\n\n    _this.insert = function (index, value) {\n      return _this.updateArrayField(function (array) {\n        return insert(array, index, value);\n      }, function (array) {\n        return insert(array, index, null);\n      }, function (array) {\n        return insert(array, index, null);\n      });\n    };\n\n    _this.handleInsert = function (index, value) {\n      return function () {\n        return _this.insert(index, value);\n      };\n    };\n\n    _this.replace = function (index, value) {\n      return _this.updateArrayField(function (array) {\n        return replace(array, index, value);\n      }, false, false);\n    };\n\n    _this.handleReplace = function (index, value) {\n      return function () {\n        return _this.replace(index, value);\n      };\n    };\n\n    _this.unshift = function (value) {\n      var length = -1;\n\n      _this.updateArrayField(function (array) {\n        var arr = array ? [value].concat(array) : [value];\n        length = arr.length;\n        return arr;\n      }, function (array) {\n        return array ? [null].concat(array) : [null];\n      }, function (array) {\n        return array ? [null].concat(array) : [null];\n      });\n\n      return length;\n    };\n\n    _this.handleUnshift = function (value) {\n      return function () {\n        return _this.unshift(value);\n      };\n    };\n\n    _this.handleRemove = function (index) {\n      return function () {\n        return _this.remove(index);\n      };\n    };\n\n    _this.handlePop = function () {\n      return function () {\n        return _this.pop();\n      };\n    };\n\n    _this.remove = _this.remove.bind(_assertThisInitialized(_this));\n    _this.pop = _this.pop.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  var _proto = FieldArrayInner.prototype;\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.validateOnChange && this.props.formik.validateOnChange && !isEqual(getIn(prevProps.formik.values, prevProps.name), getIn(this.props.formik.values, this.props.name))) {\n      this.props.formik.validateForm(this.props.formik.values);\n    }\n  };\n\n  _proto.remove = function remove(index) {\n    // We need to make sure we also remove relevant pieces of `touched` and `errors`\n    var result;\n    this.updateArrayField( // so this gets call 3 times\n    function (array) {\n      var copy = array ? copyArrayLike(array) : [];\n\n      if (!result) {\n        result = copy[index];\n      }\n\n      if (isFunction(copy.splice)) {\n        copy.splice(index, 1);\n      } // if the array only includes undefined values we have to return an empty array\n\n\n      return isFunction(copy.every) ? copy.every(function (v) {\n        return v === undefined;\n      }) ? [] : copy : copy;\n    }, true, true);\n    return result;\n  };\n\n  _proto.pop = function pop() {\n    // Remove relevant pieces of `touched` and `errors` too!\n    var result;\n    this.updateArrayField( // so this gets call 3 times\n    function (array) {\n      var tmp = array.slice();\n\n      if (!result) {\n        result = tmp && tmp.pop && tmp.pop();\n      }\n\n      return tmp;\n    }, true, true);\n    return result;\n  };\n\n  _proto.render = function render() {\n    var arrayHelpers = {\n      push: this.push,\n      pop: this.pop,\n      swap: this.swap,\n      move: this.move,\n      insert: this.insert,\n      replace: this.replace,\n      unshift: this.unshift,\n      remove: this.remove,\n      handlePush: this.handlePush,\n      handlePop: this.handlePop,\n      handleSwap: this.handleSwap,\n      handleMove: this.handleMove,\n      handleInsert: this.handleInsert,\n      handleReplace: this.handleReplace,\n      handleUnshift: this.handleUnshift,\n      handleRemove: this.handleRemove\n    };\n\n    var _this$props2 = this.props,\n        component = _this$props2.component,\n        render = _this$props2.render,\n        children = _this$props2.children,\n        name = _this$props2.name,\n        _this$props2$formik = _this$props2.formik,\n        restOfFormik = _objectWithoutPropertiesLoose(_this$props2$formik, [\"validate\", \"validationSchema\"]);\n\n    var props = _extends({}, arrayHelpers, {\n      form: restOfFormik,\n      name: name\n    });\n\n    return component ? createElement(component, props) : render ? render(props) : children // children come last, always called\n    ? typeof children === 'function' ? children(props) : !isEmptyChildren(children) ? Children.only(children) : null : null;\n  };\n\n  return FieldArrayInner;\n}(Component);\n\nFieldArrayInner.defaultProps = {\n  validateOnChange: true\n};\nvar FieldArray = /*#__PURE__*/connect(FieldArrayInner);\n\nvar ErrorMessageImpl = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ErrorMessageImpl, _React$Component);\n\n  function ErrorMessageImpl() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n\n  var _proto = ErrorMessageImpl.prototype;\n\n  _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n    if (getIn(this.props.formik.errors, this.props.name) !== getIn(props.formik.errors, this.props.name) || getIn(this.props.formik.touched, this.props.name) !== getIn(props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        component = _this$props.component,\n        formik = _this$props.formik,\n        render = _this$props.render,\n        children = _this$props.children,\n        name = _this$props.name,\n        rest = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"formik\", \"render\", \"children\", \"name\"]);\n\n    var touch = getIn(formik.touched, name);\n    var error = getIn(formik.errors, name);\n    return !!touch && !!error ? render ? isFunction(render) ? render(error) : null : children ? isFunction(children) ? children(error) : null : component ? createElement(component, rest, error) : error : null;\n  };\n\n  return ErrorMessageImpl;\n}(Component);\n\nvar ErrorMessage = /*#__PURE__*/connect(ErrorMessageImpl);\n\n/**\r\n * Custom Field component for quickly hooking into Formik\r\n * context and wiring up forms.\r\n */\n\nvar FastFieldInner = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(FastFieldInner, _React$Component);\n\n  function FastFieldInner(props) {\n    var _this;\n\n    _this = _React$Component.call(this, props) || this;\n    var render = props.render,\n        children = props.children,\n        component = props.component,\n        is = props.as,\n        name = props.name;\n    !!render ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"<FastField render> has been deprecated. Please use a child callback function instead: <FastField name={\" + name + \"}>{props => ...}</FastField> instead.\") : invariant(false) : void 0;\n    !!(component && render) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <FastField component> and <FastField render> in the same <FastField> component; <FastField component> will be ignored') : invariant(false) : void 0;\n    !!(is && children && isFunction(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <FastField as> and <FastField children> as a function in the same <FastField> component; <FastField as> will be ignored.') : invariant(false) : void 0;\n    !!(component && children && isFunction(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <FastField component> and <FastField children> as a function in the same <FastField> component; <FastField component> will be ignored.') : invariant(false) : void 0;\n    !!(render && children && !isEmptyChildren(children)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'You should not use <FastField render> and <FastField children> in the same <FastField> component; <FastField children> will be ignored') : invariant(false) : void 0;\n    return _this;\n  }\n\n  var _proto = FastFieldInner.prototype;\n\n  _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n    if (this.props.shouldUpdate) {\n      return this.props.shouldUpdate(props, this.props);\n    } else if (props.name !== this.props.name || getIn(props.formik.values, this.props.name) !== getIn(this.props.formik.values, this.props.name) || getIn(props.formik.errors, this.props.name) !== getIn(this.props.formik.errors, this.props.name) || getIn(props.formik.touched, this.props.name) !== getIn(this.props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length || props.formik.isSubmitting !== this.props.formik.isSubmitting) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n\n  _proto.componentDidMount = function componentDidMount() {\n    // Register the Field with the parent Formik. Parent will cycle through\n    // registered Field's validate fns right prior to submit\n    this.props.formik.registerField(this.props.name, {\n      validate: this.props.validate\n    });\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.name !== prevProps.name) {\n      this.props.formik.unregisterField(prevProps.name);\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate\n      });\n    }\n\n    if (this.props.validate !== prevProps.validate) {\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate\n      });\n    }\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.props.formik.unregisterField(this.props.name);\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        name = _this$props.name,\n        render = _this$props.render,\n        is = _this$props.as,\n        children = _this$props.children,\n        component = _this$props.component,\n        formik = _this$props.formik,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"validate\", \"name\", \"render\", \"as\", \"children\", \"component\", \"shouldUpdate\", \"formik\"]);\n\n    var restOfFormik = _objectWithoutPropertiesLoose(formik, [\"validate\", \"validationSchema\"]);\n\n    var field = formik.getFieldProps(_extends({\n      name: name\n    }, props));\n    var meta = {\n      value: getIn(formik.values, name),\n      error: getIn(formik.errors, name),\n      touched: !!getIn(formik.touched, name),\n      initialValue: getIn(formik.initialValues, name),\n      initialTouched: !!getIn(formik.initialTouched, name),\n      initialError: getIn(formik.initialErrors, name)\n    };\n    var bag = {\n      field: field,\n      meta: meta,\n      form: restOfFormik\n    };\n\n    if (render) {\n      return render(bag);\n    }\n\n    if (isFunction(children)) {\n      return children(bag);\n    }\n\n    if (component) {\n      // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n      if (typeof component === 'string') {\n        var innerRef = props.innerRef,\n            rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n        return createElement(component, _extends({\n          ref: innerRef\n        }, field, rest), children);\n      } // We don't pass `meta` for backwards compat\n\n\n      return createElement(component, _extends({\n        field: field,\n        form: formik\n      }, props), children);\n    } // default to input here so we can check for both `as` and `children` above\n\n\n    var asElement = is || 'input';\n\n    if (typeof asElement === 'string') {\n      var _innerRef = props.innerRef,\n          _rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n      return createElement(asElement, _extends({\n        ref: _innerRef\n      }, field, _rest), children);\n    }\n\n    return createElement(asElement, _extends({}, field, props), children);\n  };\n\n  return FastFieldInner;\n}(Component);\n\nvar FastField = /*#__PURE__*/connect(FastFieldInner);\n\nexport { ErrorMessage, FastField, Field, FieldArray, Form, Formik, FormikConsumer, FormikContext, FormikProvider, connect, getActiveElement, getIn, insert, isEmptyArray, isEmptyChildren, isFunction, isInputEvent, isInteger, isNaN$1 as isNaN, isObject, isPromise, isString, move, prepareDataForValidation, replace, setIn, setNestedObjectValues, swap, useField, useFormik, useFormikContext, validateYupSchema, withFormik, yupToFormErrors };\n//# sourceMappingURL=formik.esm.js.map\n", "import { useRef, useEffect, useCallback } from \"react\";\nimport pick from \"lodash/pick\";\nimport { useField } from \"formik\";\nimport { \n\tcallFormOnChange, \n\tcallFormOnSubmit, \n\tcallFormOnSubmitFail, \n\tcallFormOnSubmitSuccess,\n\tcallFormValidate,\n\tcallFieldOnBlur,\n\tformPropTypes,\n\tfieldPropTypes,\n\tdeprecatedFormPropTypes,\n\tdeprecatedFieldPropTypes\n} from \"reactor2/src/Form/_adapters/FormInterface\";\n\nexport const forbiddenArgument = { error: \"Deprecated usage! Please contact the framework team.\" };\n\nconst initialLink = `https://formik.org/docs/api/formik#initialvalues-values`;\n\nconst forbiddenCall = (method) => () => console.error(\n\t`Its no longer possible to call ${method} because it uses a non-standard funcionality\\nPlease contact the framework team.`\n);\n\nexport const allowedFormPropTypes = {\n\t...deprecatedFormPropTypes,\n\t...formPropTypes,\n\tinitialValues: (props, propName) => {\n\t\t/* istanbul ignore next */\n\t\tif (!props[propName] || Object.keys(props[propName]).length === 0) {\n\t\t\treturn new Error(\n\t\t\t\t`initialValues on Form/EasyForm should have all fields initialized otherwise will throw uncontroled component warning: ${initialLink}`\n\t\t\t);\n\t\t}\n\t}\n};\n\nexport const allowedFieldPropTypes = {\n\t...deprecatedFieldPropTypes,\n\t...fieldPropTypes\n};\n\nconst getFormSubmitParameters = (parameters) => ({\n\t...parameters,\n\tdispatch: forbiddenCall(\"dispatch\"), \n\tformProps: forbiddenArgument\n});\n\nexport function useFormikFormAdaptations(props, formRef) {\n\tconst { initialValues, onChange, onSubmit, onSubmitFail, onSubmitSuccess, validate } = props;\n\tconst initialValuesRef = useRef(initialValues);\n\tconst onlyInitializedValues = { ...initialValuesRef.current, ...pick(initialValues, Object.keys(initialValuesRef.current)) };\n\n\tuseEffect(() => {\n\t\tconst initialKeys = Object.keys(initialValuesRef.current);\n\t\tconst actualKeys = Object.keys(initialValues);\n\n\t\t/* istanbul ignore next */\n\t\tif (initialKeys.some((value) => actualKeys.indexOf(value) === -1)) {\n\t\t\tconsole.error(\n\t\t\t\t`You cannot add keys to initialValues, all keys must exist when form first mounts: ${initialLink}`\n\t\t\t);\n\t\t}\n\n\t}, [initialValues, initialValuesRef]);\n\n\tconst handleChange = useCallback((values) => {\n\t\tcallFormOnChange(\n\t\t\tonChange,\n\t\t\tgetFormSubmitParameters({\n\t\t\t\tvalues,\n\t\t\t\tpreviousValues: forbiddenArgument\n\t\t\t})\n\t\t);\n\t}, [onChange]);\n\n\tconst handleSubmit = useCallback(async(values, { setSubmitting }) => {\n\t\tconst formSubmitParameters = getFormSubmitParameters({ values });\n\n\t\ttry {\n\t\t\tawait callFormOnSubmit(onSubmit, formSubmitParameters);\n\t\t\tsetSubmitting(false);\n\t\t\tcallFormOnSubmitSuccess(onSubmitSuccess, formSubmitParameters);\n\t\t} catch (e) {\n\t\t\tsetSubmitting(false);\n\n\t\t\tif (e instanceof Error) {\n\t\t\t\tconsole.error(e);\n\t\t\t} else {\n\t\t\t\tfor (let field in e) {\n\t\t\t\t\tformRef.current.setFieldError(field, e[field]);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcallFormOnSubmitFail(onSubmitFail, {\n\t\t\t\t...formSubmitParameters,\n\t\t\t\terrors: forbiddenArgument, \n\t\t\t\tsubmitError: forbiddenArgument\n\t\t\t});\n\t\t}\n\t}, [onSubmit, onSubmitSuccess, onSubmitFail, formRef]);\n\n\tconst handleValidate = useCallback((values) => callFormValidate(validate, {\n\t\tvalues, \n\t\tprops: forbiddenArgument\n\t}), [validate]);\n\n\treturn {\n\t\tinitialValues: onlyInitializedValues,\n\t\tinitialValuesRef,\n\t\tonChange: handleChange,\n\t\tonSubmit: handleSubmit,\n\t\tvalidate: handleValidate\n\t};\n}\n\nfunction getValidationState(error, state) {\n\tif (state) return state;\n\n\treturn error ? \"error\" : undefined;\n}\n\nfunction getValidationMessage(error, message) {\n\tif (message) return message;\n\n\treturn error;\n}\n\nfunction executeValidattors(value, validators) {\n\treturn validators.reduce(\n\t\t(result, validator) => result || validator(\n\t\t\tvalue, \n\t\t\tforbiddenArgument, \n\t\t\tforbiddenArgument, \n\t\t\tforbiddenArgument.error\n\t\t),\n\t\tundefined\n\t);\n}\n\nexport function useFormikFieldAdaptations(props) {\n\tconst { format, onChange, onBlur, validationMessage, validationState, validate, validators, name } = props;\n\tconst ref = useRef({ validation: () => null});\n\n\tconst registerValidationHandler = useCallback((fn) => {\n\t\tref.current.validation = fn;\n\t}, []);\n\n\tconst executeValidation = useCallback((value) => {\n\t\tconst validationFunctions = [].concat(validate)\n\t\t\t.concat(validators)\n\t\t\t.concat((...args) => ref.current.validation(...args))\n\t\t\t.filter(Boolean);\n\n\t\treturn executeValidattors(value, validationFunctions);\n\t}, [validate, validators]);\n\n\tconst [ field, meta, helpers ] = useField({ name, validate: executeValidation });\n\tconst { value, error } = meta;\n\tconst { setValue, setTouched, setError } = helpers;\n\n\tconst handleValidation = useCallback((val) => {\n\t\tsetError(executeValidation(val || value));\n\t}, [value, setError, executeValidation]);\n\n\tconst handleChange = useCallback((val) => {\n\t\tsetValue(val);\n\t\thandleValidation(val);\n\n\t\tif (onChange) {\n\t\t\tonChange(val);\n\t\t}\n\t}, [onChange, setValue, handleValidation]);\n\n\tconst handleBlur = useCallback((event) => {\n\t\tsetTouched(true);\n\t\thandleValidation();\n\n\t\t/* istanbul ignore else */\n\t\tif (onBlur) {\n\t\t\tcallFieldOnBlur(onBlur, {\n\t\t\t\tevent, \n\t\t\t\tnewValue: forbiddenArgument, \n\t\t\t\tpreviousValues: forbiddenArgument, \n\t\t\t\tname: forbiddenArgument.error\n\t\t\t});\n\t\t}\n\t}, [onBlur, setTouched, handleValidation]);\n\n\treturn {\n\t\tname: field.name,\n\t\tonChange: handleChange,\n\t\tonBlur: handleBlur,\n\t\tvalue: format(value),\n\t\tvalidationMessage: getValidationMessage(error, validationMessage),\n\t\tvalidationState: getValidationState(error, validationState),\n\t\tregisterValidationHandler\n\t};\n}", "import React, { useRef, useEffect, createElement, useCallback } from \"react\";\nimport omit from \"lodash/omit\";\nimport identity from \"lodash/identity\";\nimport { Provider } from \"react-redux\";\nimport { Form, Formik, FieldArray, getIn, useFormikContext } from \"formik\";\nimport Store from \"reactor2/src/store/store\";\nimport { getFormFactoryProps, getFieldArrayFactoryProps } from \"reactor2/src/Form/_adapters/FormInterface\";\nimport { \n\tuseFormikFormAdaptations, \n\tuseFormikFieldAdaptations, \n\tallowedFormPropTypes, \n\tallowedFieldPropTypes \n} from \"reactor2/src/Form/_adapters/formik/adaptation\";\n\nfunction FormikOnChangeAdapter({ values, onChange }) {\n\tuseEffect(() => onChange(values), [values, onChange]);\n\n\treturn null;\n}\n\nfunction FormikForm(props) {\n\tconst { register, form, children } = props;\n\tconst formik = useRef();\n\tconst { initialValuesRef, initialValues, onSubmit, onChange, validate } = useFormikFormAdaptations(props, formik);\n\n\tuseEffect(\n\t\t() => register({\n\t\t\tsubmit: () => formik.current.submitForm(),\n\t\t\tsubmitForm: () => formik.current.submitForm(),\n\t\t\treset: () => formik.current.handleReset(),\n\t\t\tgetValues: () => formik.current.values,\n\t\t\tgetInitialValues: () => initialValuesRef.current,\n\t\t\tgetDirty: () => formik.current.dirty,\n\t\t\tgetValid: () => formik.current.isValid,\n\t\t\tgetErrors: () => formik.current.errors,\n\t\t\tsetFieldValue: (field, value, shouldValidate) => formik.current.setFieldValue(field, value, shouldValidate)\n\t\t}),\n\t\t[register, formik, initialValuesRef]\n\t);\n\n\treturn (\n\t\t<Provider store={Store}>\n\t\t\t<Formik\n\t\t\t\t{...props}\n\t\t\t\tonSubmit={onSubmit}\n\t\t\t\tinitialValues={initialValues}\n\t\t\t\tvalidate={validate}\n\t\t\t\tinnerRef={formik}\n\t\t\t\tvalidateOnChange={false}\n\t\t\t\tvalidateOnBlur={false}\n\t\t\t>\n\t\t\t\t{\n\t\t\t\t\t({ isValid, dirty, handleSubmit, initialValues, handleReset, values, ...unsafeProps }) => (\n\t\t\t\t\t\t<>\n\t\t\t\t\t\t\t<Form>\n\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\tchildren || createElement(\n\t\t\t\t\t\t\t\t\t\tform,\n\t\t\t\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\t\t\t\t...props,\n\t\t\t\t\t\t\t\t\t\t\t...getFormFactoryProps(unsafeProps, isValid, dirty, handleSubmit, initialValues, handleReset)\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t</Form>\n\t\t\t\t\t\t\t<FormikOnChangeAdapter values={values} onChange={onChange} />\n\t\t\t\t\t\t</>\n\t\t\t\t\t)\n\t\t\t\t}\n\t\t\t</Formik>\n\t\t</Provider>\n\t);\n}\n\nFormikForm.propTypes = allowedFormPropTypes;\n\nFormikForm.defaultProps = {\n\tinitialValues: {}\n};\n\nfunction FormikField({\n\twrapperStyle,\n\tcomponent: Component,\n\trequired,\n\tinnerRef,\n\t...props\n}) {\n\tconst { name, onChange, onBlur, value, validationMessage, validationState, registerValidationHandler } = useFormikFieldAdaptations(props);\n\tlet otherProps;\n\n\tif (Component.registerValidationHandler)\n\t\totherProps = { registerValidationHandler };\n\n\treturn (\n\t\t<div style={wrapperStyle}>\n\t\t\t<Component\n\t\t\t\t{...omit(props, \"component\", \"format\", \"validate\")}\n\t\t\t\tid={name}\n\t\t\t\tname={name}\n\t\t\t\tonChange={onChange}\n\t\t\t\tonBlur={onBlur}\n\t\t\t\tvalue={value}\n\t\t\t\trequired={required}\n\t\t\t\tvalidationMessage={validationMessage}\n\t\t\t\tvalidationState={validationState}\n\t\t\t\tref={innerRef}\n\t\t\t\t{...otherProps}\n\t\t\t/>\n\t\t</div>\n\t);\n}\n\nFormikField.propTypes = allowedFieldPropTypes;\n\nFormikField.defaultProps = {\n\tformat: identity\n};\n\nfunction FormikFieldArray(props) {\n\tconst { component, name } = props;\n\n\tconst Component = useCallback(\n\t\t({ form, push, swap, move, insert, unshift, remove, pop, replace }) => createElement(\n\t\t\tcomponent, \n\t\t\tgetFieldArrayFactoryProps(\n\t\t\t\tgetIn(form.values, name),\n\t\t\t\tpush,\n\t\t\t\tswap,\n\t\t\t\tmove,\n\t\t\t\tinsert,\n\t\t\t\tunshift,\n\t\t\t\tremove,\n\t\t\t\tpop,\n\t\t\t\treplace\n\t\t\t)\n\t\t),\n\t\t[component, name]\n\t);\n\n\treturn <FieldArray {...props} component={component ? Component : undefined} />;\n}\n\nfunction useFormikForm() {\n\tconst { values, touched, setFieldValue, setFieldTouched, isValid } = useFormikContext();\n\n\treturn {\n\t\tvalues,\n\t\ttouched,\n\t\tsetFieldValue,\n\t\tsetFieldTouched,\n\t\tisValid\n\t};\n}\n\nexport default {\n\tForm: FormikForm,\n\tField: FormikField,\n\tFieldArray: FormikFieldArray,\n\tuseForm: useFormikForm\n};", "import CounterSign from 'reactor2/src/helpers/CounterSign/CounterSign';\n\nexport default CounterSign;"], "names": ["useRef", "useEffect", "useCallback", "pick", "useField", "callFormOnChange", "callFormOnSubmit", "callFormOnSubmitFail", "callFormOnSubmitSuccess", "callFormValidate", "callFieldOnBlur", "formPropTypes", "fieldPropTypes", "deprecatedFormPropTypes", "deprecatedFieldPropTypes", "forbiddenArgument", "initialLink", "forbiddenCall", "method", "console", "allowedFormPropTypes", "props", "propName", "Object", "Error", "allowedFieldPropTypes", "getFormSubmitParameters", "parameters", "useFormikFormAdaptations", "formRef", "initialValues", "onChange", "onSubmit", "onSubmitFail", "onSubmitSuccess", "validate", "initialValuesRef", "onlyInitializedValues", "initialKeys", "actualKeys", "value", "handleChange", "values", "handleSubmit", "setSubmitting", "formSubmitParameters", "e", "field", "handleValidate", "getValidationState", "error", "state", "undefined", "getValidationMessage", "message", "executeValidattors", "validators", "result", "validator", "useFormikFieldAdaptations", "format", "onBlur", "validationMessage", "validationState", "name", "ref", "registerValidationHandler", "fn", "executeValidation", "_ref_current", "validationFunctions", "args", "Boolean", "_useField", "meta", "helpers", "setValue", "setTouched", "setError", "handleValidation", "val", "handleBlur", "event", "React", "createElement", "omit", "identity", "Provider", "Form", "<PERSON><PERSON>", "FieldArray", "getIn", "useFormikContext", "Store", "getFormFactoryProps", "getFieldArrayFactoryProps", "FormikOnChangeAdapter", "param", "FormikForm", "register", "form", "children", "formik", "_useFormikFormAdaptations", "shouldValidate", "<PERSON><PERSON><PERSON><PERSON>", "dirty", "handleReset", "unsafeProps", "FormikField", "_param", "wrapperStyle", "Component", "required", "innerRef", "_useFormikFieldAdaptations", "otherProps", "FormikFieldArray", "component", "push", "swap", "move", "insert", "unshift", "remove", "pop", "replace", "useFormikForm", "_useFormikContext", "touched", "setFieldValue", "setFieldTouched", "CounterSign"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAkC;AACkB;AACR;AAC+I;AAClJ;AACJ;AACD;AACE;AACqB;;AAE3D;AACA;AACA,oBAAoB,sBAAsB;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,cAAc,uBAAuB;AACrC;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,iCAAiC,oDAAa;AAC9C;AACA;AACA;AACA;AACA,eAAe,iDAAU;AACzB,cAAc,KAAqC,GAAG,wDAAS,6HAA6H,CAAgB;AAC5M;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,SAAS,iDAAc;AACvB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,WAAW;AACtB,YAAY;AACZ;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,aAAa,4DAAM;;AAEnB;AACA;AACA,IAAI;;;AAGJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,YAAY,2DAAK,OAAO;;AAExB;AACA;AACA,kBAAkB,4DAAM;;AAExB,SAAS,0BAA0B;AACnC;AACA;;AAEA;AACA,qCAAqC,2DAAK;AAC1C,MAAM;AACN;AACA;AACA;AACA,IAAI;;;AAGJ;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,uDAAuD,0BAA0B;AACjF;AACA;;AAEA;AACA;AACA,gCAAgC;AAChC;AACA,+BAA+B,kBAAkB,UAAU,WAAW;;AAEtE;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA,UAAU,yDAAO;AACjB;AACA;;AAEA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA,wBAAwB;;AAExB;AACA;;AAEA;AACA,wBAAwB;AACxB;AACA;AACA;AACA,OAAO;;AAEP;AACA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA,wBAAwB;AACxB;AACA,OAAO;;AAEP;AACA;AACA;AACA,EAAE;;;AAGF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH,sBAAsB,6CAAM;AAC5B,sBAAsB,6CAAM;AAC5B,uBAAuB,6CAAM;AAC7B,sBAAsB,6CAAM;AAC5B,kBAAkB,6CAAM;AACxB,sBAAsB,6CAAM,GAAG;;AAE/B,MAAM,IAAqC;AAC3C;AACA,IAAI,gDAAS;AACb,iDAAiD,KAAqC,GAAG,wDAAS,uJAAuJ,CAAgB,WAAW;AACpR,KAAK;AACL;;AAEA,EAAE,gDAAS;AACX;AACA;AACA;AACA;AACA,GAAG;;AAEH,wBAAwB,+CAAQ;AAChC;;AAEA,iBAAiB,6CAAM;AACvB,YAAY,+DAAS;AACrB,YAAY,+DAAS;AACrB,aAAa,+DAAS;AACtB,YAAY,+DAAS;AACrB;AACA;AACA;AACA,GAAG;AACH;AACA,iBAAiB,kDAAW;AAC5B;AACA,oDAAoD;;AAEpD;AACA;AACA,KAAK;AACL,GAAG;AACH,2BAA2B,kDAAW;AACtC;AACA;;AAEA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,SAAS;AACT,cAAc,IAAqC;AACnD;AACA;;AAEA;AACA,SAAS;AACT,QAAQ;AACR;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;;AAEA,4BAA4B,kDAAW;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA,cAAc,IAAqC;AACnD;AACA;;AAEA;AACA;AACA,OAAO;AACP,KAAK;AACL,GAAG;AACH,sCAAsC,kDAAW;AACjD;AACA;AACA,KAAK;AACL,GAAG;AACH,iCAAiC,kDAAW;AAC5C;AACA;AACA,KAAK,GAAG;;AAER;AACA;AACA,KAAK,0DAA0D,qBAAqB;;AAEpF;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,OAAO,IAAI;AACX,KAAK;AACL,GAAG,oCAAoC;;AAEvC,0BAA0B,kDAAW;AACrC,mHAAmH,kDAAkD;AACrK;AACA;AACA;AACA,2BAA2B,qDAAa;AACxC;AACA,OAAO;AACP;AACA,KAAK;AACL,GAAG,gHAAgH;;AAEnH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA,KAAK;AACL,GAAG;AACH,EAAE,gDAAS;AACX,yDAAyD,yDAAO;AAChE;AACA;AACA,GAAG;AACH,kBAAkB,kDAAW;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;AACH,EAAE,gDAAS;AACX,uCAAuC,yDAAO;AAC9C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,EAAE,gDAAS;AACX,6DAA6D,yDAAO;AACpE;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH,EAAE,gDAAS;AACX,6DAA6D,yDAAO;AACpE;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH,EAAE,gDAAS;AACX,6DAA6D,yDAAO;AACpE;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;;AAEA;AACA,GAAG;AACH,sBAAsB,kDAAW;AACjC;AACA;AACA;AACA;AACA,GAAG;AACH,wBAAwB,kDAAW;AACnC;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH,kBAAkB,kDAAW;AAC7B;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH,sBAAsB,kDAAW;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH,sBAAsB,kDAAW;AACjC;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,oBAAoB,aAAoB;AACxC;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH,oBAAoB,kDAAW;AAC/B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,kBAAkB,aAAoB;AACtC;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,GAAG;AACH,uBAAuB,kDAAW;AAClC;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH,kBAAkB,kDAAW;AAC7B;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH,sBAAsB,kDAAW;AACjC;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,gDAAgD;AAChD;;AAEA;AACA;AACA;AACA,UAAU;AACV;AACA;;AAEA;AACA;AACA;AACA;AACA,aAAa;AACb;;AAEA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa,GAAG;AAChB;;AAEA;AACA;AACA,SAAS;AACT,QAAQ;AACR;AACA;AACA;AACA,SAAS,GAAG;;AAEZ;AACA;AACA;AACA;;AAEA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;;;AAGA,QAAQ,KAAqC;AAC7C;AACA;;AAEA;AACA,uFAAuF,KAAqC,GAAG,wDAAS,qNAAqN,CAAgB;AAC7W;AACA;;AAEA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,GAAG;AACH,qBAAqB,kDAAW;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,wBAAwB,kDAAW;AACnC;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH,sBAAsB,kDAAW;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH,cAAc,8CAAO;AACrB,YAAY,yDAAO;AACnB,GAAG;AACH,gBAAgB,8CAAO;AACvB;AACA,GAAG;;AAEH,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;;AAEjC,EAAE,0DAAmB;AACrB;AACA,GAAG;;AAEH,MAAM,IAAqC;AAC3C;AACA,IAAI,gDAAS;AACb,uBAAuB,KAAqC,GAAG,wDAAS,iMAAiM,gBAAgB,iBAAiB,eAAe,cAAc,CAAgB,WAAW;AAClW,KAAK;AACL;;AAEA,SAAS,oDAAa;AACtB;AACA,GAAG,cAAc,oDAAa;AAC9B,8EAA8E,gDAAa;AAC3F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,uJAAuJ;AACvJ;;AAEA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,+CAA+C,mEAAa;AAC5D;AACA,YAAY;AACZ;AACA;AACA,SAAS;AACT,QAAQ,SAAS,mEAAa;AAC9B;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,qDAAS,2BAA2B;AACzE,MAAM;AACN,uBAAuB,qDAAS;AAChC,MAAM;AACN;AACA;AACA,GAAG;AACH;AACA;AACA;;;AAGA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;;AAGA;AACA;AACA;AACA;AACA,IAAI;;;AAGJ;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA,IAAI;;;AAGJ;AACA;AACA,IAAI;;;AAGJ;AACA;AACA,IAAI;;;AAGJ;AACA,EAAE;AACF;AACA;AACA;;;AAGA,kKAAkK,kDAAe,GAAG,4CAAS;;AAE7L;AACA,YAAY,6CAAM,MAAM;;AAExB;AACA;AACA,GAAG;AACH,SAAS,kDAAW;AACpB,wEAAwE,aAAa;AACrF;AACA;;AAEA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C;;AAE/C;AACA;AACA;AACA;AACA;AACA,EAAE,gDAAS;AACX;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH,MAAM,IAAqC;AAC3C,cAAc,KAAqC,GAAG,wDAAS,wHAAwH,CAAgB;AACvM;;AAEA,eAAe,KAAqC,GAAG,wDAAS,uGAAuG,CAAgB;AACvL,qBAAqB,8CAAO;AAC5B;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,MAAM,IAAqC;AAC3C;AACA,IAAI,gDAAS;AACb,iBAAiB,KAAqC,GAAG,wDAAS,qNAAqN,EAAE,YAAY,UAAU,sCAAsC,EAAE,kBAAkB,SAAS,aAAa,CAAgB;AAC/Y,mDAAmD,KAAqC,GAAG,wDAAS,yGAAyG,gCAAgC,CAAgB;AAC7P,0DAA0D,KAAqC,GAAG,wDAAS,gHAAgH,uCAAuC,CAAgB;AAClR,6DAA6D,KAAqC,GAAG,wDAAS,+FAA+F,qCAAqC,CAAgB,WAAW;AAC7Q,KAAK;AACL,IAAI;;;AAGJ;AACA;AACA,EAAE,gDAAS;AACX;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA,6BAA6B;AAC7B;AACA,KAAK;AACL;;AAEA;AACA,+BAA+B;AAC/B;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;;AAEA,aAAa,oDAAa;AAC1B;AACA,OAAO;AACP;AACA,OAAO;AACP,MAAM;;;AAGN,WAAW,oDAAa;AACxB;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL,IAAI;;;AAGJ;;AAEA;AACA;AACA;;AAEA,WAAW,oDAAa;AACxB;AACA,KAAK;AACL;AACA,KAAK;AACL;;AAEA,SAAS,oDAAa,uBAAuB;AAC7C;AACA,GAAG;AACH;;AAEA,wBAAwB,iDAAU;AAClC;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS,oDAAa;AACtB;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,4EAA4E,aAAa;AACzF;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,wDAAwD;AACxD;AACA,WAAW;AACX;;AAEA;AACA,iBAAiB,oDAAa,yBAAyB;AACvD;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,eAAe,oDAAa,oBAAoB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA,KAAK,CAAC,4CAAS;;AAEf;AACA,WAAW,8DAAoB;AAC/B;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,oDAAa;AACxB,kBAAkB,KAAqC,GAAG,wDAAS,8NAA8N,CAAgB;AACjT,aAAa,oDAAa,kBAAkB;AAC5C;AACA,OAAO;AACP,KAAK;AACL;;AAEA,wHAAwH;AACxH;;AAEA;AACA;AACA,SAAS,8DAAoB;AAC7B;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL,iCAAiC;AACjC;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;;;AAGA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,wDAAwD;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,uEAAuE;AACvE;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,0BAA0B;AAC1B;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;;AAEA;AACA;AACA,oDAAoD,+DAAS;AAC7D,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;;AAEP;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,8EAA8E,yDAAO;AACrF;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,QAAQ;;;AAGR;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,2BAA2B;AAC3B;AACA;AACA,KAAK;;AAEL,uBAAuB,oDAAa;AACpC,sFAAsF,gDAAa;AACnG;;AAEA;AACA,CAAC,CAAC,4CAAS;;AAEX;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,4JAA4J,oDAAa;AACzK;;AAEA;AACA,CAAC,CAAC,4CAAS;;AAEX;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,KAAqC,GAAG,wDAAS,gHAAgH,aAAa,EAAE,aAAa,0BAA0B,CAAgB;AACtP,8BAA8B,KAAqC,GAAG,wDAAS,4GAA4G,0CAA0C,CAAgB;AACrP,iDAAiD,KAAqC,GAAG,wDAAS,qHAAqH,oCAAoC,CAAgB;AAC3Q,wDAAwD,KAAqC,GAAG,wDAAS,4HAA4H,2CAA2C,CAAgB;AAChS,2DAA2D,KAAqC,GAAG,wDAAS,2GAA2G,yCAAyC,CAAgB;AAChR;AACA;;AAEA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,eAAe,oDAAa;AAC5B;AACA,SAAS;AACT,QAAQ;;;AAGR,aAAa,oDAAa;AAC1B;AACA;AACA,OAAO;AACP,MAAM;;;AAGN;;AAEA;AACA;AACA;;AAEA,aAAa,oDAAa;AAC1B;AACA,OAAO;AACP;;AAEA,WAAW,oDAAa,uBAAuB;AAC/C;;AAEA;AACA,CAAC,CAAC,4CAAS;;AAEX;;AAEsb;AACtb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9wDC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAtMsD;AACxB;AACG;AAYiB;AAE5C,IAAMe,oBAAoB;IAAE,OAAO;AAAuD,EAAE;AAEnG,IAAMC,cAAe;AAErB,IAAMC,gBAAgB,SAACC;WAAW;eAAMC,QAAQ,KAAK,CACnD,kCAAwC,OAAPD,QAAO;;;AAGnC,IAAME,uBAAuB,wCAChCP,8FAAuBA,EACvBF,oFAAaA;IAChB,eAAe,SAACU,OAAOC;QACtB,wBAAwB,GACxB,IAAI,CAACD,KAAK,CAACC,SAAS,IAAIC,OAAO,IAAI,CAACF,KAAK,CAACC,SAAS,EAAE,MAAM,KAAK,GAAG;YAClE,OAAO,IAAIE,MACT,yHAAoI,OAAZR;QAE3H;IACD;GACC;AAEK,IAAMS,wBAAwB,mBACjCX,+FAAwBA,EACxBF,qFAAcA,EAChB;AAEF,IAAMc,0BAA0B,SAACC;WAAgB,wCAC7CA;QACH,UAAUV,cAAc;QACxB,WAAWF;;;AAGL,SAASa,yBAAyBP,KAAK,EAAEQ,OAAO;IACtD,IAAQC,gBAA+ET,MAA/ES,eAAeC,WAAgEV,MAAhEU,UAAUC,WAAsDX,MAAtDW,UAAUC,eAA4CZ,MAA5CY,cAAcC,kBAA8Bb,MAA9Ba,iBAAiBC,WAAad,MAAbc;IAC1E,IAAMC,mBAAmBpC,8CAAMA,CAAC8B;IAChC,IAAMO,wBAAwB,mBAAKD,iBAAiB,OAAO,EAAKjC,mDAAIA,CAAC2B,eAAeP,OAAO,IAAI,CAACa,iBAAiB,OAAO;IAExHnC,iDAASA,CAAC;QACT,IAAMqC,cAAcf,OAAO,IAAI,CAACa,iBAAiB,OAAO;QACxD,IAAMG,aAAahB,OAAO,IAAI,CAACO;QAE/B,wBAAwB,GACxB,IAAIQ,YAAY,IAAI,CAAC,SAACE;mBAAUD,WAAW,OAAO,CAACC,WAAW,CAAC;YAAI;YAClErB,QAAQ,KAAK,CACX,qFAAgG,OAAZH;QAEvF;IAED,GAAG;QAACc;QAAeM;KAAiB;IAEpC,IAAMK,eAAevC,mDAAWA,CAAC,SAACwC;QACjCrC,2FAAgBA,CACf0B,UACAL,wBAAwB;YACvBgB,QAAAA;YACA,gBAAgB3B;QACjB;IAEF,GAAG;QAACgB;KAAS;IAEb,IAAMY,eAAezC,mDAAWA;mBAAC,6BAAMwC;gBAAUE,eAC1CC,sBAMGC,GAMEC;;;;wBAbqCH,sBAAAA;wBAC1CC,uBAAuBnB,wBAAwB;4BAAEgB,QAAAA;wBAAO;;;;;;;;;wBAG7D;;4BAAMpC,2FAAgBA,CAAC0B,UAAUa;;;wBAAjC;wBACAD,cAAc;wBACdpC,kGAAuBA,CAAC0B,iBAAiBW;;;;;;wBACjCC;wBACRF,cAAc;wBAEd,IAAIE,aAAatB,OAAO;4BACvBL,QAAQ,KAAK,CAAC2B;wBACf,OAAO;4BACN,IAAK,IAAIC,SAASD,EAAG;gCACpBjB,QAAQ,OAAO,CAAC,aAAa,CAACkB,OAAOD,CAAC,CAACC,MAAM;4BAC9C;wBACD;wBAEAxC,+FAAoBA,CAAC0B,cAAc,wCAC/BY;4BACH,QAAQ9B;4BACR,aAAaA;;;;;;;;;;;;QAGhB;wBAxBuC2B;;;SAwBpC;QAACV;QAAUE;QAAiBD;QAAcJ;KAAQ;IAErD,IAAMmB,iBAAiB9C,mDAAWA,CAAC,SAACwC;eAAWjC,2FAAgBA,CAAC0B,UAAU;YACzEO,QAAAA;YACA,OAAO3B;QACR;OAAI;QAACoB;KAAS;IAEd,OAAO;QACN,eAAeE;QACfD,kBAAAA;QACA,UAAUK;QACV,UAAUE;QACV,UAAUK;IACX;AACD;AAEA,SAASC,mBAAmBC,KAAK,EAAEC,KAAK;IACvC,IAAIA,OAAO,OAAOA;IAElB,OAAOD,QAAQ,UAAUE;AAC1B;AAEA,SAASC,qBAAqBH,KAAK,EAAEI,OAAO;IAC3C,IAAIA,SAAS,OAAOA;IAEpB,OAAOJ;AACR;AAEA,SAASK,mBAAmBf,KAAK,EAAEgB,UAAU;IAC5C,OAAOA,WAAW,MAAM,CACvB,SAACC,QAAQC;eAAcD,UAAUC,UAChClB,OACAzB,mBACAA,mBACAA,kBAAkB,KAAK;OAExBqC;AAEF;AAEO,SAASO,0BAA0BtC,KAAK;IAC9C,IAAQuC,SAA6FvC,MAA7FuC,QAAQ7B,WAAqFV,MAArFU,UAAU8B,SAA2ExC,MAA3EwC,QAAQC,oBAAmEzC,MAAnEyC,mBAAmBC,kBAAgD1C,MAAhD0C,iBAAiB5B,WAA+Bd,MAA/Bc,UAAUqB,aAAqBnC,MAArBmC,YAAYQ,OAAS3C,MAAT2C;IAC5F,IAAMC,MAAMjE,8CAAMA,CAAC;QAAE,YAAY;mBAAM;;IAAI;IAE3C,IAAMkE,4BAA4BhE,mDAAWA,CAAC,SAACiE;QAC9CF,IAAI,OAAO,CAAC,UAAU,GAAGE;IAC1B,GAAG,EAAE;IAEL,IAAMC,oBAAoBlE,mDAAWA,CAAC,SAACsC;YAGhB6B;QAFtB,IAAMC,sBAAsB,EAAE,CAAC,MAAM,CAACnC,UACpC,MAAM,CAACqB,YACP,MAAM,CAAC;6CAAIe;gBAAAA;;mBAASF,CAAAA,eAAAA,IAAI,OAAO,EAAC,UAAU,OAAtBA,cAAuB,qBAAGE;WAC9C,MAAM,CAACC;QAET,OAAOjB,mBAAmBf,OAAO8B;IAClC,GAAG;QAACnC;QAAUqB;KAAW;IAEzB,IAAiCiB,6BAAAA,iDAAQA,CAAC;QAAET,MAAAA;QAAM,UAAUI;IAAkB,QAAtErB,QAAyB0B,cAAlBC,OAAkBD,cAAZE,UAAYF;IACjC,IAAQjC,QAAiBkC,KAAjBlC,OAAOU,QAAUwB,KAAVxB;IACf,IAAQ0B,WAAmCD,QAAnCC,UAAUC,aAAyBF,QAAzBE,YAAYC,WAAaH,QAAbG;IAE9B,IAAMC,mBAAmB7E,mDAAWA,CAAC,SAAC8E;QACrCF,SAASV,kBAAkBY,OAAOxC;IACnC,GAAG;QAACA;QAAOsC;QAAUV;KAAkB;IAEvC,IAAM3B,eAAevC,mDAAWA,CAAC,SAAC8E;QACjCJ,SAASI;QACTD,iBAAiBC;QAEjB,IAAIjD,UAAU;YACbA,SAASiD;QACV;IACD,GAAG;QAACjD;QAAU6C;QAAUG;KAAiB;IAEzC,IAAME,aAAa/E,mDAAWA,CAAC,SAACgF;QAC/BL,WAAW;QACXE;QAEA,wBAAwB,GACxB,IAAIlB,QAAQ;YACXnD,0FAAeA,CAACmD,QAAQ;gBACvBqB,OAAAA;gBACA,UAAUnE;gBACV,gBAAgBA;gBAChB,MAAMA,kBAAkB,KAAK;YAC9B;QACD;IACD,GAAG;QAAC8C;QAAQgB;QAAYE;KAAiB;IAEzC,OAAO;QACN,MAAMhC,MAAM,IAAI;QAChB,UAAUN;QACV,QAAQwC;QACR,OAAOrB,OAAOpB;QACd,mBAAmBa,qBAAqBH,OAAOY;QAC/C,iBAAiBb,mBAAmBC,OAAOa;QAC3CG,2BAAAA;IACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvCE;AAAA;AAAA;AAAA;AAAA;AAAA;AA/J2E;AAC9C;AACQ;AACA;AACoC;AAC9B;AAC8D;AAMpD;AAEvD,SAAS8B,sBAAsBC,KAAoB;QAAlBvD,SAAFuD,MAAEvD,QAAQX,WAAVkE,MAAUlE;IACxC9B,gDAASA,CAAC;eAAM8B,SAASW;OAAS;QAACA;QAAQX;KAAS;IAEpD,OAAO;AACR;AAEA,SAASmE,WAAW7E,KAAK;IACxB,IAAQ8E,WAA6B9E,MAA7B8E,UAAUC,OAAmB/E,MAAnB+E,MAAMC,WAAahF,MAAbgF;IACxB,IAAMC,SAAStG,6CAAMA;IACrB,IAA0EuG,4BAAAA,uGAAwBA,CAAClF,OAAOiF,SAAlGlE,mBAAkEmE,0BAAlEnE,kBAAkBN,gBAAgDyE,0BAAhDzE,eAAeE,WAAiCuE,0BAAjCvE,UAAUD,WAAuBwE,0BAAvBxE,UAAUI,WAAaoE,0BAAbpE;IAE7DlC,gDAASA,CACR;eAAMkG,SAAS;YACd,QAAQ;uBAAMG,OAAO,OAAO,CAAC,UAAU;;YACvC,YAAY;uBAAMA,OAAO,OAAO,CAAC,UAAU;;YAC3C,OAAO;uBAAMA,OAAO,OAAO,CAAC,WAAW;;YACvC,WAAW;uBAAMA,OAAO,OAAO,CAAC,MAAM;;YACtC,kBAAkB;uBAAMlE,iBAAiB,OAAO;;YAChD,UAAU;uBAAMkE,OAAO,OAAO,CAAC,KAAK;;YACpC,UAAU;uBAAMA,OAAO,OAAO,CAAC,OAAO;;YACtC,WAAW;uBAAMA,OAAO,OAAO,CAAC,MAAM;;YACtC,eAAe,SAACvD,OAAOP,OAAOgE;uBAAmBF,OAAO,OAAO,CAAC,aAAa,CAACvD,OAAOP,OAAOgE;;QAC7F;OACA;QAACL;QAAUG;QAAQlE;KAAiB;IAGrC,qBACC,2DAACmD,iDAAQA;QAAC,OAAOM,kEAAKA;qBACrB,2DAACJ,2CAAMA,0CACFpE;QACJ,UAAUW;QACV,eAAeF;QACf,UAAUK;QACV,UAAUmE;QACV,kBAAkB;QAClB,gBAAgB;QAGf;YAAGG,iBAAAA,SAASC,eAAAA,OAAO/D,sBAAAA,cAAcb,uBAAAA,eAAe6E,qBAAAA,aAAajE,gBAAAA,QAAWkE;YAArEH;YAASC;YAAO/D;YAAcb;YAAe6E;YAAajE;;6BAC5D,wIACC,2DAAC8C,yCAAIA,QAEHa,0BAAYjB,oDAAaA,CACxBgB,MACA,mBACI/E,OACAyE,8FAAmBA,CAACc,aAAaH,SAASC,OAAO/D,cAAcb,eAAe6E,+BAKrF,2DAACX;YAAsB,QAAQtD;YAAQ,UAAUX;;;AAOxD;AAEAmE,WAAW,SAAS,GAAG9E,+FAAoBA;AAE3C8E,WAAW,YAAY,GAAG;IACzB,eAAe,CAAC;AACjB;AAEA,SAASW,YAAYC;QACpBC,eADoBD,OACpBC,cACWC,YAFSF,OAEpB,WACAG,WAHoBH,OAGpBG,UACAC,WAJoBJ,OAIpBI,UACG7F,mCALiByF;QACpBC;QACA;QACAE;QACAC;;IAGA,IAAyGC,6BAAAA,wGAAyBA,CAAC9F,QAA3H2C,OAAiGmD,2BAAjGnD,MAAMjC,WAA2FoF,2BAA3FpF,UAAU8B,SAAiFsD,2BAAjFtD,QAAQrB,QAAyE2E,2BAAzE3E,OAAOsB,oBAAkEqD,2BAAlErD,mBAAmBC,kBAA+CoD,2BAA/CpD,iBAAiBG,4BAA8BiD,2BAA9BjD;IAC3E,IAAIkD;IAEJ,IAAIJ,UAAU,yBAAyB,EACtCI,aAAa;QAAElD,2BAAAA;IAA0B;IAE1C,qBACC,2DAAC;QAAI,OAAO6C;qBACX,2DAACC,kEACI3B,kDAAIA,CAAChE,OAAO,aAAa,UAAU;QACvC,IAAI2C;QACJ,MAAMA;QACN,UAAUjC;QACV,QAAQ8B;QACR,OAAOrB;QACP,UAAUyE;QACV,mBAAmBnD;QACnB,iBAAiBC;QACjB,KAAKmD;QACDE;AAIR;AAEAP,YAAY,SAAS,GAAGpF,gGAAqBA;AAE7CoF,YAAY,YAAY,GAAG;IAC1B,QAAQvB,wDAAQA;AACjB;AAEA,SAAS+B,iBAAiBhG,KAAK;IAC9B,IAAQiG,YAAoBjG,MAApBiG,WAAWtD,OAAS3C,MAAT2C;IAEnB,IAAMgD,YAAY9G,kDAAWA,CAC5B;YAAGkG,aAAAA,MAAMmB,aAAAA,MAAMC,aAAAA,MAAMC,aAAAA,MAAMC,eAAAA,QAAQC,gBAAAA,SAASC,eAAAA,QAAQC,YAAAA,KAAKC,gBAAAA;6BAAc1C,oDAAaA,CACnFkC,WACAvB,oGAAyBA,CACxBJ,8CAAKA,CAACS,KAAK,MAAM,EAAEpC,OACnBuD,MACAC,MACAC,MACAC,QACAC,SACAC,QACAC,KACAC;OAGF;QAACR;QAAWtD;KAAK;IAGlB,qBAAO,2DAAC0B,+CAAUA,0CAAKrE;QAAO,WAAWiG,YAAYN,YAAY5D;;AAClE;AAEA,SAAS2E;IACR,IAAqEC,oBAAAA,yDAAgBA,IAA7EtF,SAA6DsF,kBAA7DtF,QAAQuF,UAAqDD,kBAArDC,SAASC,gBAA4CF,kBAA5CE,eAAeC,kBAA6BH,kBAA7BG,iBAAiB1B,UAAYuB,kBAAZvB;IAEzD,OAAO;QACN/D,QAAAA;QACAuF,SAAAA;QACAC,eAAAA;QACAC,iBAAAA;QACA1B,SAAAA;IACD;AACD;AAEA,6DAAe;IACd,MAAMP;IACN,OAAOW;IACP,YAAYQ;IACZ,SAASU;AACV,CAAC,EAAC;;;;;;;;;;AC/JqE;AAEvE,6DAAeK,oFAAWA,EAAC"}