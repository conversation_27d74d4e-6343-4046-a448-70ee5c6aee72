define(["tokens!reactorCmps/tokens/general","when","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","create-react-class","Connector","suite-storage"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_when__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEB<PERSON><PERSON><PERSON>_EXTERNAL_MODULE_suite_storage__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/SavedSearchFilter"], {
"../reactor/src/Atomic/components/Mols/HintBox/HintBox.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.object.assign.js */ "../node_modules/core-js/modules/es.object.assign.js");
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var style = {
    boxStyle: {
        position: "relative",
        width: "100%",
        backgroundColor: "#f4f4f4",
        margin: "0 0 5px 0",
        padding: "6px 25px 6px 10px",
        borderRadius: "3px",
        border: "1px solid #d8d8d8"
    },
    boxClosedStyle: {
        display: "none"
    },
    cancelStyle: {
        position: "absolute",
        top: "9px",
        right: "4px",
        fontSize: "13px",
        color: "#cccccc",
        cursor: "pointer"
    }
};
module.exports = createReactClass({
    displayName: "Atomic/components/Mols/HintBox/HintBox",
    propTypes: {
        /**
		 * método que deve ser executado ao fechar o HintBox.
		 */ onClose: PropTypes.func
    },
    getInitialState: function getInitialState() {
        return {
            closed: false
        };
    },
    componentDidUpdate: function componentDidUpdate() {
        var me = this;
        if (me.state.closed && me.props.onClose) me.props.onClose();
    },
    closeHintBox: function closeHintBox() {
        var me = this;
        me.setState({
            closed: true
        });
    },
    render: function render() {
        var me = this, props = me.props, boxStyle = me.state.closed ? Object.assign({}, style.boxStyle, style.boxClosedStyle) : style.boxStyle;
        return /*#__PURE__*/ React.createElement("div", {
            style: boxStyle
        }, /*#__PURE__*/ React.createElement("span", {
            className: "seicon-cancel",
            style: style.cancelStyle,
            onClick: me.closeHintBox
        }), props.children);
    }
});


}),
"../reactor/src/Atomic/components/Mols/HintBox/HintBoxItem.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var style = {
    color: "#6b6b6b",
    fontSize: "13px",
    lineHeight: "1em",
    padding: "3px 0"
};
module.exports = createReactClass({
    displayName: "Atomic/components/Mols/HintBox/HintBoxItem",
    render: function render() {
        var me = this, props = me.props;
        return /*#__PURE__*/ React.createElement("div", {
            style: style
        }, props.children);
    }
});


}),
"./src/framework/components/SavedSearchFilter.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.json.stringify.js */ "../node_modules/core-js/modules/es.json.stringify.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var when = __webpack_require__(/*! when */ "when");
var AsyncLoading = __webpack_require__(/*! reactor/src/Atomic/components/Helpers/AsyncLoading/AsyncLoading */ "../reactor/src/Atomic/components/Helpers/AsyncLoading/AsyncLoading.jsx");
var reactRedux = __webpack_require__(/*! react-redux */ "../node_modules/react-redux/es/index.js");
var Provider = reactRedux.Provider;
var easyGrid = __webpack_require__(/*! reactor/src/FlexGrid/components/Helpers/EasyGrid */ "../reactor/src/FlexGrid/components/Helpers/EasyGrid.js");
var Connector = __webpack_require__(/*! Connector */ "Connector");
var SavedSearchFilterView = __webpack_require__(/*! ./SavedSearchFilterView */ "./src/framework/components/SavedSearchFilterView.jsx");
var easy = easyGrid({
    gridName: "savedSearchFilter"
});
__webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");
module.exports = createReactClass({
    propTypes: {
        onRender: PropTypes.func,
        onChange: PropTypes.func,
        logicUrl: PropTypes.string,
        publicSearchesUrl: PropTypes.string,
        cdIsoSystem: PropTypes.string,
        cdMenu: PropTypes.string,
        onFooterButtonClick: PropTypes.func
    },
    componentDidMount: function() {
        var me = this;
        me.reload();
    },
    reload: function() {
        var me = this;
        return me.refs.async.getWrappedInstance().loadData(true);
    },
    reset: function() {
        var me = this;
        return me.refs.async.getWrappedInstance().refs.list.reset();
    },
    selectByReload: function(value, expanded) {
        var me = this, list = me.refs.async.getWrappedInstance().refs.list;
        return list.setState({
            currentValue: value,
            expanded: expanded
        });
    },
    onDismiss: function() {
        var me = this, cdMenu = me.props.cdMenu, cdIsoSystem = me.props.cdIsoSystem, json = [
            {
                cdMenu: cdMenu
            },
            {
                cdIsoSystem: cdIsoSystem
            }
        ];
        Connector.callLogic2("COSavedSearchLogic/markAssignedSavedSearchAsUsed", {
            json: JSON.stringify(json)
        });
        Connector.callLogic2("COSavedSearchLogic/markPublicSavedSearchAsUsed", {
            json: JSON.stringify(json)
        });
    },
    getChildren: function(data) {
        var me = this, list = [], privateList = [], publicList = [], loading = true, newSearchCount = {
            privateQuantity: 0,
            publicQuantity: 0
        };
        if (data.results) {
            privateList = data.results[0];
            publicList = data.results[1];
            loading = false;
            $.each(privateList, function(index, obj) {
                if (obj.newSavedSearch && !obj.viewed) {
                    newSearchCount.privateQuantity++;
                }
            });
            $.each(publicList, function(index, objt) {
                if (objt.newSavedSearch && !objt.viewed) {
                    newSearchCount.publicQuantity++;
                }
            });
        }
        list = privateList;
        return /*#__PURE__*/ React.createElement(SavedSearchFilterView, {
            data: list,
            newSearchCount: newSearchCount,
            loading: loading,
            ref: "list",
            onRender: me.props.onRender,
            onChange: me.props.onChange,
            onFooterButtonClick: me.props.onFooterButtonClick,
            onDismiss: me.onDismiss
        });
    },
    getData: function() {
        var me = this, cdIsoSystem = me.props.cdIsoSystem, cdMenu = me.props.cdMenu, json = encodeURIComponent(JSON.stringify([
            {
                cdMenu: cdMenu
            },
            {
                cdIsoSystem: cdIsoSystem
            }
        ])), list = me.refs.async.getWrappedInstance().refs.list, currentValue = list.state.currentValue, expanded = list.state.expanded, deferred = when.defer();
        Connector.callLogic2(me.props.logicUrl, {
            json: json
        }, {
            success: function success(privateSearches) {
                Connector.callLogic2(me.props.publicSearchesUrl, {
                    json: json
                }, {
                    success: function success(publicSearches) {
                        if (currentValue) me.selectByReload(currentValue, expanded);
                        deferred.resolve({
                            results: [
                                privateSearches.results,
                                publicSearches.results
                            ]
                        });
                    }
                });
            }
        });
        return deferred.promise;
    },
    render: function() {
        var me = this;
        return /*#__PURE__*/ React.createElement(Provider, {
            store: easy.Store
        }, /*#__PURE__*/ React.createElement(AsyncLoading, {
            ref: "async",
            oid: "savedSearchFilter",
            fireLoad: true,
            loadFn: me.getData,
            getChildren: me.getChildren
        }));
    },
    displayName: "exports"
});


}),
"./src/framework/components/SavedSearchFilterView.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.object.assign.js */ "../node_modules/core-js/modules/es.object.assign.js");
__webpack_require__(/*! core-js/modules/es.string.split.js */ "../node_modules/core-js/modules/es.string.split.js");
__webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
__webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
__webpack_require__(/*! core-js/modules/es.array.find.js */ "../node_modules/core-js/modules/es.array.find.js");
__webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
__webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
__webpack_require__(/*! core-js/modules/es.string.replace.js */ "../node_modules/core-js/modules/es.string.replace.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var SelectableList = __webpack_require__(/*! reactor/src/Form/components/Mols/SelectableList/SelectableList.jsx */ "../reactor/src/Form/components/Mols/SelectableList/SelectableList.jsx");
var SelectableItem = __webpack_require__(/*! reactor/src/Form/components/Mols/SelectableList/SelectableItem.jsx */ "../reactor/src/Form/components/Mols/SelectableList/SelectableItem.jsx");
var HintBox = __webpack_require__(/*! reactor/src/Atomic/components/Mols/HintBox/HintBox.jsx */ "../reactor/src/Atomic/components/Mols/HintBox/HintBox.jsx");
var HintBoxItem = __webpack_require__(/*! reactor/src/Atomic/components/Mols/HintBox/HintBoxItem.jsx */ "../reactor/src/Atomic/components/Mols/HintBox/HintBoxItem.jsx");
var Ellipsis = __webpack_require__(/*! reactor/src/Atomic/components/Helpers/Ellipsis.jsx */ "../reactor/src/Atomic/components/Helpers/Ellipsis.jsx");
var loadingContainerStyle = {
    height: "60px"
};
var itemContainerStyle = {
    display: "flex",
    alignItems: "center",
    padding: "6px 5px"
};
var itemTextStyle = {
    wordWrap: "break-word",
    width: "200px"
};
var itemContentStyle = {
    width: "14px",
    color: "#d1d1d1",
    fontSize: "15px"
};
var footerActionsStyle = {
    display: "inline-block",
    width: "100%",
    paddingTop: "5px",
    marginRight: "6px"
};
var expandStyle = {
    float: "left",
    fontSize: "12px",
    paddingLeft: "1px",
    textDecoration: "none",
    color: "#428bca",
    cursor: "pointer"
};
var emptyMessageStyle = {
    container: {
        minHeight: "30px",
        color: "#ACACAC",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        textAlign: "center",
        padding: "5px 4px 1px 4px"
    },
    title: {
        width: "100%",
        fontWeight: "bold",
        fontSize: "12px"
    },
    text: {
        padding: "6px 0px",
        width: "100%"
    }
};
var publicSearchStyle = Object.assign({}, expandStyle, {
    float: "right"
});
module.exports = createReactClass({
    propTypes: {
        data: PropTypes.array,
        loading: PropTypes.bool,
        onRender: PropTypes.func,
        onChange: PropTypes.func,
        onFooterButtonClick: PropTypes.func
    },
    getInitialState: function() {
        return {
            currentValue: null,
            expanded: false,
            expandedListChecked: false
        };
    },
    componentDidMount: function() {
        var me = this;
        if (me.props.onRender) me.props.onRender();
        me.bindEvents();
    },
    componentDidUpdate: function() {
        var me = this, count;
        if (me.props.onRender) me.props.onRender();
        me.bindEvents();
        if (me.state.expandedListChecked) return;
        for(count = 0; count < me.props.data.length; count++){
            if (me.props.data[count].oid.split(",")[3] === me.state.currentValue && count > 4 && !me.state.expanded) {
                me.setState({
                    expanded: true,
                    expandedListChecked: true
                });
                break;
            }
        }
    },
    componentWillUnmount: function() {
        var me = this;
        me.unbindEvents();
    },
    bindEvents: function() {
        var me = this, filterContainer = me.refs.filterContainer, classList = [
            ".openMySearches",
            ".openSavedSearches"
        ], currentEl;
        me.unbindEvents();
        if (!me.props.onFooterButtonClick) return;
        classList.map(function(current, index) {
            currentEl = $(filterContainer).find(current);
            currentEl.css("color", publicSearchStyle.color);
            currentEl.css("textDecoration", publicSearchStyle.textDecoration);
            currentEl.on("click", index === 0 ? me.props.onFooterButtonClick.bind(me, false, true, true) : me.props.onFooterButtonClick.bind(me, true, false, true));
        });
    },
    unbindEvents: function() {
        var me = this, filterContainer = me.refs.filterContainer, classList = [
            ".openMySearches",
            ".openSavedSearches"
        ];
        classList.map(function(current) {
            $(filterContainer).find(current).off("click");
        });
    },
    reset: function() {
        var me = this;
        me.props.onChange(null);
        me.setState({
            currentValue: null
        });
    },
    handleChange: function(value, loadedByPageLoader) {
        var me = this;
        if (value.length && value[0] !== me.state.currentValue) {
            me.props.onChange(value[0], loadedByPageLoader);
            me.setState({
                currentValue: value[0]
            });
        }
    },
    handleExpand: function() {
        var me = this, expand = me.state.expanded ? false : true;
        me.setState({
            expanded: expand
        });
    },
    getExpandItem: function() {
        var me = this, expanded = me.state.expanded;
        return /*#__PURE__*/ React.createElement("span", {
            className: expanded ? "seicon-up-dir" : "seicon-right-dir",
            style: expandStyle,
            onClick: me.handleExpand
        }, expanded ? SE.t(218541) : SE.t(218540));
    },
    getEmptyMessage: function() {
        return /*#__PURE__*/ React.createElement("div", {
            style: emptyMessageStyle.container
        }, /*#__PURE__*/ React.createElement("div", {
            className: "seicon-info-circle",
            style: emptyMessageStyle.title
        }, SE.t(218542)), /*#__PURE__*/ React.createElement("div", {
            style: emptyMessageStyle.text
        }, SE.t(218543)));
    },
    getChildren: function() {
        var me = this;
        return me.props.data.map(function(current, index) {
            var id = "savedSearch-" + index + "-" + current.oid, formattedOid = current.oid.split(",")[3];
            return /*#__PURE__*/ React.createElement(SelectableItem, {
                key: id,
                oid: formattedOid
            }, /*#__PURE__*/ React.createElement("div", {
                style: itemContainerStyle,
                id: formattedOid
            }, /*#__PURE__*/ React.createElement("li", {
                style: itemContentStyle
            }), /*#__PURE__*/ React.createElement("div", {
                style: itemTextStyle
            }, /*#__PURE__*/ React.createElement(Ellipsis, {
                clamp: 2
            }, current.text))));
        });
    },
    getPublicSearchLink: function() {
        var me = this;
        return /*#__PURE__*/ React.createElement("span", {
            className: "openPublicSearches",
            onClick: me.handleFooterButtonClick,
            style: publicSearchStyle
        }, SE.t(215827));
    },
    onHintBoxClose: function() {
        var me = this;
        me.props.onRender();
        me.props.onDismiss();
    },
    getSearchHint: function() {
        var me = this, newSearchCount = me.props.newSearchCount, itemList = [];
        /* eslint-disable react/no-danger */ if (newSearchCount.publicQuantity > 0) itemList.push(/*#__PURE__*/ React.createElement(HintBoxItem, {
            key: 1
        }, /*#__PURE__*/ React.createElement("div", {
            dangerouslySetInnerHTML: {
                __html: SE.t(218584).replace(/\\"/g, '"')
            }
        })));
        if (newSearchCount.privateQuantity > 0) itemList.push(/*#__PURE__*/ React.createElement(HintBoxItem, {
            key: 2
        }, /*#__PURE__*/ React.createElement("div", {
            dangerouslySetInnerHTML: {
                __html: SE.t(218583).replace(/\\"/g, '"')
            }
        })));
        /* eslint-enable react/no-danger */ if (!itemList.length) return null;
        return /*#__PURE__*/ React.createElement(HintBox, {
            onClose: me.onHintBoxClose
        }, itemList);
    },
    handleFooterButtonClick: function() {
        var me = this;
        me.props.onFooterButtonClick(true);
    },
    render: function() {
        var me = this, children = me.getChildren(), showExpand = false;
        if (!children.length && !me.props.loading) return me.getEmptyMessage();
        if (!me.state.expanded) {
            while(children.length > 5){
                showExpand = true;
                children.pop();
            }
        }
        return /*#__PURE__*/ React.createElement("div", {
            className: "rctSavedSearchFilter",
            ref: "filterContainer",
            style: !children.length ? loadingContainerStyle : {}
        }, me.getSearchHint(), /*#__PURE__*/ React.createElement(SelectableList, {
            multiSelect: false,
            value: [
                me.state.currentValue
            ],
            onChange: me.handleChange
        }, children), /*#__PURE__*/ React.createElement("div", {
            style: footerActionsStyle
        }, children.length > 5 || showExpand ? me.getExpandItem() : null, children.length ? me.getPublicSearchLink() : null));
    },
    displayName: "exports"
});


}),
"Connector": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"when": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_when__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/components/SavedSearchFilter.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=SavedSearchFilter.js.map