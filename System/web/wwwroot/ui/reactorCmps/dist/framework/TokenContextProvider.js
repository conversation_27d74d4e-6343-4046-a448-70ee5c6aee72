define(["js!wwwroot/ui/reactorCmps/dist/watch1749037385376","react","tokens!reactorCmps/tokens/general","Utils"], function(__WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_Utils__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/TokenContextProvider"], {
"./src/framework/helpers/TokenContextProvider.js": (function (module, __unused_webpack_exports, __webpack_require__) {
module.exports = __webpack_require__(/*! reactor/src/Atomic/components/Helpers/Language/TokenContextProvider */ "../reactor/src/Atomic/components/Helpers/Language/TokenContextProvider.js");


}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/helpers/TokenContextProvider.js"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=TokenContextProvider.js.map