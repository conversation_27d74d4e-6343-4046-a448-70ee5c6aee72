{"version": 3, "file": "framework/TCGTextArea.js", "sources": ["webpack://watch1749037385376/./src/framework/components/TCG/TCGTextArea.jsx"], "sourcesContent": ["import \"reactorCmps/tokens/general\";\nimport TCGTextArea from \"reactor2/src/Atomic/components/Mols/TextArea/TCGTextArea\";\n\nexport default TCGTextArea;"], "names": ["TCGTextArea"], "mappings": ";;;;;;;;;;;AAAoC;AAC+C;AAEnF,6DAAeA,gGAAWA,EAAC"}