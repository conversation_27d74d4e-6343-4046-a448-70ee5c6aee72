define(["tokens!reactorCmps/tokens/general","when","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","create-react-class","Connector","suite-storage"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_when__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEB<PERSON><PERSON><PERSON>_EXTERNAL_MODULE_suite_storage__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["framework/OldRichText"], {
"./src/framework/components/OldRichText.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var when = __webpack_require__(/*! when */ "when");
var RichText = __webpack_require__(/*! reactor/src/Form/components/Atoms/RichText */ "../reactor/src/Form/components/Atoms/RichText.jsx");
var AsyncLoading = __webpack_require__(/*! reactor/src/Atomic/components/Helpers/AsyncLoading/AsyncLoading */ "../reactor/src/Atomic/components/Helpers/AsyncLoading/AsyncLoading.jsx");
var reactRedux = __webpack_require__(/*! react-redux */ "../node_modules/react-redux/es/index.js");
var Provider = reactRedux.Provider;
var easyGrid = __webpack_require__(/*! reactor/src/FlexGrid/components/Helpers/EasyGrid */ "../reactor/src/FlexGrid/components/Helpers/EasyGrid.js");
var Connector = __webpack_require__(/*! Connector */ "Connector");
var easy = easyGrid({
    gridName: "RichText"
});
var containerStyle = {
    height: "100%"
};
__webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");
module.exports = createReactClass({
    propTypes: {
        oid: PropTypes.string.isRequired,
        getFromOid: PropTypes.string,
        height: PropTypes.string
    },
    getInitialState: function() {
        var hasOid = Boolean(this.props.oid || this.props.getFromOid);
        return {
            contentLoaded: !hasOid
        };
    },
    componentDidMount: function() {
        var me = this;
        me.refs.async.getWrappedInstance().loadData(me.props.forceLoadData);
        if (this.state.contentLoaded) {
            this.handleContentLoad();
        }
    },
    componentDidUpdate: function(_prevProps, prevState) {
        if (!prevState.contentLoaded && this.state.contentLoaded) {
            this.handleContentLoad();
        }
    },
    handleContentLoad: function() {
        this.props.onContentLoad && this.props.onContentLoad();
    },
    getChildren: function(asyncData) {
        var me = this, html = "";
        if (asyncData.results && asyncData.results[0]) {
            html = asyncData.results[0].html;
        }
        return /*#__PURE__*/ React.createElement(RichText, me.props, html);
    },
    retrieveData: function() {
        var me = this, props = me.props, method = "find", oid = props.oid;
        if (props.getFromOid) {
            method = "copyInMemory";
            oid = props.getFromOid;
        }
        if (!oid) {
            return when.resolve('');
        }
        return Connector.callLogic2("RichTextResourceLogic/" + method, {
            json: '[{"String": "' + oid + '"}]'
        }, {
            success: me.handleRetrievedData
        });
    },
    handleRetrievedData: function handleRetrievedData(data) {
        var me = this, props = me.props;
        if (data.results[0]) {
            me.setState({
                contentLoaded: true
            });
            props.onChange && props.onChange(data.results[0].html);
        }
    },
    render: function() {
        var me = this;
        return /*#__PURE__*/ React.createElement(Provider, {
            store: easy.Store
        }, /*#__PURE__*/ React.createElement(AsyncLoading, {
            ref: "async",
            oid: "richText" + me.props.oid,
            fireLoad: true,
            loadFn: me.retrieveData,
            getChildren: me.getChildren,
            containerStyle: containerStyle
        }));
    },
    displayName: "exports"
});


}),
"Connector": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"when": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_when__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/framework/components/OldRichText.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=OldRichText.js.map