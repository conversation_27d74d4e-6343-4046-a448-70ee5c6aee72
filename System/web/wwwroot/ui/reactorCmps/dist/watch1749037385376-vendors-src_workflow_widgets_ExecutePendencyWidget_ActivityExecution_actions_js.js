"use strict";
(self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["vendors-src_workflow_widgets_ExecutePendencyWidget_ActivityExecution_actions_js"], {
"./src/responsiveform/components/Attribute/AttributeCollection.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0__);

/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0___default()({
    model: {
        primaryKey: "oid",
        proxyCfg: {
            type: "katanaProxy",
            route: "responsive/attributes"
        }
    },
    collection: {
        defaultOrder: {
            label: "ASC"
        }
    }
}));


}),
"./src/responsiveform/components/Attribute/helpers/AttributeHelpers.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_math_trunc_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.math.trunc.js */ "../node_modules/core-js/modules/es.math.trunc.js");
/* ESM import */var core_js_modules_es_math_trunc_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_math_trunc_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.error.to-string.js */ "../node_modules/core-js/modules/es.error.to-string.js");
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.date.to-string.js */ "../node_modules/core-js/modules/es.date.to-string.js");
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ "../node_modules/core-js/modules/es.regexp.to-string.js");
/* ESM import */var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var RegionalOpts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! RegionalOpts */ "RegionalOpts");
/* ESM import */var RegionalOpts__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(RegionalOpts__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var Formatters_Number__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! Formatters/Number */ "Formatters/Number");
/* ESM import */var Formatters_Number__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(Formatters_Number__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var Formatters_TimeMilli__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! Formatters/TimeMilli */ "Formatters/TimeMilli");
/* ESM import */var Formatters_TimeMilli__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(Formatters_TimeMilli__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var moment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! moment */ "../node_modules/moment/moment.js");
/* ESM import */var moment__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var _AttributeMapping__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./AttributeMapping */ "./src/responsiveform/components/Attribute/helpers/AttributeMapping.js");
/* ESM import */var _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../../helpers/constants/AttributeConstants */ "./src/responsiveform/helpers/constants/AttributeConstants.js");
/* ESM import */var _helpers_constants_RuleConstants__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../../helpers/constants/RuleConstants */ "./src/responsiveform/helpers/constants/RuleConstants.js");
/* ESM import */var _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../../helpers/constants/SharedConstants */ "./src/responsiveform/helpers/constants/SharedConstants.js");
/* ESM import */var _helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../../helpers/ValidationHelpers */ "./src/responsiveform/helpers/ValidationHelpers.js");
/* ESM import */var reactorCmps_src_responsiveform_helpers_DecimalHelpers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactorCmps/src/responsiveform/helpers/DecimalHelpers */ "./src/responsiveform/helpers/DecimalHelpers.js");
/* ESM import */var reactorCmps_src_responsiveform_components_FormElement_helpers_DateTimeHelpers__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! reactorCmps/src/responsiveform/components/FormElement/helpers/DateTimeHelpers */ "./src/responsiveform/components/FormElement/helpers/DateTimeHelpers.js");

















/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    getAttributeTypeInfo: function getAttributeTypeInfo(type) {
        return _AttributeMapping__WEBPACK_IMPORTED_MODULE_10__["default"][type] || {};
    },
    formatAttributeValue: function formatAttributeValue(type, value, getToken) {
        var config = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {}, thousandSeparator = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : true;
        if (_helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_14__["default"].isEmpty(value)) {
            return null;
        }
        if (typeof getToken !== 'function') {
            getToken = SE.t;
        }
        switch(type){
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].INTEGER:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].LONG_INTEGER:
                var integerFormatter = new (Formatters_Number__WEBPACK_IMPORTED_MODULE_7___default())({
                    numberDecimalDigits: 0,
                    disableThousandsSeparator: !thousandSeparator
                });
                return integerFormatter.format(value);
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].DECIMAL:
                return reactorCmps_src_responsiveform_helpers_DecimalHelpers__WEBPACK_IMPORTED_MODULE_15__["default"].formatDecimalValue(value, config);
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].TIME:
                var timeFormatter = new (Formatters_TimeMilli__WEBPACK_IMPORTED_MODULE_8___default())({
                    withSeconds: false
                });
                return timeFormatter.format(value);
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].DATE:
                return moment__WEBPACK_IMPORTED_MODULE_9___default()(value).format(RegionalOpts__WEBPACK_IMPORTED_MODULE_6___default().getRegionalOptions().javaDateFormat.toUpperCase());
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].BOOLEAN:
                if (value === _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_13__["default"].valueOptions.VALUE_TRUE) {
                    return getToken(100092);
                } else if (value === _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_13__["default"].valueOptions.VALUE_FALSE) {
                    return getToken(100093);
                }
                return Boolean(value) ? getToken(100092) : getToken(100093);
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].FILE:
                return value.fileName;
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].OPTION:
                return value.name;
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].USER:
                return value.name;
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].DATETIME:
                return reactorCmps_src_responsiveform_components_FormElement_helpers_DateTimeHelpers__WEBPACK_IMPORTED_MODULE_16__["default"].formatDateTimeValue(value);
            default:
                return value;
        }
    },
    formatValueToAttribute: function formatValueToAttribute(type, value) {
        var config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
        if (_helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_14__["default"].isEmpty(value)) {
            return null;
        }
        switch(type){
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].INTEGER:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].LONG_INTEGER:
                return Math.trunc(value);
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].DECIMAL:
                return reactorCmps_src_responsiveform_helpers_DecimalHelpers__WEBPACK_IMPORTED_MODULE_15__["default"].formatDecimalValue(value, config);
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].TIME:
                return moment__WEBPACK_IMPORTED_MODULE_9___default().duration(value, 'milliseconds').asMilliseconds();
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].DATE:
                if (typeof value == "string") {
                    var dateFormat = RegionalOpts__WEBPACK_IMPORTED_MODULE_6___default().getRegionalOptions().javaDateFormat.toUpperCase();
                    var date = moment__WEBPACK_IMPORTED_MODULE_9___default()(value, dateFormat);
                    value = date.valueOf();
                }
                return value;
            default:
                return value;
        }
    },
    formatAttributeValueFromDatabase: function formatAttributeValueFromDatabase(type, value) {
        if (_helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_14__["default"].isEmpty(value)) {
            return null;
        }
        switch(type){
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].DATE:
                var dateFormat = "YYYY-MM-DD";
                var date = moment__WEBPACK_IMPORTED_MODULE_9___default()(value, dateFormat);
                return date.valueOf();
            default:
                return value;
        }
    },
    formatAttributeValueToDatabase: function formatAttributeValueToDatabase(type, value) {
        if (_helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_14__["default"].isEmpty(value)) {
            return null;
        }
        switch(type){
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].DATE:
                return moment__WEBPACK_IMPORTED_MODULE_9___default()(value).format("YYYY-MM-DD");
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].TEXT:
                return value.toString();
            default:
                return value;
        }
    },
    getAvailableOperationsByAttribute: function getAvailableOperationsByAttribute(attribute) {
        if (_helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_14__["default"].isEmpty(attribute)) {
            return [];
        }
        switch(attribute.type){
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].TEXT:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].LONG_TEXT:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].SHORT_TEXT:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].UNLIMITED_TEXT:
                return [
                    _helpers_constants_RuleConstants__WEBPACK_IMPORTED_MODULE_12__["default"].operationValues.COUNTER,
                    _helpers_constants_RuleConstants__WEBPACK_IMPORTED_MODULE_12__["default"].operationValues.CONCAT
                ];
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].TIME:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].DATE:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].BOOLEAN:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].OPTION:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].USER:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].DATETIME:
                return [
                    _helpers_constants_RuleConstants__WEBPACK_IMPORTED_MODULE_12__["default"].operationValues.COUNTER
                ];
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].INTEGER:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].LONG_INTEGER:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_11__["default"].DECIMAL:
                return [
                    _helpers_constants_RuleConstants__WEBPACK_IMPORTED_MODULE_12__["default"].operationValues.COUNTER,
                    _helpers_constants_RuleConstants__WEBPACK_IMPORTED_MODULE_12__["default"].operationValues.SUM,
                    _helpers_constants_RuleConstants__WEBPACK_IMPORTED_MODULE_12__["default"].operationValues.MIN,
                    _helpers_constants_RuleConstants__WEBPACK_IMPORTED_MODULE_12__["default"].operationValues.MAX
                ];
            default:
                return [];
        }
    }
});


}),
"./src/responsiveform/components/Attribute/helpers/AttributeMapping.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../helpers/constants/AttributeConstants */ "./src/responsiveform/helpers/constants/AttributeConstants.js");
/* ESM import */var _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../helpers/constants/SharedConstants */ "./src/responsiveform/helpers/constants/SharedConstants.js");
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}


var _obj;
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((_obj = {}, _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].SHORT_TEXT, {
    name: 100552,
    icon: "seicon-varchar",
    color: "#00A3F0",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.STRING,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].SHORT_TEXT
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].TEXT, {
    name: 100552,
    icon: "seicon-varchar",
    color: "#00A3F0",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.STRING,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].TEXT
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].LONG_TEXT, {
    name: 217528,
    icon: "seicon-comment-alt",
    color: "#00A3F0",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.STRING,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].LONG_TEXT
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].INTEGER, {
    name: 113041,
    icon: "seicon-integer",
    color: "#FF8000",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.INTEGER,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].INTEGER
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].DECIMAL, {
    name: 206404,
    icon: "seicon-float",
    color: "#BE4153",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.DECIMAL,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].DECIMAL
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].TIME, {
    name: 100210,
    icon: "seicon-clock",
    color: "#00BE6C",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.TIME,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].TIME
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].DATE, {
    name: 100209,
    icon: "seicon-calendar",
    color: "#00BE6C",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.DATE,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].DATE
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].BOOLEAN, {
    name: 101017,
    icon: "seicon-checked",
    color: "#666666",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.BOOLEAN,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].BOOLEAN
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].FILE, {
    name: 100567,
    icon: "seicon-file",
    color: "#B7B7B7",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.CUSTOM,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].FILE
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].COMPLEX, {
    name: 207972,
    icon: "seicon-folder-close",
    color: "#FFC64D",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.CUSTOM,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].COMPLEX
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].ENUM, {
    name: 218021,
    icon: "seicon-options",
    color: "#FFC64D",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.CUSTOM,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].ENUM
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].UNLIMITED_TEXT, {
    name: 217528,
    icon: "seicon-comment-alt",
    color: "#00A3F0",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.STRING,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].UNLIMITED_TEXT
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].RICH_TEXT, {
    name: 306790,
    icon: "seicon-file-code",
    color: "#00A3F0",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.STRING,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].RICH_TEXT
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].LONG_INTEGER, {
    name: 113041,
    icon: "seicon-integer",
    color: "#FF8000",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.INTEGER,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].LONG_INTEGER
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].OPTION, {
    name: 100413,
    icon: "seicon-list",
    color: "#007BA4",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.STRING,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].OPTION
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].USER, {
    name: 100044,
    icon: "seicon-user",
    color: "#B7B7B7",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.CUSTOM,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].USER
}), _define_property(_obj, _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].DATETIME, {
    name: 101802,
    icon: "seicon-datetime",
    color: "#00BE6C",
    returnType: _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_1__["default"].returnTypes.DATETIME,
    value: _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_0__["default"].DATETIME
}), _obj));


}),
"./src/responsiveform/components/Entity/EntityCategorySelection.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var _helpers_FieldHelpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../helpers/FieldHelpers */ "./src/responsiveform/helpers/FieldHelpers.js");
/* ESM import */var reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactor2/src/Atomic/components/Mols/TextView */ "../reactor2/src/Atomic/components/Mols/TextView.jsx");
/* ESM import */var reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var reactor2_src_Form_components_Mols_Zoom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactor2/src/Form/components/Mols/Zoom */ "../reactor2/src/Form/components/Mols/Zoom.jsx");
/* ESM import */var reactor2_src_Form_components_Mols_Zoom__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Mols_Zoom__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/context/SEFieldsContext */ "../reactor2/src/Form/components/Orgs/SEFields/context/SEFieldsContext.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC */ "../reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC.js");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_8__);
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function _object_without_properties(source, excluded) {
    if (source == null) return {};
    var target = _object_without_properties_loose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _object_without_properties_loose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}










var EntityCategorySelectionCollection = SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_3___default()({
    model: {
        primaryKey: "oid",
        proxyCfg: {
            type: "katanaProxy",
            route: "responsive/categories/with-permission"
        }
    },
    collection: {
        defaultOrder: {
            idcategory: "ASC"
        }
    }
});
var EntityCategorySelection = function(_param) {
    var getToken = _param.getToken, value = _param.value, onChange = _param.onChange, zoomProps = _object_without_properties(_param, [
        "getToken",
        "value",
        "onChange"
    ]);
    var renderMode = react__WEBPACK_IMPORTED_MODULE_2___default().useContext((reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_7___default())).renderMode;
    var selected = value ? [
        value
    ] : null;
    if (_helpers_FieldHelpers__WEBPACK_IMPORTED_MODULE_4__["default"].isViewMode(renderMode)) {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_5___default()), {
            title: getToken(302120),
            value: value ? "".concat(value.id, " - ").concat(value.name) : null
        });
    }
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Form_components_Mols_Zoom__WEBPACK_IMPORTED_MODULE_6___default()), _object_spread({
        limit: 1,
        title: getToken(302120),
        Collection: EntityCategorySelectionCollection,
        multireducerKey: "EntityCategorySelection",
        value: selected,
        onChange: function(types) {
            return onChange(types && types[0] ? types[0] : null);
        },
        fields: [
            {
                title: "null",
                field: "oid",
                showOnGrid: false,
                showOnTag: false,
                primaryKey: true
            },
            {
                title: getToken(100528),
                field: "id",
                showOnGrid: true,
                showOnTag: true,
                customType: null
            },
            {
                title: getToken(100111),
                field: "name",
                showOnGrid: true,
                showOnTag: true,
                customType: null
            }
        ]
    }, zoomProps));
};
EntityCategorySelection.displayName = "responsiveform/components/Entity/EntityCategorySelection";
EntityCategorySelection.propTypes = {
    value: prop_types__WEBPACK_IMPORTED_MODULE_9___default().shape({
        oid: (prop_types__WEBPACK_IMPORTED_MODULE_9___default().string),
        id: (prop_types__WEBPACK_IMPORTED_MODULE_9___default().string),
        name: (prop_types__WEBPACK_IMPORTED_MODULE_9___default().string)
    }),
    onChange: (prop_types__WEBPACK_IMPORTED_MODULE_9___default().func),
    getToken: (prop_types__WEBPACK_IMPORTED_MODULE_9___default().func)
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_8___default()(EntityCategorySelection));


}),
"./src/responsiveform/components/Entity/EntitySelection.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  EntitySelectionCollection: function() { return EntitySelectionCollection; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.error.to-string.js */ "../node_modules/core-js/modules/es.error.to-string.js");
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.date.to-string.js */ "../node_modules/core-js/modules/es.date.to-string.js");
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ "../node_modules/core-js/modules/es.regexp.to-string.js");
/* ESM import */var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_14__);
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var _helpers_FieldHelpers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../helpers/FieldHelpers */ "./src/responsiveform/helpers/FieldHelpers.js");
/* ESM import */var reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! reactor2/src/Atomic/components/Mols/TextView */ "../reactor2/src/Atomic/components/Mols/TextView.jsx");
/* ESM import */var reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var reactor2_src_Form_components_Mols_Zoom__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactor2/src/Form/components/Mols/Zoom */ "../reactor2/src/Form/components/Mols/Zoom.jsx");
/* ESM import */var reactor2_src_Form_components_Mols_Zoom__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Mols_Zoom__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/context/SEFieldsContext */ "../reactor2/src/Form/components/Orgs/SEFields/context/SEFieldsContext.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_11__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC */ "../reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC.js");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var _Register_EntityRegisterModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./Register/EntityRegisterModal */ "./src/responsiveform/components/Entity/Register/EntityRegisterModal.jsx");
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function _object_without_properties(source, excluded) {
    if (source == null) return {};
    var target = _object_without_properties_loose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _object_without_properties_loose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}















var EntitySelectionCollection = SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_7___default()({
    model: {
        primaryKey: "oid",
        proxyCfg: {
            type: "katanaProxy",
            route: "responsive/entities"
        }
    },
    collection: {
        defaultOrder: {
            displayName: "ASC"
        }
    }
});
var EntitySelection = function(_param) {
    var getToken = _param.getToken, value = _param.value, onChange = _param.onChange, ignoredEntities = _param.ignoredEntities, createNew = _param.createNew, zoomProps = _object_without_properties(_param, [
        "getToken",
        "value",
        "onChange",
        "ignoredEntities",
        "createNew"
    ]);
    var _React_useState = _sliced_to_array(react__WEBPACK_IMPORTED_MODULE_6___default().useState(false), 2), showCreateNew = _React_useState[0], setShowCreateNew = _React_useState[1];
    var _React_useState1 = _sliced_to_array(react__WEBPACK_IMPORTED_MODULE_6___default().useState(""), 2), newEntityName = _React_useState1[0], setNewEntityName = _React_useState1[1];
    var renderMode = react__WEBPACK_IMPORTED_MODULE_6___default().useContext((reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_11___default())).renderMode;
    var selected = value ? [
        value
    ] : null;
    var openRegisterModal = react__WEBPACK_IMPORTED_MODULE_6___default().useCallback(function(newEntity) {
        setNewEntityName(newEntity);
        setShowCreateNew(true);
    }, []);
    var closeRegisterModal = react__WEBPACK_IMPORTED_MODULE_6___default().useCallback(function() {
        setNewEntityName("");
        setShowCreateNew(false);
    }, []);
    var handleSaveNew = react__WEBPACK_IMPORTED_MODULE_6___default().useCallback(function(entity) {
        onChange(entity);
        closeRegisterModal();
    }, [
        onChange,
        closeRegisterModal
    ]);
    if (_helpers_FieldHelpers__WEBPACK_IMPORTED_MODULE_8__["default"].isViewMode(renderMode)) {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_9___default()), {
            title: zoomProps.title || getToken(302117),
            value: value ? "".concat(value.name, " - ").concat(value.displayName) : null
        });
    }
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((react__WEBPACK_IMPORTED_MODULE_6___default().Fragment), null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Form_components_Mols_Zoom__WEBPACK_IMPORTED_MODULE_10___default()), _object_spread({
        limit: 1,
        title: getToken(302117),
        Collection: EntitySelectionCollection,
        multireducerKey: "entitySelection",
        value: selected,
        addCustomItem: createNew ? openRegisterModal : null,
        onChange: function(entities) {
            return onChange(entities && entities[0] ? entities[0] : null);
        },
        fields: [
            {
                title: "null",
                field: "oid",
                showOnGrid: false,
                showOnTag: false,
                primaryKey: true
            },
            {
                title: getToken(100528),
                field: "name",
                showOnGrid: true,
                showOnTag: true,
                customType: null
            },
            {
                title: getToken(100111),
                field: "displayName",
                showOnGrid: true,
                showOnTag: true,
                customType: null
            }
        ],
        extraParams: {
            ignoredRecords: ignoredEntities.toString()
        }
    }, zoomProps)), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement(_Register_EntityRegisterModal__WEBPACK_IMPORTED_MODULE_13__["default"], {
        show: showCreateNew,
        newEntityName: newEntityName,
        onSave: handleSaveNew,
        onClose: closeRegisterModal
    }));
};
EntitySelection.displayName = "responsiveform/components/Entity/EntitySelection";
EntitySelection.defaultProps = {
    createNew: true,
    ignoredEntities: []
};
EntitySelection.propTypes = {
    createNew: (prop_types__WEBPACK_IMPORTED_MODULE_14___default().bool),
    ignoredEntities: prop_types__WEBPACK_IMPORTED_MODULE_14___default().arrayOf((prop_types__WEBPACK_IMPORTED_MODULE_14___default().string)),
    value: prop_types__WEBPACK_IMPORTED_MODULE_14___default().shape({
        oid: (prop_types__WEBPACK_IMPORTED_MODULE_14___default().string),
        name: (prop_types__WEBPACK_IMPORTED_MODULE_14___default().string),
        displayName: (prop_types__WEBPACK_IMPORTED_MODULE_14___default().string)
    }),
    onChange: (prop_types__WEBPACK_IMPORTED_MODULE_14___default().func),
    getToken: (prop_types__WEBPACK_IMPORTED_MODULE_14___default().func)
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_12___default()(EntitySelection));


}),
"./src/responsiveform/components/Entity/Register/EntityRegisterModal.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.json.stringify.js */ "../node_modules/core-js/modules/es.json.stringify.js");
/* ESM import */var core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.parse-int.js */ "../node_modules/core-js/modules/es.parse-int.js");
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ "../node_modules/core-js/modules/es.symbol.js");
/* ESM import */var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ "../node_modules/core-js/modules/es.symbol.description.js");
/* ESM import */var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_19__);
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var _helpers_FieldHelpers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../helpers/FieldHelpers */ "./src/responsiveform/helpers/FieldHelpers.js");
/* ESM import */var _helpers_StringHelpers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../helpers/StringHelpers */ "./src/responsiveform/helpers/StringHelpers.js");
/* ESM import */var _helpers_RequestHelpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../helpers/RequestHelpers */ "./src/responsiveform/helpers/RequestHelpers.js");
/* ESM import */var _helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../../helpers/ValidationHelpers */ "./src/responsiveform/helpers/ValidationHelpers.js");
/* ESM import */var reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! reactor2/src/Atomic/components/Layout/StackedLayout */ "../reactor2/src/Atomic/components/Layout/StackedLayout.jsx");
/* ESM import */var reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var reactor2_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactor2/src/Form/components/Mols/Text */ "../reactor2/src/Form/components/Mols/Text.jsx");
/* ESM import */var reactor2_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_13__);
/* ESM import */var reactor2_src_Form_components_Mols_TextArea__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactor2/src/Form/components/Mols/TextArea */ "../reactor2/src/Form/components/Mols/TextArea.jsx");
/* ESM import */var reactor2_src_Form_components_Mols_TextArea__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Mols_TextArea__WEBPACK_IMPORTED_MODULE_14__);
/* ESM import */var _Common_Ui_Modal_ModalWithAction__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../Common/Ui/Modal/ModalWithAction */ "./src/responsiveform/components/Common/Ui/Modal/ModalWithAction.jsx");
/* ESM import */var reactor2_src_constants_sizeConstants__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! reactor2/src/constants/sizeConstants */ "../reactor2/src/constants/sizeConstants.js");
/* ESM import */var _EntityCategorySelection__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../EntityCategorySelection */ "./src/responsiveform/components/Entity/EntityCategorySelection.jsx");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC */ "../reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC.js");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_18__);
function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _call_super(_this, derived, args) {
    derived = _get_prototype_of(derived);
    return _possible_constructor_return(_this, _is_native_reflect_construct() ? Reflect.construct(derived, args || [], _get_prototype_of(_this).constructor) : derived.apply(_this, args));
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _is_native_reflect_construct() {
    try {
        var result = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
    } catch (_) {}
    return (_is_native_reflect_construct = function() {
        return !!result;
    })();
}




















var EntityRegisterModal = /*#__PURE__*/ function(_React_Component) {
    "use strict";
    _inherits(EntityRegisterModal, _React_Component);
    function EntityRegisterModal() {
        _class_call_check(this, EntityRegisterModal);
        var _this;
        _this = _call_super(this, EntityRegisterModal);
        _this.state = {
            saving: false,
            validateFields: false,
            name: "",
            displayName: "",
            description: "",
            category: null
        };
        _this.onShowModal = _this.onShowModal.bind(_this);
        _this.onClickSave = _this.onClickSave.bind(_this);
        _this.onChangeName = _this.onChangeName.bind(_this);
        _this.onChangeDisplayName = _this.onChangeDisplayName.bind(_this);
        _this.onChangeDescription = _this.onChangeDescription.bind(_this);
        _this.onChangeCategory = _this.onChangeCategory.bind(_this);
        return _this;
    }
    _create_class(EntityRegisterModal, [
        {
            key: "onShowModal",
            value: function onShowModal() {
                var newEntityName = this.props.newEntityName;
                this.setState({
                    name: _helpers_StringHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].normalizeModelName(newEntityName, 15),
                    displayName: newEntityName,
                    description: "",
                    category: null,
                    validateFields: false,
                    saving: false
                });
            }
        },
        {
            key: "onClickSave",
            value: function onClickSave() {
                var _this = this;
                if (_helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_11__["default"].anyEmpty([
                    this.state.name,
                    this.state.displayName,
                    this.state.category
                ])) {
                    this.setState({
                        validateFields: true
                    });
                    return;
                }
                this.setState({
                    saving: true
                });
                _helpers_RequestHelpers__WEBPACK_IMPORTED_MODULE_10__["default"].callKatana({
                    path: "responsive/entities",
                    params: JSON.stringify(_object_spread_props(_object_spread({}, this.state), {
                        responsible: {
                            code: parseInt(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_7___default().getCDUser(), 10)
                        }
                    })),
                    options: {
                        type: "POST"
                    },
                    success: function(response) {
                        _this.setState({
                            saving: false
                        });
                        _this.props.onSave(response);
                    },
                    error: function() {
                        _this.setState({
                            saving: false
                        });
                    }
                });
            }
        },
        {
            key: "onChangeName",
            value: function onChangeName(value) {
                this.setState({
                    name: _helpers_StringHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].normalizeModelName(value, 15)
                });
            }
        },
        {
            key: "onChangeDisplayName",
            value: function onChangeDisplayName(value) {
                this.setState({
                    displayName: value
                });
            }
        },
        {
            key: "onChangeDescription",
            value: function onChangeDescription(value) {
                this.setState({
                    description: value
                });
            }
        },
        {
            key: "onChangeCategory",
            value: function onChangeCategory(value) {
                this.setState({
                    category: value
                });
            }
        },
        {
            key: "render",
            value: function render() {
                var _this_props = this.props, show = _this_props.show, onClose = _this_props.onClose, getToken = _this_props.getToken;
                var _this_state = this.state, saving = _this_state.saving, validateFields = _this_state.validateFields, name = _this_state.name, displayName = _this_state.displayName, description = _this_state.description, category = _this_state.category;
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement(_Common_Ui_Modal_ModalWithAction__WEBPACK_IMPORTED_MODULE_15__["default"], {
                    show: show,
                    saving: saving,
                    title: getToken("102268"),
                    width: reactor2_src_constants_sizeConstants__WEBPACK_IMPORTED_MODULE_16__.SIZE_MEDIUM,
                    onClose: onClose,
                    onSave: this.onClickSave,
                    onShowModal: this.onShowModal
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_12___default()), null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement("div", {
                    style: {
                        display: "flex",
                        width: "100%"
                    }
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement("div", {
                    style: {
                        flex: "1 1 40%",
                        marginRight: 8
                    }
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_13___default()), {
                    autoFocus: true,
                    required: true,
                    title: getToken("100528"),
                    value: name,
                    onChange: this.onChangeName,
                    validationState: _helpers_FieldHelpers__WEBPACK_IMPORTED_MODULE_8__["default"].getRequiredState(name, validateFields)
                })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement("div", {
                    style: {
                        flex: "1 1 60%",
                        marginLeft: 8
                    }
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_13___default()), {
                    required: true,
                    title: getToken("100111"),
                    value: displayName,
                    onChange: this.onChangeDisplayName,
                    validationState: _helpers_FieldHelpers__WEBPACK_IMPORTED_MODULE_8__["default"].getRequiredState(displayName, validateFields)
                }))), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement("div", {
                    style: {
                        marginTop: 9
                    }
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement(_EntityCategorySelection__WEBPACK_IMPORTED_MODULE_17__["default"], {
                    required: true,
                    value: category,
                    onChange: this.onChangeCategory,
                    validationState: _helpers_FieldHelpers__WEBPACK_IMPORTED_MODULE_8__["default"].getRequiredState(category, validateFields)
                })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Form_components_Mols_TextArea__WEBPACK_IMPORTED_MODULE_14___default()), {
                    title: getToken("100087"),
                    value: description,
                    onChange: this.onChangeDescription,
                    height: 90
                })));
            }
        }
    ]);
    return EntityRegisterModal;
}((react__WEBPACK_IMPORTED_MODULE_6___default().Component));
EntityRegisterModal.displayName = "responsiveform/components/Entity/Register/EntityRegisterModal";
EntityRegisterModal.defaultProps = {
    show: false
};
EntityRegisterModal.propTypes = {
    newEntityName: (prop_types__WEBPACK_IMPORTED_MODULE_19___default().string),
    show: (prop_types__WEBPACK_IMPORTED_MODULE_19___default().bool),
    onClose: (prop_types__WEBPACK_IMPORTED_MODULE_19___default().func),
    onSave: (prop_types__WEBPACK_IMPORTED_MODULE_19___default().func),
    getToken: (prop_types__WEBPACK_IMPORTED_MODULE_19___default().func)
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_18___default()(EntityRegisterModal));


}),
"./src/responsiveform/components/FormEditor/EditorContent/FormTab/Properties/TableConfig/TableConfigHelpers.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.symbol.js */ "../node_modules/core-js/modules/es.symbol.js");
/* ESM import */var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.symbol.description.js */ "../node_modules/core-js/modules/es.symbol.description.js");
/* ESM import */var core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_description_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ "../node_modules/core-js/modules/es.array.filter.js");
/* ESM import */var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.index-of.js */ "../node_modules/core-js/modules/es.array.index-of.js");
/* ESM import */var core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactor2/src/helpers/ToasterSystem/ToasterSystem */ "../reactor2/src/helpers/ToasterSystem/ToasterSystem.js");
/* ESM import */var reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var _helpers_FormHelpers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../../../helpers/FormHelpers */ "./src/responsiveform/helpers/FormHelpers.js");
/* ESM import */var _helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../../../../helpers/ValidationHelpers */ "./src/responsiveform/helpers/ValidationHelpers.js");
/* ESM import */var _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../../../../helpers/constants/AttributeConstants */ "./src/responsiveform/helpers/constants/AttributeConstants.js");
/* ESM import */var _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../../../../helpers/constants/SharedConstants */ "./src/responsiveform/helpers/constants/SharedConstants.js");










/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    validateTableConfig: function validateTableConfig(state, getToken) {
        var entity = state.entity, reference = state.reference, referenceFields = state.referenceFields, multiSelect = state.multiSelect, entityForAdvancedList = state.entityForAdvancedList;
        if (_helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_7__["default"].isEmpty(entity)) {
            reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().ERROR), getToken("302118"));
            return false;
        }
        if (!multiSelect && (_helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_7__["default"].isEmpty(reference) || _helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_7__["default"].isEmpty(reference.description))) {
            reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().ERROR), getToken("303735"));
            return false;
        }
        if (multiSelect && _helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_7__["default"].isEmpty(entityForAdvancedList)) {
            reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().ERROR), getToken("302118"));
            return false;
        }
        if (_helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_7__["default"].isEmpty(referenceFields)) {
            reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().ERROR), getToken("303736"));
            return false;
        }
        if (this.validateOrderBy(state)) {
            reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().ERROR), getToken(113870));
            return false;
        }
        return true;
    },
    validateOrderBy: function validateOrderBy(state) {
        var referenceFields = state.referenceFields, orderOrientation = state.orderOrientation, orderInsert = state.orderInsert;
        var fieldSelected = referenceFields.filter(function(field) {
            return field.orderBy;
        });
        var hasOrderBy = fieldSelected.length > 0 || orderInsert;
        return !hasOrderBy || !orderOrientation ? true : false;
    },
    validateTableFilter: function validateTableFilter(filter) {
        var tabField = filter.selectedTab === "attribute" ? "attribute" : "reference";
        if (_helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_7__["default"].isEmpty(filter[tabField]) || _helpers_ValidationHelpers__WEBPACK_IMPORTED_MODULE_7__["default"].isEmpty(filter.operator)) {
            return false;
        } else if (this.isSigleOperator(filter.operator)) {
            return true;
        }
        return _helpers_FormHelpers__WEBPACK_IMPORTED_MODULE_6__["default"].validateSourceConfig(filter.sourceConfig);
    },
    getAllowedAttributes: function getAllowedAttributes(isOfflineEnabled) {
        var allowedAttributes = [
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].SHORT_TEXT,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].TEXT,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].LONG_TEXT,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].UNLIMITED_TEXT,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].INTEGER,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].LONG_INTEGER,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].DECIMAL,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].TIME,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].DATE,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].BOOLEAN,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].DATETIME
        ];
        if (!isOfflineEnabled) {
            allowedAttributes.push(_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].FILE);
            allowedAttributes.push(_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].OPTION);
            allowedAttributes.push(_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].USER);
        }
        return allowedAttributes;
    },
    getAllowedAttributesToFilter: function getAllowedAttributesToFilter(isOfflineEnabled) {
        var ignoredAttributes = [
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].LONG_TEXT,
            _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].UNLIMITED_TEXT
        ];
        return this.getAllowedAttributes(isOfflineEnabled).filter(function(type) {
            return ignoredAttributes.indexOf(type) === -1;
        });
    },
    getFilterOperatorsByAttribute: function getFilterOperatorsByAttribute(attribute) {
        if (!attribute || !attribute.type) {
            return [];
        }
        switch(attribute.type){
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].SHORT_TEXT:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].TEXT:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].LONG_TEXT:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].OPTION:
                return [
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.EQUAL,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.DIFFERENT,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NULL,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NOT_NULL,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.CONTAINS,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NOT_CONTAINS
                ];
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].INTEGER:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].LONG_INTEGER:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].DECIMAL:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].TIME:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].DATE:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].DATETIME:
                return [
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.EQUAL,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.DIFFERENT,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NULL,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NOT_NULL,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.GREATER,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.LESS,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.GREATER_EQUAL,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.LESS_EQUAL
                ];
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].BOOLEAN:
                return [
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.EQUAL,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.DIFFERENT,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.TRUE,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.FALSE
                ];
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].FILE:
            case _helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_8__["default"].USER:
                return [
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NULL,
                    _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NOT_NULL
                ];
            default:
                return [];
        }
    },
    getReferenceFilterOperators: function getReferenceFilterOperators() {
        return [
            _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.EQUAL,
            _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.DIFFERENT,
            _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NULL,
            _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NOT_NULL
        ];
    },
    isSigleOperator: function isSigleOperator(operator) {
        return [
            _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NULL,
            _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.NOT_NULL,
            _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.TRUE,
            _helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_9__["default"].conditionalOperators.FALSE
        ].indexOf(operator) >= 0;
    }
});


}),
"./src/responsiveform/components/FormEditor/helpers/TermHelper.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.array.index-of.js */ "../node_modules/core-js/modules/es.array.index-of.js");
/* ESM import */var core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactor2_src_store_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactor2/src/store/store */ "../reactor2/src/store/store.js");
/* ESM import */var reactor2_src_store_store__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_store_store__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactorCmps_src_responsiveform_components_FormEditor_reducer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactorCmps/src/responsiveform/components/FormEditor/reducer */ "./src/responsiveform/components/FormEditor/reducer/index.js");
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}





/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    getShowTermNumber: function() {
        var getState = (reactor2_src_store_store__WEBPACK_IMPORTED_MODULE_3___default().getState);
        return reactorCmps_src_responsiveform_components_FormEditor_reducer__WEBPACK_IMPORTED_MODULE_4__["default"].selectors.getShowTermNumber(getState());
    },
    getValueElementName: function(value) {
        return _object_spread_props(_object_spread({}, value), {
            name: this.getElementIdName(value),
            translatedTermName: this.getElementIdName(value)
        });
    },
    getElementIdName: function(param) {
        var id = param.id, name = param.name, translatedTermName = param.translatedTermName;
        var elementName = (this.getShowTermNumber() ? name : translatedTermName) || "";
        if (id && elementName.indexOf("".concat(id, " - ")) === -1) {
            elementName = "".concat(id, " - ").concat(elementName);
        }
        return elementName;
    },
    getElementName: function(param) {
        var name = param.name, translatedTermName = param.translatedTermName;
        return this.getElementIdName({
            name: name,
            translatedTermName: translatedTermName
        });
    },
    getPlaceholderElement: function(param) {
        var placeholder = param.placeholder, translatedTermPlaceholder = param.translatedTermPlaceholder;
        return (this.getShowTermNumber() ? placeholder : translatedTermPlaceholder) || "";
    },
    getTextFieldLookup: function() {
        return this.getShowTermNumber() ? "name" : "translatedTermName";
    }
});


}),
"./src/responsiveform/components/FormEditor/reducer/actions.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  loadUserPreferences: function() { return loadUserPreferences; },
  setActiveTab: function() { return setActiveTab; },
  setCreateFieldsInTableAutomatically: function() { return setCreateFieldsInTableAutomatically; },
  setFormData: function() { return setFormData; },
  setShowTermNumber: function() { return setShowTermNumber; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ "./src/responsiveform/components/FormEditor/reducer/constants.js");
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactorCmps_src_responsiveform_helpers_RequestHelpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactorCmps/src/responsiveform/helpers/RequestHelpers */ "./src/responsiveform/helpers/RequestHelpers.js");




var setActiveTab = function(activeTab) {
    return {
        type: _constants__WEBPACK_IMPORTED_MODULE_1__["default"].SET_ACTIVE_TAB,
        activeTab: activeTab
    };
};
var setFormData = function(formData) {
    return {
        type: _constants__WEBPACK_IMPORTED_MODULE_1__["default"].SET_FORM_DATA,
        data: formData
    };
};
var setCreateFieldsInTableAutomatically = function(value) {
    return {
        type: _constants__WEBPACK_IMPORTED_MODULE_1__["default"].SET_CREATE_FIELDS_IN_TABLE_AUTOMATICALLY,
        createFieldsInTableAutomatically: value
    };
};
var setShowTermNumber = function(value) {
    return {
        type: _constants__WEBPACK_IMPORTED_MODULE_1__["default"].SET_SHOW_TERM_NUMBER,
        showTermNumber: value
    };
};
var loadUserPreferences = function() {
    return function(dispatch) {
        reactorCmps_src_responsiveform_helpers_RequestHelpers__WEBPACK_IMPORTED_MODULE_3__["default"].callKatana({
            path: "responsive/editor/".concat(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_2___default().getCDUser(), "/preferences"),
            options: {
                type: "GET"
            },
            success: function(data) {
                dispatch(setCreateFieldsInTableAutomatically(data.shouldAutoCreateFieldOfTable));
                dispatch(setShowTermNumber(data.showTermNumber));
            }
        });
    };
};


}),
"./src/responsiveform/components/FormEditor/reducer/constants.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    STORE_MOUNT_POINT: "FormEditor",
    SET_ACTIVE_TAB: "FormEditor::SET_ACTIVE_TAB",
    SET_FORM_DATA: "FormEditor::SET_FORM_DATA",
    SET_CREATE_FIELDS_IN_TABLE_AUTOMATICALLY: "FormEditor::SET_CREATE_FIELDS_IN_TABLE_AUTOMATICALLY",
    SET_SHOW_TERM_NUMBER: "FormEditor::SET_SHOW_TERM_NUMBER"
});


}),
"./src/responsiveform/components/FormEditor/reducer/index.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./actions */ "./src/responsiveform/components/FormEditor/reducer/actions.js");
/* ESM import */var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ "./src/responsiveform/components/FormEditor/reducer/constants.js");
/* ESM import */var _reducer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./reducer */ "./src/responsiveform/components/FormEditor/reducer/reducer.js");
/* ESM import */var _helpers_ReduxHelpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../helpers/ReduxHelpers */ "./src/responsiveform/helpers/ReduxHelpers.js");




var getState = function(state, slice) {
    return _helpers_ReduxHelpers__WEBPACK_IMPORTED_MODULE_3__["default"].selectStateSlice(state[_constants__WEBPACK_IMPORTED_MODULE_1__["default"].STORE_MOUNT_POINT], slice, true);
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    mountPoint: _constants__WEBPACK_IMPORTED_MODULE_1__["default"].STORE_MOUNT_POINT,
    constants: _constants__WEBPACK_IMPORTED_MODULE_1__["default"],
    reducer: _reducer__WEBPACK_IMPORTED_MODULE_2__["default"],
    actions: _actions__WEBPACK_IMPORTED_MODULE_0__,
    selectors: {
        getActiveTab: function(state) {
            return getState(state, "activeTab");
        },
        getFormData: function(state) {
            return getState(state, "formData");
        },
        getOidRevisionForm: function(state) {
            return getState(state, [
                "formData",
                "oid"
            ]);
        },
        getOidEntity: function(state) {
            return getState(state, [
                "formData",
                "entity",
                "oid"
            ]);
        },
        isOfflineEnabled: function(state) {
            if (state[_constants__WEBPACK_IMPORTED_MODULE_1__["default"].STORE_MOUNT_POINT]) {
                return getState(state, [
                    "formData",
                    "enableOffline"
                ]);
            }
            return false;
        },
        getCreateFieldsInTableAutomatically: function(state) {
            return getState(state, "createFieldsInTableAutomatically");
        },
        getShowTermNumber: function(state) {
            return getState(state, "showTermNumber");
        }
    }
});


}),
"./src/responsiveform/components/FormEditor/reducer/reducer.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; },
  setActiveTab: function() { return setActiveTab; },
  setCreateFieldsInTableAutomatically: function() { return setCreateFieldsInTableAutomatically; },
  setFormData: function() { return setFormData; },
  setShowTermNumber: function() { return setShowTermNumber; }
});
/* ESM import */var immutable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! immutable */ "../node_modules/immutable/dist/immutable.js");
/* ESM import */var immutable__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(immutable__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ "./src/responsiveform/components/FormEditor/reducer/constants.js");
/* ESM import */var _helpers_ReduxHelpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../helpers/ReduxHelpers */ "./src/responsiveform/helpers/ReduxHelpers.js");
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}



var setActiveTab = function(state, param) {
    var activeTab = param.activeTab;
    return state.set("activeTab", activeTab);
};
var setFormData = function(state, param) {
    var data = param.data;
    return state.set("formData", (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)(data));
};
var setCreateFieldsInTableAutomatically = function(state, param) {
    var createFieldsInTableAutomatically = param.createFieldsInTableAutomatically;
    return state.set("createFieldsInTableAutomatically", createFieldsInTableAutomatically);
};
var setShowTermNumber = function(state, param) {
    var showTermNumber = param.showTermNumber;
    return state.set("showTermNumber", showTermNumber);
};
var initialState = (0,immutable__WEBPACK_IMPORTED_MODULE_0__.fromJS)({
    activeTab: null,
    formData: null,
    createFieldsInTableAutomatically: true
});
var _obj;
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_helpers_ReduxHelpers__WEBPACK_IMPORTED_MODULE_2__["default"].createStrategyReducer(initialState, (_obj = {}, _define_property(_obj, _constants__WEBPACK_IMPORTED_MODULE_1__["default"].SET_ACTIVE_TAB, setActiveTab), _define_property(_obj, _constants__WEBPACK_IMPORTED_MODULE_1__["default"].SET_FORM_DATA, setFormData), _define_property(_obj, _constants__WEBPACK_IMPORTED_MODULE_1__["default"].SET_CREATE_FIELDS_IN_TABLE_AUTOMATICALLY, setCreateFieldsInTableAutomatically), _define_property(_obj, _constants__WEBPACK_IMPORTED_MODULE_1__["default"].SET_SHOW_TERM_NUMBER, setShowTermNumber), _obj)));


}),
"./src/responsiveform/components/FormElement/helpers/DateTimeHelpers.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.date.to-string.js */ "../node_modules/core-js/modules/es.date.to-string.js");
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ "../node_modules/moment/moment.js");
/* ESM import */var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_DateHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/DateHelper */ "../reactor2/src/Atomic/components/Helpers/DateHelper.js");
/* ESM import */var reactor2_src_Atomic_components_Helpers_DateHelper__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Helpers_DateHelper__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactor2_src_helpers_suiteInfo_regionalOptions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactor2/src/helpers/suiteInfo/regionalOptions */ "../reactor2/src/helpers/suiteInfo/regionalOptions.js");





/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    removeSeconds: function(epoch) {
        var isMilliseconds = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;
        if (typeof epoch != 'number') {
            return epoch;
        }
        var multiplier = isMilliseconds ? 1 : 1000, dateTime = new Date(epoch * multiplier);
        dateTime.setSeconds(0, 0);
        return Math.floor(dateTime.getTime() / multiplier);
    },
    formatDateTimeValue: function formatDateTimeValue(value) {
        var dateFormat = reactor2_src_Atomic_components_Helpers_DateHelper__WEBPACK_IMPORTED_MODULE_3___default().getNormalizedFormat({
            Y: "YYYY",
            M: "MM",
            D: "DD"
        }, (0,reactor2_src_helpers_suiteInfo_regionalOptions__WEBPACK_IMPORTED_MODULE_4__.getRegionalOptions)().javaDateFormat.toUpperCase(), (0,reactor2_src_helpers_suiteInfo_regionalOptions__WEBPACK_IMPORTED_MODULE_4__.getRegionalOptions)().dateSeparator);
        var timeFormat = reactor2_src_Atomic_components_Helpers_DateHelper__WEBPACK_IMPORTED_MODULE_3___default().getNormalizedFormat({
            H: "HH",
            M: "mm"
        }, "HH:mm", (0,reactor2_src_helpers_suiteInfo_regionalOptions__WEBPACK_IMPORTED_MODULE_4__.getRegionalOptions)().timeSeparator);
        return moment__WEBPACK_IMPORTED_MODULE_2___default()(value).format("".concat(dateFormat, " ").concat(timeFormat));
    }
});


}),
"./src/responsiveform/helpers/DecimalHelpers.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.error.to-string.js */ "../node_modules/core-js/modules/es.error.to-string.js");
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.date.to-string.js */ "../node_modules/core-js/modules/es.date.to-string.js");
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ "../node_modules/core-js/modules/es.regexp.to-string.js");
/* ESM import */var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.index-of.js */ "../node_modules/core-js/modules/es.array.index-of.js");
/* ESM import */var core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_index_of_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.number.constructor.js */ "../node_modules/core-js/modules/es.number.constructor.js");
/* ESM import */var core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_constructor_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_es_number_to_fixed_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.number.to-fixed.js */ "../node_modules/core-js/modules/es.number.to-fixed.js");
/* ESM import */var core_js_modules_es_number_to_fixed_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_number_to_fixed_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.string.replace.js */ "../node_modules/core-js/modules/es.string.replace.js");
/* ESM import */var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.string.split.js */ "../node_modules/core-js/modules/es.string.split.js");
/* ESM import */var core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.array.slice.js */ "../node_modules/core-js/modules/es.array.slice.js");
/* ESM import */var core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_slice_js__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_11__);
/* ESM import */var RegionalOpts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! RegionalOpts */ "RegionalOpts");
/* ESM import */var RegionalOpts__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(RegionalOpts__WEBPACK_IMPORTED_MODULE_12__);
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}













/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    formatDecimalValue: function formatDecimalValue(_value, config) {
        var value = _value;
        if (value.toString().indexOf("e") != -1) {
            value = Number(value).toFixed(20).replace(/0+$/, '').replace(/\.$/, '');
        }
        var _config_dataLength;
        var decimalDigits = (_config_dataLength = config.dataLength) !== null && _config_dataLength !== void 0 ? _config_dataLength : RegionalOpts__WEBPACK_IMPORTED_MODULE_12___default().getRegionalOptions().numberDecimalDigits;
        var valueStr = value.toString();
        var _valueStr_split = _sliced_to_array(valueStr.split('.'), 2), integerPart = _valueStr_split[0], decimalPart = _valueStr_split[1];
        if (decimalPart && decimalPart.length > decimalDigits) {
            decimalPart = decimalPart.slice(0, decimalDigits);
        } else if (!decimalPart) {
            decimalPart = 0;
        }
        return "".concat(integerPart, ".").concat(decimalPart);
    }
});


}),
"./src/responsiveform/helpers/FormHelpers.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ "../node_modules/core-js/modules/es.array.filter.js");
/* ESM import */var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.string.replace.js */ "../node_modules/core-js/modules/es.string.replace.js");
/* ESM import */var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_es_array_for_each_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.array.for-each.js */ "../node_modules/core-js/modules/es.array.for-each.js");
/* ESM import */var core_js_modules_es_array_for_each_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_for_each_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./constants/SharedConstants */ "./src/responsiveform/helpers/constants/SharedConstants.js");
/* ESM import */var _ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ValidationHelpers */ "./src/responsiveform/helpers/ValidationHelpers.js");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/tokenManager */ "../reactor2/src/Atomic/components/Helpers/Language/tokenManager.js");
/* ESM import */var reactorCmps_src_generic_helpers_ProductConstants__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactorCmps/src/generic/helpers/ProductConstants */ "./src/generic/helpers/ProductConstants.js");
/* ESM import */var _components_Attribute_helpers_AttributeHelpers__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/Attribute/helpers/AttributeHelpers */ "./src/responsiveform/components/Attribute/helpers/AttributeHelpers.js");
/* ESM import */var reactorCmps_src_responsiveform_components_FormEditor_helpers_TermHelper_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactorCmps/src/responsiveform/components/FormEditor/helpers/TermHelper.js */ "./src/responsiveform/components/FormEditor/helpers/TermHelper.js");














/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    getUserLanguageName: function getUserLanguageName() {
        var defaultLang = WorkspaceInfo__WEBPACK_IMPORTED_MODULE_7___default().getDefaultLanguage();
        var langName = WorkspaceInfo__WEBPACK_IMPORTED_MODULE_7___default().getLanguageList().filter(function(lang) {
            return lang.abrev === defaultLang;
        });
        if (langName.length > 0) {
            return langName[0].name;
        }
        return "".concat(defaultLang).replace("-", "_").toUpperCase();
    },
    getTranslation: function getTranslation(param) {
        var languages = param.languages;
        if (languages && languages.length > 0) {
            var langName = this.getUserLanguageName();
            var translation = languages.filter(function(lang) {
                return lang.language === langName;
            });
            if (translation.length > 0) {
                return translation[0];
            }
        }
        return null;
    },
    getTranslatedName: function getTranslatedName(param) {
        var name = param.name, languages = param.languages;
        var translation = this.getTranslation({
            languages: languages
        });
        return (translation === null || translation === void 0 ? void 0 : translation.name) || name;
    },
    getTranslatedAttribute: function getTranslatedAttribute(param) {
        var attribute = param.attribute, languages = param.languages;
        var translation = this.getTranslation({
            languages: languages
        });
        return translation ? translation.attribute : attribute;
    },
    validateSourceConfig: function validateSourceConfig(sourceConfig) {
        if (_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig) || _ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig.valueSource)) {
            return false;
        }
        switch(sourceConfig.valueSource){
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.ELEMENT:
                return !_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig.sourceElement);
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.VARIABLE:
                return !_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig.sourceVariable);
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.EXPRESSION:
                return !_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig.sourceExpression);
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.FUNCTION:
                return !_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig.sourceFunction);
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.RELATED_TABLE_DATA:
                return !_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig.sourceEntityAttribute);
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.QUICK_SEARCH:
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.SIZE_NUMBER:
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.PAGINATION:
                return !_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig.valueSource);
            default:
                return !sourceConfig.optionalFixedValue ? !_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig.sourceFixedValue) : true;
        }
    },
    validateOperationSourceConfigValue: function validateOperationSourceConfigValue(sourceConfig, operationValue) {
        switch(sourceConfig.operationSourceValue){
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].operationSourceTypes.CONSTANT:
                return !_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(operationValue);
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].operationSourceTypes.VARIABLE:
                return !_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig.operationSourceVariable);
            default:
                return true;
        }
    },
    getSourceConfigValue: function getSourceConfigValue(sourceConfig) {
        if (_ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig) || _ValidationHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].isEmpty(sourceConfig.valueSource)) {
            return null;
        }
        switch(sourceConfig.valueSource){
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.ELEMENT:
                return sourceConfig.sourceElement;
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.VARIABLE:
                return sourceConfig.sourceVariable;
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.EXPRESSION:
                return sourceConfig.sourceExpression;
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.FUNCTION:
                return sourceConfig.sourceFunction;
            default:
                return sourceConfig.sourceFixedValue;
        }
    },
    getSourceConfigFormattedValue: function getSourceConfigFormattedValue(sourceConfig, getToken) {
        var sourceConfigValue = this.getSourceConfigValue(sourceConfig);
        if (sourceConfigValue == null) {
            return null;
        }
        switch(sourceConfig.valueSource){
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.VARIABLE:
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.EXPRESSION:
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.FUNCTION:
                return sourceConfigValue.name;
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.ELEMENT:
                return reactorCmps_src_responsiveform_components_FormEditor_helpers_TermHelper_js__WEBPACK_IMPORTED_MODULE_13__["default"].getElementIdName(sourceConfigValue);
            case _constants_SharedConstants__WEBPACK_IMPORTED_MODULE_8__["default"].sourceTypes.CONSTANT:
                return _components_Attribute_helpers_AttributeHelpers__WEBPACK_IMPORTED_MODULE_12__["default"].formatAttributeValue(sourceConfig.dataType, sourceConfigValue, getToken);
            default:
                return sourceConfigValue;
        }
    },
    /**
	 * Returns an instance of tokenManager for **FORM**
	 * @returns {tokenManager}
	 */ getTokenManager: function() {
        return (0,reactor2_src_Atomic_components_Helpers_Language_tokenManager__WEBPACK_IMPORTED_MODULE_10__["default"])(reactorCmps_src_generic_helpers_ProductConstants__WEBPACK_IMPORTED_MODULE_11__["default"].FORM);
    },
    formatEditableGridRecords: function(data, fields) {
        var records = [];
        data === null || data === void 0 ? void 0 : data.forEach(function(value) {
            var attributes = {};
            fields === null || fields === void 0 ? void 0 : fields.forEach(function(field) {
                var _field_attribute;
                attributes[field.attribute.oid] = {
                    value: value["COL".concat(field.order)] || null,
                    dirty: false,
                    type: (_field_attribute = field.attribute) === null || _field_attribute === void 0 ? void 0 : _field_attribute.type
                };
            });
            records.push({
                id: value.id,
                oid: value.oid,
                attributes: attributes
            });
        });
        return records;
    }
});


}),
"./src/responsiveform/helpers/ReduxHelpers.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_is_array_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.is-array.js */ "../node_modules/core-js/modules/es.array.is-array.js");
/* ESM import */var core_js_modules_es_array_is_array_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_is_array_js__WEBPACK_IMPORTED_MODULE_0__);
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_without_holes(arr) {
    if (Array.isArray(arr)) return _array_like_to_array(arr);
}
function _iterable_to_array(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _non_iterable_spread() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _to_consumable_array(arr) {
    return _array_without_holes(arr) || _iterable_to_array(arr) || _unsupported_iterable_to_array(arr) || _non_iterable_spread();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}

/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    selectStateSlice: function selectStateSlice(state, slice) {
        var asJs = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
        if (!state) {
            return null;
        }
        var stateSlice = Array.isArray(slice) ? state.getIn(_to_consumable_array(slice)) : state.get(slice);
        if (stateSlice === undefined || stateSlice === null) {
            return null;
        }
        return asJs && stateSlice.toJS ? stateSlice.toJS() : stateSlice;
    },
    createStrategyReducer: function createStrategyReducer(initialState, strategies) {
        var defaultStr = function(state) {
            return state;
        };
        return function(state, action) {
            var transformer = strategies[action.type] || defaultStr;
            return transformer(state || initialState, action);
        };
    }
});


}),
"./src/responsiveform/helpers/constants/AttributeConstants.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    SHORT_TEXT: "SHORT_TEXT",
    TEXT: "TEXT",
    LONG_TEXT: "LONG_TEXT",
    INTEGER: "INTEGER",
    DECIMAL: "DECIMAL",
    TIME: "TIME",
    DATE: "DATE",
    BOOLEAN: "BOOLEAN",
    FILE: "FILE",
    COMPLEX: "COMPLEX",
    ENUM: "ENUM",
    UNLIMITED_TEXT: "UNLIMITED_TEXT",
    RICH_TEXT: "RICH_TEXT",
    LONG_INTEGER: "LONG_INTEGER",
    OPTION: "OPTION",
    USER: "USER",
    DATETIME: "DATETIME"
});


}),
"./src/responsiveform/helpers/constants/RuleConstants.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    actions: {
        IF: "IF",
        ELSE: "ELSE",
        SET_PROPERTY: "SET_PROPERTY",
        SET_VARIABLE: "SET_VARIABLE",
        CLEAR_FIELD: "CLEAR_FIELD",
        DISPLAY_MESSAGE: "DISPLAY_MESSAGE",
        EXECUTE_RULE: "EXECUTE_RULE",
        EXECUTE_DATASET: "EXECUTE_DATASET",
        EXECUTE_TABLE_DATA_SOURCE: "EXECUTE_TABLE_DATA_SOURCE",
        EXECUTE_WEBSERVICE: "EXECUTE_WEBSERVICE",
        EXECUTE_WEBSERVICE_REST: "EXECUTE_WEBSERVICE_REST",
        READ_BARCODE: "READ_BARCODE",
        ADD_COMMENT: "ADD_COMMENT",
        OPEN_EXTERNAL_PAGE: "OPEN_EXTERNAL_PAGE",
        PERFORM_OPERATION_ON_TABLE: "PERFORM_OPERATION_ON_TABLE",
        CHANGE_INSTANCE_SITUATION: "CHANGE_INSTANCE_SITUATION",
        CHANGE_INSTANCE_PRIORITY: "CHANGE_INSTANCE_PRIORITY",
        EXECUTE_TASK: "EXECUTE_TASK",
        CANCEL_TASK: "CANCEL_TASK",
        TRANSLATE_FILE: "TRANSLATE_FILE",
        REFRESH_FIELD: "REFRESH_FIELD"
    },
    messages: {
        INFO: "INFO",
        SUCCESS: "SUCCESS",
        WARNING: "WARNING",
        CRITICAL: "CRITICAL"
    },
    elementProperties: {
        ENABLED: "ENABLED",
        VISIBLE: "VISIBLE",
        REQUIRED: "REQUIRED",
        VALUE: "VALUE",
        COLLAPSED: "COLLAPSED",
        ONLY_DATE: "ONLY_DATE"
    },
    flagValues: {
        YES: "YES",
        NO: "NO"
    },
    operationValues: {
        ADD: "ADD",
        EDIT: "EDIT",
        DELETE: "DELETE",
        IMPORT: "IMPORT",
        SUM: "SUM",
        COUNTER: "COUNTER",
        MIN: "MIN",
        MAX: "MAX",
        CONCAT: "CONCAT"
    }
});


}),
"./src/responsiveform/helpers/constants/SharedConstants.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    operationSourceTypes: {
        CONSTANT: "CONSTANT",
        VARIABLE: "VARIABLE"
    },
    sourceTypes: {
        ELEMENT: "ELEMENT",
        CONSTANT: "CONSTANT",
        USER_DATA: "USER_DATA",
        INSTANCE_DATA: "INSTANCE_DATA",
        RELATED_TABLE_DATA: "RELATED_TABLE_DATA",
        DATE: "DATE",
        TIME: "TIME",
        DATETIME: "DATETIME",
        VARIABLE: "VARIABLE",
        GEOLOCATION: "GEOLOCATION",
        EXPRESSION: "EXPRESSION",
        FUNCTION: "FUNCTION",
        RISK_DATA: "RISK_DATA",
        QUICK_SEARCH: "QUICK_SEARCH",
        PAGINATION: "PAGINATION",
        SIZE_NUMBER: "SIZE_NUMBER"
    },
    valueOptions: {
        VALUE_TRUE: "TRUE",
        VALUE_FALSE: "FALSE",
        USER_ID: "USER_ID",
        USER_NAME: "USER_NAME",
        USER_DEPARTMENT: "USER_DEPARTMENT",
        USER_POSITION: "USER_POSITION",
        PROCESS_CODE: "PROCESS_CODE",
        PROCESS_TITLE: "PROCESS_TITLE",
        PROCESS_ID: "PROCESS_ID",
        PROCESS_DESCRIPTION: "PROCESS_DESCRIPTION",
        PROCESS_STATUS: "PROCESS_STATUS",
        PROCESS_PRIORITY: "PROCESS_PRIORITY",
        PROCESS_ACTIVITY_ID: "PROCESS_ACTIVITY_ID",
        PROCESS_ACTIVITY_ACTION: "PROCESS_ACTIVITY_ACTION",
        PROCESS_ACTIVITY: "PROCESS_ACTIVITY",
        TODAY: "TODAY",
        CURRENT: "CURRENT",
        COORDINATE_LATITUDE: "COORDINATE_LATITUDE",
        COORDINATE_LONGITUDE: "COORDINATE_LONGITUDE",
        REQUESTER_USER: "REQUESTER_USER",
        REQUESTER_USER_LOGIN: "REQUESTER_USER_LOGIN",
        REQUESTER_USER_EMAIL: "REQUESTER_USER_EMAIL",
        REQUESTER_USER_REGISTRATION: "REQUESTER_USER_REGISTRATION",
        REQUESTER_USER_NAME: "REQUESTER_USER_NAME",
        REQUESTER_USER_DEFAULT_AREA_IDENTIFIER: "REQUESTER_USER_DEFAULT_AREA_IDENTIFIER",
        REQUESTER_USER_DEFAULT_AREA_NAME: "REQUESTER_USER_DEFAULT_AREA_NAME",
        REQUESTER_USER_DEFAULT_POSITION_IDENTIFIER: "REQUESTER_USER_DEFAULT_POSITION_IDENTIFIER",
        REQUESTER_USER_DEFAULT_POSITION_NAME: "REQUESTER_USER_DEFAULT_POSITION_NAME",
        REQUESTER_CUSTOMER: "REQUESTER_CUSTOMER",
        REQUESTER_CUSTOMER_COMPANY: "REQUESTER_CUSTOMER_COMPANY",
        REQUESTER_CUSTOMER_NAME: "REQUESTER_CUSTOMER_NAME",
        REQUESTER_CUSTOMER_EMAIL: "REQUESTER_CUSTOMER_EMAIL",
        INITIATOR: "INITIATOR",
        INITIATOR_LOGIN: "INITIATOR_LOGIN",
        INITIATOR_REGISTRATION: "INITIATOR_REGISTRATION",
        INITIATOR_NAME: "INITIATOR_NAME",
        INITIATOR_EMAIL: "INITIATOR_EMAIL",
        INITIATOR_DEFAULT_AREA_IDENTIFIER: "INITIATOR_DEFAULT_AREA_IDENTIFIER",
        INITIATOR_DEFAULT_AREA_NAME: "INITIATOR_DEFAULT_AREA_NAME",
        INITIATOR_DEFAULT_POSITION_IDENTIFIER: "INITIATOR_DEFAULT_POSITION_IDENTIFIER",
        INITIATOR_DEFAULT_POSITION_NAME: "INITIATOR_DEFAULT_POSITION_NAME",
        DATETIME: "DATETIME",
        RISK_PLAN_CODE: "RISK_PLAN_CODE",
        RISK_PLAN_REVISION_CODE: "RISK_PLAN_REVISION_CODE",
        RISK_PLAN_ID: "RISK_PLAN_ID",
        RISK_PLAN_NAME: "RISK_PLAN_NAME",
        RISK_PLAN_CONTEXT: "RISK_PLAN_CONTEXT",
        RISK_PLAN_BUSINESS_UNITED: "RISK_PLAN_BUSINESS_UNITED",
        RISK_PLAN_REVISION: "RISK_PLAN_REVISION",
        RISK_RISK_CODE: "RISK_RISK_CODE",
        RISK_RISK_ID: "RISK_RISK_ID",
        RISK_RISK_NAME: "RISK_RISK_NAME",
        RISK_CONTROL_CODE: "RISK_CONTROL_CODE",
        RISK_CONTROL_ID: "RISK_CONTROL_ID",
        RISK_CONTROL_NAME: "RISK_CONTROL_NAME",
        RISK_OPPORTUNITY_CODE: "RISK_OPPORTUNITY_CODE",
        RISK_OPPORTUNITY_ID: "RISK_OPPORTUNITY_ID",
        RISK_OPPORTUNITY_NAME: "RISK_OPPORTUNITY_NAME",
        RISK_EVALUATION_CODE: "RISK_EVALUATION_CODE",
        RISK_EVALUATION_ID: "RISK_EVALUATION_ID"
    },
    objectOperators: {
        SUM: "SUM",
        SUBTRACT: "SUBTRACT",
        MULTIPLY: "MULTIPLY",
        DIVIDE: "DIVIDE",
        CONCAT: "CONCAT",
        MOD: "MOD",
        DAYS_BETWEEN: "DAYS_BETWEEN",
        WORKING_DAYS_BETWEEN: "WORKING_DAYS_BETWEEN",
        TIME_BETWEEN: "TIME_BETWEEN"
    },
    logicOperators: {
        AND: "AND",
        OR: "OR"
    },
    conditionalOperators: {
        EQUAL: "EQUAL",
        DIFFERENT: "DIFFERENT",
        NULL: "NULL",
        NOT_NULL: "NOT_NULL",
        CONTAINS: "CONTAINS",
        NOT_CONTAINS: "NOT_CONTAINS",
        GREATER: "GREATER",
        LESS: "LESS",
        GREATER_EQUAL: "GREATER_EQUAL",
        LESS_EQUAL: "LESS_EQUAL",
        TRUE: "TRUE",
        FALSE: "FALSE"
    },
    formatFunctions: {
        UPPERCASE: "UPPERCASE",
        LOWERCASE: "LOWERCASE",
        TRIM: "TRIM",
        ABSOLUTE: "ABSOLUTE",
        SQUARE_ROOT: "SQUARE_ROOT",
        FACTORIAL: "FACTORIAL",
        ROUND_UP: "ROUND_UP",
        ROUND_DOWN: "ROUND_DOWN",
        ROUND: "ROUND",
        TRUNCATE: "TRUNCATE",
        HOURS: "HOURS",
        MINUTES: "MINUTES",
        CONVERT_MINUTES: "CONVERT_MINUTES",
        CONVERT_SECONDS: "CONVERT_SECONDS",
        YEAR: "YEAR",
        MONTH: "MONTH",
        DAY: "DAY",
        WEEK_DAY: "WEEK_DAY",
        WEEK_NUMBER: "WEEK_NUMBER",
        DENY: "DENY",
        ADD_DAY: "ADD_DAY",
        ADD_HOUR: "ADD_HOUR",
        TABLE_FIELD_VALUE: "TABLE_FIELD_VALUE",
        WORKING_DATE: "WORKING_DATE",
        TRANSLATION: "TRANSLATION",
        TEXT: "TEXT"
    },
    returnTypes: {
        STRING: "STRING",
        INTEGER: "INTEGER",
        DECIMAL: "DECIMAL",
        DATE: "DATE",
        TIME: "TIME",
        BOOLEAN: "BOOLEAN",
        CUSTOM: "CUSTOM",
        DATETIME: "DATETIME"
    }
});


}),
"./src/responsiveform/helpers/constants/SharedConstantsMapping.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _SharedConstants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SharedConstants */ "./src/responsiveform/helpers/constants/SharedConstants.js");
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}

var _obj, _obj1, _obj2, _obj3, _obj4, _obj5, _obj6;
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    operationSourceTypes: (_obj = {}, _define_property(_obj, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.CONSTANT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.CONSTANT,
        name: 101495
    }), _define_property(_obj, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.VARIABLE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.VARIABLE,
        name: 200014
    }), _obj),
    sourceTypes: (_obj1 = {}, _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.ELEMENT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.ELEMENT,
        name: 219110
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.CONSTANT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.CONSTANT,
        name: 101495
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.USER_DATA, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.USER_DATA,
        name: 100107
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.INSTANCE_DATA, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.INSTANCE_DATA,
        name: 209129
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.RELATED_TABLE_DATA, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.RELATED_TABLE_DATA,
        name: 315342,
        helpText: 315343
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.DATE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.DATE,
        name: 100209
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.TIME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.TIME,
        name: 100210
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.DATETIME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.DATETIME,
        name: 101802
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.VARIABLE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.VARIABLE,
        name: 200014
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.GEOLOCATION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.GEOLOCATION,
        name: 306554
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.EXPRESSION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.EXPRESSION,
        name: 107190
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.FUNCTION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.FUNCTION,
        name: 114832
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.RISK_DATA, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.RISK_DATA,
        name: 100354
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.QUICK_SEARCH, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.QUICK_SEARCH,
        name: 317862
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.PAGINATION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.PAGINATION,
        name: 318071
    }), _define_property(_obj1, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.SIZE_NUMBER, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].sourceTypes.SIZE_NUMBER,
        name: 318294
    }), _obj1),
    valueOptions: (_obj2 = {}, _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.VALUE_TRUE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.VALUE_TRUE,
        name: 301789,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.BOOLEAN
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.VALUE_FALSE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.VALUE_FALSE,
        name: 301790,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.BOOLEAN
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.USER_ID, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.USER_ID,
        name: 100113,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.USER_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.USER_NAME,
        name: 100111,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.USER_DEPARTMENT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.USER_DEPARTMENT,
        name: 100112,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.USER_POSITION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.USER_POSITION,
        name: 100029,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_CODE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_CODE,
        name: 100028,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_TITLE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_TITLE,
        name: 100380,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_ID, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_ID,
        name: 100528,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_DESCRIPTION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_DESCRIPTION,
        name: 100087,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_STATUS, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_STATUS,
        name: 201738,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_PRIORITY, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_PRIORITY,
        name: 101554,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_ACTIVITY_ID, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_ACTIVITY_ID,
        group: 200361,
        name: 100528,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_ACTIVITY, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_ACTIVITY,
        group: 200361,
        name: 100111,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_ACTIVITY_ACTION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.PROCESS_ACTIVITY_ACTION,
        group: 200361,
        name: 208040,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.TODAY, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.TODAY,
        name: 102361,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.DATE
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.CURRENT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.CURRENT,
        name: 101385,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.TIME
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.COORDINATE_LATITUDE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.COORDINATE_LATITUDE,
        name: 204425,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.DECIMAL
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.COORDINATE_LONGITUDE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.COORDINATE_LONGITUDE,
        name: 204421,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.DECIMAL
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_LOGIN, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_LOGIN,
        group: 300902,
        name: 100109,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_EMAIL, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_EMAIL,
        group: 300902,
        name: 100114,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_REGISTRATION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_REGISTRATION,
        group: 300902,
        name: 100113,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_NAME,
        group: 300902,
        name: 100111,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_DEFAULT_AREA_IDENTIFIER, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_DEFAULT_AREA_IDENTIFIER,
        group: 300902,
        name: 308587,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_DEFAULT_AREA_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_DEFAULT_AREA_NAME,
        group: 300902,
        name: 308588,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_DEFAULT_POSITION_IDENTIFIER, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_DEFAULT_POSITION_IDENTIFIER,
        group: 300902,
        name: 308589,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_DEFAULT_POSITION_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_USER_DEFAULT_POSITION_NAME,
        group: 300902,
        name: 308590,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_CUSTOMER_COMPANY, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_CUSTOMER_COMPANY,
        group: 304168,
        name: 102351,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_CUSTOMER_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_CUSTOMER_NAME,
        group: 304168,
        name: 100111,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_CUSTOMER_EMAIL, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.REQUESTER_CUSTOMER_EMAIL,
        group: 304168,
        name: 100114,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_LOGIN, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_LOGIN,
        group: 106947,
        name: 100109,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_REGISTRATION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_REGISTRATION,
        group: 106947,
        name: 100113,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_NAME,
        group: 106947,
        name: 100111,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_EMAIL, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_EMAIL,
        group: 106947,
        name: 100114,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_DEFAULT_AREA_IDENTIFIER, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_DEFAULT_AREA_IDENTIFIER,
        group: 106947,
        name: 308587,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_DEFAULT_AREA_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_DEFAULT_AREA_NAME,
        group: 106947,
        name: 308588,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_DEFAULT_POSITION_IDENTIFIER, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_DEFAULT_POSITION_IDENTIFIER,
        group: 106947,
        name: 308589,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_DEFAULT_POSITION_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.INITIATOR_DEFAULT_POSITION_NAME,
        group: 106947,
        name: 308590,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.DATETIME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.DATETIME,
        name: 101385,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.DATETIME
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_CODE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_CODE,
        group: 105942,
        name: 100028,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_REVISION_CODE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_REVISION_CODE,
        group: 105942,
        name: 203049,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_ID, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_ID,
        group: 105942,
        name: 100528,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_NAME,
        group: 105942,
        name: 100111,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_NAME,
        group: 105942,
        name: 100111,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_CONTEXT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_CONTEXT,
        group: 105942,
        name: 112850,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_BUSINESS_UNITED, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_BUSINESS_UNITED,
        group: 105942,
        name: 102598,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_REVISION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_PLAN_REVISION,
        group: 105942,
        name: 100385,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_RISK_CODE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_RISK_CODE,
        group: 100354,
        name: 100028,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_RISK_ID, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_RISK_ID,
        group: 100354,
        name: 100528,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_RISK_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_RISK_NAME,
        group: 100354,
        name: 100111,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_CONTROL_CODE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_CONTROL_CODE,
        group: 100898,
        name: 100028,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_CONTROL_ID, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_CONTROL_ID,
        group: 100898,
        name: 100528,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_CONTROL_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_CONTROL_NAME,
        group: 100898,
        name: 100111,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_OPPORTUNITY_CODE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_OPPORTUNITY_CODE,
        group: 200629,
        name: 100028,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_OPPORTUNITY_ID, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_OPPORTUNITY_ID,
        group: 200629,
        name: 100528,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_OPPORTUNITY_NAME, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_OPPORTUNITY_NAME,
        group: 200629,
        name: 100111,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_EVALUATION_CODE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_EVALUATION_CODE,
        group: 100940,
        name: 100028,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj2, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_EVALUATION_ID, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].valueOptions.RISK_EVALUATION_ID,
        group: 100940,
        name: 100528,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _obj2),
    objectOperators: (_obj3 = {}, _define_property(_obj3, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.SUM, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.SUM,
        name: 306549
    }), _define_property(_obj3, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.SUBTRACT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.SUBTRACT,
        name: 306550
    }), _define_property(_obj3, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.MULTIPLY, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.MULTIPLY,
        name: 306551
    }), _define_property(_obj3, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.DIVIDE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.DIVIDE,
        name: 306552
    }), _define_property(_obj3, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.CONCAT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.CONCAT,
        name: 306553
    }), _define_property(_obj3, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.MOD, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.MOD,
        name: 309342
    }), _define_property(_obj3, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.DAYS_BETWEEN, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.DAYS_BETWEEN,
        name: 309379
    }), _define_property(_obj3, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.TIME_BETWEEN, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.TIME_BETWEEN,
        name: 309380
    }), _define_property(_obj3, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.WORKING_DAYS_BETWEEN, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].objectOperators.WORKING_DAYS_BETWEEN,
        name: 316740
    }), _obj3),
    logicOperators: (_obj4 = {}, _define_property(_obj4, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].logicOperators.AND, {
        name: 202379,
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].logicOperators.AND
    }), _define_property(_obj4, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].logicOperators.OR, {
        name: 202377,
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].logicOperators.OR
    }), _obj4),
    conditionalOperators: (_obj5 = {}, _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.EQUAL, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.EQUAL,
        name: 301791
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.DIFFERENT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.DIFFERENT,
        name: 301792
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.NULL, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.NULL,
        name: 301793
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.NOT_NULL, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.NOT_NULL,
        name: 301794
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.CONTAINS, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.CONTAINS,
        name: 301795
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.NOT_CONTAINS, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.NOT_CONTAINS,
        name: 301796
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.GREATER, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.GREATER,
        name: 301797
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.LESS, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.LESS,
        name: 301798
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.GREATER_EQUAL, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.GREATER_EQUAL,
        name: 301799
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.LESS_EQUAL, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.LESS_EQUAL,
        name: 301800
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.TRUE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.TRUE,
        name: 301789
    }), _define_property(_obj5, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.FALSE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].conditionalOperators.FALSE,
        name: 301790
    }), _obj5),
    formatFunctions: (_obj6 = {}, _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.UPPERCASE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.UPPERCASE,
        name: 301805,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.LOWERCASE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.LOWERCASE,
        name: 301806,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.TRIM, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.TRIM,
        name: 301807,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ABSOLUTE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ABSOLUTE,
        name: 202153,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.DECIMAL
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.SQUARE_ROOT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.SQUARE_ROOT,
        name: 105946,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.DECIMAL
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.FACTORIAL, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.FACTORIAL,
        name: 301808,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.DECIMAL
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ROUND_UP, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ROUND_UP,
        name: 215760,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ROUND_DOWN, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ROUND_DOWN,
        name: 215759,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ROUND, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ROUND,
        name: 301809,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.TRUNCATE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.TRUNCATE,
        name: 301810,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.HOURS, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.HOURS,
        name: 107372,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.MINUTES, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.MINUTES,
        name: 112083,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.CONVERT_MINUTES, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.CONVERT_MINUTES,
        name: 301811,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.CONVERT_SECONDS, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.CONVERT_SECONDS,
        name: 301812,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.YEAR, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.YEAR,
        name: 100437,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.MONTH, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.MONTH,
        name: 100436,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.DAY, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.DAY,
        name: 100434,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.WEEK_DAY, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.WEEK_DAY,
        name: 301813,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.WEEK_NUMBER, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.WEEK_NUMBER,
        name: 107881,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.INTEGER
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.DENY, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.DENY,
        name: 301814,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.BOOLEAN
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ADD_DAY, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ADD_DAY,
        name: 308181,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.DATE
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ADD_HOUR, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.ADD_HOUR,
        name: 308182,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.DATE
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.TABLE_FIELD_VALUE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.TABLE_FIELD_VALUE,
        name: 309201,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.CUSTOM
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.WORKING_DATE, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.WORKING_DATE,
        name: 107385,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.BOOLEAN
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.TRANSLATION, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.TRANSLATION,
        name: 315759,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _define_property(_obj6, _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.TEXT, {
        value: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].formatFunctions.TEXT,
        name: 100552,
        returnType: _SharedConstants__WEBPACK_IMPORTED_MODULE_0__["default"].returnTypes.STRING
    }), _obj6)
});


}),
"./src/workflow/Defaultframe/common/Filters/Collections/ProcessModelFilterCollection.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0__);

var ProcessModelFilterCollection = SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0___default()({
    model: {
        primaryKey: "code",
        proxyCfg: {
            route: "workflow/app/defaultframe/filter/list_process_filter.php?classname=wf_exec_activity_pend&"
        }
    },
    collection: {
        defaultOrder: {
            ccode: "ASC"
        }
    }
});
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProcessModelFilterCollection);


}),
"./src/workflow/Defaultframe/common/Filters/Collections/WorkflowTypeFilterCollection.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0__);

var WorkflowTypeFilterCollection = SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_0___default()({
    model: {
        primaryKey: "code",
        proxyCfg: {
            route: "workflow/app/defaultframe/filter/list_type_filter.php?classname=wf_exec_activity_pend&"
        }
    },
    collection: {
        defaultOrder: {
            ccode: "ASC"
        }
    }
});
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WorkflowTypeFilterCollection);


}),
"./src/workflow/Defaultframe/common/Filters/Constants/AssociationFilterConst.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var ARTICLE = "article";
var ASSET = "asset";
var CONTROL = "control";
var CONSEQUENCE = "consequence";
var DOCUMENT = "document";
var INITIATIVE = "initiative";
var ISOLATED_ACTION = "isolated-action";
var KANBAN = "kanban";
var MAINTENANCE = "maintenance";
var MEETING = "meeting";
var OBJECT = "object";
var PROCESS = "process";
var PRODUCT = "product";
var PROGRAM = "program";
var PROJECT = "project";
var PROJECT_TASK = "project-task";
var REQUIREMENT = "requirement";
var RISK = "risk";
var SUPPLY = "supply";
var SURVEY = "survey";
var STOREROOM = "storeroom";
var STOREROOM_TRANSARCHIVAL = "storeroom-transarchival";
var TRAINING = "training";
var WORKFLOW = "workflow";
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    ARTICLE: ARTICLE,
    ASSET: ASSET,
    CONTROL: CONTROL,
    CONSEQUENCE: CONSEQUENCE,
    DOCUMENT: DOCUMENT,
    ISOLATED_ACTION: ISOLATED_ACTION,
    INITIATIVE: INITIATIVE,
    KANBAN: KANBAN,
    MAINTENANCE: MAINTENANCE,
    MEETING: MEETING,
    OBJECT: OBJECT,
    PROCESS: PROCESS,
    PRODUCT: PRODUCT,
    PROGRAM: PROGRAM,
    PROJECT: PROJECT,
    PROJECT_TASK: PROJECT_TASK,
    REQUIREMENT: REQUIREMENT,
    RISK: RISK,
    SUPPLY: SUPPLY,
    SURVEY: SURVEY,
    STOREROOM: STOREROOM,
    STOREROOM_TRANSARCHIVAL: STOREROOM_TRANSARCHIVAL,
    TRAINING: TRAINING,
    WORKFLOW: WORKFLOW
});


}),
"./src/workflow/Defaultframe/common/Filters/Constants/InstanceStatusConst.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var IN_PROGRESS = 1;
var POSTPONED = 2;
var CANCELLED = 3;
var FINISHED = 4;
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    IN_PROGRESS: IN_PROGRESS,
    POSTPONED: POSTPONED,
    CANCELLED: CANCELLED,
    FINISHED: FINISHED
});


}),
"./src/workflow/Defaultframe/common/Filters/Constants/SLAStatusConst.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var PLAY = 10;
var FIRST_RESPONSE = 20;
var PAUSE = 30;
var STOP = 40;
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    PLAY: PLAY,
    FIRST_RESPONSE: FIRST_RESPONSE,
    PAUSE: PAUSE,
    STOP: STOP
});


}),
"./src/workflow/Defaultframe/common/Filters/CustomSEFields/ExternalExecutorSEFieldMulti.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var reactor2_src_Form_components_Mols_MultiSelect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor2/src/Form/components/Mols/MultiSelect */ "../reactor2/src/Form/components/Mols/MultiSelect.jsx");
/* ESM import */var reactor2_src_Form_components_Mols_MultiSelect__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Mols_MultiSelect__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/TextField */ "../reactor2/src/Form/components/Orgs/SEFields/TextField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC */ "../reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC.js");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/context/SEFieldsContext */ "../reactor2/src/Form/components/Orgs/SEFields/context/SEFieldsContext.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_helpers_SEFieldsHelpers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/helpers/SEFieldsHelpers */ "../reactor2/src/Form/components/Orgs/SEFields/helpers/SEFieldsHelpers.js");
/* ESM import */var reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactor2/src/Atomic/components/Layout/StackedLayout */ "../reactor2/src/Atomic/components/Layout/StackedLayout.jsx");
/* ESM import */var reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_7__);
/* istanbul ignore file */ function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}








var getFields = function(getToken) {
    return [
        {
            title: getToken(104256),
            value: "department"
        },
        {
            title: getToken(114832),
            value: "role"
        },
        {
            title: getToken(100044),
            value: "user"
        }
    ];
};
var initialValue = {
    fields: [],
    value: ""
};
var CustomSEFieldEditMode = function(props) {
    var value = props.value, onChange = props.onChange, getToken = props.getToken, setCustomBackToList = props.setCustomBackToList, setCustomTitle = props.setCustomTitle;
    var _useState = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value || initialValue), 2), values = _useState[0], setValues = _useState[1];
    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(type, value) {
        var newValues = _object_spread_props(_object_spread({}, values), _define_property({}, type, value));
        setValues(newValues);
        onChange(newValues.fields.length && newValues.value ? newValues : null);
    }, [
        setValues,
        onChange,
        values
    ]);
    var handleChangeFields = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(value) {
        return handleChange("fields", value);
    }, [
        handleChange
    ]);
    var handleChangeValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(value) {
        return handleChange("value", value);
    }, [
        handleChange
    ]);
    var memoizedFieldsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {
        return getFields(getToken);
    }, [
        getToken
    ]);
    var memoizedFieldsList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_7___default()), null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Form_components_Mols_MultiSelect__WEBPACK_IMPORTED_MODULE_2___default()), {
            data: memoizedFieldsData,
            onChange: handleChangeFields,
            textField: "title",
            value: values.fields,
            valueField: "value",
            limit: 1
        }));
    }, [
        memoizedFieldsData,
        handleChangeFields,
        values
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {
        setCustomBackToList(values.fields.length ? function() {
            return setValues(initialValue);
        } : null);
        setCustomTitle(values.fields.length && values.fields[0].title);
    }, [
        values.fields,
        setCustomBackToList,
        setCustomTitle
    ]);
    var getFilterComponent = function() {
        var component = memoizedFieldsList;
        if (values.fields.length) {
            component = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_7___default()), null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_3___default()), {
                title: values.fields[0].title,
                onChange: handleChangeValue,
                value: values.value
            }));
        }
        return component;
    };
    return getFilterComponent();
};
var getFormattedTagValue = function(props) {
    return props.value;
};
function ExternalExecutorSEFieldMulti(props) {
    var getCmp = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(ctx) {
        var renderMode = ctx.renderMode;
        var Component = reactor2_src_Form_components_Orgs_SEFields_helpers_SEFieldsHelpers__WEBPACK_IMPORTED_MODULE_6__["default"].getComponent(props.isEdit, renderMode, null, CustomSEFieldEditMode);
        var value = props.value, getToken = props.getToken, title = props.title;
        if (reactor2_src_Form_components_Orgs_SEFields_helpers_SEFieldsHelpers__WEBPACK_IMPORTED_MODULE_6__["default"].getIsRenderingTag(renderMode)) {
            var _value_fields_;
            title = "".concat(props.getToken(108527), " ➔ ").concat(value === null || value === void 0 ? void 0 : (_value_fields_ = value.fields[0]) === null || _value_fields_ === void 0 ? void 0 : _value_fields_.title);
            value = getFormattedTagValue(value, getToken);
        }
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(Component, _object_spread_props(_object_spread({}, props), {
            value: value,
            title: title
        }));
    }, [
        props
    ]);
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_5___default().Consumer), null, getCmp);
}
ExternalExecutorSEFieldMulti.propTypes = reactor2_src_Form_components_Orgs_SEFields_helpers_SEFieldsHelpers__WEBPACK_IMPORTED_MODULE_6__["default"].getDefaultProps();
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_4___default()(ExternalExecutorSEFieldMulti));


}),
"./src/workflow/Defaultframe/common/Filters/CustomSEFields/TransarchivalTypeField.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/SelectField */ "../reactor2/src/Form/components/Orgs/SEFields/SelectField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_2__);
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}



var TransarchivalTypeField = function(props, getToken) {
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement((reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_1___default()), _object_spread({
        title: getToken("100366"),
        autoFocus: false,
        data: [
            {
                value: 1,
                name: getToken("207578")
            },
            {
                value: 2,
                name: getToken("207579")
            },
            {
                value: 3,
                name: getToken("104030")
            },
            {
                value: 4,
                name: getToken("104428")
            },
            {
                value: 5,
                name: getToken("103200")
            },
            {
                value: 6,
                name: getToken("208527")
            }
        ],
        valueField: "value",
        textField: "name"
    }, props));
};
TransarchivalTypeField.propTypes = {
    props: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().array),
    getToken: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func)
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TransarchivalTypeField);


}),
"./src/workflow/Defaultframe/common/Filters/CustomSEFields/ViewByProcessOrWorkflowTypeField.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  CustomSEFieldEditMode: function() { return CustomSEFieldEditMode; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; },
  getCountPendingTasks: function() { return getCountPendingTasks; }
});
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.parse-int.js */ "../node_modules/core-js/modules/es.parse-int.js");
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Badge */ "../reactor2/src/Atomic/components/Atoms/Badge.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Badge__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Badge__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactor2_src_Form_components_Mols_MultiSelect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactor2/src/Form/components/Mols/MultiSelect */ "../reactor2/src/Form/components/Mols/MultiSelect.jsx");
/* ESM import */var reactor2_src_Form_components_Mols_MultiSelect__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Mols_MultiSelect__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/ZoomField */ "../reactor2/src/Form/components/Orgs/SEFields/ZoomField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactor2/src/Atomic/components/Layout/StackedLayout */ "../reactor2/src/Atomic/components/Layout/StackedLayout.jsx");
/* ESM import */var reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC */ "../reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC.js");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/context/SEFieldsContext */ "../reactor2/src/Form/components/Orgs/SEFields/context/SEFieldsContext.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_helpers_SEFieldsHelpers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/helpers/SEFieldsHelpers */ "../reactor2/src/Form/components/Orgs/SEFields/helpers/SEFieldsHelpers.js");
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters_Collections_WorkflowTypeFilterCollection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters/Collections/WorkflowTypeFilterCollection */ "./src/workflow/Defaultframe/common/Filters/Collections/WorkflowTypeFilterCollection.js");
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters_Collections_ProcessModelFilterCollection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters/Collections/ProcessModelFilterCollection */ "./src/workflow/Defaultframe/common/Filters/Collections/ProcessModelFilterCollection.js");
/* ESM import */var reactorCmps_src_workflow_Defaultframe_constants_ViewByProcessOrWfTypeConstant__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/constants/ViewByProcessOrWfTypeConstant */ "./src/workflow/Defaultframe/constants/ViewByProcessOrWfTypeConstant.js");
/* ESM import */var reactor2_src_constants_statusConstants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactor2/src/constants/statusConstants */ "../reactor2/src/constants/statusConstants.js");
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}














var initialValue = {
    fields: [],
    value: ""
};
var deadlineTypeMap = {
    1: reactor2_src_constants_statusConstants__WEBPACK_IMPORTED_MODULE_13__.STATUS_SUCCESS,
    2: reactor2_src_constants_statusConstants__WEBPACK_IMPORTED_MODULE_13__.STATUS_WARNING,
    3: reactor2_src_constants_statusConstants__WEBPACK_IMPORTED_MODULE_13__.STATUS_ERROR
};
var getFields = function(getToken) {
    return [
        {
            title: getToken(200037),
            value: reactorCmps_src_workflow_Defaultframe_constants_ViewByProcessOrWfTypeConstant__WEBPACK_IMPORTED_MODULE_12__.PROCESS
        },
        {
            title: getToken(301294),
            value: reactorCmps_src_workflow_Defaultframe_constants_ViewByProcessOrWfTypeConstant__WEBPACK_IMPORTED_MODULE_12__.WORKFLOW_TYPE
        }
    ];
};
var getCountPendingTasks = function(param) {
    var record = param.record;
    var deadlineType = parseInt(record.deadline);
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Atomic_components_Atoms_Badge__WEBPACK_IMPORTED_MODULE_3___default()), {
        value: parseInt(record.count),
        status: deadlineTypeMap[deadlineType]
    });
};
var getZoomFields = function(getToken) {
    return [
        {
            field: "code",
            primaryKey: true,
            showOnTag: false,
            showOnGrid: false,
            showOnFilter: false
        },
        {
            field: "count",
            showOnTag: true,
            showOnGrid: false,
            showOnFilter: false,
            customType: getCountPendingTasks,
            title: getToken(100111)
        },
        {
            field: "title",
            showOnTag: true,
            showOnGrid: true,
            showOnFilter: true,
            title: getToken(214946)
        },
        {
            field: "text",
            showOnTag: true,
            showOnGrid: true,
            showOnFilter: true,
            title: getToken(100111)
        }
    ];
};
var CustomSEFieldEditMode = function(props) {
    var value = props.value, onChange = props.onChange, getToken = props.getToken, setCustomBackToList = props.setCustomBackToList, setCustomTitle = props.setCustomTitle;
    var _useState = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(value || initialValue), 2), values = _useState[0], setValues = _useState[1];
    // istanbul ignore next because is called by useEffect only for external prop
    var defineInitialValue = function() {
        setValues(initialValue);
    };
    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(type, value) {
        var newValues = _object_spread_props(_object_spread({}, values), _define_property({}, type, value));
        setValues(newValues);
        onChange(newValues.fields.length && newValues.value ? newValues : null);
    }, [
        setValues,
        onChange,
        values
    ]);
    var handleChangeFields = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(value) {
        return handleChange("fields", value);
    }, [
        handleChange
    ]);
    var handleChangeValue = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(value) {
        return handleChange("value", value);
    }, [
        handleChange
    ]);
    var memoizedFieldsData = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {
        return getFields(getToken);
    }, [
        getToken
    ]);
    var memoizedFieldsList = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_6___default()), null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Form_components_Mols_MultiSelect__WEBPACK_IMPORTED_MODULE_4___default()), {
            data: memoizedFieldsData,
            onChange: handleChangeFields,
            textField: "title",
            value: values.fields,
            valueField: "value",
            limit: 1
        }));
    }, [
        memoizedFieldsData,
        handleChangeFields,
        values
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {
        setCustomBackToList(values.fields.length ? defineInitialValue : null);
        setCustomTitle(values.fields.length && values.fields[0].title);
    }, [
        values.fields,
        setCustomBackToList,
        setCustomTitle
    ]);
    var getSelectField = function(type, title, onChange, value) {
        var collection = type === "workflowType" ? reactorCmps_src_workflow_Defaultframe_common_Filters_Collections_WorkflowTypeFilterCollection__WEBPACK_IMPORTED_MODULE_10__["default"] : reactorCmps_src_workflow_Defaultframe_common_Filters_Collections_ProcessModelFilterCollection__WEBPACK_IMPORTED_MODULE_11__["default"];
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_5___default()), {
            limit: 1,
            title: title,
            value: value,
            textField: "text",
            valueField: "code",
            onChange: onChange,
            Collection: collection,
            fields: getZoomFields(getToken),
            extraRequestColumns: [
                "code",
                "title"
            ]
        });
    };
    var getFilterComponent = function() {
        var component = memoizedFieldsList;
        if (values.fields.length) {
            component = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Atomic_components_Layout_StackedLayout__WEBPACK_IMPORTED_MODULE_6___default()), null, getSelectField(values.fields[0].value, values.fields[0].title, handleChangeValue, values.value));
        }
        return component;
    };
    return getFilterComponent();
};
function ViewByProcessOrWorkflowTypeField(props) {
    var getFormattedTagValue = function(value) {
        var _value_value_;
        return value === null || value === void 0 ? void 0 : (_value_value_ = value.value[0]) === null || _value_value_ === void 0 ? void 0 : _value_value_.title;
    };
    var getCmp = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(ctx) {
        var renderMode = ctx.renderMode;
        var Component = reactor2_src_Form_components_Orgs_SEFields_helpers_SEFieldsHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].getComponent(props.isEdit, renderMode, null, CustomSEFieldEditMode);
        var value = props.value, getToken = props.getToken, title = props.title;
        if (reactor2_src_Form_components_Orgs_SEFields_helpers_SEFieldsHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].getIsRenderingTag(renderMode)) {
            var _value_fields_;
            title = "".concat(getToken(106056), " ➔ ").concat(value === null || value === void 0 ? void 0 : (_value_fields_ = value.fields[0]) === null || _value_fields_ === void 0 ? void 0 : _value_fields_.title);
            value = getFormattedTagValue(value);
        }
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(Component, _object_spread_props(_object_spread({}, props), {
            value: value,
            title: title
        }));
    }, [
        props
    ]);
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((reactor2_src_Form_components_Orgs_SEFields_context_SEFieldsContext__WEBPACK_IMPORTED_MODULE_8___default().Consumer), null, getCmp);
}
ViewByProcessOrWorkflowTypeField.propTypes = reactor2_src_Form_components_Orgs_SEFields_helpers_SEFieldsHelpers__WEBPACK_IMPORTED_MODULE_9__["default"].getDefaultProps();
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_7___default()(ViewByProcessOrWorkflowTypeField));


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getAssignedToMeField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_getToggleField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/getToggleField */ "../reactor2/src/Form/containers/Filter/Fields/getToggleField.js");

/**
 * Deprecated use getEnabledActivityExecutorField
 */ var getAssignedToMeField = function(getToken, name) {
    return (0,reactor2_src_Form_containers_Filter_Fields_getToggleField__WEBPACK_IMPORTED_MODULE_0__["default"])({
        name: name,
        title: 305433,
        toggleValue: 1,
        toggleTitle: getToken("305433")
    }, {});
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getAssignedToMeField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getAssociationField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; },
  getField: function() { return getField; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_array_sort_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.array.sort.js */ "../node_modules/core-js/modules/es.array.sort.js");
/* ESM import */var core_js_modules_es_array_sort_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_sort_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_getAssociationField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/getAssociationField */ "../reactor2/src/Form/containers/Filter/Fields/getAssociationField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_MacheteZoomField__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/MacheteZoomField */ "../reactor2/src/Form/components/Orgs/SEFields/MacheteZoomField.jsx");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/tokenManager */ "../reactor2/src/Atomic/components/Helpers/Language/tokenManager.js");
/* ESM import */var reactorCmps_src_workflow_helpers_constants_WorkflowConstants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactorCmps/src/workflow/helpers/constants/WorkflowConstants */ "./src/workflow/helpers/constants/WorkflowConstants.js");
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters/Constants/AssociationFilterConst */ "./src/workflow/Defaultframe/common/Filters/Constants/AssociationFilterConst.js");
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters_CustomSEFields_TransarchivalTypeField__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters/CustomSEFields/TransarchivalTypeField */ "./src/workflow/Defaultframe/common/Filters/CustomSEFields/TransarchivalTypeField.jsx");








/**
* Retorna um provedor getToken para **arquivos javascript puros**
* @returns {tokenManager}
*/ var getTokenManager = function() {
    return (0,reactor2_src_Atomic_components_Helpers_Language_tokenManager__WEBPACK_IMPORTED_MODULE_4__["default"])(reactorCmps_src_workflow_helpers_constants_WorkflowConstants__WEBPACK_IMPORTED_MODULE_5__["default"].PRODUCT);
};
var getField = function(name, title, identifier, idField, nmField) {
    var getToken = getTokenManager();
    var primaryField = identifier === reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].OBJECT ? "cdAssocItem" : "cdAssoc";
    var fields = [];
    if (identifier === reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].STOREROOM_TRANSARCHIVAL) {
        fields = [
            {
                field: primaryField,
                primaryKey: true
            },
            {
                field: idField,
                showOnTag: true,
                showOnGrid: true,
                showOnFilter: true,
                title: getToken("100528")
            },
            {
                field: nmField,
                showOnTag: true,
                showOnGrid: true,
                title: getToken("100366"),
                primaryText: true
            },
            {
                field: "fgType",
                showOnTag: false,
                showOnGrid: false,
                showOnFilter: true,
                customType: function(props) {
                    return (0,reactorCmps_src_workflow_Defaultframe_common_Filters_CustomSEFields_TransarchivalTypeField__WEBPACK_IMPORTED_MODULE_7__["default"])(props, getToken);
                },
                title: getToken("100366")
            }
        ];
    } else {
        fields = [
            {
                field: primaryField,
                primaryKey: true
            },
            {
                field: idField,
                showOnTag: true,
                showOnGrid: true,
                title: getToken("100528")
            },
            {
                field: nmField,
                showOnTag: true,
                showOnGrid: true,
                title: getToken("100380"),
                primaryText: true
            }
        ];
    }
    return {
        name: name,
        component: reactor2_src_Form_components_Orgs_SEFields_MacheteZoomField__WEBPACK_IMPORTED_MODULE_3__["default"],
        props: {
            title: getToken(title),
            identifier: "workflow-filter,".concat(identifier),
            fields: fields
        }
    };
};
var getAssociationField = function(getToken) {
    var items = [
        getField("cdArticle", "108328", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].ARTICLE, "idArticle", "nmArticle"),
        getField("cdAsset", "102270", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].ASSET, "idObject", "nmObject"),
        getField("cdConsequence", "111388", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].CONSEQUENCE, "idConsequence", "nmConsequence"),
        getField("cdControl", "204042", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].CONTROL, "idControlAnalysis", "nmControl"),
        getField("cdGenActivity", "107897", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].ISOLATED_ACTION, "idActivity", "nmActivity"),
        getField("cdPortfolio", "103998", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].INITIATIVE, "idPortfolio", "nmPortfolio"),
        getField("cdDocument", "200231", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].DOCUMENT, "idDocument", "nmTitle"),
        getField("cdKanban", "300483", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].KANBAN, "nrTask", "nmTitle"),
        getField("cdMaintenance", "100289", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].MAINTENANCE, "idActivity", "nmActivity"),
        getField("cdMeeting", "304785", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].MEETING, "idMeeting", "nmMeeting"),
        getField("cdObject", "111287", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].OBJECT, "idItem", "nmItem"),
        getField("cdProc", "100377", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].PROCESS, "idActivity", "nmActivity"),
        getField("cdProduct", "101524", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].PRODUCT, "idObject", "nmObject"),
        getField("cdTask", "101000", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].PROGRAM, "nmIdtask", "nmTask"),
        getField("cdProject", "102175", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].PROJECT, "nmIdtask", "nmTask"),
        getField("cdProjectTask", "113519", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].PROJECT_TASK, "nmIdtask", "nmTask"),
        getField("cdRequirement", "218888", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].REQUIREMENT, "idRequirement", "nmRequirement"),
        getField("cdRisk", "204041", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].RISK, "idRisk", "nmRisk"),
        getField("cdSupply", "200388", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].SUPPLY, "idObject", "nmObject"),
        getField("cdStoreroom", "200322", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].STOREROOM, "idStoreroom", "nmStoreroom"),
        getField("cdTransarchival", "101839", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].STOREROOM_TRANSARCHIVAL, "idTransarchival", "nmTransarchival"),
        getField("cdTraining", "103580", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].TRAINING, "idTrain", "nmTrain"),
        getField("cdWorkflow", "108546", reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_AssociationFilterConst__WEBPACK_IMPORTED_MODULE_6__["default"].WORKFLOW, "idProcess", "nmProcess")
    ];
    // Ordena por ordem alfabética
    items = items.sort(function(firstElement, secondElement) {
        return firstElement.props.title.localeCompare(secondElement.props.title);
    });
    return (0,reactor2_src_Form_containers_Filter_Fields_getAssociationField__WEBPACK_IMPORTED_MODULE_2__["default"])(getToken, items);
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getAssociationField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getAutomationObjectField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/SelectField */ "../reactor2/src/Form/components/Orgs/SEFields/SelectField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactorCmps_src_workflow_Defaultframe_constants_ObjectAutomationConstants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/constants/ObjectAutomationConstants */ "./src/workflow/Defaultframe/constants/ObjectAutomationConstants.js");


var getAutomationObjectField = function(getToken) {
    var name = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "automationObject";
    return {
        name: name,
        component: (reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: 111287,
            data: [
                {
                    title: getToken("107063"),
                    value: reactorCmps_src_workflow_Defaultframe_constants_ObjectAutomationConstants__WEBPACK_IMPORTED_MODULE_1__["default"].INCIDENT_OBJECT
                },
                {
                    title: getToken("200745"),
                    value: reactorCmps_src_workflow_Defaultframe_constants_ObjectAutomationConstants__WEBPACK_IMPORTED_MODULE_1__["default"].PROBLEM_OBJECT
                }
            ],
            valueField: "value",
            textField: "title"
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getAutomationObjectField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getCauseZoomField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/ZoomField */ "../reactor2/src/Form/components/Orgs/SEFields/ZoomField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! SG2/proxy/Types */ "SG2/proxy/Types");
/* ESM import */var SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2__);



var CauseZoomCollection = SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1___default()({
    model: {
        primaryKey: "cdCause",
        proxyCfg: {
            type: (SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2___default().MACHETE_PROXY),
            route: "framework/zoom-field/workflow-filter/search?type=cause"
        }
    },
    collection: {
        silent: true,
        defaultOrder: {
            cdCause: "ASC"
        }
    }
});
var getCauseZoomField = function(getToken, name) {
    return {
        name: name,
        component: (reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: getToken("200341"),
            Collection: CauseZoomCollection,
            extraRequestColumns: [
                "idCause",
                "nmCause"
            ],
            textField: "nmCause",
            valueField: "cdCause",
            fields: [
                {
                    field: "cdCause",
                    title: "code",
                    primaryKey: true,
                    showOnTag: false,
                    showOnGrid: false,
                    showOnFilter: false
                },
                {
                    field: "idCause",
                    showOnTag: false,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken("214946")
                },
                {
                    field: "nmCause",
                    showOnTag: true,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken("100111")
                }
            ]
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getCauseZoomField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getCompanyZoomField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/ZoomField */ "../reactor2/src/Form/components/Orgs/SEFields/ZoomField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! SG2/proxy/Types */ "SG2/proxy/Types");
/* ESM import */var SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2__);



var CompanyCollection = SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1___default()({
    model: {
        primaryKey: "cdCompany",
        proxyCfg: {
            type: (SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2___default().MACHETE_PROXY),
            route: "workflow/company/search"
        }
    },
    collection: {
        defaultOrder: {
            nmCompany: "ASC"
        }
    }
});
var getCompanyZoomField = function(getToken, name, title) {
    return {
        name: name !== null && name !== void 0 ? name : "company",
        component: (reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: title !== null && title !== void 0 ? title : getToken(200357),
            Collection: CompanyCollection,
            extraRequestColumns: [
                "idCommercial",
                "nmCompany"
            ],
            textField: "nmCompany",
            valueField: "cdCompany",
            fields: [
                {
                    field: "cdCompany",
                    title: "code",
                    primaryKey: true,
                    showOnTag: false,
                    showOnGrid: false,
                    showOnFilter: false
                },
                {
                    field: "idCommercial",
                    showOnTag: false,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken(214946)
                },
                {
                    field: "nmCompany",
                    showOnTag: true,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken(100111)
                }
            ]
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getCompanyZoomField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getDeadlineField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_DateConstantsField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/DateConstantsField */ "../reactor2/src/Form/containers/Filter/Fields/DateConstantsField.jsx");

var getDeadlineField = function() {
    return {
        name: "deadline",
        component: reactor2_src_Form_containers_Filter_Fields_DateConstantsField__WEBPACK_IMPORTED_MODULE_0__["default"],
        props: {
            title: 100431,
            fixed: true
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getDeadlineField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getDeadlineStatusField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_constants_deadlineConstants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/constants/deadlineConstants */ "../reactor2/src/constants/deadlineConstants.js");
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_getDeadlineField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/getDeadlineField */ "../reactor2/src/Form/containers/Filter/Fields/getDeadlineField.js");


var getDeadlineStatusField = function(getToken) {
    return (0,reactor2_src_Form_containers_Filter_Fields_getDeadlineField__WEBPACK_IMPORTED_MODULE_1__["default"])(getToken, "fgDeadline", {
        title: 218832,
        options: {
            UP_TO_DATE: reactor2_src_constants_deadlineConstants__WEBPACK_IMPORTED_MODULE_0__.UP_TO_DATE,
            CLOSE_TO_EXPIRATION: reactor2_src_constants_deadlineConstants__WEBPACK_IMPORTED_MODULE_0__.CLOSE_TO_EXPIRATION,
            IN_OVERDUE: reactor2_src_constants_deadlineConstants__WEBPACK_IMPORTED_MODULE_0__.IN_OVERDUE
        }
    });
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getDeadlineStatusField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getEnabledActivityExecutorField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/UserField */ "../reactor2/src/Form/components/Orgs/SEFields/UserField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_0__);

var getEnabledActivityExecutorField = function(getToken, name) {
    return {
        name: name,
        component: (reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: getToken(317298),
            showId: true
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getEnabledActivityExecutorField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getEnabledActivityZoomField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/ZoomField */ "../reactor2/src/Form/components/Orgs/SEFields/ZoomField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! SG2/proxy/Types */ "SG2/proxy/Types");
/* ESM import */var SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactorCmps_src_workflow_Defaultframe_constants_EnabledActivityFilterConstants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/constants/EnabledActivityFilterConstants */ "./src/workflow/Defaultframe/constants/EnabledActivityFilterConstants.js");
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}





var EnabledActivityCollection = SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_2___default()({
    model: {
        primaryKey: "cdActivity",
        proxyCfg: {
            type: (SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_3___default().MACHETE_PROXY),
            route: "framework/zoom-field/workflow-filter/search?type=enabled-activity"
        }
    },
    collection: {
        defaultOrder: {
            cdActivity: "ASC"
        }
    }
});
var getEnabledActivityZoomField = function(getToken, name) {
    return {
        name: name !== null && name !== void 0 ? name : "enabledActivity",
        component: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().forwardRef(function(props, ref) {
            var related = props.related;
            var viewByProcessOrWorkflowType = related.viewByProcessOrWorkflowType;
            var extraParams = {};
            if (viewByProcessOrWorkflowType) {
                var _viewByProcessOrWorkflowType_fields_, _viewByProcessOrWorkflowType_fields, _viewByProcessOrWorkflowType_value_, _viewByProcessOrWorkflowType_value;
                var categoryType = (_viewByProcessOrWorkflowType_fields = viewByProcessOrWorkflowType["fields"]) === null || _viewByProcessOrWorkflowType_fields === void 0 ? void 0 : (_viewByProcessOrWorkflowType_fields_ = _viewByProcessOrWorkflowType_fields[0]) === null || _viewByProcessOrWorkflowType_fields_ === void 0 ? void 0 : _viewByProcessOrWorkflowType_fields_.value;
                extraParams.cdProcess = (_viewByProcessOrWorkflowType_value = viewByProcessOrWorkflowType["value"]) === null || _viewByProcessOrWorkflowType_value === void 0 ? void 0 : (_viewByProcessOrWorkflowType_value_ = _viewByProcessOrWorkflowType_value[0]) === null || _viewByProcessOrWorkflowType_value_ === void 0 ? void 0 : _viewByProcessOrWorkflowType_value_.value;
                extraParams.fgWfType = categoryType === "workflowType" ? reactorCmps_src_workflow_Defaultframe_constants_EnabledActivityFilterConstants__WEBPACK_IMPORTED_MODULE_4__.WORKFLOW_TYPE : reactorCmps_src_workflow_Defaultframe_constants_EnabledActivityFilterConstants__WEBPACK_IMPORTED_MODULE_4__.PROCESS;
            }
            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement((reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_1___default()), _object_spread_props(_object_spread({
                ref: ref
            }, props), {
                extraParams: extraParams
            }));
        }),
        props: {
            title: 108524,
            Collection: EnabledActivityCollection,
            extraRequestColumns: [
                "idActivity",
                "nmActivity"
            ],
            textField: "nmActivity",
            valueField: "cdActivity",
            fields: [
                {
                    field: "cdActivity",
                    title: "code",
                    primaryKey: true,
                    showOnTag: false,
                    showOnGrid: false,
                    showOnFilter: false
                },
                {
                    field: "idActivity",
                    showOnTag: false,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken(214946)
                },
                {
                    field: "nmActivity",
                    showOnTag: true,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken(100111)
                }
            ]
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getEnabledActivityZoomField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getEndDateField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_DateConstantsField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/DateConstantsField */ "../reactor2/src/Form/containers/Filter/Fields/DateConstantsField.jsx");

var getEndDateField = function(getToken, name) {
    return {
        name: name !== null && name !== void 0 ? name : "endDate",
        component: reactor2_src_Form_containers_Filter_Fields_DateConstantsField__WEBPACK_IMPORTED_MODULE_0__["default"],
        props: {
            title: 100534,
            fixed: true
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getEndDateField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getExternalExecutorField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters_CustomSEFields_ExternalExecutorSEFieldMulti__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters/CustomSEFields/ExternalExecutorSEFieldMulti */ "./src/workflow/Defaultframe/common/Filters/CustomSEFields/ExternalExecutorSEFieldMulti.jsx");

var getExternalExecutorField = function(getToken) {
    return {
        name: "executor",
        component: reactorCmps_src_workflow_Defaultframe_common_Filters_CustomSEFields_ExternalExecutorSEFieldMulti__WEBPACK_IMPORTED_MODULE_0__["default"],
        props: {
            title: getToken(108527)
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getExternalExecutorField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getFailedInstancesField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_getToggleField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/getToggleField */ "../reactor2/src/Form/containers/Filter/Fields/getToggleField.js");

var getFailedInstancesField = function(getToken) {
    return (0,reactor2_src_Form_containers_Filter_Fields_getToggleField__WEBPACK_IMPORTED_MODULE_0__["default"])({
        name: "showOnlyInstanceFailed",
        title: 220534,
        toggleValue: 1,
        toggleTitle: getToken("220534")
    }, {});
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getFailedInstancesField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getFormTableModalField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.array.includes.js */ "../node_modules/core-js/modules/es.array.includes.js");
/* ESM import */var core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.string.includes.js */ "../node_modules/core-js/modules/es.string.includes.js");
/* ESM import */var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters_Modals_FormTableFieldModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters/Modals/FormTableFieldModal */ "./src/workflow/Defaultframe/common/Filters/Modals/FormTableFieldModal.jsx");
/* ESM import */var reactorCmps_src_responsiveform_helpers_constants_SharedConstantsMapping__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactorCmps/src/responsiveform/helpers/constants/SharedConstantsMapping */ "./src/responsiveform/helpers/constants/SharedConstantsMapping.js");
/* ESM import */var reactorCmps_src_responsiveform_helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactorCmps/src/responsiveform/helpers/constants/SharedConstants */ "./src/responsiveform/helpers/constants/SharedConstants.js");
/* ESM import */var Formatters_Date__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! Formatters/Date */ "Formatters/Date");
/* ESM import */var Formatters_Date__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(Formatters_Date__WEBPACK_IMPORTED_MODULE_6__);







var dateFormatter = new (Formatters_Date__WEBPACK_IMPORTED_MODULE_6___default())();
var getOperatorToken = function(operator) {
    var mapping = reactorCmps_src_responsiveform_helpers_constants_SharedConstantsMapping__WEBPACK_IMPORTED_MODULE_4__["default"].conditionalOperators[operator];
    var _mapping_name;
    return (_mapping_name = mapping.name) !== null && _mapping_name !== void 0 ? _mapping_name : "";
};
var getFormTableModalField = function(getToken) {
    return {
        name: "form-table-filter",
        fixed: false,
        modal: {
            component: reactorCmps_src_workflow_Defaultframe_common_Filters_Modals_FormTableFieldModal__WEBPACK_IMPORTED_MODULE_3__["default"],
            fieldIdFormatter: function(param) {
                var fieldName = param.fieldName, fieldValue = param.fieldValue;
                if ([
                    "formTable",
                    "tableField"
                ].includes(fieldName) && fieldValue) {
                    return fieldValue[0].value;
                }
            },
            fieldTextFormatter: function(param) {
                var fieldName = param.fieldName, fieldValue = param.fieldValue, formValues = param.formValues;
                if (!fieldValue) {
                    return "";
                }
                switch(fieldName){
                    case "formTable":
                    case "tableField":
                        return fieldValue[0].title;
                    case "operator":
                        var operator = getOperatorToken(fieldValue);
                        return getToken(operator);
                    case "value":
                        var fieldType = formValues.tableField[0].type;
                        if (fieldType === reactorCmps_src_responsiveform_helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_5__["default"].returnTypes.DATE) {
                            return dateFormatter.format(fieldValue);
                        }
                        return fieldValue;
                    default:
                        return null;
                }
            },
            initialValues: {
                value: ""
            }
        },
        props: {
            title: getToken("314918")
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getFormTableModalField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getIdentifierField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/TextField */ "../reactor2/src/Form/components/Orgs/SEFields/TextField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0__);

var getIdentifierField = function(getToken, name) {
    return {
        name: name !== null && name !== void 0 ? name : "identifier",
        component: (reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: 100528
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getIdentifierField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getInitializerViewByField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/SelectField */ "../reactor2/src/Form/components/Orgs/SEFields/SelectField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactorCmps_src_workflow_Defaultframe_constants_ViewByConstants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/constants/ViewByConstants */ "./src/workflow/Defaultframe/constants/ViewByConstants.js");


var getInitializerViewByField = function(getToken) {
    var name = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "viewBy";
    return {
        name: name,
        component: (reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: getToken(106056),
            data: [
                {
                    title: getToken(208957),
                    value: reactorCmps_src_workflow_Defaultframe_constants_ViewByConstants__WEBPACK_IMPORTED_MODULE_1__["default"].FAVORITE
                },
                {
                    title: getToken(312899),
                    value: reactorCmps_src_workflow_Defaultframe_constants_ViewByConstants__WEBPACK_IMPORTED_MODULE_1__["default"].LAST_STARTED
                },
                {
                    title: getToken(312111),
                    value: reactorCmps_src_workflow_Defaultframe_constants_ViewByConstants__WEBPACK_IMPORTED_MODULE_1__["default"].MOST_STARTED
                }
            ],
            valueField: "value",
            textField: "title"
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getInitializerViewByField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getInstanceStatusField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_MultiSelectField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/MultiSelectField */ "../reactor2/src/Form/components/Orgs/SEFields/MultiSelectField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_MultiSelectField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_MultiSelectField__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_InstanceStatusConst__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters/Constants/InstanceStatusConst */ "./src/workflow/Defaultframe/common/Filters/Constants/InstanceStatusConst.js");
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}


var getInstanceStatusField = function(getToken, otherProps) {
    return _object_spread_props(_object_spread({
        name: "fgStatus",
        component: (reactor2_src_Form_components_Orgs_SEFields_MultiSelectField__WEBPACK_IMPORTED_MODULE_0___default())
    }, otherProps), {
        props: _object_spread({
            title: 100072,
            valueField: "value",
            textField: "title",
            simpleValue: true,
            data: [
                {
                    value: reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_InstanceStatusConst__WEBPACK_IMPORTED_MODULE_1__["default"].IN_PROGRESS,
                    title: getToken(103131)
                },
                {
                    value: reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_InstanceStatusConst__WEBPACK_IMPORTED_MODULE_1__["default"].POSTPONED,
                    title: getToken(107788)
                },
                {
                    value: reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_InstanceStatusConst__WEBPACK_IMPORTED_MODULE_1__["default"].CANCELLED,
                    title: getToken(104230)
                },
                {
                    value: reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_InstanceStatusConst__WEBPACK_IMPORTED_MODULE_1__["default"].FINISHED,
                    title: getToken(100667)
                }
            ]
        }, otherProps)
    });
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getInstanceStatusField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getPastDueRetentionDeadlineField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  CustomToggleField: function() { return CustomToggleField; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ToggleField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/ToggleField */ "../reactor2/src/Form/components/Orgs/SEFields/ToggleField.jsx");
/* ESM import */var reactor2_src_Atomic_components_Mols_ImageTextView__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactor2/src/Atomic/components/Mols/ImageTextView */ "../reactor2/src/Atomic/components/Mols/ImageTextView.jsx");
/* ESM import */var reactor2_src_Atomic_components_Mols_ImageTextView__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Mols_ImageTextView__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactor2/src/Styles/styleVariables */ "../reactor2/src/Styles/styleVariables.js");
/* ESM import */var reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_InstanceStatusConst__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters/Constants/InstanceStatusConst */ "./src/workflow/Defaultframe/common/Filters/Constants/InstanceStatusConst.js");
/* ESM import */var _getInstanceStatusField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./getInstanceStatusField */ "./src/workflow/Defaultframe/common/Filters/Fields/getInstanceStatusField.js");
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}








var CustomToggleField = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(function(props, ref) {
    var related = props.related;
    var fgStatus = related.fgStatus;
    if (fgStatus && fgStatus.length === 1 && fgStatus[0] === reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_InstanceStatusConst__WEBPACK_IMPORTED_MODULE_5__["default"].FINISHED) {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(reactor2_src_Form_components_Orgs_SEFields_ToggleField__WEBPACK_IMPORTED_MODULE_2__["default"], _object_spread({
            ref: ref
        }, props));
    }
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Mols_ImageTextView__WEBPACK_IMPORTED_MODULE_3___default()), {
        text: props.getToken("314149"),
        icon: "seicon-warning-circle",
        color: (reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_4___default().colors.solidBlue4)
    });
});
CustomToggleField.displayName = "workflow/Defaultframe/common/Filters/Fields/CustomToggleField";
CustomToggleField.propTypes = {
    getToken: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),
    hideSearch: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().bool),
    title: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),
    toggleValue: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),
    toggleTitle: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),
    name: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().string),
    related: prop_types__WEBPACK_IMPORTED_MODULE_7___default().shape({
        fgStatus: prop_types__WEBPACK_IMPORTED_MODULE_7___default().arrayOf((prop_types__WEBPACK_IMPORTED_MODULE_7___default().string))
    })
};
var getDependsOn = function(getToken) {
    var instanceStatusField = (0,_getInstanceStatusField__WEBPACK_IMPORTED_MODULE_6__["default"])(getToken);
    return instanceStatusField.name;
};
var getPastDueRetentionDeadlineField = function(getToken) {
    return {
        name: "showPastDueRetentionDeadline",
        props: {
            getToken: getToken,
            hideSearch: true,
            title: 113394,
            toggleValue: 1,
            toggleTitle: 113394,
            name: "showPastDueRetentionDeadline"
        },
        component: CustomToggleField,
        dependsOn: getDependsOn(getToken)
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getPastDueRetentionDeadlineField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getPriorityField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/TextField */ "../reactor2/src/Form/components/Orgs/SEFields/TextField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0__);

var getPriorityField = function(getToken) {
    return {
        name: "priority",
        component: (reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: getToken(101554)
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getPriorityField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getProcessTypeTreeField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_getCategoryField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/getCategoryField */ "../reactor2/src/Form/containers/Filter/Fields/getCategoryField.js");
/* ESM import */var reactor2_src_constants_backendCallerConstants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactor2/src/constants/backendCallerConstants */ "../reactor2/src/constants/backendCallerConstants.js");


var getProcessTypeTreeField = function(getToken) {
    var name = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "processType";
    return (0,reactor2_src_Form_containers_Filter_Fields_getCategoryField__WEBPACK_IMPORTED_MODULE_0__["default"])({
        name: name,
        title: getToken(101308),
        categoryTreeParams: {
            backendCaller: reactor2_src_constants_backendCallerConstants__WEBPACK_IMPORTED_MODULE_1__.MACHETE_CALLER,
            getUrl: function() {
                return "framework/categories/15";
            },
            getImage: function() {
                return "/common/images/generic_icons/16x16/pasta.png";
            }
        }
    });
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getProcessTypeTreeField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getRecentlyModifiedField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_DateConstantsField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/DateConstantsField */ "../reactor2/src/Form/containers/Filter/Fields/DateConstantsField.jsx");

var getRecentlyModifiedField = function() {
    return {
        name: "recentlyModified",
        component: reactor2_src_Form_containers_Filter_Fields_DateConstantsField__WEBPACK_IMPORTED_MODULE_0__["default"],
        props: {
            title: 204033,
            fixed: true,
            operatorOptions: {
                disableEmptyOperator: true
            }
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getRecentlyModifiedField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getRequesterContactZoomField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  RequesterCollection: function() { return RequesterCollection; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/ZoomField */ "../reactor2/src/Form/components/Orgs/SEFields/ZoomField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! SG2/proxy/Types */ "SG2/proxy/Types");
/* ESM import */var SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2__);
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}



var RequesterCollection = SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1___default()({
    model: {
        primaryKey: "cdContact",
        proxyCfg: {
            type: (SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2___default().MACHETE_PROXY),
            route: "workflow/company-contact/search"
        }
    },
    collection: {
        defaultOrder: {
            nmResponsible: "ASC"
        }
    }
});
var getRequesterContactZoomField = function(getToken, name, title) {
    var otherProps = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};
    return _object_spread({
        name: name !== null && name !== void 0 ? name : "requester",
        component: (reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: title !== null && title !== void 0 ? title : getToken("100261"),
            Collection: RequesterCollection,
            extraRequestColumns: [
                "nmResponsible",
                "nmDepartment",
                "nmPosition",
                "nmEmail"
            ],
            textField: "nmResponsible",
            valueField: "cdContact",
            fields: [
                {
                    field: "cdContact",
                    title: "code",
                    primaryKey: true,
                    showOnTag: false,
                    showOnGrid: false,
                    showOnFilter: false
                },
                {
                    field: "nmResponsible",
                    showOnTag: true,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken(100111)
                },
                {
                    field: "nmDepartment",
                    showOnTag: false,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken(100112)
                },
                {
                    field: "nmPosition",
                    showOnTag: false,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken(200833)
                },
                {
                    field: "nmEmail",
                    showOnTag: false,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken(100114)
                }
            ]
        }
    }, otherProps);
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getRequesterContactZoomField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getRequesterField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  RequesterContactZoom: function() { return RequesterContactZoom; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/ZoomField */ "../reactor2/src/Form/components/Orgs/SEFields/ZoomField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/UserField */ "../reactor2/src/Form/components/Orgs/SEFields/UserField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var _getRequesterContactZoomField__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getRequesterContactZoomField */ "./src/workflow/Defaultframe/common/Filters/Fields/getRequesterContactZoomField.js");
/* ESM import */var _getCompanyZoomField__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getCompanyZoomField */ "./src/workflow/Defaultframe/common/Filters/Fields/getCompanyZoomField.js");
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}






var RequesterContactZoom = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().forwardRef(function(props, ref) {
    var related = props.related;
    var requesterCompany = related.requesterCompany;
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement((reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_1___default()), _object_spread_props(_object_spread({
        ref: ref
    }, props), {
        extraParams: {
            requesterCompany: requesterCompany
        }
    }));
});
RequesterContactZoom.displayName = "workflow/Defaultframe/common/Filters/Fields/RequesterContactZoom";
RequesterContactZoom.propTypes = {
    title: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string),
    Collection: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().any),
    extraRequestColumns: prop_types__WEBPACK_IMPORTED_MODULE_5___default().arrayOf((prop_types__WEBPACK_IMPORTED_MODULE_5___default().string)),
    textField: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string),
    valueField: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string),
    fields: prop_types__WEBPACK_IMPORTED_MODULE_5___default().arrayOf((prop_types__WEBPACK_IMPORTED_MODULE_5___default().object))
};
var getRequesterField = function(getToken) {
    return {
        name: "requester",
        props: {
            title: getToken(100261),
            showId: true
        },
        fields: [
            {
                form: [
                    {
                        name: "requesterUser",
                        component: (reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_2___default()),
                        props: {
                            title: getToken(200835)
                        }
                    },
                    (0,_getCompanyZoomField__WEBPACK_IMPORTED_MODULE_4__["default"])(getToken, "requesterCompany"),
                    (0,_getRequesterContactZoomField__WEBPACK_IMPORTED_MODULE_3__["default"])(getToken, "requesterContact", getToken(101042), {
                        component: RequesterContactZoom,
                        dependsOn: "requesterCompany"
                    })
                ]
            }
        ]
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getRequesterField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getSLAStatusField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_MultiSelectField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/MultiSelectField */ "../reactor2/src/Form/components/Orgs/SEFields/MultiSelectField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_MultiSelectField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_MultiSelectField__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_SLAStatusConst__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters/Constants/SLAStatusConst */ "./src/workflow/Defaultframe/common/Filters/Constants/SLAStatusConst.js");


var getSLAStatusField = function(getToken) {
    return {
        name: "SLAStatus",
        component: (reactor2_src_Form_components_Orgs_SEFields_MultiSelectField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: 314191,
            valueField: "value",
            textField: "title",
            simpleValue: true,
            data: [
                {
                    value: reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_SLAStatusConst__WEBPACK_IMPORTED_MODULE_1__["default"].FIRST_RESPONSE,
                    title: getToken(218183)
                },
                {
                    value: reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_SLAStatusConst__WEBPACK_IMPORTED_MODULE_1__["default"].PLAY,
                    title: getToken(218492)
                },
                {
                    value: reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_SLAStatusConst__WEBPACK_IMPORTED_MODULE_1__["default"].PAUSE,
                    title: getToken(218493)
                },
                {
                    value: reactorCmps_src_workflow_Defaultframe_common_Filters_Constants_SLAStatusConst__WEBPACK_IMPORTED_MODULE_1__["default"].STOP,
                    title: getToken(218494)
                }
            ]
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getSLAStatusField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getSimpleAttributeField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/TextField */ "../reactor2/src/Form/components/Orgs/SEFields/TextField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0__);

var getSimpleAttributeField = function(name, title) {
    return {
        name: name,
        component: (reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            showId: true,
            title: title
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getSimpleAttributeField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getStartDateField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_DateConstantsField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/DateConstantsField */ "../reactor2/src/Form/containers/Filter/Fields/DateConstantsField.jsx");

var getStartDateField = function(getToken, name) {
    return {
        name: name !== null && name !== void 0 ? name : "startDate",
        component: reactor2_src_Form_containers_Filter_Fields_DateConstantsField__WEBPACK_IMPORTED_MODULE_0__["default"],
        props: {
            title: 100533,
            fixed: true
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getStartDateField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getStarterField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/TextField */ "../reactor2/src/Form/components/Orgs/SEFields/TextField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0__);

var getStarterField = function() {
    return {
        name: "starter",
        component: (reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: 106947
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getStarterField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getStarterUserField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/UserField */ "../reactor2/src/Form/components/Orgs/SEFields/UserField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_0__);

var getStarterUserField = function(getToken) {
    return {
        name: "starter",
        component: (reactor2_src_Form_components_Orgs_SEFields_UserField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: getToken(106947),
            showId: true
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getStarterUserField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getTitleField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/TextField */ "../reactor2/src/Form/components/Orgs/SEFields/TextField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0__);

var getTitleField = function(getToken, name) {
    return {
        name: name !== null && name !== void 0 ? name : "title",
        component: (reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            showId: true,
            title: 100380
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getTitleField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getViewByProcessOrWorkflowTypeField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters_CustomSEFields_ViewByProcessOrWorkflowTypeField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters/CustomSEFields/ViewByProcessOrWorkflowTypeField */ "./src/workflow/Defaultframe/common/Filters/CustomSEFields/ViewByProcessOrWorkflowTypeField.jsx");

var getViewByProcessOrWorkflowTypeField = function(getToken) {
    return {
        name: "viewByProcessOrWorkflowType",
        component: reactorCmps_src_workflow_Defaultframe_common_Filters_CustomSEFields_ViewByProcessOrWorkflowTypeField__WEBPACK_IMPORTED_MODULE_0__["default"],
        props: {
            title: getToken(106056)
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getViewByProcessOrWorkflowTypeField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getWorkflowCategoryField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; },
  getImage: function() { return getImage; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_getCategoryField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/getCategoryField */ "../reactor2/src/Form/containers/Filter/Fields/getCategoryField.js");


var getFolderIcon = function() {
    return "/common/images/generic_icons/16x16/pasta.png";
};
var getCategoriesImage = function(fgLogo) {
    return "/ui/desktop/lite/resources/images/imageSelectorIcon-default-16x16/".concat(fgLogo, ".png");
};
var getImage = function(fgLogo) {
    if (fgLogo === 'pasta') {
        return getFolderIcon();
    }
    return getCategoriesImage(fgLogo);
};
var getWorkflowCategoryField = function(getToken) {
    return (0,reactor2_src_Form_containers_Filter_Fields_getCategoryField__WEBPACK_IMPORTED_MODULE_1__["default"])({
        title: 114759,
        categoryTreeType: "process",
        dynamicBrowser: true,
        categoryTreeParams: {
            getImage: getImage,
            customMapping: {
                code: "code",
                text: "text",
                image: "icon",
                idText: "idText",
                parentCode: "parentCode",
                type: "type"
            }
        },
        mainCategories: [
            {
                type: "process",
                text: getToken("101308")
            },
            {
                type: "workflowType",
                text: getToken("301294")
            }
        ]
    });
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getWorkflowCategoryField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getWorkflowStatusZoomField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  WorkflowStatusCollection: function() { return WorkflowStatusCollection; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/ZoomField */ "../reactor2/src/Form/components/Orgs/SEFields/ZoomField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
/* ESM import */var SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! SG2/proxy/Types */ "SG2/proxy/Types");
/* ESM import */var SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2__);



var WorkflowStatusCollection = SG2_collection_Factory__WEBPACK_IMPORTED_MODULE_1___default()({
    model: {
        primaryKey: "cdRevisionStatus",
        proxyCfg: {
            type: (SG2_proxy_Types__WEBPACK_IMPORTED_MODULE_2___default().MACHETE_PROXY),
            route: "framework/zoom-field/workflow-filter/search?type=workflow-status"
        }
    },
    collection: {
        defaultOrder: {
            cdRevisionStatus: "ASC"
        }
    }
});
var getWorkflowStatusZoomField = function(getToken) {
    return {
        name: "workflowStatus",
        component: (reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_0___default()),
        props: {
            title: 217560,
            Collection: WorkflowStatusCollection,
            extraRequestColumns: [
                "idRevisionStatus",
                "nmRevisionStatus"
            ],
            textField: "nmRevisionStatus",
            valueField: "cdRevisionStatus",
            fields: [
                {
                    field: "cdRevisionStatus",
                    title: "code",
                    primaryKey: true,
                    showOnTag: false,
                    showOnGrid: false,
                    showOnFilter: false
                },
                {
                    field: "idRevisionStatus",
                    showOnTag: true,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken(100528)
                },
                {
                    field: "nmRevisionStatus",
                    showOnTag: true,
                    showOnGrid: true,
                    showOnFilter: true,
                    title: getToken(100111)
                }
            ]
        }
    };
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getWorkflowStatusZoomField);


}),
"./src/workflow/Defaultframe/common/Filters/Fields/getWorkflowTypeTreeField.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_getCategoryField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/getCategoryField */ "../reactor2/src/Form/containers/Filter/Fields/getCategoryField.js");
/* ESM import */var reactor2_src_constants_backendCallerConstants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor2/src/constants/backendCallerConstants */ "../reactor2/src/constants/backendCallerConstants.js");



var getWorkflowTypeTreeField = function(getToken) {
    var name = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "workflowType";
    return (0,reactor2_src_Form_containers_Filter_Fields_getCategoryField__WEBPACK_IMPORTED_MODULE_1__["default"])({
        name: name,
        title: getToken(301294),
        categoryTreeParams: {
            backendCaller: reactor2_src_constants_backendCallerConstants__WEBPACK_IMPORTED_MODULE_2__.MACHETE_CALLER,
            getUrl: function() {
                return "framework/categories/39?type=workflowType";
            },
            getImage: function(fgLogo) {
                return "/ui/desktop/lite/resources/images/imageSelectorIcon-default-16x16/".concat(fgLogo, ".png");
            }
        }
    });
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getWorkflowTypeTreeField);


}),
"./src/workflow/Defaultframe/common/Filters/Modals/FormTableFieldModal.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_16__);
/* ESM import */var lodash_get__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/get */ "../node_modules/lodash/get.js");
/* ESM import */var lodash_get__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_get__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactor2_src_Form_containers_Filter_Fields_ModalField__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactor2/src/Form/containers/Filter/Fields/ModalField */ "../reactor2/src/Form/containers/Filter/Fields/ModalField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/TextField */ "../reactor2/src/Form/components/Orgs/SEFields/TextField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_DateField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/DateField */ "../reactor2/src/Form/components/Orgs/SEFields/DateField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_DateField__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_DateField__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_UnifiedDateTimeFixedField__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/UnifiedDateTimeFixedField */ "../reactor2/src/Form/components/Orgs/SEFields/UnifiedDateTimeFixedField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/SelectField */ "../reactor2/src/Form/components/Orgs/SEFields/SelectField.jsx");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! reactor2/src/Form/components/Orgs/SEFields/ZoomField */ "../reactor2/src/Form/components/Orgs/SEFields/ZoomField.js");
/* ESM import */var reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var reactorCmps_src_responsiveform_components_Entity_EntitySelection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactorCmps/src/responsiveform/components/Entity/EntitySelection */ "./src/responsiveform/components/Entity/EntitySelection.jsx");
/* ESM import */var reactorCmps_src_responsiveform_components_Attribute_AttributeCollection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactorCmps/src/responsiveform/components/Attribute/AttributeCollection */ "./src/responsiveform/components/Attribute/AttributeCollection.js");
/* ESM import */var reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! reactorCmps/src/responsiveform/helpers/constants/AttributeConstants */ "./src/responsiveform/helpers/constants/AttributeConstants.js");
/* ESM import */var reactorCmps_src_responsiveform_components_FormEditor_EditorContent_FormTab_Properties_TableConfig_TableConfigHelpers__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactorCmps/src/responsiveform/components/FormEditor/EditorContent/FormTab/Properties/TableConfig/TableConfigHelpers */ "./src/responsiveform/components/FormEditor/EditorContent/FormTab/Properties/TableConfig/TableConfigHelpers.js");
/* ESM import */var reactorCmps_src_responsiveform_helpers_constants_SharedConstantsMapping__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactorCmps/src/responsiveform/helpers/constants/SharedConstantsMapping */ "./src/responsiveform/helpers/constants/SharedConstantsMapping.js");
/* ESM import */var reactorCmps_src_responsiveform_helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactorCmps/src/responsiveform/helpers/constants/SharedConstants */ "./src/responsiveform/helpers/constants/SharedConstants.js");

















var EMPTY_OPERATORS = [
    reactorCmps_src_responsiveform_helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_15__["default"].conditionalOperators.NULL,
    reactorCmps_src_responsiveform_helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_15__["default"].conditionalOperators.NOT_NULL,
    reactorCmps_src_responsiveform_helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_15__["default"].conditionalOperators.TRUE,
    reactorCmps_src_responsiveform_helpers_constants_SharedConstants__WEBPACK_IMPORTED_MODULE_15__["default"].conditionalOperators.FALSE
];
function FormTableFieldModal(param) {
    var value = param.value, setValue = param.setValue, getToken = param.getToken;
    var attribute = lodash_get__WEBPACK_IMPORTED_MODULE_3___default()(value, "tableField.0");
    var oidFormTable = lodash_get__WEBPACK_IMPORTED_MODULE_3___default()(value, "formTable.0.value");
    var operatorsList = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function() {
        return reactorCmps_src_responsiveform_components_FormEditor_EditorContent_FormTab_Properties_TableConfig_TableConfigHelpers__WEBPACK_IMPORTED_MODULE_13__["default"].getFilterOperatorsByAttribute(attribute).map(function(op) {
            return reactorCmps_src_responsiveform_helpers_constants_SharedConstantsMapping__WEBPACK_IMPORTED_MODULE_14__["default"].conditionalOperators[op];
        }).map(function(op) {
            return {
                value: op.value,
                name: getToken(op.name)
            };
        });
    }, [
        attribute,
        getToken
    ]);
    var handleFormTableChange = function(val) {
        setValue("formTable", val);
    };
    var handleAttributeChange = function(val) {
        setValue("tableField", val);
    };
    var handleOperatorChange = function(val) {
        setValue("operator", val);
    };
    var UserField = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(reactor2_src_Form_containers_Filter_Fields_ModalField__WEBPACK_IMPORTED_MODULE_4__["default"], {
        name: "tableField",
        component: (reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_5___default()),
        title: getToken("110069")
    });
    if (oidFormTable) {
        UserField = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(reactor2_src_Form_containers_Filter_Fields_ModalField__WEBPACK_IMPORTED_MODULE_4__["default"], {
            name: "tableField",
            component: (reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_9___default()),
            multireducerKey: "tableField",
            Collection: reactorCmps_src_responsiveform_components_Attribute_AttributeCollection__WEBPACK_IMPORTED_MODULE_11__["default"],
            onChange: handleAttributeChange,
            textField: "name",
            valueField: "oid",
            extraParams: {
                types: [
                    reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].SHORT_TEXT,
                    reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].TEXT,
                    reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].LONG_TEXT,
                    reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].INTEGER,
                    reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].LONG_INTEGER,
                    reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].DECIMAL,
                    reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].DATE,
                    reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].BOOLEAN,
                    reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].OPTION,
                    reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].DATETIME
                ],
                entity: oidFormTable
            },
            title: getToken("110069"),
            value: value.tableField,
            limit: 1,
            fields: [
                {
                    title: "null",
                    field: "oid",
                    showOnGrid: false,
                    showOnTag: false,
                    showOnFilter: false,
                    primaryKey: true
                },
                {
                    title: getToken("101625"),
                    field: "type",
                    showOnGrid: false,
                    showOnFilter: false,
                    showOnTag: true,
                    customType: function() {
                        return null;
                    }
                },
                {
                    title: getToken("100528"),
                    field: "name",
                    showOnGrid: true,
                    showOnTag: true,
                    showOnFilter: false,
                    customType: null
                },
                {
                    title: getToken("100111"),
                    field: "label",
                    showOnGrid: true,
                    showOnTag: true,
                    showOnFilter: false,
                    customType: null
                }
            ]
        });
    }
    var componentField;
    switch(attribute === null || attribute === void 0 ? void 0 : attribute.type){
        case reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].DATETIME:
            componentField = reactor2_src_Form_components_Orgs_SEFields_UnifiedDateTimeFixedField__WEBPACK_IMPORTED_MODULE_7__["default"];
            break;
        case reactorCmps_src_responsiveform_helpers_constants_AttributeConstants__WEBPACK_IMPORTED_MODULE_12__["default"].DATE:
            componentField = (reactor2_src_Form_components_Orgs_SEFields_DateField__WEBPACK_IMPORTED_MODULE_6___default());
            break;
        default:
            componentField = (reactor2_src_Form_components_Orgs_SEFields_TextField__WEBPACK_IMPORTED_MODULE_5___default());
            break;
    }
    var FieldValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(reactor2_src_Form_containers_Filter_Fields_ModalField__WEBPACK_IMPORTED_MODULE_4__["default"].Value, {
        title: getToken("100414"),
        component: componentField,
        value: value.value,
        onChange: function(val) {
            return setValue("value", val);
        }
    });
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(reactor2_src_Form_containers_Filter_Fields_ModalField__WEBPACK_IMPORTED_MODULE_4__["default"], {
        name: "formTable",
        nameGroup: "formTable",
        component: (reactor2_src_Form_components_Orgs_SEFields_ZoomField__WEBPACK_IMPORTED_MODULE_9___default()),
        multireducerKey: "formTable",
        Collection: reactorCmps_src_responsiveform_components_Entity_EntitySelection__WEBPACK_IMPORTED_MODULE_10__.EntitySelectionCollection,
        onChange: handleFormTableChange,
        textField: "displayName",
        valueField: "oid",
        title: getToken("102268"),
        value: value.formTable,
        limit: 1,
        fields: [
            {
                title: "null",
                field: "oid",
                showOnGrid: false,
                showOnTag: false,
                primaryKey: true
            },
            {
                title: getToken("100528"),
                field: "name",
                showOnGrid: true,
                showOnTag: true,
                customType: null
            },
            {
                title: getToken("100111"),
                field: "displayName",
                showOnGrid: true,
                showOnTag: true,
                customType: null
            }
        ]
    }), UserField, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(reactor2_src_Form_containers_Filter_Fields_ModalField__WEBPACK_IMPORTED_MODULE_4__["default"].Operator, {
        name: "operator",
        title: getToken("106302"),
        component: (reactor2_src_Form_components_Orgs_SEFields_SelectField__WEBPACK_IMPORTED_MODULE_8___default()),
        valueField: "value",
        textField: "name",
        data: operatorsList,
        value: value.operator,
        emptyOperators: EMPTY_OPERATORS,
        onChange: handleOperatorChange
    }), FieldValue);
}
FormTableFieldModal.propTypes = {
    value: (prop_types__WEBPACK_IMPORTED_MODULE_16___default().object),
    setValue: (prop_types__WEBPACK_IMPORTED_MODULE_16___default().func),
    getToken: (prop_types__WEBPACK_IMPORTED_MODULE_16___default().func)
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FormTableFieldModal);


}),
"./src/workflow/Defaultframe/common/Filters/index.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  getAssignedToMeField: function() { return /* reexport safe */ _Fields_getAssignedToMeField__WEBPACK_IMPORTED_MODULE_24__["default"]; },
  getAssociationField: function() { return /* reexport safe */ _Fields_getAssociationField__WEBPACK_IMPORTED_MODULE_0__["default"]; },
  getAutomationObjectField: function() { return /* reexport safe */ _Fields_getAutomationObjectField__WEBPACK_IMPORTED_MODULE_28__["default"]; },
  getCauseZoomField: function() { return /* reexport safe */ _Fields_getCauseZoomField__WEBPACK_IMPORTED_MODULE_27__["default"]; },
  getCompanyZoomField: function() { return /* reexport safe */ _Fields_getCompanyZoomField__WEBPACK_IMPORTED_MODULE_22__["default"]; },
  getDeadlineField: function() { return /* reexport safe */ _Fields_getDeadlineField__WEBPACK_IMPORTED_MODULE_1__["default"]; },
  getDeadlineStatusField: function() { return /* reexport safe */ _Fields_getDeadlineStatusField__WEBPACK_IMPORTED_MODULE_2__["default"]; },
  getEnabledActivityExecutorField: function() { return /* reexport safe */ _Fields_getEnabledActivityExecutorField__WEBPACK_IMPORTED_MODULE_23__["default"]; },
  getEnabledActivityZoomField: function() { return /* reexport safe */ _Fields_getEnabledActivityZoomField__WEBPACK_IMPORTED_MODULE_26__["default"]; },
  getEndDateField: function() { return /* reexport safe */ _Fields_getEndDateField__WEBPACK_IMPORTED_MODULE_3__["default"]; },
  getExternalExecutorField: function() { return /* reexport safe */ _Fields_getExternalExecutorField__WEBPACK_IMPORTED_MODULE_4__["default"]; },
  getFailedInstancesField: function() { return /* reexport safe */ _Fields_getFailedInstancesField__WEBPACK_IMPORTED_MODULE_6__["default"]; },
  getFormTableModalField: function() { return /* reexport safe */ _Fields_getFormTableModalField__WEBPACK_IMPORTED_MODULE_30__["default"]; },
  getIdentifierField: function() { return /* reexport safe */ _Fields_getIdentifierField__WEBPACK_IMPORTED_MODULE_7__["default"]; },
  getInitializerViewByField: function() { return /* reexport safe */ _Fields_getInitializerViewByField__WEBPACK_IMPORTED_MODULE_29__["default"]; },
  getInstanceStatusField: function() { return /* reexport safe */ _Fields_getInstanceStatusField__WEBPACK_IMPORTED_MODULE_8__["default"]; },
  getPastDueRetentionDeadlineField: function() { return /* reexport safe */ _Fields_getPastDueRetentionDeadlineField__WEBPACK_IMPORTED_MODULE_9__["default"]; },
  getPriorityField: function() { return /* reexport safe */ _Fields_getPriorityField__WEBPACK_IMPORTED_MODULE_10__["default"]; },
  getProcessTypeTreeField: function() { return /* reexport safe */ _Fields_getProcessTypeTreeField__WEBPACK_IMPORTED_MODULE_11__["default"]; },
  getRecentlyModifiedField: function() { return /* reexport safe */ _Fields_getRecentlyModifiedField__WEBPACK_IMPORTED_MODULE_25__["default"]; },
  getRequesterContactZoomField: function() { return /* reexport safe */ _Fields_getRequesterContactZoomField__WEBPACK_IMPORTED_MODULE_12__["default"]; },
  getRequesterField: function() { return /* reexport safe */ _Fields_getRequesterField__WEBPACK_IMPORTED_MODULE_13__["default"]; },
  getSLAStatusField: function() { return /* reexport safe */ _Fields_getSLAStatusField__WEBPACK_IMPORTED_MODULE_14__["default"]; },
  getSimpleAttributeField: function() { return /* reexport safe */ _Fields_getSimpleAttributeField__WEBPACK_IMPORTED_MODULE_31__["default"]; },
  getStartDateField: function() { return /* reexport safe */ _Fields_getStartDateField__WEBPACK_IMPORTED_MODULE_15__["default"]; },
  getStarterField: function() { return /* reexport safe */ _Fields_getStarterField__WEBPACK_IMPORTED_MODULE_16__["default"]; },
  getStarterUserField: function() { return /* reexport safe */ _Fields_getStarterUserField__WEBPACK_IMPORTED_MODULE_17__["default"]; },
  getTitleField: function() { return /* reexport safe */ _Fields_getTitleField__WEBPACK_IMPORTED_MODULE_18__["default"]; },
  getViewByProcessOrWorkflowTypeField: function() { return /* reexport safe */ _Fields_getViewByProcessOrWorkflowTypeField__WEBPACK_IMPORTED_MODULE_5__["default"]; },
  getWorkflowCategoryField: function() { return /* reexport safe */ _Fields_getWorkflowCategoryField__WEBPACK_IMPORTED_MODULE_19__["default"]; },
  getWorkflowStatusZoomField: function() { return /* reexport safe */ _Fields_getWorkflowStatusZoomField__WEBPACK_IMPORTED_MODULE_20__["default"]; },
  getWorkflowTypeTreeField: function() { return /* reexport safe */ _Fields_getWorkflowTypeTreeField__WEBPACK_IMPORTED_MODULE_21__["default"]; }
});
/* ESM import */var _Fields_getAssociationField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Fields/getAssociationField */ "./src/workflow/Defaultframe/common/Filters/Fields/getAssociationField.js");
/* ESM import */var _Fields_getDeadlineField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Fields/getDeadlineField */ "./src/workflow/Defaultframe/common/Filters/Fields/getDeadlineField.js");
/* ESM import */var _Fields_getDeadlineStatusField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Fields/getDeadlineStatusField */ "./src/workflow/Defaultframe/common/Filters/Fields/getDeadlineStatusField.js");
/* ESM import */var _Fields_getEndDateField__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Fields/getEndDateField */ "./src/workflow/Defaultframe/common/Filters/Fields/getEndDateField.js");
/* ESM import */var _Fields_getExternalExecutorField__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Fields/getExternalExecutorField */ "./src/workflow/Defaultframe/common/Filters/Fields/getExternalExecutorField.js");
/* ESM import */var _Fields_getViewByProcessOrWorkflowTypeField__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Fields/getViewByProcessOrWorkflowTypeField */ "./src/workflow/Defaultframe/common/Filters/Fields/getViewByProcessOrWorkflowTypeField.js");
/* ESM import */var _Fields_getFailedInstancesField__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Fields/getFailedInstancesField */ "./src/workflow/Defaultframe/common/Filters/Fields/getFailedInstancesField.js");
/* ESM import */var _Fields_getIdentifierField__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Fields/getIdentifierField */ "./src/workflow/Defaultframe/common/Filters/Fields/getIdentifierField.js");
/* ESM import */var _Fields_getInstanceStatusField__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Fields/getInstanceStatusField */ "./src/workflow/Defaultframe/common/Filters/Fields/getInstanceStatusField.js");
/* ESM import */var _Fields_getPastDueRetentionDeadlineField__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Fields/getPastDueRetentionDeadlineField */ "./src/workflow/Defaultframe/common/Filters/Fields/getPastDueRetentionDeadlineField.js");
/* ESM import */var _Fields_getPriorityField__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Fields/getPriorityField */ "./src/workflow/Defaultframe/common/Filters/Fields/getPriorityField.js");
/* ESM import */var _Fields_getProcessTypeTreeField__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./Fields/getProcessTypeTreeField */ "./src/workflow/Defaultframe/common/Filters/Fields/getProcessTypeTreeField.js");
/* ESM import */var _Fields_getRequesterContactZoomField__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./Fields/getRequesterContactZoomField */ "./src/workflow/Defaultframe/common/Filters/Fields/getRequesterContactZoomField.js");
/* ESM import */var _Fields_getRequesterField__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./Fields/getRequesterField */ "./src/workflow/Defaultframe/common/Filters/Fields/getRequesterField.js");
/* ESM import */var _Fields_getSLAStatusField__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Fields/getSLAStatusField */ "./src/workflow/Defaultframe/common/Filters/Fields/getSLAStatusField.js");
/* ESM import */var _Fields_getStartDateField__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./Fields/getStartDateField */ "./src/workflow/Defaultframe/common/Filters/Fields/getStartDateField.js");
/* ESM import */var _Fields_getStarterField__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./Fields/getStarterField */ "./src/workflow/Defaultframe/common/Filters/Fields/getStarterField.js");
/* ESM import */var _Fields_getStarterUserField__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./Fields/getStarterUserField */ "./src/workflow/Defaultframe/common/Filters/Fields/getStarterUserField.js");
/* ESM import */var _Fields_getTitleField__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./Fields/getTitleField */ "./src/workflow/Defaultframe/common/Filters/Fields/getTitleField.js");
/* ESM import */var _Fields_getWorkflowCategoryField__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./Fields/getWorkflowCategoryField */ "./src/workflow/Defaultframe/common/Filters/Fields/getWorkflowCategoryField.js");
/* ESM import */var _Fields_getWorkflowStatusZoomField__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./Fields/getWorkflowStatusZoomField */ "./src/workflow/Defaultframe/common/Filters/Fields/getWorkflowStatusZoomField.js");
/* ESM import */var _Fields_getWorkflowTypeTreeField__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./Fields/getWorkflowTypeTreeField */ "./src/workflow/Defaultframe/common/Filters/Fields/getWorkflowTypeTreeField.js");
/* ESM import */var _Fields_getCompanyZoomField__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./Fields/getCompanyZoomField */ "./src/workflow/Defaultframe/common/Filters/Fields/getCompanyZoomField.js");
/* ESM import */var _Fields_getEnabledActivityExecutorField__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./Fields/getEnabledActivityExecutorField */ "./src/workflow/Defaultframe/common/Filters/Fields/getEnabledActivityExecutorField.js");
/* ESM import */var _Fields_getAssignedToMeField__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./Fields/getAssignedToMeField */ "./src/workflow/Defaultframe/common/Filters/Fields/getAssignedToMeField.js");
/* ESM import */var _Fields_getRecentlyModifiedField__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./Fields/getRecentlyModifiedField */ "./src/workflow/Defaultframe/common/Filters/Fields/getRecentlyModifiedField.js");
/* ESM import */var _Fields_getEnabledActivityZoomField__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./Fields/getEnabledActivityZoomField */ "./src/workflow/Defaultframe/common/Filters/Fields/getEnabledActivityZoomField.js");
/* ESM import */var _Fields_getCauseZoomField__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./Fields/getCauseZoomField */ "./src/workflow/Defaultframe/common/Filters/Fields/getCauseZoomField.js");
/* ESM import */var _Fields_getAutomationObjectField__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./Fields/getAutomationObjectField */ "./src/workflow/Defaultframe/common/Filters/Fields/getAutomationObjectField.js");
/* ESM import */var _Fields_getInitializerViewByField__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./Fields/getInitializerViewByField */ "./src/workflow/Defaultframe/common/Filters/Fields/getInitializerViewByField.js");
/* ESM import */var _Fields_getFormTableModalField__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./Fields/getFormTableModalField */ "./src/workflow/Defaultframe/common/Filters/Fields/getFormTableModalField.js");
/* ESM import */var _Fields_getSimpleAttributeField__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! ./Fields/getSimpleAttributeField */ "./src/workflow/Defaultframe/common/Filters/Fields/getSimpleAttributeField.js");
































/**
 * Obtém o campo Deadline
 */ 
/**
 * Obtém o status do campo Deadline
 */ 
/**
 * Obtém o campo Data de Término
 */ 
/**
 * Obtém o campo Executor Externo
 */ 
/**
 * Obtém o campo Identificador
 */ 
/**
 * Obtém o campo Status da Instância
 */ 
/**
 * Obtém o campo Prioridade
 */ 
/**
 * Obtém o campo de Tipo de Processo
 */ 
/**
 * Obtém o campo Contato do Solicitante (Zoom)
 */ 
/**
 * Obtém o campo Status do SLA
 */ 
/**
 * Obtém o campo Data de Início
 */ 
/**
 * Obtém o campo de Iniciador
 */ 
/**
 * Obtém o campo Título
 */ 
/**
 * Obtém o campo de Tipo de Fluxo de Trabalho
 */ 
/**
 * Obtém o campo Status do Fluxo de Trabalho (Zoom)
 */ 
/**
 * Obtém o campo Árvore de Tipo de Fluxo de Trabalho
 */ 
/**
 * Obtém o campo de associação
 */ 
/**
 * Obtém o campo de usuário iniciador (utilização interna)
 */ 
/**
 * Obtém o campo de solicitante (utilização interna)
 */ 
/**
 * Obtém o toogle de exibir somente instâncias com falha na atividade de sistema
 */ 
/**
 * Obtém o toogle de exibir apenas processos com prazo de retenção vencido
 */ 
/**
 * Obtém o campo zoom de empresas
 */ 
/**
 * Obtém o campo de Executor de atividade habilitada
 */ 
/**
 * Deprecated use getEnabledActivityExecutorField
 * Obtém o filtro de Atribuídos a mim
 */ 
/**
 * Obtém o campo de modificados recentemente
 */ 
/**
 * Obtém o zoom de atividades habiltiadas
 */ 
/**
 * Obtém o zoom de causas de catologo (previamente cadastradas)
 */ 
/**
 * Obtém o campo de objeto de automação workflow
 */ 
/**
 * Obtém o campo "visualizar por"
 */ 
/**
 * Obtém o modal de campo de tabela
 */ 
/**
 * Obtém um filtro de attributo simples
 */ 
/**
 * Obtém o campo Visualizar por (Processo modelo ou Tipo de Workflow) - Level 1
 * Zoom de um dos componentes - Level 2
 */ 


}),
"./src/workflow/Defaultframe/constants/EnabledActivityFilterConstants.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  PROCESS: function() { return PROCESS; },
  WORKFLOW_TYPE: function() { return WORKFLOW_TYPE; }
});
var PROCESS = 1;
var WORKFLOW_TYPE = 2;


}),
"./src/workflow/Defaultframe/constants/ObjectAutomationConstants.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var WORKFLOW_OBJECT = 1;
var INCIDENT_OBJECT = 2;
var PROBLEM_OBJECT = 3;
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    WORKFLOW_OBJECT: WORKFLOW_OBJECT,
    INCIDENT_OBJECT: INCIDENT_OBJECT,
    PROBLEM_OBJECT: PROBLEM_OBJECT
});


}),
"./src/workflow/Defaultframe/constants/ViewByConstants.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var FAVORITE = 1;
var LAST_STARTED = 2;
var MOST_STARTED = 3;
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    FAVORITE: FAVORITE,
    LAST_STARTED: LAST_STARTED,
    MOST_STARTED: MOST_STARTED
});


}),
"./src/workflow/Defaultframe/constants/ViewByProcessOrWfTypeConstant.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  PROCESS: function() { return PROCESS; },
  WORKFLOW_TYPE: function() { return WORKFLOW_TYPE; }
});
var PROCESS = "process";
var WORKFLOW_TYPE = "workflowType";


}),
"./src/workflow/Defaultframe/helpers/RequestHelpers.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.json.stringify.js */ "../node_modules/core-js/modules/es.json.stringify.js");
/* ESM import */var core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! Connector */ "Connector");
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(Connector__WEBPACK_IMPORTED_MODULE_2__);
/*istanbul ignore file*/ function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}



function callBaseclassLogic(param) {
    var method = param.method, path = param.path, params = param.params, success = param.success;
    Connector__WEBPACK_IMPORTED_MODULE_2___default().callBaseclassLogic(path, {
        json: JSON.stringify(params)
    }, {
        method: method,
        success: success
    });
}
function callBaseclassLogicPromisse(args) {
    return new Promise(function(resolve) {
        callBaseclassLogic(_object_spread_props(_object_spread({}, args), {
            success: function(response) {
                return resolve(response);
            }
        }));
    });
}
function callBaseclass(param) {
    var method = param.method, path = param.path, params = param.params, success = param.success;
    Connector__WEBPACK_IMPORTED_MODULE_2___default().callBaseclass(path, {
        method: method,
        data: params,
        success: success
    });
}
function callBaseclassPromisse(args) {
    return new Promise(function(resolve) {
        callBaseclass(_object_spread_props(_object_spread({}, args), {
            success: function(response) {
                return resolve(response);
            }
        }));
    });
}
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    callBaseclassPromisse: callBaseclassPromisse,
    callBaseclassLogicPromisse: callBaseclassLogicPromisse
});


}),
"./src/workflow/components/Helpers/SLAProgress/index.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  SLADataPresentation: function() { return SLADataPresentation; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ "../node_modules/core-js/modules/es.object.keys.js");
/* ESM import */var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ "../node_modules/core-js/modules/es.array.filter.js");
/* ESM import */var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_web_btoa_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/web.btoa.js */ "../node_modules/core-js/modules/web.btoa.js");
/* ESM import */var core_js_modules_web_btoa_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_btoa_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_web_dom_exception_constructor_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/web.dom-exception.constructor.js */ "../node_modules/core-js/modules/web.dom-exception.constructor.js");
/* ESM import */var core_js_modules_web_dom_exception_constructor_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_exception_constructor_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var core_js_modules_web_dom_exception_stack_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/web.dom-exception.stack.js */ "../node_modules/core-js/modules/web.dom-exception.stack.js");
/* ESM import */var core_js_modules_web_dom_exception_stack_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_exception_stack_js__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var core_js_modules_web_dom_exception_to_string_tag_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/web.dom-exception.to-string-tag.js */ "../node_modules/core-js/modules/web.dom-exception.to-string-tag.js");
/* ESM import */var core_js_modules_web_dom_exception_to_string_tag_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_exception_to_string_tag_js__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.error.to-string.js */ "../node_modules/core-js/modules/es.error.to-string.js");
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_24__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC */ "../reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC.js");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_11__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Label__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Label */ "../reactor2/src/Atomic/components/Atoms/Label.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Label__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Label__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var reactor2_src_Atomic_components_Orgs_PopOver_PopOver__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactor2/src/Atomic/components/Orgs/PopOver/PopOver */ "../reactor2/src/Atomic/components/Orgs/PopOver/PopOver.jsx");
/* ESM import */var reactor2_src_Atomic_components_Orgs_PopOver_PopOver__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Orgs_PopOver_PopOver__WEBPACK_IMPORTED_MODULE_13__);
/* ESM import */var reactor2_src_Atomic_components_Orgs_OverlayTrigger__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactor2/src/Atomic/components/Orgs/OverlayTrigger */ "../reactor2/src/Atomic/components/Orgs/OverlayTrigger.jsx");
/* ESM import */var reactor2_src_Atomic_components_Orgs_OverlayTrigger__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Orgs_OverlayTrigger__WEBPACK_IMPORTED_MODULE_14__);
/* ESM import */var reactor2_src_Atomic_components_Mols_LinearProgressBar__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactor2/src/Atomic/components/Mols/LinearProgressBar */ "../reactor2/src/Atomic/components/Mols/LinearProgressBar.jsx");
/* ESM import */var reactor2_src_Atomic_components_Mols_LinearProgressBar__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Mols_LinearProgressBar__WEBPACK_IMPORTED_MODULE_15__);
/* ESM import */var reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! reactor2/src/Styles/styleVariables */ "../reactor2/src/Styles/styleVariables.js");
/* ESM import */var reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_16__);
/* ESM import */var reactor2_src_Atomic_components_Orgs_SimpleList_SimpleList__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! reactor2/src/Atomic/components/Orgs/SimpleList/SimpleList */ "../reactor2/src/Atomic/components/Orgs/SimpleList/SimpleList.jsx");
/* ESM import */var reactor2_src_Atomic_components_Orgs_SimpleList_SimpleList__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Orgs_SimpleList_SimpleList__WEBPACK_IMPORTED_MODULE_17__);
/* ESM import */var reactor2_src_Atomic_components_Orgs_SimpleList_SimpleListItem__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! reactor2/src/Atomic/components/Orgs/SimpleList/SimpleListItem */ "../reactor2/src/Atomic/components/Orgs/SimpleList/SimpleListItem.jsx");
/* ESM import */var reactor2_src_Atomic_components_Orgs_SimpleList_SimpleListItem__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Orgs_SimpleList_SimpleListItem__WEBPACK_IMPORTED_MODULE_18__);
/* ESM import */var reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! reactor2/src/Atomic/components/Mols/TextView */ "../reactor2/src/Atomic/components/Mols/TextView.jsx");
/* ESM import */var reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_19__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Image/Image */ "../reactor2/src/Atomic/components/Atoms/Image/Image.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_20__);
/* ESM import */var reactorCmps_src_workflow_Defaultframe_helpers_RequestHelpers__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/helpers/RequestHelpers */ "./src/workflow/Defaultframe/helpers/RequestHelpers.js");
/* ESM import */var reactor2_src_constants_statusConstants__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! reactor2/src/constants/statusConstants */ "../reactor2/src/constants/statusConstants.js");
/* ESM import */var reactor2_src_constants_sizeConstants__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! reactor2/src/constants/sizeConstants */ "../reactor2/src/constants/sizeConstants.js");
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}






















var SLABarMinSize = {
    width: "min-content"
};
var SLABarMaxSize = {
    width: "100%"
};
var SLABarMinSizeStyle = {
    display: "inline-flex"
};
var SLABarMaxSizeStyle = {
    display: "flex",
    alignItems: "center"
};
var lineProgressBarStyle = {
    cursor: "pointer"
};
var lineProgressBarMaxSizeStyle = {
    flex: 1
};



function SLADataPresentation(param) {
    var slaData = param.slaData, getToken = param.getToken;
    var _useState = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_10__.useState)([]), 2), dataList = _useState[0], setDataList = _useState[1];
    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(function() {
        var resoltitle = getToken(302082);
        if (slaData.fgslastatus === 1) {
            resoltitle = slaData.isfinished ? getToken(213998) : getToken(302081);
            var totalTimeTitle = slaData.isfrstresp ? getToken(309304) : getToken(309305);
            resoltitle = slaData.isfinished ? getToken(213998) : totalTimeTitle;
        }
        var plannedTime = slaData.nmplaytime && slaData.nmplannedtime ? " ".concat(getToken(100729), " ").concat(slaData.nmplannedtime) : '';
        var firstResptime = slaData.firstResptime ? " ".concat(getToken(100729), " ").concat(slaData.firstResptime) : '';
        var items = {
            nmstatus: {
                label: getToken(100072),
                value: slaData.icon.title
            },
            dtdeadline: {
                label: getToken(100431),
                value: slaData.deadline
            },
            nmplaytime: {
                label: getToken(114700),
                value: "".concat(slaData.nmplaytime).concat(plannedTime)
            },
            nmresoltime: {
                label: resoltitle,
                value: slaData.nmresoltime
            },
            nmpausedtime: {
                label: getToken(302083),
                value: slaData.nmpausedtime
            },
            playfrtime: {
                label: getToken(218183),
                value: "".concat(slaData.playfrtime).concat(firstResptime)
            },
            nmlevel: {
                label: getToken(100773),
                value: slaData.nmlevel
            }
        };
        setDataList(items);
    }, [
        slaData,
        getToken
    ]);
    var createSlaListItem = function(label, value, idx) {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement((reactor2_src_Atomic_components_Orgs_SimpleList_SimpleListItem__WEBPACK_IMPORTED_MODULE_18___default()), {
            key: idx,
            id: idx,
            fontSize: (reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_16___default().font.fontXXS),
            content: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement((reactor2_src_Atomic_components_Mols_TextView__WEBPACK_IMPORTED_MODULE_19___default()), {
                title: label,
                value: value,
                smallFont: true
            })
        });
    };
    var items = Object.keys(dataList).map(function(item, idx) {
        if (Boolean(dataList[item].value)) {
            return createSlaListItem(dataList[item].label, dataList[item].value, idx);
        }
    }).filter(function(item) {
        return item !== undefined;
    });
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement((reactor2_src_Atomic_components_Orgs_SimpleList_SimpleList__WEBPACK_IMPORTED_MODULE_17___default()), null, items);
}
function SLAProgress(param) {
    var slaControlCode = param.slaControlCode, isExecution = param.isExecution, showTitle = param.showTitle, getToken = param.getToken, largeProgressBar = param.largeProgressBar;
    var _useState = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null), 2), slaData = _useState[0], setSlaData = _useState[1];
    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(function() {
        var payload = {
            method: "GET",
            path: "/workflow/wf_sla/sla_data.php?code=".concat(btoa(slaControlCode), "&isExecution=").concat(isExecution)
        };
        reactorCmps_src_workflow_Defaultframe_helpers_RequestHelpers__WEBPACK_IMPORTED_MODULE_21__["default"].callBaseclassPromisse(payload).then(function(response) {
            var data = response.results[0];
            setSlaData(data);
        });
    }, [
        slaControlCode,
        setSlaData,
        isExecution
    ]);
    var getStatusIcon = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(function() {
        var component = null;
        if (slaData) {
            component = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement((reactor2_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_20___default()), {
                src: slaData.icon.src,
                size: reactor2_src_constants_sizeConstants__WEBPACK_IMPORTED_MODULE_23__.SIZE_IMAGEICON,
                style: {
                    marginRight: "8px"
                },
                tooltip: slaData.icon.title
            });
        }
        return component;
    }, [
        slaData
    ]);
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement("div", {
        style: largeProgressBar ? SLABarMaxSize : SLABarMinSize
    }, showTitle && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement((reactor2_src_Atomic_components_Atoms_Label__WEBPACK_IMPORTED_MODULE_12___default()), {
        text: getToken(302084)
    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement("div", {
        className: "SLAProgressBar",
        style: largeProgressBar ? SLABarMaxSizeStyle : SLABarMinSizeStyle
    }, getStatusIcon(), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement((reactor2_src_Atomic_components_Orgs_OverlayTrigger__WEBPACK_IMPORTED_MODULE_14___default()), {
        trigger: "click",
        placement: "bottom",
        rootClose: true,
        overlay: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement((reactor2_src_Atomic_components_Orgs_PopOver_PopOver__WEBPACK_IMPORTED_MODULE_13___default()), {
            title: getToken(302084),
            width: 240
        }, slaData && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement(SLADataPresentation, {
            slaData: slaData,
            getToken: getToken
        }))
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement("div", {
        style: _object_spread({}, lineProgressBarStyle, largeProgressBar ? lineProgressBarMaxSizeStyle : {})
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_10___default().createElement((reactor2_src_Atomic_components_Mols_LinearProgressBar__WEBPACK_IMPORTED_MODULE_15___default()), {
        value: ((slaData === null || slaData === void 0 ? void 0 : slaData.vlpercentage) > 100 ? 100 : slaData === null || slaData === void 0 ? void 0 : slaData.vlpercentage) || 0,
        status: (slaData === null || slaData === void 0 ? void 0 : slaData.fgdue) || reactor2_src_constants_statusConstants__WEBPACK_IMPORTED_MODULE_22__.STATUS_DEFAULT,
        decimals: 2,
        showTooltip: false
    })))));
}
SLAProgress.defaultProps = {
    showTitle: true,
    largeProgressBar: false
};
SLAProgress.propTypes = {
    showTitle: (prop_types__WEBPACK_IMPORTED_MODULE_24___default().bool),
    slaData: (prop_types__WEBPACK_IMPORTED_MODULE_24___default().object),
    getToken: (prop_types__WEBPACK_IMPORTED_MODULE_24___default().func),
    largeProgressBar: (prop_types__WEBPACK_IMPORTED_MODULE_24___default().bool)
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_11___default()(SLAProgress));


}),
"./src/workflow/helpers/constants/WorkflowConstants.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    PRODUCT: 39,
    MANAGER: 104,
    STAFF: 105,
    BASIC: 106,
    VIEW: 258
});


}),
"./src/workflow/widgets/ExecutePendencyWidget/ActivityExecution/actions.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  getButtonsToolbar: function() { return getButtonsToolbar; },
  getCallbackExec: function() { return getCallbackExec; },
  getExtraInfo: function() { return getExtraInfo; },
  getFilters: function() { return getFilters; },
  handleUserActivityAssociation: function() { return handleUserActivityAssociation; },
  verifyCounterSignActivity: function() { return verifyCounterSignActivity; },
  viewActivity: function() { return viewActivity; },
  viewFlowchart: function() { return viewFlowchart; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! Connector */ "Connector");
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(Connector__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var reactorCmps_src_executependency_widgets_ExecutePendencyWidget_helpers_ConfirmAlert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactorCmps/src/executependency/widgets/ExecutePendencyWidget/helpers/ConfirmAlert */ "./src/executependency/widgets/ExecutePendencyWidget/helpers/ConfirmAlert.js");
/* ESM import */var reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactor2/src/helpers/ToasterSystem/ToasterSystem */ "../reactor2/src/helpers/ToasterSystem/ToasterSystem.js");
/* ESM import */var reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var reactor2_src_helpers_CounterSign_CounterSign__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactor2/src/helpers/CounterSign/CounterSign */ "../reactor2/src/helpers/CounterSign/CounterSign.js");
/* ESM import */var reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_Utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! reactorCmps/src/workflow/widgets/ExecutePendencyWidget/helpers/Utils */ "./src/workflow/widgets/ExecutePendencyWidget/helpers/Utils.js");
/* ESM import */var reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactorCmps/src/workflow/widgets/ExecutePendencyWidget/helpers/ActionsConstants */ "./src/workflow/widgets/ExecutePendencyWidget/helpers/ActionsConstants.js");
/* ESM import */var reactorCmps_src_workflow_Defaultframe_common_Filters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactorCmps/src/workflow/Defaultframe/common/Filters */ "./src/workflow/Defaultframe/common/Filters/index.js");
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
    try {
        var info = gen[key](arg);
        var value = info.value;
    } catch (error) {
        reject(error);
        return;
    }
    if (info.done) {
        resolve(value);
    } else {
        Promise.resolve(value).then(_next, _throw);
    }
}
function _async_to_generator(fn) {
    return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
            var gen = fn.apply(self, args);
            function _next(value) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
            }
            function _throw(err) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
            }
            _next(undefined);
        });
    };
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _ts_generator(thisArg, body) {
    var f, y, t, g, _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    };
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}












var dataActivity = {};
var viewActivity = function(record) {
    var _dataActivity_record_idTaskAux, _record_product;
    var params = {
        oid: (_dataActivity_record_idTaskAux = dataActivity[record.idTaskAux]) === null || _dataActivity_record_idTaskAux === void 0 ? void 0 : _dataActivity_record_idTaskAux.cdprocess,
        typeScreen: reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.viewInstanceScreen,
        read: reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.readOnly
    };
    if ((record === null || record === void 0 ? void 0 : (_record_product = record.product) === null || _record_product === void 0 ? void 0 : _record_product.cdIsosystem) !== reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.cdIsoSystemWorkflow) {
        params.read = reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.readOnly;
    }
    (0,reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_Utils__WEBPACK_IMPORTED_MODULE_9__.openWindowActivity)(params);
};
var viewFlowchart = function(record) {
    var _record_product, _dataActivity_record_idTaskAux, _record_product1;
    var path = "".concat(Utils__WEBPACK_IMPORTED_MODULE_3___default().getSystemUrl(), "/workflow/wf_cafe/main_frame.php");
    var fgRestriction = (record === null || record === void 0 ? void 0 : (_record_product = record.product) === null || _record_product === void 0 ? void 0 : _record_product.cdIsosystem) == reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.cdIsoSystemWorkflow && reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.workflowManager;
    var params = {
        idprocess: (_dataActivity_record_idTaskAux = dataActivity[record.idTaskAux]) === null || _dataActivity_record_idTaskAux === void 0 ? void 0 : _dataActivity_record_idTaskAux.cdprocess,
        cdprod: record === null || record === void 0 ? void 0 : (_record_product1 = record.product) === null || _record_product1 === void 0 ? void 0 : _record_product1.cdProduct,
        fgRestriction: fgRestriction,
        path: "".concat(Utils__WEBPACK_IMPORTED_MODULE_3___default().getSystemUrl(), "/")
    };
    Utils__WEBPACK_IMPORTED_MODULE_3___default().openStealthPopUp(path, params, null, null, true);
};
var handleUserActivityAssociation = function(props, type, executeActivity) {
    var _dataActivity_record_idTaskAux, _dataActivity_record_idTaskAux1, _dataActivity_record_idTaskAux2, _dataActivity_record_idTaskAux3;
    var record = props.record, getToken = props.getToken, fetchData = props.fetchData;
    var paramsToChangeAssociation = {
        valor: type === reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.AssociationUser ? "".concat((_dataActivity_record_idTaskAux = dataActivity[record.idTaskAux]) === null || _dataActivity_record_idTaskAux === void 0 ? void 0 : _dataActivity_record_idTaskAux.cdprocess, ";").concat(record === null || record === void 0 ? void 0 : record.idTaskAux) : "".concat((_dataActivity_record_idTaskAux1 = dataActivity[record.idTaskAux]) === null || _dataActivity_record_idTaskAux1 === void 0 ? void 0 : _dataActivity_record_idTaskAux1.cdprocess, ";").concat(record === null || record === void 0 ? void 0 : record.idTaskAux, ";").concat((_dataActivity_record_idTaskAux2 = dataActivity[record.idTaskAux]) === null || _dataActivity_record_idTaskAux2 === void 0 ? void 0 : _dataActivity_record_idTaskAux2.fgexecutortype),
        REF: type,
        isTaskCenter: reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.isTaskCenter
    };
    var paramsToExecute = {
        isTaskCenter: reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.isTaskCenter,
        oid: record === null || record === void 0 ? void 0 : record.idTaskAux,
        idprocess: (_dataActivity_record_idTaskAux3 = dataActivity[record.idTaskAux]) === null || _dataActivity_record_idTaskAux3 === void 0 ? void 0 : _dataActivity_record_idTaskAux3.cdprocess,
        typeScreen: reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.executeAcitivityScreen
    };
    var updateUserActivityAssociation = function() {
        return new Promise(function(reject) {
            Connector__WEBPACK_IMPORTED_MODULE_4___default().callBaseclass("workflow/wf_task/task_action.php", {
                method: (Connector__WEBPACK_IMPORTED_MODULE_4___default().POST),
                data: paramsToChangeAssociation,
                success: function(response) {
                    if (!isNaN(response)) {
                        /**
          	 * Lista de tokens do que o backend pode retornar
           	* @tokens
           	* 216264, 216263, 216262
          	 */ reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_7___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_7___default().WARNING), getToken(response));
                    } else {
                        if (executeActivity) {
                            (0,reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_Utils__WEBPACK_IMPORTED_MODULE_9__.openWindowActivity)(paramsToExecute);
                        }
                        fetchData();
                    }
                },
                error: reject
            });
        });
    };
    if (type === reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.DisassociationUser) {
        (0,reactorCmps_src_executependency_widgets_ExecutePendencyWidget_helpers_ConfirmAlert__WEBPACK_IMPORTED_MODULE_6__.confirmAlert)({
            id: String(record === null || record === void 0 ? void 0 : record.oid),
            show: true,
            header: getToken(100088),
            body: getToken(219024),
            onConfirm: function() {
                updateUserActivityAssociation();
            },
            onCancel: function() {}
        });
    } else {
        updateUserActivityAssociation();
    }
};
var openActivity = function(props) {
    var _dataActivity_record_idTaskAux, _dataActivity_record_idTaskAux1, _dataActivity_record_idTaskAux2;
    var record = props.record, getToken = props.getToken;
    var params = {
        oid: record === null || record === void 0 ? void 0 : record.idTaskAux,
        idprocess: (_dataActivity_record_idTaskAux = dataActivity[record.idTaskAux]) === null || _dataActivity_record_idTaskAux === void 0 ? void 0 : _dataActivity_record_idTaskAux.idprocess,
        typeScreen: reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.executeAcitivityScreen
    };
    if (((_dataActivity_record_idTaskAux1 = dataActivity[record.idTaskAux]) === null || _dataActivity_record_idTaskAux1 === void 0 ? void 0 : _dataActivity_record_idTaskAux1.nmuser) !== WorkspaceInfo__WEBPACK_IMPORTED_MODULE_5___default().getUserName() && ((_dataActivity_record_idTaskAux2 = dataActivity[record.idTaskAux]) === null || _dataActivity_record_idTaskAux2 === void 0 ? void 0 : _dataActivity_record_idTaskAux2.fgtypetask) === reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.normalTaskType) {
        (0,reactorCmps_src_executependency_widgets_ExecutePendencyWidget_helpers_ConfirmAlert__WEBPACK_IMPORTED_MODULE_6__.confirmAlert)({
            id: String(record === null || record === void 0 ? void 0 : record.oid),
            show: true,
            header: getToken(100088),
            body: getToken(208961),
            onConfirm: function() {
                handleUserActivityAssociation(props, reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.AssociationUser, true);
            },
            onCancel: function() {
                params.view = reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.readOnly;
                (0,reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_Utils__WEBPACK_IMPORTED_MODULE_9__.openWindowActivity)(params);
            }
        });
    } else {
        (0,reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_Utils__WEBPACK_IMPORTED_MODULE_9__.openWindowActivity)(params);
    }
};
var verifyCounterSignActivity = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function(idprocess) {
        var params;
        return _ts_generator(this, function(_state) {
            params = "oidprocess=" + idprocess;
            return [
                2,
                new Promise(function(resolve) {
                    Connector__WEBPACK_IMPORTED_MODULE_4___default().callBaseclass("workflow/wf_task/counter_sign_process_rpc.php", {
                        dataType: "json",
                        method: "POST",
                        data: params,
                        success: function(response) {
                            return resolve(response === reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.requiredCounterSign);
                        }
                    });
                })
            ];
        });
    });
    return function verifyCounterSignActivity(idprocess) {
        return _ref.apply(this, arguments);
    };
}();
var getCallbackExec = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function(props) {
        var verifyCounterSign;
        return _ts_generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        verifyCounterSignActivity(dataActivity[props.record.idTaskAux].idprocess)
                    ];
                case 1:
                    verifyCounterSign = _state.sent();
                    if (verifyCounterSign) {
                        reactor2_src_helpers_CounterSign_CounterSign__WEBPACK_IMPORTED_MODULE_8__["default"].confirm({
                            reason: props.getToken(200526),
                            onConfirm: function() {
                                openActivity(props);
                            }
                        });
                    } else {
                        openActivity(props);
                    }
                    return [
                        2
                    ];
            }
        });
    });
    return function getCallbackExec(props) {
        return _ref.apply(this, arguments);
    };
}();
var getButtonsToolbar = function(props) {
    var _dataActivity_props_record_idTaskAux, _dataActivity_props_record_idTaskAux1;
    var record = props.record;
    var buttons = [
        {
            icon: 'seicon-eye',
            tooltip: props.getToken(101433),
            onClick: function() {
                viewActivity(record);
            }
        },
        {
            icon: 'seicon-flowchart',
            tooltip: props.getToken(102017),
            onClick: function() {
                viewFlowchart(record);
            }
        }
    ];
    if (!((_dataActivity_props_record_idTaskAux = dataActivity[props.record.idTaskAux]) === null || _dataActivity_props_record_idTaskAux === void 0 ? void 0 : _dataActivity_props_record_idTaskAux.nmuser) && ((_dataActivity_props_record_idTaskAux1 = dataActivity[props.record.idTaskAux]) === null || _dataActivity_props_record_idTaskAux1 === void 0 ? void 0 : _dataActivity_props_record_idTaskAux1.fgtypetask) === reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.normalTaskType) {
        buttons.push({
            icon: 'seicon-user-plus',
            tooltip: props.getToken(108254),
            onClick: function() {
                handleUserActivityAssociation(props, reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.AssociationUser);
            }
        });
    } else {
        buttons.push({
            icon: 'seicon-user-minus',
            tooltip: props.getToken(108255),
            onClick: function() {
                handleUserActivityAssociation(props, reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_ActionsConstants__WEBPACK_IMPORTED_MODULE_10__.ActionsConstants.DisassociationUser);
            }
        });
    }
    return buttons;
};
var getExtraInfo = function(records) {
    return new Promise(function(resolve, reject) {
        Connector__WEBPACK_IMPORTED_MODULE_4___default().callBaseclass("workflow/rest/taskcenter/getActivityData.php", {
            method: (Connector__WEBPACK_IMPORTED_MODULE_4___default().POST),
            data: {
                mappedTaskAuxIds: records.map(function(record) {
                    return record.idTaskAux;
                })
            },
            success: function(param) {
                var results = param.results;
                resolve(records.map(function(record) {
                    var idTaskAux = record.idTaskAux, cdTask = record.cdTask;
                    var _results__idTaskAux = results[0][idTaskAux], idprocessmodel = _results__idTaskAux.idprocessmodel, nmworkflowtype = _results__idTaskAux.nmworkflowtype, nmstruct = _results__idTaskAux.nmstruct, cdslacontrol = _results__idTaskAux.cdslacontrol;
                    dataActivity = _object_spread_props(_object_spread({}, dataActivity), _define_property({}, idTaskAux, results[0][idTaskAux]));
                    var extraInfos = [
                        {
                            label: 100377,
                            value: idprocessmodel
                        },
                        {
                            label: 100355,
                            value: nmstruct
                        },
                        {
                            label: 301294,
                            value: nmworkflowtype
                        }
                    ];
                    if (cdslacontrol) {
                        extraInfos.push({
                            label: 217842,
                            value: cdslacontrol,
                            component: (0,reactorCmps_src_workflow_widgets_ExecutePendencyWidget_helpers_Utils__WEBPACK_IMPORTED_MODULE_9__.getSLAProgress)(cdslacontrol)
                        });
                    }
                    return {
                        cdTask: cdTask,
                        extraInfos: extraInfos
                    };
                }));
            },
            error: reject
        });
    });
};
var getFilters = function(getToken) {
    return {
        title: "Fields",
        form: [
            _object_spread({}, (0,reactorCmps_src_workflow_Defaultframe_common_Filters__WEBPACK_IMPORTED_MODULE_11__.getViewByProcessOrWorkflowTypeField)(getToken)),
            _object_spread_props(_object_spread({}, (0,reactorCmps_src_workflow_Defaultframe_common_Filters__WEBPACK_IMPORTED_MODULE_11__.getEnabledActivityZoomField)(getToken)), {
                dependsOn: "viewByProcessOrWorkflowType"
            })
        ]
    };
};


}),
"./src/workflow/widgets/ExecutePendencyWidget/helpers/ActionsConstants.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  ActionsConstants: function() { return ActionsConstants; }
});
var ActionsConstants = {
    requiredCounterSign: 1,
    AssociationUser: 1,
    DisassociationUser: 2,
    viewInstanceScreen: 1,
    executeAcitivityScreen: 2,
    readOnly: 1,
    workflowManager: 1,
    normalTaskType: 1,
    cdIsoSystemWorkflow: 104,
    isTaskCenter: 1
};


}),
"./src/workflow/widgets/ExecutePendencyWidget/helpers/Utils.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  addParamsUrl: function() { return addParamsUrl; },
  getSLAProgress: function() { return getSLAProgress; },
  openWindowActivity: function() { return openWindowActivity; },
  watchWindow: function() { return watchWindow; }
});
/* ESM import */var core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/web.timers.js */ "../node_modules/core-js/modules/web.timers.js");
/* ESM import */var core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_timers_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_array_for_each_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.array.for-each.js */ "../node_modules/core-js/modules/es.array.for-each.js");
/* ESM import */var core_js_modules_es_array_for_each_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_for_each_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var reactorCmps_src_workflow_components_Helpers_SLAProgress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactorCmps/src/workflow/components/Helpers/SLAProgress */ "./src/workflow/components/Helpers/SLAProgress/index.jsx");








var watchWindow = function(windowInstance, callback) {
    var timer = setInterval(function() {
        if (windowInstance.closed) {
            clearInterval(timer);
            callback();
        }
    }, 500);
};
var addParamsUrl = function(params) {
    var url = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '';
    var queryParams = "";
    params.forEach(function(param, index) {
        var prefix = index === 0 ? "?" : "&";
        queryParams += "".concat(prefix).concat(param.name, "=").concat(param.value);
    });
    return "".concat(url).concat(queryParams);
};
var openWindowActivity = function(params) {
    var path = "".concat(Utils__WEBPACK_IMPORTED_MODULE_6___default().getSystemUrl(), "/workflow/wf_gen_instance/wf_gen_instance_data.php");
    Utils__WEBPACK_IMPORTED_MODULE_6___default().openStealthPopUp(path, params, null, null, true);
};
var getSLAProgress = function(value) {
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_5___default().createElement(reactorCmps_src_workflow_components_Helpers_SLAProgress__WEBPACK_IMPORTED_MODULE_7__["default"], {
        slaControlCode: value,
        isExecution: true,
        showTitle: false,
        largeProgressBar: true
    });
};


}),
"../node_modules/core-js/modules/es.math.trunc.js": (function (__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

var $ = __webpack_require__(/*! ../internals/export */ "../node_modules/core-js/internals/export.js");
var trunc = __webpack_require__(/*! ../internals/math-trunc */ "../node_modules/core-js/internals/math-trunc.js");

// `Math.trunc` method
// https://tc39.es/ecma262/#sec-math.trunc
$({ target: 'Math', stat: true }, {
  trunc: trunc
});


}),

}]);
//# sourceMappingURL=watch1749037385376-vendors-src_workflow_widgets_ExecutePendencyWidget_ActivityExecution_actions_js.js.map