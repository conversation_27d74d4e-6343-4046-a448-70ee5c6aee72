{"version": 3, "file": "analytics/OLAPDefaultFrameValidator.js", "sources": ["webpack://watch1749037385376/./src/analytics/components/OLAPDefaultFrameValidator.jsx"], "sourcesContent": ["/* eslint-disable no-console */\nimport PropTypes from 'prop-types';\nimport React, { Fragment, memo, useEffect, useState } from 'react';\nimport Utils from 'Utils';\nimport Link from 'reactor2/src/Atomic/components/Atoms/Link/Link';\nimport Banner from 'reactor2/src/Atomic/components/Mols/Banner/Banner';\nimport DefaultAlert from 'reactor2/src/Atomic/components/Orgs/DefaultAlerts/DefaultAlert';\nimport { STATUS_WARNING } from 'reactor2/src/constants/statusConstants';\nimport zIndex from 'reactor2/src/helpers/zIndex';\nimport { spacing } from 'reactor2/src/Styles/styleVariables';\n\nconst containerStyle = {\n\tposition: 'absolute',\n\twidth: '100%',\n\tzIndex: zIndex.modal\n};\n\nconst listStyle = {\n\ttextAlign: 'left'\n};\n\nconst listItemStyle = {\n\tpadding: spacing.spacing1 + ' 0'\n};\n\nconst logHeaderStyle = 'color: red; font-size: 18px; font-weight: bold;';\n\nconst title = 'Há itens a serem adequados na abertura do OLAP pela DefaultFrame';\n\n/* istanbul ignore next: just a developer resource */\nconst OLAPDefaultFrameValidator = ({ items }) => {\n\tconst [showAlert, setAlert] = useState(false);\n\tconst itemsList = (\n\t\t<Fragment>\n\t\t\t<ul style={listStyle}>\n\t\t\t\t{ items.map((item, key) => <li key={key} style={listItemStyle}>{ item }</li>) }\n\t\t\t</ul>\n\t\t\t<Link href={Utils.getSystemUrl() + '/seonly/se-analytics/adequacoes.html'}>\n\t\t\t\t{ 'Confira a planilha de itens para mais informações.' }\n\t\t\t</Link>\n\t\t</Fragment>\n\t);\n\tconst link = <Link children={'Ver itens'} onClick={() => setAlert(true)} />;\n\n\tuseEffect(() => {\n\t\tconsole.log('%c' + title, logHeaderStyle);\n\t\tconsole.log(items);\n\t}, [items]);\n\n\treturn (\n\t\t<div style={containerStyle}>\n\t\t\t<Banner link={link} text={title} type={STATUS_WARNING} dismissable />\n\t\t\t<DefaultAlert body={itemsList} onClose={() => setAlert(false)} show={showAlert} type={STATUS_WARNING} />\n\t\t</div>\n\t);\n};\n\nOLAPDefaultFrameValidator.displayName = 'Analytics/components/OLAPDefaultFrameValidator';\n\nOLAPDefaultFrameValidator.propTypes = {\n\titems: PropTypes.array\n};\n\nexport default memo(OLAPDefaultFrameValidator);"], "names": ["PropTypes", "React", "Fragment", "memo", "useEffect", "useState", "Utils", "Link", "Banner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS_WARNING", "zIndex", "spacing", "containerStyle", "listStyle", "listItemStyle", "logHeaderStyle", "title", "OLAPDefaultFrameValidator", "items", "_useState", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "itemsList", "item", "key", "link", "console"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6BAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DkB;AA9DZ;AACgC;AACzC;AACwC;AACK;AACmB;AAClB;AACvB;AACY;AAE7D,IAAMa,iBAAiB;IACtB,UAAU;IACV,OAAO;IACP,QAAQF,0EAAY;AACrB;AAEA,IAAMG,YAAY;IACjB,WAAW;AACZ;AAEA,IAAMC,gBAAgB;IACrB,SAASH,gFAAgB,GAAG;AAC7B;AAEA,IAAMI,iBAAiB;AAEvB,IAAMC,QAAQ;AAEd,mDAAmD,GACnD,IAAMC,4BAA4B;QAAGC,cAAAA;IACpC,IAA8BC,6BAAAA,+CAAQA,CAAC,YAAhCC,YAAuBD,cAAZE,WAAYF;IAC9B,IAAMG,0BACL,2DAACrB,2CAAQA,sBACR,2DAAC;QAAG,OAAOY;OACRK,MAAM,GAAG,CAAC,SAACK,MAAMC;6BAAQ,2DAAC;YAAG,KAAKA;YAAK,OAAOV;WAAiBS;uBAElE,2DAACjB,uFAAIA;QAAC,MAAMD,yDAAkB,KAAK;OAChC;IAIL,IAAMoB,qBAAO,2DAACnB,uFAAIA;QAAC,UAAU;QAAa,SAAS;mBAAMe,SAAS;;;IAElElB,gDAASA,CAAC;QACTuB,QAAQ,GAAG,CAAC,OAAOV,OAAOD;QAC1BW,QAAQ,GAAG,CAACR;IACb,GAAG;QAACA;KAAM;IAEV,qBACC,2DAAC;QAAI,OAAON;qBACX,2DAACL,0FAAMA;QAAC,MAAMkB;QAAM,MAAMT;QAAO,MAAMP,kFAAcA;QAAE;sBACvD,2DAACD,uGAAYA;QAAC,MAAMc;QAAW,SAAS;mBAAMD,SAAS;;QAAQ,MAAMD;QAAW,MAAMX,kFAAcA;;AAGvG;AAEAQ,0BAA0B,WAAW,GAAG;AAExCA,0BAA0B,SAAS,GAAG;IACrC,OAAOlB,yDAAe;AACvB;AAEA,0EAAeG,2CAAIA,CAACe,0BAA0BA,EAAC"}