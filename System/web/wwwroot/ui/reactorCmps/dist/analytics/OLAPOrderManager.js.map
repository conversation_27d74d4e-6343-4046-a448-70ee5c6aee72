{"version": 3, "file": "analytics/OLAPOrderManager.js", "sources": ["webpack://watch1749037385376/./src/analytics/components/DraggableListItem/DraggableListItem.mcss", "webpack://watch1749037385376/./src/analytics/components/OLAPDataTypeIcon/OLAPDataTypeIcon.mcss", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/Popover/Popover.mcss", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/BrowserDetector.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/EnterLeaveCounter.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/HTML5Backend.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/MonotonicInterpolant.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/NativeDragSource.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/getDataFromDataTransfer.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/index.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/NativeDragSources/nativeTypesConfig.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/NativeTypes.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/OffsetUtils.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/OptionsReader.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/getEmptyImage.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/index.js", "webpack://watch1749037385376/../node_modules/react-dnd-html5-backend/dist/esm/utils/js_utils.js", "webpack://watch1749037385376/./src/analytics/actions/SortActions.js", "webpack://watch1749037385376/./src/analytics/actions/sortConstants.js", "webpack://watch1749037385376/./src/analytics/components/DraggableListItem/DraggableListItem.jsx", "webpack://watch1749037385376/./src/analytics/components/DraggableListItem/DraggableListItemPlaceholder.jsx", "webpack://watch1749037385376/./src/analytics/components/DraggableListItem/DraggableListItemPresentation.jsx", "webpack://watch1749037385376/./src/analytics/components/FieldsList.jsx", "webpack://watch1749037385376/./src/analytics/components/ListItem.jsx", "webpack://watch1749037385376/./src/analytics/components/OLAPDataTypeIcon/OLAPDataTypeIcon.jsx", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/DropableFieldsList/DropableFieldsList.jsx", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/DropableFieldsList/DropableFieldsListPresentation.jsx", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/OLAPViewOrderManager.jsx", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/Popover/PopoverContent.jsx", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/Popover/PopoverContentPresentation.jsx", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/SortTinyDropdown/SortTinyDropdown.jsx", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/SortTinyDropdown/SortTinyDropdownPresentation.jsx", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/SortableListItem/SortableListItem.jsx", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/SortableListItem/SortableListItemPresentation.jsx", "webpack://watch1749037385376/./src/analytics/components/ScopeRedux/ScopeRedux.js", "webpack://watch1749037385376/./src/analytics/components/ScopeRedux/SortRedux.js", "webpack://watch1749037385376/./src/analytics/components/TinyDropdown.jsx", "webpack://watch1749037385376/./src/analytics/constants/dataType.js", "webpack://watch1749037385376/./src/analytics/constants/dragAndDrop.js", "webpack://watch1749037385376/./src/analytics/constants/sort.js", "webpack://watch1749037385376/./src/analytics/contexts/PopoverContextProvider.jsx", "webpack://watch1749037385376/./src/analytics/helpers/Cube/cubeConfigurations.js", "webpack://watch1749037385376/./src/analytics/helpers/SortUtils.js", "webpack://watch1749037385376/./src/analytics/helpers/orderManagerHelper.js", "webpack://watch1749037385376/./src/analytics/reducers/SortReducer.js", "webpack://watch1749037385376/./src/analytics/store/SortStore.js", "webpack://watch1749037385376/./src/chartengine2/constants/datatypes.js", "webpack://watch1749037385376/./src/chartengine2/constants/formulas.js", "webpack://watch1749037385376/./src/chartengine2/constants/responsiveDimensions.js", "webpack://watch1749037385376/./src/chartengine2/constants/serieTypes.js", "webpack://watch1749037385376/./src/chartengine2/constants/supportedTypes.js", "webpack://watch1749037385376/./src/chartengine2/constants/utils.js", "webpack://watch1749037385376/./src/chartengine2/helpers/ChartLayoutHelper.js", "webpack://watch1749037385376/./src/chartengine2/helpers/ChartUtils.js", "webpack://watch1749037385376/./src/chartengine2/helpers/commonHelper.js", "webpack://watch1749037385376/./src/workspace/helpers/DashboardDragNDropContext.js", "webpack://watch1749037385376/./src/analytics/components/DraggableListItem/DraggableListItem.mcss?3932", "webpack://watch1749037385376/./src/analytics/components/OLAPDataTypeIcon/OLAPDataTypeIcon.mcss?c6ea", "webpack://watch1749037385376/./src/analytics/components/OLAPOrderManager/Popover/Popover.mcss?79ef"], "sourcesContent": ["// Imports\nvar ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ = require(\"../../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\");\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.DraggableListItem_rctDraggableListItem_fMgeC {\n  border: 1px solid transparent;\n}\n.DraggableListItem_rctDraggableListItem_fMgeC .DraggableListItem_hoverLeftContent_azrnZ {\n  display: none;\n}\n.DraggableListItem_rctDraggableListItem_fMgeC:hover {\n  border: 1px solid #CCCCCC;\n  -webkit-box-shadow: 0 5px 15px -3px #CCCCCC;\n  -moz-box-shadow: 0 5px 15px -3px #CCCCCC;\n  box-shadow: 0 5px 15px -3px #CCCCCC;\n  cursor: move;\n}\n.DraggableListItem_rctDraggableListItem_fMgeC:hover .DraggableListItem_hoverLeftContent_azrnZ {\n  display: block;\n}\n.DraggableListItem_rctDraggableListItem_fMgeC:hover .DraggableListItem_icon_an7w7:before {\n  margin: 0;\n  width: 6px;\n  left: -4px;\n  position: relative;\n}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"rctDraggableListItem\": `DraggableListItem_rctDraggableListItem_fMgeC`,\n\t\"hoverLeftContent\": `DraggableListItem_hoverLeftContent_azrnZ`,\n\t\"icon\": `DraggableListItem_icon_an7w7`\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// Imports\nvar ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ = require(\"../../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\");\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.OLAPDataTypeIcon_icon_qh0iS[class*=\" seicon-\"]:before, .OLAPDataTypeIcon_icon_qh0iS[class*=\" glyphicon-\"]:before, .OLAPDataTypeIcon_icon_qh0iS[class^=glyphicon-]:before, .OLAPDataTypeIcon_icon_qh0iS[class^=seicon-]:before {\n  margin-left: 0;\n  margin-right: 0;\n}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"icon\": `OLAPDataTypeIcon_icon_qh0iS`\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// Imports\nvar ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ = require(\"../../../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\");\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.Popover_rctSortedItem_GHIhY .Popover_rctRemoveBtn_ozxKr {\n  opacity: 0;\n}\n.Popover_rctSortedItem_GHIhY:hover .Popover_rctRemoveBtn_ozxKr {\n  opacity: 1;\n}\n\n.Popover_rctOLAPOrderManagerPopover_xdVtz.arrowStyle div.arrow:after {\n  border-top-color: #fff;\n  border-bottom-color: #fff;\n}\n.Popover_rctOLAPOrderManagerPopover_xdVtz div.rctPopOverContent {\n  background: #fff;\n}`, \"\"]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"rctSortedItem\": `Popover_rctSortedItem_GHIhY`,\n\t\"rctRemoveBtn\": `Popover_rctRemoveBtn_ozxKr`,\n\t\"rctOLAPOrderManagerPopover\": `Popover_rctOLAPOrderManagerPopover_xdVtz`\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "import { memoize } from './utils/js_utils';\nexport var isFirefox = memoize(function () {\n  return /firefox/i.test(navigator.userAgent);\n});\nexport var isSafari = memoize(function () {\n  return Boolean(window.safari);\n});", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nimport { union, without } from './utils/js_utils';\n\nvar EnterLeaveCounter =\n/*#__PURE__*/\nfunction () {\n  function EnterLeaveCounter(isNodeInDocument) {\n    _classCallCheck(this, EnterLeaveCounter);\n\n    this.entered = [];\n    this.isNodeInDocument = isNodeInDocument;\n  }\n\n  _createClass(EnterLeaveCounter, [{\n    key: \"enter\",\n    value: function enter(enteringNode) {\n      var _this = this;\n\n      var previousLength = this.entered.length;\n\n      var isNodeEntered = function isNodeEntered(node) {\n        return _this.isNodeInDocument(node) && (!node.contains || node.contains(enteringNode));\n      };\n\n      this.entered = union(this.entered.filter(isNodeEntered), [enteringNode]);\n      return previousLength === 0 && this.entered.length > 0;\n    }\n  }, {\n    key: \"leave\",\n    value: function leave(leavingNode) {\n      var previousLength = this.entered.length;\n      this.entered = without(this.entered.filter(this.isNodeInDocument), leavingNode);\n      return previousLength > 0 && this.entered.length === 0;\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this.entered = [];\n    }\n  }]);\n\n  return EnterLeaveCounter;\n}();\n\nexport { EnterLeaveCounter as default };", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nimport EnterLeaveCounter from './EnterLeaveCounter';\nimport { isFirefox } from './BrowserDetector';\nimport { getNodeClientOffset, getEventClientOffset, getDragPreviewOffset } from './OffsetUtils';\nimport { createNativeDragSource, matchNativeItemType } from './NativeDragSources';\nimport * as NativeTypes from './NativeTypes';\nimport { OptionsReader } from './OptionsReader';\n\nvar HTML5Backend =\n/*#__PURE__*/\nfunction () {\n  function HTML5Backend(manager, globalContext) {\n    var _this = this;\n\n    _classCallCheck(this, HTML5Backend);\n\n    this.sourcePreviewNodes = new Map();\n    this.sourcePreviewNodeOptions = new Map();\n    this.sourceNodes = new Map();\n    this.sourceNodeOptions = new Map();\n    this.dragStartSourceIds = null;\n    this.dropTargetIds = [];\n    this.dragEnterTargetIds = [];\n    this.currentNativeSource = null;\n    this.currentNativeHandle = null;\n    this.currentDragSourceNode = null;\n    this.altKeyPressed = false;\n    this.mouseMoveTimeoutTimer = null;\n    this.asyncEndDragFrameId = null;\n    this.dragOverTargetIds = null;\n\n    this.getSourceClientOffset = function (sourceId) {\n      return getNodeClientOffset(_this.sourceNodes.get(sourceId));\n    };\n\n    this.endDragNativeItem = function () {\n      if (!_this.isDraggingNativeItem()) {\n        return;\n      }\n\n      _this.actions.endDrag();\n\n      _this.registry.removeSource(_this.currentNativeHandle);\n\n      _this.currentNativeHandle = null;\n      _this.currentNativeSource = null;\n    };\n\n    this.isNodeInDocument = function (node) {\n      // Check the node either in the main document or in the current context\n      return _this.document && _this.document.body && document.body.contains(node);\n    };\n\n    this.endDragIfSourceWasRemovedFromDOM = function () {\n      var node = _this.currentDragSourceNode;\n\n      if (_this.isNodeInDocument(node)) {\n        return;\n      }\n\n      if (_this.clearCurrentDragSourceNode()) {\n        _this.actions.endDrag();\n      }\n    };\n\n    this.handleTopDragStartCapture = function () {\n      _this.clearCurrentDragSourceNode();\n\n      _this.dragStartSourceIds = [];\n    };\n\n    this.handleTopDragStart = function (e) {\n      if (e.defaultPrevented) {\n        return;\n      }\n\n      var dragStartSourceIds = _this.dragStartSourceIds;\n      _this.dragStartSourceIds = null;\n      var clientOffset = getEventClientOffset(e); // Avoid crashing if we missed a drop event or our previous drag died\n\n      if (_this.monitor.isDragging()) {\n        _this.actions.endDrag();\n      } // Don't publish the source just yet (see why below)\n\n\n      _this.actions.beginDrag(dragStartSourceIds || [], {\n        publishSource: false,\n        getSourceClientOffset: _this.getSourceClientOffset,\n        clientOffset: clientOffset\n      });\n\n      var dataTransfer = e.dataTransfer;\n      var nativeType = matchNativeItemType(dataTransfer);\n\n      if (_this.monitor.isDragging()) {\n        if (dataTransfer && typeof dataTransfer.setDragImage === 'function') {\n          // Use custom drag image if user specifies it.\n          // If child drag source refuses drag but parent agrees,\n          // use parent's node as drag image. Neither works in IE though.\n          var sourceId = _this.monitor.getSourceId();\n\n          var sourceNode = _this.sourceNodes.get(sourceId);\n\n          var dragPreview = _this.sourcePreviewNodes.get(sourceId) || sourceNode;\n\n          if (dragPreview) {\n            var _this$getCurrentSourc = _this.getCurrentSourcePreviewNodeOptions(),\n                anchorX = _this$getCurrentSourc.anchorX,\n                anchorY = _this$getCurrentSourc.anchorY,\n                offsetX = _this$getCurrentSourc.offsetX,\n                offsetY = _this$getCurrentSourc.offsetY;\n\n            var anchorPoint = {\n              anchorX: anchorX,\n              anchorY: anchorY\n            };\n            var offsetPoint = {\n              offsetX: offsetX,\n              offsetY: offsetY\n            };\n            var dragPreviewOffset = getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint);\n            dataTransfer.setDragImage(dragPreview, dragPreviewOffset.x, dragPreviewOffset.y);\n          }\n        }\n\n        try {\n          // Firefox won't drag without setting data\n          dataTransfer.setData('application/json', {});\n        } catch (err) {} // IE doesn't support MIME types in setData\n        // Store drag source node so we can check whether\n        // it is removed from DOM and trigger endDrag manually.\n\n\n        _this.setCurrentDragSourceNode(e.target); // Now we are ready to publish the drag source.. or are we not?\n\n\n        var _this$getCurrentSourc2 = _this.getCurrentSourcePreviewNodeOptions(),\n            captureDraggingState = _this$getCurrentSourc2.captureDraggingState;\n\n        if (!captureDraggingState) {\n          // Usually we want to publish it in the next tick so that browser\n          // is able to screenshot the current (not yet dragging) state.\n          //\n          // It also neatly avoids a situation where render() returns null\n          // in the same tick for the source element, and browser freaks out.\n          setTimeout(function () {\n            return _this.actions.publishDragSource();\n          }, 0);\n        } else {\n          // In some cases the user may want to override this behavior, e.g.\n          // to work around IE not supporting custom drag previews.\n          //\n          // When using a custom drag layer, the only way to prevent\n          // the default drag preview from drawing in IE is to screenshot\n          // the dragging state in which the node itself has zero opacity\n          // and height. In this case, though, returning null from render()\n          // will abruptly end the dragging, which is not obvious.\n          //\n          // This is the reason such behavior is strictly opt-in.\n          _this.actions.publishDragSource();\n        }\n      } else if (nativeType) {\n        // A native item (such as URL) dragged from inside the document\n        _this.beginDragNativeItem(nativeType);\n      } else if (dataTransfer && !dataTransfer.types && (e.target && !e.target.hasAttribute || !e.target.hasAttribute('draggable'))) {\n        // Looks like a Safari bug: dataTransfer.types is null, but there was no draggable.\n        // Just let it drag. It's a native type (URL or text) and will be picked up in\n        // dragenter handler.\n        return;\n      } else {\n        // If by this time no drag source reacted, tell browser not to drag.\n        e.preventDefault();\n      }\n    };\n\n    this.handleTopDragEndCapture = function () {\n      if (_this.clearCurrentDragSourceNode()) {\n        // Firefox can dispatch this event in an infinite loop\n        // if dragend handler does something like showing an alert.\n        // Only proceed if we have not handled it already.\n        _this.actions.endDrag();\n      }\n    };\n\n    this.handleTopDragEnterCapture = function (e) {\n      _this.dragEnterTargetIds = [];\n\n      var isFirstEnter = _this.enterLeaveCounter.enter(e.target);\n\n      if (!isFirstEnter || _this.monitor.isDragging()) {\n        return;\n      }\n\n      var dataTransfer = e.dataTransfer;\n      var nativeType = matchNativeItemType(dataTransfer);\n\n      if (nativeType) {\n        // A native item (such as file or URL) dragged from outside the document\n        _this.beginDragNativeItem(nativeType, dataTransfer);\n      }\n    };\n\n    this.handleTopDragEnter = function (e) {\n      var dragEnterTargetIds = _this.dragEnterTargetIds;\n      _this.dragEnterTargetIds = [];\n\n      if (!_this.monitor.isDragging()) {\n        // This is probably a native item type we don't understand.\n        return;\n      }\n\n      _this.altKeyPressed = e.altKey;\n\n      if (!isFirefox()) {\n        // Don't emit hover in `dragenter` on Firefox due to an edge case.\n        // If the target changes position as the result of `dragenter`, Firefox\n        // will still happily dispatch `dragover` despite target being no longer\n        // there. The easy solution is to only fire `hover` in `dragover` on FF.\n        _this.actions.hover(dragEnterTargetIds, {\n          clientOffset: getEventClientOffset(e)\n        });\n      }\n\n      var canDrop = dragEnterTargetIds.some(function (targetId) {\n        return _this.monitor.canDropOnTarget(targetId);\n      });\n\n      if (canDrop) {\n        // IE requires this to fire dragover events\n        e.preventDefault();\n\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = _this.getCurrentDropEffect();\n        }\n      }\n    };\n\n    this.handleTopDragOverCapture = function () {\n      _this.dragOverTargetIds = [];\n    };\n\n    this.handleTopDragOver = function (e) {\n      var dragOverTargetIds = _this.dragOverTargetIds;\n      _this.dragOverTargetIds = [];\n\n      if (!_this.monitor.isDragging()) {\n        // This is probably a native item type we don't understand.\n        // Prevent default \"drop and blow away the whole document\" action.\n        e.preventDefault();\n\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = 'none';\n        }\n\n        return;\n      }\n\n      _this.altKeyPressed = e.altKey;\n\n      _this.actions.hover(dragOverTargetIds || [], {\n        clientOffset: getEventClientOffset(e)\n      });\n\n      var canDrop = (dragOverTargetIds || []).some(function (targetId) {\n        return _this.monitor.canDropOnTarget(targetId);\n      });\n\n      if (canDrop) {\n        // Show user-specified drop effect.\n        e.preventDefault();\n\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = _this.getCurrentDropEffect();\n        }\n      } else if (_this.isDraggingNativeItem()) {\n        // Don't show a nice cursor but still prevent default\n        // \"drop and blow away the whole document\" action.\n        e.preventDefault();\n      } else {\n        e.preventDefault();\n\n        if (e.dataTransfer) {\n          e.dataTransfer.dropEffect = 'none';\n        }\n      }\n    };\n\n    this.handleTopDragLeaveCapture = function (e) {\n      if (_this.isDraggingNativeItem()) {\n        e.preventDefault();\n      }\n\n      var isLastLeave = _this.enterLeaveCounter.leave(e.target);\n\n      if (!isLastLeave) {\n        return;\n      }\n\n      if (_this.isDraggingNativeItem()) {\n        _this.endDragNativeItem();\n      }\n    };\n\n    this.handleTopDropCapture = function (e) {\n      _this.dropTargetIds = [];\n      e.preventDefault();\n\n      if (_this.isDraggingNativeItem()) {\n        _this.currentNativeSource.loadDataTransfer(e.dataTransfer);\n      }\n\n      _this.enterLeaveCounter.reset();\n    };\n\n    this.handleTopDrop = function (e) {\n      var dropTargetIds = _this.dropTargetIds;\n      _this.dropTargetIds = [];\n\n      _this.actions.hover(dropTargetIds, {\n        clientOffset: getEventClientOffset(e)\n      });\n\n      _this.actions.drop({\n        dropEffect: _this.getCurrentDropEffect()\n      });\n\n      if (_this.isDraggingNativeItem()) {\n        _this.endDragNativeItem();\n      } else {\n        _this.endDragIfSourceWasRemovedFromDOM();\n      }\n    };\n\n    this.handleSelectStart = function (e) {\n      var target = e.target; // Only IE requires us to explicitly say\n      // we want drag drop operation to start\n\n      if (typeof target.dragDrop !== 'function') {\n        return;\n      } // Inputs and textareas should be selectable\n\n\n      if (target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {\n        return;\n      } // For other targets, ask IE\n      // to enable drag and drop\n\n\n      e.preventDefault();\n      target.dragDrop();\n    };\n\n    this.options = new OptionsReader(globalContext);\n    this.actions = manager.getActions();\n    this.monitor = manager.getMonitor();\n    this.registry = manager.getRegistry();\n    this.enterLeaveCounter = new EnterLeaveCounter(this.isNodeInDocument);\n  } // public for test\n\n\n  _createClass(HTML5Backend, [{\n    key: \"setup\",\n    value: function setup() {\n      if (this.window === undefined) {\n        return;\n      }\n\n      if (this.window.__isReactDndBackendSetUp) {\n        throw new Error('Cannot have two HTML5 backends at the same time.');\n      }\n\n      this.window.__isReactDndBackendSetUp = true;\n      this.addEventListeners(this.window);\n    }\n  }, {\n    key: \"teardown\",\n    value: function teardown() {\n      if (this.window === undefined) {\n        return;\n      }\n\n      this.window.__isReactDndBackendSetUp = false;\n      this.removeEventListeners(this.window);\n      this.clearCurrentDragSourceNode();\n\n      if (this.asyncEndDragFrameId) {\n        this.window.cancelAnimationFrame(this.asyncEndDragFrameId);\n      }\n    }\n  }, {\n    key: \"connectDragPreview\",\n    value: function connectDragPreview(sourceId, node, options) {\n      var _this2 = this;\n\n      this.sourcePreviewNodeOptions.set(sourceId, options);\n      this.sourcePreviewNodes.set(sourceId, node);\n      return function () {\n        _this2.sourcePreviewNodes.delete(sourceId);\n\n        _this2.sourcePreviewNodeOptions.delete(sourceId);\n      };\n    }\n  }, {\n    key: \"connectDragSource\",\n    value: function connectDragSource(sourceId, node, options) {\n      var _this3 = this;\n\n      this.sourceNodes.set(sourceId, node);\n      this.sourceNodeOptions.set(sourceId, options);\n\n      var handleDragStart = function handleDragStart(e) {\n        return _this3.handleDragStart(e, sourceId);\n      };\n\n      var handleSelectStart = function handleSelectStart(e) {\n        return _this3.handleSelectStart(e);\n      };\n\n      node.setAttribute('draggable', 'true');\n      node.addEventListener('dragstart', handleDragStart);\n      node.addEventListener('selectstart', handleSelectStart);\n      return function () {\n        _this3.sourceNodes.delete(sourceId);\n\n        _this3.sourceNodeOptions.delete(sourceId);\n\n        node.removeEventListener('dragstart', handleDragStart);\n        node.removeEventListener('selectstart', handleSelectStart);\n        node.setAttribute('draggable', 'false');\n      };\n    }\n  }, {\n    key: \"connectDropTarget\",\n    value: function connectDropTarget(targetId, node) {\n      var _this4 = this;\n\n      var handleDragEnter = function handleDragEnter(e) {\n        return _this4.handleDragEnter(e, targetId);\n      };\n\n      var handleDragOver = function handleDragOver(e) {\n        return _this4.handleDragOver(e, targetId);\n      };\n\n      var handleDrop = function handleDrop(e) {\n        return _this4.handleDrop(e, targetId);\n      };\n\n      node.addEventListener('dragenter', handleDragEnter);\n      node.addEventListener('dragover', handleDragOver);\n      node.addEventListener('drop', handleDrop);\n      return function () {\n        node.removeEventListener('dragenter', handleDragEnter);\n        node.removeEventListener('dragover', handleDragOver);\n        node.removeEventListener('drop', handleDrop);\n      };\n    }\n  }, {\n    key: \"addEventListeners\",\n    value: function addEventListeners(target) {\n      // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n      if (!target.addEventListener) {\n        return;\n      }\n\n      target.addEventListener('dragstart', this.handleTopDragStart);\n      target.addEventListener('dragstart', this.handleTopDragStartCapture, true);\n      target.addEventListener('dragend', this.handleTopDragEndCapture, true);\n      target.addEventListener('dragenter', this.handleTopDragEnter);\n      target.addEventListener('dragenter', this.handleTopDragEnterCapture, true);\n      target.addEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n      target.addEventListener('dragover', this.handleTopDragOver);\n      target.addEventListener('dragover', this.handleTopDragOverCapture, true);\n      target.addEventListener('drop', this.handleTopDrop);\n      target.addEventListener('drop', this.handleTopDropCapture, true);\n    }\n  }, {\n    key: \"removeEventListeners\",\n    value: function removeEventListeners(target) {\n      // SSR Fix (https://github.com/react-dnd/react-dnd/pull/813\n      if (!target.removeEventListener) {\n        return;\n      }\n\n      target.removeEventListener('dragstart', this.handleTopDragStart);\n      target.removeEventListener('dragstart', this.handleTopDragStartCapture, true);\n      target.removeEventListener('dragend', this.handleTopDragEndCapture, true);\n      target.removeEventListener('dragenter', this.handleTopDragEnter);\n      target.removeEventListener('dragenter', this.handleTopDragEnterCapture, true);\n      target.removeEventListener('dragleave', this.handleTopDragLeaveCapture, true);\n      target.removeEventListener('dragover', this.handleTopDragOver);\n      target.removeEventListener('dragover', this.handleTopDragOverCapture, true);\n      target.removeEventListener('drop', this.handleTopDrop);\n      target.removeEventListener('drop', this.handleTopDropCapture, true);\n    }\n  }, {\n    key: \"getCurrentSourceNodeOptions\",\n    value: function getCurrentSourceNodeOptions() {\n      var sourceId = this.monitor.getSourceId();\n      var sourceNodeOptions = this.sourceNodeOptions.get(sourceId);\n      return _objectSpread({\n        dropEffect: this.altKeyPressed ? 'copy' : 'move'\n      }, sourceNodeOptions || {});\n    }\n  }, {\n    key: \"getCurrentDropEffect\",\n    value: function getCurrentDropEffect() {\n      if (this.isDraggingNativeItem()) {\n        // It makes more sense to default to 'copy' for native resources\n        return 'copy';\n      }\n\n      return this.getCurrentSourceNodeOptions().dropEffect;\n    }\n  }, {\n    key: \"getCurrentSourcePreviewNodeOptions\",\n    value: function getCurrentSourcePreviewNodeOptions() {\n      var sourceId = this.monitor.getSourceId();\n      var sourcePreviewNodeOptions = this.sourcePreviewNodeOptions.get(sourceId);\n      return _objectSpread({\n        anchorX: 0.5,\n        anchorY: 0.5,\n        captureDraggingState: false\n      }, sourcePreviewNodeOptions || {});\n    }\n  }, {\n    key: \"isDraggingNativeItem\",\n    value: function isDraggingNativeItem() {\n      var itemType = this.monitor.getItemType();\n      return Object.keys(NativeTypes).some(function (key) {\n        return NativeTypes[key] === itemType;\n      });\n    }\n  }, {\n    key: \"beginDragNativeItem\",\n    value: function beginDragNativeItem(type, dataTransfer) {\n      this.clearCurrentDragSourceNode();\n      this.currentNativeSource = createNativeDragSource(type, dataTransfer);\n      this.currentNativeHandle = this.registry.addSource(type, this.currentNativeSource);\n      this.actions.beginDrag([this.currentNativeHandle]);\n    }\n  }, {\n    key: \"setCurrentDragSourceNode\",\n    value: function setCurrentDragSourceNode(node) {\n      var _this5 = this;\n\n      this.clearCurrentDragSourceNode();\n      this.currentDragSourceNode = node; // A timeout of > 0 is necessary to resolve Firefox issue referenced\n      // See:\n      //   * https://github.com/react-dnd/react-dnd/pull/928\n      //   * https://github.com/react-dnd/react-dnd/issues/869\n\n      var MOUSE_MOVE_TIMEOUT = 1000; // Receiving a mouse event in the middle of a dragging operation\n      // means it has ended and the drag source node disappeared from DOM,\n      // so the browser didn't dispatch the dragend event.\n      //\n      // We need to wait before we start listening for mousemove events.\n      // This is needed because the drag preview needs to be drawn or else it fires an 'mousemove' event\n      // immediately in some browsers.\n      //\n      // See:\n      //   * https://github.com/react-dnd/react-dnd/pull/928\n      //   * https://github.com/react-dnd/react-dnd/issues/869\n      //\n\n      this.mouseMoveTimeoutTimer = setTimeout(function () {\n        return _this5.window && _this5.window.addEventListener('mousemove', _this5.endDragIfSourceWasRemovedFromDOM, true);\n      }, MOUSE_MOVE_TIMEOUT);\n    }\n  }, {\n    key: \"clearCurrentDragSourceNode\",\n    value: function clearCurrentDragSourceNode() {\n      if (this.currentDragSourceNode) {\n        this.currentDragSourceNode = null;\n\n        if (this.window) {\n          this.window.clearTimeout(this.mouseMoveTimeoutTimer || undefined);\n          this.window.removeEventListener('mousemove', this.endDragIfSourceWasRemovedFromDOM, true);\n        }\n\n        this.mouseMoveTimeoutTimer = null;\n        return true;\n      }\n\n      return false;\n    }\n  }, {\n    key: \"handleDragStart\",\n    value: function handleDragStart(e, sourceId) {\n      if (e.defaultPrevented) {\n        return;\n      }\n\n      if (!this.dragStartSourceIds) {\n        this.dragStartSourceIds = [];\n      }\n\n      this.dragStartSourceIds.unshift(sourceId);\n    }\n  }, {\n    key: \"handleDragEnter\",\n    value: function handleDragEnter(e, targetId) {\n      this.dragEnterTargetIds.unshift(targetId);\n    }\n  }, {\n    key: \"handleDragOver\",\n    value: function handleDragOver(e, targetId) {\n      if (this.dragOverTargetIds === null) {\n        this.dragOverTargetIds = [];\n      }\n\n      this.dragOverTargetIds.unshift(targetId);\n    }\n  }, {\n    key: \"handleDrop\",\n    value: function handleDrop(e, targetId) {\n      this.dropTargetIds.unshift(targetId);\n    }\n  }, {\n    key: \"window\",\n    get: function get() {\n      return this.options.window;\n    }\n  }, {\n    key: \"document\",\n    get: function get() {\n      return this.options.document;\n    }\n  }]);\n\n  return HTML5Backend;\n}();\n\nexport { HTML5Backend as default };", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nvar MonotonicInterpolant =\n/*#__PURE__*/\nfunction () {\n  function MonotonicInterpolant(xs, ys) {\n    _classCallCheck(this, MonotonicInterpolant);\n\n    var length = xs.length; // Rearrange xs and ys so that xs is sorted\n\n    var indexes = [];\n\n    for (var i = 0; i < length; i++) {\n      indexes.push(i);\n    }\n\n    indexes.sort(function (a, b) {\n      return xs[a] < xs[b] ? -1 : 1;\n    }); // Get consecutive differences and slopes\n\n    var dys = [];\n    var dxs = [];\n    var ms = [];\n    var dx;\n    var dy;\n\n    for (var _i = 0; _i < length - 1; _i++) {\n      dx = xs[_i + 1] - xs[_i];\n      dy = ys[_i + 1] - ys[_i];\n      dxs.push(dx);\n      dys.push(dy);\n      ms.push(dy / dx);\n    } // Get degree-1 coefficients\n\n\n    var c1s = [ms[0]];\n\n    for (var _i2 = 0; _i2 < dxs.length - 1; _i2++) {\n      var m2 = ms[_i2];\n      var mNext = ms[_i2 + 1];\n\n      if (m2 * mNext <= 0) {\n        c1s.push(0);\n      } else {\n        dx = dxs[_i2];\n        var dxNext = dxs[_i2 + 1];\n        var common = dx + dxNext;\n        c1s.push(3 * common / ((common + dxNext) / m2 + (common + dx) / mNext));\n      }\n    }\n\n    c1s.push(ms[ms.length - 1]); // Get degree-2 and degree-3 coefficients\n\n    var c2s = [];\n    var c3s = [];\n    var m;\n\n    for (var _i3 = 0; _i3 < c1s.length - 1; _i3++) {\n      m = ms[_i3];\n      var c1 = c1s[_i3];\n      var invDx = 1 / dxs[_i3];\n\n      var _common = c1 + c1s[_i3 + 1] - m - m;\n\n      c2s.push((m - c1 - _common) * invDx);\n      c3s.push(_common * invDx * invDx);\n    }\n\n    this.xs = xs;\n    this.ys = ys;\n    this.c1s = c1s;\n    this.c2s = c2s;\n    this.c3s = c3s;\n  }\n\n  _createClass(MonotonicInterpolant, [{\n    key: \"interpolate\",\n    value: function interpolate(x) {\n      var xs = this.xs,\n          ys = this.ys,\n          c1s = this.c1s,\n          c2s = this.c2s,\n          c3s = this.c3s; // The rightmost point in the dataset should give an exact result\n\n      var i = xs.length - 1;\n\n      if (x === xs[i]) {\n        return ys[i];\n      } // Search for the interval x is in, returning the corresponding y if x is one of the original xs\n\n\n      var low = 0;\n      var high = c3s.length - 1;\n      var mid;\n\n      while (low <= high) {\n        mid = Math.floor(0.5 * (low + high));\n        var xHere = xs[mid];\n\n        if (xHere < x) {\n          low = mid + 1;\n        } else if (xHere > x) {\n          high = mid - 1;\n        } else {\n          return ys[mid];\n        }\n      }\n\n      i = Math.max(0, high); // Interpolate\n\n      var diff = x - xs[i];\n      var diffSq = diff * diff;\n      return ys[i] + c1s[i] * diff + c2s[i] * diffSq + c3s[i] * diff * diffSq;\n    }\n  }]);\n\n  return MonotonicInterpolant;\n}();\n\nexport { MonotonicInterpolant as default };", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nexport var NativeDragSource =\n/*#__PURE__*/\nfunction () {\n  function NativeDragSource(config) {\n    _classCallCheck(this, NativeDragSource);\n\n    this.config = config;\n    this.item = {};\n    this.initializeExposedProperties();\n  }\n\n  _createClass(NativeDragSource, [{\n    key: \"initializeExposedProperties\",\n    value: function initializeExposedProperties() {\n      var _this = this;\n\n      Object.keys(this.config.exposeProperties).forEach(function (property) {\n        Object.defineProperty(_this.item, property, {\n          configurable: true,\n          enumerable: true,\n          get: function get() {\n            // eslint-disable-next-line no-console\n            console.warn(\"Browser doesn't allow reading \\\"\".concat(property, \"\\\" until the drop event.\"));\n            return null;\n          }\n        });\n      });\n    }\n  }, {\n    key: \"loadDataTransfer\",\n    value: function loadDataTransfer(dataTransfer) {\n      var _this2 = this;\n\n      if (dataTransfer) {\n        var newProperties = {};\n        Object.keys(this.config.exposeProperties).forEach(function (property) {\n          newProperties[property] = {\n            value: _this2.config.exposeProperties[property](dataTransfer, _this2.config.matchesTypes),\n            configurable: true,\n            enumerable: true\n          };\n        });\n        Object.defineProperties(this.item, newProperties);\n      }\n    }\n  }, {\n    key: \"canDrag\",\n    value: function canDrag() {\n      return true;\n    }\n  }, {\n    key: \"beginDrag\",\n    value: function beginDrag() {\n      return this.item;\n    }\n  }, {\n    key: \"isDragging\",\n    value: function isDragging(monitor, handle) {\n      return handle === monitor.getSourceId();\n    }\n  }, {\n    key: \"endDrag\",\n    value: function endDrag() {// empty\n    }\n  }]);\n\n  return NativeDragSource;\n}();", "export function getDataFromDataTransfer(dataTransfer, typesToTry, defaultValue) {\n  var result = typesToTry.reduce(function (resultSoFar, typeToTry) {\n    return resultSoFar || dataTransfer.getData(typeToTry);\n  }, '');\n  return result != null ? result : defaultValue;\n}", "import { nativeTypesConfig } from './nativeTypesConfig';\nimport { NativeDragSource } from './NativeDragSource';\nexport function createNativeDragSource(type, dataTransfer) {\n  var result = new NativeDragSource(nativeTypesConfig[type]);\n  result.loadDataTransfer(dataTransfer);\n  return result;\n}\nexport function matchNativeItemType(dataTransfer) {\n  if (!dataTransfer) {\n    return null;\n  }\n\n  var dataTransferTypes = Array.prototype.slice.call(dataTransfer.types || []);\n  return Object.keys(nativeTypesConfig).filter(function (nativeItemType) {\n    var matchesTypes = nativeTypesConfig[nativeItemType].matchesTypes;\n    return matchesTypes.some(function (t) {\n      return dataTransferTypes.indexOf(t) > -1;\n    });\n  })[0] || null;\n}", "var _nativeTypesConfig;\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport * as NativeTypes from '../NativeTypes';\nimport { getDataFromDataTransfer } from './getDataFromDataTransfer';\nexport var nativeTypesConfig = (_nativeTypesConfig = {}, _defineProperty(_nativeTypesConfig, NativeTypes.FILE, {\n  exposeProperties: {\n    files: function files(dataTransfer) {\n      return Array.prototype.slice.call(dataTransfer.files);\n    },\n    items: function items(dataTransfer) {\n      return dataTransfer.items;\n    }\n  },\n  matchesTypes: ['Files']\n}), _defineProperty(_nativeTypesConfig, NativeTypes.URL, {\n  exposeProperties: {\n    urls: function urls(dataTransfer, matchesTypes) {\n      return getDataFromDataTransfer(dataTransfer, matchesTypes, '').split('\\n');\n    }\n  },\n  matchesTypes: ['Url', 'text/uri-list']\n}), _defineProperty(_nativeTypesConfig, NativeTypes.TEXT, {\n  exposeProperties: {\n    text: function text(dataTransfer, matchesTypes) {\n      return getDataFromDataTransfer(dataTransfer, matchesTypes, '');\n    }\n  },\n  matchesTypes: ['Text', 'text/plain']\n}), _nativeTypesConfig);", "export var FILE = '__NATIVE_FILE__';\nexport var URL = '__NATIVE_URL__';\nexport var TEXT = '__NATIVE_TEXT__';", "import { isSafari, isFirefox } from './BrowserDetector';\nimport MonotonicInterpolant from './MonotonicInterpolant';\nvar ELEMENT_NODE = 1;\nexport function getNodeClientOffset(node) {\n  var el = node.nodeType === ELEMENT_NODE ? node : node.parentElement;\n\n  if (!el) {\n    return null;\n  }\n\n  var _el$getBoundingClient = el.getBoundingClientRect(),\n      top = _el$getBoundingClient.top,\n      left = _el$getBoundingClient.left;\n\n  return {\n    x: left,\n    y: top\n  };\n}\nexport function getEventClientOffset(e) {\n  return {\n    x: e.clientX,\n    y: e.clientY\n  };\n}\n\nfunction isImageNode(node) {\n  return node.nodeName === 'IMG' && (isFirefox() || !document.documentElement.contains(node));\n}\n\nfunction getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight) {\n  var dragPreviewWidth = isImage ? dragPreview.width : sourceWidth;\n  var dragPreviewHeight = isImage ? dragPreview.height : sourceHeight; // Work around @2x coordinate discrepancies in browsers\n\n  if (isSafari() && isImage) {\n    dragPreviewHeight /= window.devicePixelRatio;\n    dragPreviewWidth /= window.devicePixelRatio;\n  }\n\n  return {\n    dragPreviewWidth: dragPreviewWidth,\n    dragPreviewHeight: dragPreviewHeight\n  };\n}\n\nexport function getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint) {\n  // The browsers will use the image intrinsic size under different conditions.\n  // Firefox only cares if it's an image, but WebKit also wants it to be detached.\n  var isImage = isImageNode(dragPreview);\n  var dragPreviewNode = isImage ? sourceNode : dragPreview;\n  var dragPreviewNodeOffsetFromClient = getNodeClientOffset(dragPreviewNode);\n  var offsetFromDragPreview = {\n    x: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n    y: clientOffset.y - dragPreviewNodeOffsetFromClient.y\n  };\n  var sourceWidth = sourceNode.offsetWidth,\n      sourceHeight = sourceNode.offsetHeight;\n  var anchorX = anchorPoint.anchorX,\n      anchorY = anchorPoint.anchorY;\n\n  var _getDragPreviewSize = getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight),\n      dragPreviewWidth = _getDragPreviewSize.dragPreviewWidth,\n      dragPreviewHeight = _getDragPreviewSize.dragPreviewHeight;\n\n  var calculateYOffset = function calculateYOffset() {\n    var interpolantY = new MonotonicInterpolant([0, 0.5, 1], [// Dock to the top\n    offsetFromDragPreview.y, // Align at the center\n    offsetFromDragPreview.y / sourceHeight * dragPreviewHeight, // Dock to the bottom\n    offsetFromDragPreview.y + dragPreviewHeight - sourceHeight]);\n    var y = interpolantY.interpolate(anchorY); // Work around Safari 8 positioning bug\n\n    if (isSafari() && isImage) {\n      // We'll have to wait for @3x to see if this is entirely correct\n      y += (window.devicePixelRatio - 1) * dragPreviewHeight;\n    }\n\n    return y;\n  };\n\n  var calculateXOffset = function calculateXOffset() {\n    // Interpolate coordinates depending on anchor point\n    // If you know a simpler way to do this, let me know\n    var interpolantX = new MonotonicInterpolant([0, 0.5, 1], [// Dock to the left\n    offsetFromDragPreview.x, // Align at the center\n    offsetFromDragPreview.x / sourceWidth * dragPreviewWidth, // Dock to the right\n    offsetFromDragPreview.x + dragPreviewWidth - sourceWidth]);\n    return interpolantX.interpolate(anchorX);\n  }; // Force offsets if specified in the options.\n\n\n  var offsetX = offsetPoint.offsetX,\n      offsetY = offsetPoint.offsetY;\n  var isManualOffsetX = offsetX === 0 || offsetX;\n  var isManualOffsetY = offsetY === 0 || offsetY;\n  return {\n    x: isManualOffsetX ? offsetX : calculateXOffset(),\n    y: isManualOffsetY ? offsetY : calculateYOffset()\n  };\n}", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nexport var OptionsReader =\n/*#__PURE__*/\nfunction () {\n  function OptionsReader(globalContext) {\n    _classCallCheck(this, OptionsReader);\n\n    this.globalContext = globalContext;\n  }\n\n  _createClass(OptionsReader, [{\n    key: \"window\",\n    get: function get() {\n      if (this.globalContext) {\n        return this.globalContext;\n      } else if (typeof window !== 'undefined') {\n        return window;\n      }\n\n      return undefined;\n    }\n  }, {\n    key: \"document\",\n    get: function get() {\n      if (this.window) {\n        return this.window.document;\n      }\n\n      return undefined;\n    }\n  }]);\n\n  return OptionsReader;\n}();", "var emptyImage;\nexport function getEmptyImage() {\n  if (!emptyImage) {\n    emptyImage = new Image();\n    emptyImage.src = 'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==';\n  }\n\n  return emptyImage;\n}", "import HTML5Backend from './HTML5Backend';\nimport * as NativeTypes from './NativeTypes';\nexport { getEmptyImage } from './getEmptyImage';\nexport { NativeTypes };\n\nvar createBackend = function createBackend(manager, context) {\n  return new HTML5Backend(manager, context);\n};\n\nexport default createBackend;", "// cheap lodash replacements\nexport function memoize(fn) {\n  var result = null;\n\n  var memoized = function memoized() {\n    if (result == null) {\n      result = fn();\n    }\n\n    return result;\n  };\n\n  return memoized;\n}\n/**\n * drop-in replacement for _.without\n */\n\nexport function without(items, item) {\n  return items.filter(function (i) {\n    return i !== item;\n  });\n}\nexport function union(itemsA, itemsB) {\n  var set = new Set();\n\n  var insertItem = function insertItem(item) {\n    return set.add(item);\n  };\n\n  itemsA.forEach(insertItem);\n  itemsB.forEach(insertItem);\n  var result = [];\n  set.forEach(function (key) {\n    return result.push(key);\n  });\n  return result;\n}", "import sortConstants from 'reactorCmps/src/analytics/actions/sortConstants';\n\nexport default {\n\tsetData(oid, data) {\n\t\treturn {\n\t\t\ttype: sortConstants.SET_DATA,\n\t\t\toid, data\n\t\t};\n\t},\n\n\taddItem(oid, item, aboveItem) {\n\t\treturn {\n\t\t\ttype: sortConstants.ADD_ITEM,\n\t\t\toid, item, aboveItem\n\t\t};\n\t},\n\n\tsortItem(oid, item, aboveItem) {\n\t\treturn {\n\t\t\ttype: sortConstants.SORT_ITEM,\n\t\t\toid, item, aboveItem\n\t\t};\n\t},\n\n\tremoveItem(oid, item) {\n\t\treturn {\n\t\t\ttype: sortConstants.REMOVE_ITEM,\n\t\t\toid, item\n\t\t};\n\t},\n\n\tsetItemAsc(oid, itemOid) {\n\t\treturn {\n\t\t\ttype: sortConstants.SET_ITEM_ASC,\n\t\t\toid, itemOid\n\t\t};\n\t},\n\n\tsetItemDesc(oid, itemOid) {\n\t\treturn {\n\t\t\ttype: sortConstants.SET_ITEM_DESC,\n\t\t\toid, itemOid\n\t\t};\n\t}\n};", "export default {\n\tSET_DATA: 'SE_ANALYTICS_ORDER_MANAGER_SET_DATA',\n\tADD_ITEM: 'SE_ANALYTICS_ORDER_MANAGER_ADD_ITEM',\n\tREMOVE_ITEM: 'SE_ANALYTICS_ORDER_MANAGER_REMOVE_ITEM',\n\tSORT_ITEM: 'SE_ANALYTICS_ORDER_MANAGER_SORT_ITEM',\n\tSET_ITEM_ASC: 'SE_ANALYTICS_ORDER_MANAGER_SET_ITEM_ASC',\n\tSET_ITEM_DESC: 'SE_ANALYTICS_ORDER_MANAGER_SET_ITEM_DESC'\n};", "import { flow, omit } from 'lodash';\nimport PropTypes from 'prop-types';\nimport React, { Component } from 'react';\nimport { DragSource, DropTarget } from 'react-dnd-old';\nimport DraggableListItemPresentation from 'reactorCmps/src/analytics/components/DraggableListItem/DraggableListItemPresentation';\nimport dndConstants from 'reactorCmps/src/analytics/constants/dragAndDrop';\n\n/* istanbul ignore next */\nconst itemSource = {\n\tbeginDrag(props, monitor, component) {\n\t\tif (props.onBeginDrag) {\n\t\t\tprops.onBeginDrag(props, monitor, component);\n\t\t}\n\n\t\treturn props;\n\t},\n\tendDrag(props, monitor, component) {\n\t\tif (props.onEndDrag) {\n\t\t\tprops.onEndDrag(props, monitor, component);\n\t\t}\n\n\t\treturn props;\n\t}\n};\n\n/* istanbul ignore next */\nconst itemTarget = {\n\tcanDrop(props, monitor) {\n\t\treturn props.item.uid !== monitor.getItem().item.uid;\n\t},\n\tdrop(props, monitor) {\n\t\tprops.onDrop(props.type, monitor.getItem(), props.item);\n\t}\n};\n\nclass DraggableListItem extends Component {\n\trender() {\n\t\tlet { connectDropTarget, connectDragSource } = this.props,\n\t\t\tprops = omit(this.props, 'connectDropTarget', 'connectDragSource');\n\n\t\treturn connectDropTarget(\n\t\t\tconnectDragSource(\n\t\t\t\t<div>\n\t\t\t\t\t<DraggableListItemPresentation {...props} />\n\t\t\t\t</div>\n\t\t\t)\n\t\t);\n\t}\n}\n\nDraggableListItem.displayName = 'Analytics/components/DraggableListItem/DraggableListItem';\n\nDraggableListItem.propTypes = {\n\tconnectDropTarget: PropTypes.func,\n\tconnectDragSource: PropTypes.func,\n\tisOver: PropTypes.bool,\n\tisDragging: PropTypes.bool\n};\n\nexport default flow([\n\tnew DropTarget(dndConstants.ORDER_ITEM, itemTarget, (connect, monitor) => ({\n\t\tconnectDropTarget: connect.dropTarget(),\n\t\tisOver: monitor.isOver()\n\t})),\n\tnew DragSource(dndConstants.ORDER_ITEM, itemSource, (connect, monitor) => ({\n\t\tconnectDragSource: connect.dragSource(),\n\t\tisDragging: monitor.isDragging()\n\t}))\n])(DraggableListItem);", "import React, { Component } from 'react';\nimport { colors } from 'reactor/src/helpers/styleVariables';\n\nconst containerStyle = {\n\tbackgroundColor: colors.borderGrey,\n\tborderRadius: 3,\n\tdisplay: 'block',\n\theight: 33,\n\twidth: '100%'\n};\n\nclass DraggableListItemPlaceholder extends Component {\n\trender() {\n\t\treturn <div style={containerStyle}></div>;\n\t}\n}\n\nDraggableListItemPlaceholder.displayName = 'Analytics/components/DraggableListItem/DraggableListItemPlaceholder';\n\nexport default DraggableListItemPlaceholder;", "import React, { Component } from 'react';\nimport { colors, fontBold } from 'reactor/src/helpers/styleVariables';\nimport DraggableListItemPlaceholder from 'reactorCmps/src/analytics/components/DraggableListItem/DraggableListItemPlaceholder';\nimport ListItem from 'reactorCmps/src/analytics/components/ListItem';\nimport styles from 'reactorCmps/src/analytics/components/DraggableListItem/DraggableListItem.mcss';\n\nconst containerStyle = {\n\tborderRadius: 3\n};\n\nconst dragIconStyle = {\n\tcolor: colors.divGray,\n\tfontSize: 18,\n\tfontWeight: fontBold,\n\tmarginRight: 4\n};\n\nconst leftContentStyle = {\n\talignItems: 'center',\n\tdisplay: 'flex',\n\tjustifyContent: 'center',\n\tminWidth: 20,\n\ttextAlign: 'center'\n};\n\nconst dragIconWidth = 10;\n\nexport default class DraggableListItemPresentation extends Component {\n\tgetLeftContent() {\n\t\tlet combinedStyle = Object.assign({}, leftContentStyle),\n\t\t\tleftContent = this.props.leftContent;\n\n\t\tif (leftContent && leftContent.props.style && leftContent.props.style.width) {\n\t\t\tcombinedStyle.width = leftContent.props.style.width + dragIconWidth;\n\t\t}\n\n\t\treturn (\n\t\t\t<div style={combinedStyle}>\n\t\t\t\t<div className={styles.hoverLeftContent}>\n\t\t\t\t\t<span className={styles.icon + ' seicon-drag'} style={dragIconStyle} />\n\t\t\t\t</div>\n\t\t\t\t{ leftContent }\n\t\t\t</div>\n\t\t);\n\t}\n\n\tgetPlaceholder() {\n\t\tif (this.props.isOver && !this.props.isDragging) {\n\t\t\treturn <DraggableListItemPlaceholder />;\n\t\t}\n\n\t\treturn null;\n\t}\n\n\trender() {\n\t\tlet props = this.props;\n\n\t\treturn (\n\t\t\t<div style={containerStyle} className={styles.rctDraggableListItem} draggable>\n\t\t\t\t{ this.getPlaceholder() }\n\t\t\t\t<ListItem\n\t\t\t\t\ttitle={props.title}\n\t\t\t\t\ttitlePrefix={props.titlePrefix}\n\t\t\t\t\tleftContent={this.getLeftContent()}\n\t\t\t\t\trightContent={props.rightContent}\n\t\t\t\t\tstyle={props.style}\n\t\t\t\t/>\n\t\t\t</div>\n\t\t);\n\t}\n}\n\nDraggableListItemPresentation.displayName = 'Analytics/components/DraggableListItem/DraggableListItemPresentation';", "import PropTypes from 'prop-types';\nimport React, { Component } from 'react';\nimport Label from 'reactor/src/Atomic/components/Atoms/Label';\nimport ScrollableContent from 'reactor/src/Atomic/components/Helpers/ScrollableContent';\n\nconst labelStyle = {\n\tfontSize: 12,\n\tmarginBottom: 4\n};\n\nexport default class FieldsList extends Component {\n\tgetContent() {\n\t\tlet me = this,\n\t\t\tscrollStyle = {\n\t\t\t\theight: me.props.height,\n\t\t\t\twidth: me.props.width,\n\t\t\t\tminHeight: me.props.height,\n\t\t\t\tminWidth: me.props.width,\n\t\t\t\toverflowX: 'hidden'\n\t\t\t};\n\n\t\treturn (\n\t\t\t<ScrollableContent style={scrollStyle}>\n\t\t\t\t{ me.props.children }\n\t\t\t</ScrollableContent>\n\t\t);\n\t}\n\n\tgetContainerStyle() {\n\t\tlet me = this,\n\t\t\tstyle = {};\n\n\t\tif (me.props.width) {\n\t\t\tstyle.width = me.props.width;\n\t\t}\n\n\t\treturn style;\n\t}\n\n\trender() {\n\t\tlet me = this;\n\n\t\treturn (\n\t\t\t<div style={me.getContainerStyle()}>\n\t\t\t\t<Label style={labelStyle} text={me.props.title} />\n\t\t\t\t{ me.getContent() }\n\t\t\t</div>\n\t\t);\n\t}\n}\n\nFieldsList.propTypes = {\n\theight: PropTypes.number.isRequired,\n\ttitle: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n\twidth: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n};\n\nFieldsList.displayName = 'Analytics/components/FieldsList';", "import PropTypes from 'prop-types';\nimport React, { Component } from 'react';\n\nconst containerStyle = {\n\t\talignItems: 'center',\n\t\tcolor: 'rgba(0,0,0,0.7)',\n\t\tdisplay: 'flex',\n\t\tlineHeight: '1.3',\n\t\twidth: '100%'\n\t},\n\tleftContentStyle = {\n\t\tflex: '0 1 auto',\n\t\tlineHeight: '1.3',\n\t\tmarginRight: '4px'\n\t},\n\trightContentStyle = {\n\t\tflex: '0 1 auto',\n\t\tlineHeight: '1.3',\n\t\tmarginLeft: '4px'\n\t},\n\ttitleStyle = {\n\t\tflex: '1 1 auto',\n\t\tlineHeight: '1.3',\n\t\toverflow: 'hidden',\n\t\ttextOverflow: 'ellipsis',\n\t\twhiteSpace: 'nowrap'\n\t};\n\nexport default class ListItem extends Component {\n\tgetLeftContent() {\n\t\tlet me = this,\n\t\t\tcontent = me.props.leftContent;\n\n\t\tif (content) {\n\t\t\treturn (\n\t\t\t\t<div style={leftContentStyle}>\n\t\t\t\t\t{ content }\n\t\t\t\t</div>\n\t\t\t);\n\t\t}\n\n\t\treturn null;\n\t}\n\n\tgetRightContent() {\n\t\tlet me = this,\n\t\t\tcontent = me.props.rightContent;\n\n\t\tif (content) {\n\t\t\treturn (\n\t\t\t\t<div style={rightContentStyle}>\n\t\t\t\t\t{ content }\n\t\t\t\t</div>\n\t\t\t);\n\t\t}\n\n\t\treturn null;\n\t}\n\n\tgetTitlePrefix() {\n\t\tlet me = this;\n\n\t\tif (!me.props.titlePrefix)\n\t\t\treturn null;\n\n\t\treturn '[' + (me.props.titlePrefix).toUpperCase() + '] ';\n\t}\n\n\tgetTitle() {\n\t\tlet me = this;\n\t\tlet hint;\n\n\t\tif (typeof me.props.title === 'string')\n\t\t\thint = me.props.title;\n\n\t\tif (me.props.hint)\n\t\t\thint = me.props.hint;\n\n\t\treturn (\n\t\t\t<div style={titleStyle} title={hint}>\n\t\t\t\t{ me.getTitlePrefix() }\n\t\t\t\t{ me.props.title }\n\t\t\t</div>\n\t\t);\n\t}\n\n\trender() {\n\t\tlet me = this,\n\t\t\tstyle = Object.assign({}, containerStyle, me.props.style);\n\n\t\treturn (\n\t\t\t<div style={style}>\n\t\t\t\t{ me.getLeftContent() }\n\t\t\t\t{ me.getTitle() }\n\t\t\t\t{ me.getRightContent() }\n\t\t\t</div>\n\t\t);\n\t}\n}\n\nListItem.propTypes = {\n\tleftContent: PropTypes.node,\n\trightContent: PropTypes.node,\n\ttitle: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,\n\thint: PropTypes.string,\n\ttitlePrefix: PropTypes.string\n};\n\nListItem.displayName = 'Analytics/components/ListItem';", "import { values } from 'lodash';\nimport PropTypes from 'prop-types';\nimport React, { memo, useMemo } from 'react';\nimport dataType from 'reactorCmps/src/analytics/constants/dataType';\nimport styles from 'reactorCmps/src/analytics/components/OLAPDataTypeIcon/OLAPDataTypeIcon.mcss';\n\nconst containerStyle = {\n\tfontSize: 17,\n\tlineHeight: 1\n};\n\nconst colors = {\n\tvarchar: '#00a3f0',\n\tinteger: '#ff8000',\n\tfloat: '#be4153',\n\tdatetime: '#00be6c'\n};\n\nconst TYPES = {\n\t[dataType.TEXT]: 'varchar',\n\t[dataType.BIGTEXT]: 'varchar',\n\t[dataType.BOOLEAN]: 'integer',\n\t[dataType.INTEGER]: 'integer',\n\t[dataType.LONG]: 'integer',\n\t[dataType.CURRENCY]: 'float',\n\t[dataType.DECIMAL]: 'float',\n\t[dataType.DATE]: 'datetime',\n\t[dataType.DATETIME]: 'datetime',\n\t[dataType.TIME]: 'datetime'\n};\n\nfunction OLAPDataTypeIcon({ type, style }) {\n\tconst normalizedType = TYPES[type];\n\tconst iconStyle = useMemo(() => ({\n\t\t...containerStyle,\n\t\tcolor: colors[normalizedType],\n\t\t...style\n\t}), [normalizedType, style]);\n\n\treturn <i style={iconStyle} title={type} className={`${styles.icon} seicon-${normalizedType}`} />;\n}\n\nOLAPDataTypeIcon.propTypes = {\n\ttype: PropTypes.oneOf(values(dataType)).isRequired,\n\tstyle: PropTypes.object\n};\n\nOLAPDataTypeIcon.displayName = 'Analytics/components/OLAPDataTypeIcon';\n\nexport default memo(OLAPDataTypeIcon);", "import { omit } from 'lodash';\nimport React, { Component } from 'react';\nimport { DropTarget } from 'react-dnd-old';\nimport DropableFieldsListPresentation from 'reactorCmps/src/analytics/components/OLAPOrderManager/DropableFieldsList/DropableFieldsListPresentation';\nimport dndConstants from 'reactorCmps/src/analytics/constants/dragAndDrop';\n\n/* istanbul ignore next */\nconst listTarget = {\n\tcanDrop(props, monitor) {\n\t\treturn monitor.isOver({ shallow: true });\n\t},\n\tdrop(props, monitor) {\n\t\tprops.onDrop(props.list, monitor.getItem(), monitor.getDropResult());\n\t}\n};\n\nclass DropableFieldsList extends Component {\n\tgetContent() {\n\t\tlet me = this,\n\t\t\tprops = omit(me.props, 'connectDropTarget', 'list', 'onDrop');\n\n\t\treturn (\n\t\t\t<div>\n\t\t\t\t<DropableFieldsListPresentation {...props} />\n\t\t\t</div>\n\t\t);\n\t}\n\n\trender() {\n\t\treturn this.props.connectDropTarget(this.getContent());\n\t}\n}\n\nDropableFieldsList.displayName = 'Analytics/components/OLAPOrderManager/DropableFieldsList/DropableFieldsList';\n\nexport default new DropTarget(dndConstants.ORDER_ITEM, listTarget, (connect, monitor) => {\n\treturn {\n\t\tconnectDropTarget: connect.dropTarget(),\n\t\tisShallowOver: monitor.isOver({ shallow: true }),\n\t\tisOver: monitor.isOver(),\n\t\tcanDrop: monitor.canDrop()\n\t};\n})(DropableFieldsList);", "import { omit } from 'lodash';\nimport PropTypes from 'prop-types';\nimport React, { Children, Component } from 'react';\nimport DraggableListItemPlaceholder from 'reactorCmps/src/analytics/components/DraggableListItem/DraggableListItemPlaceholder';\nimport FieldsList from 'reactorCmps/src/analytics/components/FieldsList';\n\nconst paddingStyle = {\n\theight: 15\n};\n\nexport default class DropableFieldsListPresentation extends Component {\n\tgetListContent() {\n\t\tlet props = this.props;\n\n\t\tif (Children.count(props.children) === 0 && !props.isOver) {\n\t\t\treturn props.placeholder;\n\t\t}\n\n\t\treturn props.children;\n\t}\n\n\trender() {\n\t\tlet me = this,\n\t\t\tprops = omit(me.props, 'isOver', 'isShallowOver', 'placeholder');\n\n\t\treturn (\n\t\t\t<FieldsList {...props}>\n\t\t\t\t{ me.getListContent() }\n\t\t\t\t{ me.props.isShallowOver ? <DraggableListItemPlaceholder /> : null }\n\t\t\t\t{ me.props.isOver && !me.props.isShallowOver ? <div style={paddingStyle} /> : null }\n\t\t\t</FieldsList>\n\t\t);\n\t}\n}\n\nDropableFieldsListPresentation.propTypes = {\n\tisShallowOver: PropTypes.bool,\n\tisOver: PropTypes.bool,\n\tplaceholder: PropTypes.node\n};\n\nDropableFieldsListPresentation.defaultProps = {\n\tisShallowOver: false,\n\tisOver: false\n};\n\nDropableFieldsListPresentation.displayName = 'Analytics/components/OLAPOrderManager/DropableFieldsList/DropableFieldsListPresentation';", "import PropTypes from 'prop-types';\nimport React, { PureComponent } from 'react';\nimport { dispatch } from 'reactor/src/store/store';\nimport SortActions from 'reactorCmps/src/analytics/actions/SortActions';\nimport PopoverContent from 'reactorCmps/src/analytics/components/OLAPOrderManager/Popover/PopoverContent';\nimport ScopeRedux from 'reactorCmps/src/analytics/components/ScopeRedux/ScopeRedux';\nimport orderManagerHelper from 'reactorCmps/src/analytics/helpers/orderManagerHelper';\n\nexport default class OLAPViewOrderManager extends PureComponent {\n\tcomponentDidMount() {\n\t\tthis.setScope();\n\t\tthis.setData();\n\t}\n\n\tcomponentDidUpdate() {\n\t\tthis.setScope();\n\t}\n\n\tsetScope() {\n\t\tconst { cardDataOid, fetchData, moveCubeSortItem, reorderSorts, setCubeSortOrder } = this.props;\n\n\t\tScopeRedux.setScope({\n\t\t\tgetData: fetchData,\n\t\t\toid: cardDataOid,\n\t\t\tcube: {\n\t\t\t\treorderCubeSortItem: reorderSorts,\n\t\t\t\tmoveCubeSortItem, setCubeSortOrder\n\t\t\t}\n\t\t});\n\t}\n\n\tsetData() {\n\t\tconst { cardDataOid, cube, viewType } = this.props;\n\t\tconst { availableItems, sortedItems } = orderManagerHelper.getAllItems(cube, viewType);\n\t\tconst method = SortActions.setData.apply(this, [cardDataOid, { availableItems, sortedItems }]);\n\n\t\tdispatch(method);\n\t}\n\n\trender() {\n\t\treturn <PopoverContent oid={this.props.cardDataOid} />;\n\t}\n}\n\nOLAPViewOrderManager.propTypes = {\n\tcardDataOid: PropTypes.string.isRequired,\n\tcube: PropTypes.object.isRequired,\n\tfetchData: PropTypes.func.isRequired,\n\tmoveCubeSortItem: PropTypes.func.isRequired,\n\treorderSorts: PropTypes.func.isRequired,\n\tsetCubeSortOrder: PropTypes.func.isRequired,\n\tviewType: PropTypes.string.isRequired\n};\n\nOLAPViewOrderManager.displayName = 'Analytics/components/OLAPOrderManager/OLAPViewOrderManager';", "import PropTypes from 'prop-types';\nimport React, { PureComponent } from 'react';\nimport { Provider, connect } from 'react-redux';\nimport Utils from 'Utils';\nimport reactReduxInstanceWrapper from 'reactor/src/helpers/reactReduxInstanceWrapper';\nimport SortActions from 'reactorCmps/src/analytics/actions/SortActions';\nimport PopoverContentPresentation from 'reactorCmps/src/analytics/components/OLAPOrderManager/Popover/PopoverContentPresentation';\nimport sort from 'reactorCmps/src/analytics/constants/sort';\nimport SortStore from 'reactorCmps/src/analytics/store/SortStore';\nimport DashboardDragNDropContext from 'reactorCmps/src/workspace/helpers/DashboardDragNDropContext';\nimport 'reactorCmps/tokens/general';\n\nconst mapStateToProps = (state, props) => {\n\tconst oid = props.oid.toString();\n\tconst data = state.analysisSort.getIn(['analysis', oid]);\n\tlet availableItems = [],\n\t\tsortedItems = [];\n\n\tif (data) {\n\t\tavailableItems = data.get(sort.AVAILABLE_ITEMS).toJS();\n\t\tsortedItems = data.get(sort.SORTED_ITEMS).toJS();\n\t}\n\n\treturn { oid, availableItems, sortedItems };\n};\n\nconst mapDispatchToProps = (dispatch, props) => {\n\treturn {\n\t\tsetItemAsc(item) {\n\t\t\tdispatch(SortActions.setItemAsc(props.oid, item.uid));\n\t\t},\n\t\tsetItemDesc(item) {\n\t\t\tdispatch(SortActions.setItemDesc(props.oid, item.uid));\n\t\t},\n\t\tremoveItem(item) {\n\t\t\tdispatch(SortActions.removeItem(props.oid, item));\n\t\t},\n\t\tonDrop(list, item, hoverItem) {\n\t\t\tlet method = 'addItem';\n\n\t\t\tif (list === sort.AVAILABLE_ITEMS) {\n\t\t\t\tdispatch(SortActions.removeItem(props.oid, item.item));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (list === item.type) {\n\t\t\t\tmethod = 'sortItem';\n\t\t\t}\n\n\t\t\tdispatch(SortActions[method](props.oid, item.item, hoverItem));\n\t\t}\n\t};\n};\n\nconst PopoverContentConnect = reactReduxInstanceWrapper(\n\tconnect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(PopoverContentPresentation)\n);\n\nclass PopoverContent extends PureComponent {\n\t/* istanbul ignore next */\n\tconstructor(props) {\n\t\tsuper(props);\n\n\t\tthis.popoverId = Utils.generateHash();\n\t}\n\n\trender() {\n\t\treturn (\n\t\t\t<Provider store={SortStore}>\n\t\t\t\t<PopoverContentConnect id={this.popoverId} ref={'analysisSort'} {...this.props} />\n\t\t\t</Provider>\n\t\t);\n\t}\n}\n\nPopoverContent.propTypes = {\n\toid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,\n\tdata: PropTypes.object\n};\n\nPopoverContent.displayName = 'Analytics/components/OLAPOrderManager/Popover/PopoverContent';\n\nexport default new DashboardDragNDropContext(PopoverContent);", "import PropTypes from 'prop-types';\r\nimport React, { PureComponent } from 'react';\r\nimport Image from 'reactor/src/Atomic/components/Atoms/Image/Image';\r\nimport tokenManagerHOC from 'reactor/src/Atomic/components/Helpers/Language/tokenManagerHOC';\r\nimport { colors, fontBold, fonts, size } from 'reactor/src/helpers/styleVariables';\r\nimport OLAPDataTypeIcon from 'reactorCmps/src/analytics/components/OLAPDataTypeIcon/OLAPDataTypeIcon';\r\nimport DropableFieldsList from 'reactorCmps/src/analytics/components/OLAPOrderManager/DropableFieldsList/DropableFieldsList';\r\nimport SortableListItem from 'reactorCmps/src/analytics/components/OLAPOrderManager/SortableListItem/SortableListItem';\r\nimport SortTinyDropdown from 'reactorCmps/src/analytics/components/OLAPOrderManager/SortTinyDropdown/SortTinyDropdown';\r\nimport styles from 'reactorCmps/src/analytics/components/OLAPOrderManager/Popover/Popover.mcss';\r\nimport sort from 'reactorCmps/src/analytics/constants/sort';\r\nimport SortUtils from 'reactorCmps/src/analytics/helpers/SortUtils';\r\nimport { getFormulaToken } from 'reactorCmps/src/chartengine2/constants/formulas';\r\n\r\nconst listsStyle = {\r\n\tdisplay: 'flex'\r\n};\r\n\r\nconst dividerStyle = {\r\n\tborderLeft: '1px solid ' + colors.borderGrey,\r\n\tmargin: '20px 8px 8px 8px',\r\n\twidth: 1\r\n};\r\n\r\nconst leftIconStyle = {\r\n\ttextAlign: 'center',\r\n\twidth: 24\r\n};\r\n\r\nconst leftIndexStyle = {\r\n\tcolor: colors.idGrey,\r\n\tfontWeight: fontBold,\r\n\ttextAlign: 'center',\r\n\twidth: size.img.small\r\n};\r\n\r\nconst rightContentStyle = {\r\n\tcolor: colors.netBlue,\r\n\tdisplay: 'flex',\r\n\tfontSize: fonts.textSize,\r\n\ttextAlign: 'right',\r\n\twidth: 70\r\n};\r\n\r\nconst dropdownStyle = {\r\n\tflex: '1 1 auto',\r\n\tmarginRight: 8\r\n};\r\n\r\nconst rightIconStyle = {\r\n\tcursor: 'pointer'\r\n};\r\n\r\nconst noContentStyle = {\r\n\talignItems: 'center',\r\n\tcolor: colors.doubleDevilGrey,\r\n\tdisplay: 'flex',\r\n\tfontSize: 12,\r\n\tpadding: 8,\r\n\theight: '100%',\r\n\twidth: '100%'\r\n};\r\n\r\nconst noContentTextStyle = {\r\n\tdisplay: 'block',\r\n\ttextAlign: 'center',\r\n\twidth: '100%'\r\n};\r\n\r\nclass PopoverContentPresentation extends PureComponent {\r\n\tgetLeftIcon(type) {\r\n\t\treturn (\r\n\t\t\t<div style={leftIconStyle}>\r\n\t\t\t\t<OLAPDataTypeIcon type={type} />\r\n\t\t\t</div>\r\n\t\t);\r\n\t}\r\n\r\n\tgetLeftIndex(index) {\r\n\t\treturn (\r\n\t\t\t<div style={leftIndexStyle}>\r\n\t\t\t\t{ index }\r\n\t\t\t</div>\r\n\t\t);\r\n\t}\r\n\r\n\tgetRightContent(item) {\r\n\t\tconst { oid, removeItem, setItemAsc, setItemDesc } = this.props;\r\n\r\n\t\treturn (\r\n\t\t\t<div style={rightContentStyle}>\r\n\t\t\t\t<div style={dropdownStyle}>\r\n\t\t\t\t\t<SortTinyDropdown\r\n\t\t\t\t\t\tanalysisOid={oid}\r\n\t\t\t\t\t\titemOid={item.uid}\r\n\t\t\t\t\t\tsetItemAsc={setItemAsc.bind(this, item)}\r\n\t\t\t\t\t\tsetItemDesc={setItemDesc.bind(this, item)}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className={styles.rctRemoveBtn}>\r\n\t\t\t\t\t<Image\r\n\t\t\t\t\t\tonClick={removeItem.bind(this, item)}\r\n\t\t\t\t\t\tstyle={rightIconStyle}\r\n\t\t\t\t\t\ticon={'seicon-cancel'}\r\n\t\t\t\t\t\twidth={'1em'}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t);\r\n\t}\r\n\r\n\tgetAvailableItems() {\r\n\t\tconst me = this;\r\n\t\t/* istanbul ignore next */\r\n\t\tconst { availableItems = [], oid, onDrop } = this.props;\r\n\t\tlet items = availableItems.slice();\r\n\r\n\t\t/* istanbul ignore if */\r\n\t\tif (!items.length)\r\n\t\t\treturn null;\r\n\r\n\t\titems.sort(SortUtils.sortItemByLabel);\r\n\r\n\t\treturn items.map(item => {\r\n\t\t\treturn (\r\n\t\t\t\t<SortableListItem key={item.uid}\r\n\t\t\t\t\tanalysisOid={oid}\r\n\t\t\t\t\tleftContent={me.getLeftIcon(item.datatype)}\r\n\t\t\t\t\tonDrop={onDrop}\r\n\t\t\t\t\ttitle={item.label}\r\n\t\t\t\t\ttitlePrefix={me.getTitlePrefix(item)}\r\n\t\t\t\t\ttype={sort.AVAILABLE_ITEMS}\r\n\t\t\t\t\tuid={item.uid}\r\n\t\t\t\t\tmoveOnDoubleClick\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t});\r\n\t}\r\n\r\n\tgetSortedItems() {\r\n\t\tconst me = this;\r\n\t\t/* istanbul ignore next */\r\n\t\tconst { oid, onDrop, sortedItems = [] } = me.props;\r\n\t\tlet items = sortedItems.slice();\r\n\r\n\t\treturn items.map((item, i) => {\r\n\t\t\treturn (\r\n\t\t\t\t<div className={styles.rctSortedItem} key={item.uid}>\r\n\t\t\t\t\t<SortableListItem\r\n\t\t\t\t\t\tanalysisOid={oid}\r\n\t\t\t\t\t\tleftContent={me.getLeftIndex(i + 1)}\r\n\t\t\t\t\t\tonDrop={onDrop}\r\n\t\t\t\t\t\trightContent={me.getRightContent(item)}\r\n\t\t\t\t\t\ttitle={item.label}\r\n\t\t\t\t\t\ttitlePrefix={me.getTitlePrefix(item)}\r\n\t\t\t\t\t\ttype={sort.SORTED_ITEMS}\r\n\t\t\t\t\t\tuid={item.uid}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</div>\r\n\t\t\t);\r\n\t\t});\r\n\t}\r\n\r\n\tgetTitlePrefix(item) {\r\n\t\tconst { getToken } = this.props;\r\n\r\n\t\t/* istanbul ignore if */\r\n\t\tif (item.aggregationFunction)\r\n\t\t\treturn item.aggregationFunction;\r\n\r\n\t\t/* istanbul ignore if */\r\n\t\tif (item.formula)\r\n\t\t\treturn getToken(getFormulaToken(item.formula));\r\n\r\n\t\treturn '';\r\n\t}\r\n\r\n\thasItems() {\r\n\t\tconst items = [...this.props.availableItems, ...this.props.sortedItems];\r\n\r\n\t\treturn items && items.length > 0;\r\n\t}\r\n\r\n\tgetLists() {\r\n\t\tconst dividerHeightStyle = Object.assign({ height: this.props.height }, dividerStyle);\r\n\r\n\t\treturn (\r\n\t\t\t<div style={listsStyle}>\r\n\t\t\t\t{ this.getList(105474, sort.AVAILABLE_ITEMS, this.getAvailableItems()) }\r\n\r\n\t\t\t\t<div style={dividerHeightStyle}></div>\r\n\r\n\t\t\t\t{ this.getList(301200, sort.SORTED_ITEMS, this.getSortedItems(), this.getNoContent(213220)) }\r\n\t\t\t</div>\r\n\t\t);\r\n\t}\r\n\r\n\tgetList(term, list, items, placeholder = null) {\r\n\t\tconst { getToken, height, onDrop } = this.props;\r\n\r\n\t\treturn (\r\n\t\t\t<DropableFieldsList\r\n\t\t\t\theight={height}\r\n\t\t\t\tlist={list}\r\n\t\t\t\tonDrop={onDrop}\r\n\t\t\t\tplaceholder={placeholder}\r\n\t\t\t\ttitle={getToken(String(term))}\r\n\t\t\t\twidth={250}\r\n\t\t\t>\r\n\t\t\t\t{ items }\r\n\t\t\t</DropableFieldsList>\r\n\t\t);\r\n\t}\r\n\r\n\tgetNoContent(term = 209068) {\r\n\t\treturn (\r\n\t\t\t<div style={noContentStyle}>\r\n\t\t\t\t<span style={noContentTextStyle}>{ this.props.getToken(String(term)) }</span>\r\n\t\t\t</div>\r\n\t\t);\r\n\t}\r\n\r\n\trender() {\r\n\t\treturn this.hasItems() ? this.getLists() : this.getNoContent();\r\n\t}\r\n}\r\n\r\nPopoverContentPresentation.propTypes = {\r\n\theight: PropTypes.number,\r\n\toid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,\r\n\tavailableItems: PropTypes.array.isRequired,\r\n\tsortedItems: PropTypes.array.isRequired,\r\n\tsetItemAsc: PropTypes.func.isRequired,\r\n\tsetItemDesc: PropTypes.func.isRequired,\r\n\tremoveItem: PropTypes.func.isRequired,\r\n\tonDrop: PropTypes.func.isRequired\r\n};\r\n\r\nPopoverContentPresentation.defaultProps = {\r\n\theight: 200\r\n};\r\n\r\nPopoverContentPresentation.displayName = 'Analytics/components/OLAPOrderManager/Popover/PopoverContentPresentation';\r\n\r\nexport default tokenManagerHOC(PopoverContentPresentation);", "import { omit } from 'lodash';\nimport PropTypes from 'prop-types';\nimport React, { Component } from 'react';\nimport { Provider, connect } from 'react-redux';\nimport reactReduxInstanceWrapper from 'reactor/src/helpers/reactReduxInstanceWrapper';\nimport tokenManagerHOC from 'reactor/src/Atomic/components/Helpers/Language/tokenManagerHOC';\nimport SortTinyDropdownPresentation from 'reactorCmps/src/analytics/components/OLAPOrderManager/SortTinyDropdown/SortTinyDropdownPresentation';\nimport sort from 'reactorCmps/src/analytics/constants/sort';\nimport SortStore from 'reactorCmps/src/analytics/store/SortStore';\n\nconst mapStateToProps = (state, props) => {\n\tlet analysisOid = props.analysisOid.toString(),\n\t\titemOid = props.itemOid.toString(),\n\t\titem = state.analysisSort\n\t\t\t.getIn(['analysis', analysisOid, sort.SORTED_ITEMS])\n\t\t\t.find(i => {\n\t\t\t\treturn i.get('uid') === itemOid;\n\t\t\t}),\n\t\tvalue = props.getToken('306533'),\n\t\tdirection;\n\n\tif (item) {\n\t\tdirection = item.get('direction');\n\t}\n\n\tif (direction === sort.DIR_ASC) {\n\t\tvalue = props.getToken('306532');\n\t}\n\n\treturn Object.assign(omit(props, 'analysisOid', 'itemOid'), { value });\n};\n\nconst SortTinyDropdownConnect = reactReduxInstanceWrapper(\n\tconnect(mapStateToProps, () => ({}), null, { forwardRef: true })(SortTinyDropdownPresentation)\n);\n\nclass SortTinyDropdown extends Component {\n\trender() {\n\t\tlet me = this;\n\n\t\treturn (\n\t\t\t<Provider store={SortStore}>\n\t\t\t\t<SortTinyDropdownConnect ref={'analysisSort'} {...me.props} />\n\t\t\t</Provider>\n\t\t);\n\t}\n}\n\nSortTinyDropdown.propTypes = {\n\tanalysisOid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,\n\titemOid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired\n};\n\nSortTinyDropdown.displayName = 'Analytics/components/OLAPOrderManager/SortTinyDropdown/SortTinyDropdown';\n\nexport default tokenManagerHOC(SortTinyDropdown);", "import PropTypes from 'prop-types';\nimport React, { PureComponent } from 'react';\nimport MenuItem from 'reactor/src/Atomic/components/Atoms/MenuItem/MenuItem';\nimport TinyDropdown from 'reactorCmps/src/analytics/components/TinyDropdown';\n\nexport default class SortTinyDropdownPresentation extends PureComponent {\n\trender() {\n\t\tconst { getToken, setItemAsc, setItemDesc, value } = this.props;\n\n\t\treturn (\n\t\t\t<TinyDropdown value={value}>\n\t\t\t\t<MenuItem onClick={setItemAsc}>{ getToken('201301') }</MenuItem>\n\t\t\t\t<MenuItem onClick={setItemDesc}>{ getToken('201302') }</MenuItem>\n\t\t\t</TinyDropdown>\n\t\t);\n\t}\n}\n\nSortTinyDropdownPresentation.propTypes = {\n\tvalue: PropTypes.string.isRequired,\n\tsetItemAsc: PropTypes.func.isRequired,\n\tsetItemDesc: PropTypes.func.isRequired\n};\n\nSortTinyDropdownPresentation.displayName = 'Analytics/components/OLAPOrderManager/SortTinyDropdown/SortTinyDropdownPresentation';", "import React, { Component } from 'react';\nimport { Provider, connect } from 'react-redux';\nimport reactReduxInstanceWrapper from 'reactor/src/helpers/reactReduxInstanceWrapper';\nimport SortableListItemPresentation from 'reactorCmps/src/analytics/components/OLAPOrderManager/SortableListItem/SortableListItemPresentation';\nimport SortStore from 'reactorCmps/src/analytics/store/SortStore';\n\nconst mapStateToProps = (state, props) => {\n\tlet analysisOid = props.analysisOid.toString(),\n\t\tuid = props.uid,\n\t\ttype = props.type;\n\n\tlet item = state.analysisSort\n\t\t\t.getIn(['analysis', analysisOid, type])\n\t\t\t.find(i => {\n\t\t\t\treturn i.get('uid') === uid;\n\t\t\t}),\n\t\titemObj = {\n\t\t\titem: null\n\t\t};\n\n\tif (item) {\n\t\titemObj.item = item.toJS();\n\t}\n\n\treturn itemObj;\n};\n\nconst SortableListItemConnect = reactReduxInstanceWrapper(\n\tconnect(mapStateToProps, () => ({}), null, { forwardRef: true })(SortableListItemPresentation)\n);\n\nclass SortableListItem extends Component {\n\trender() {\n\t\tlet me = this;\n\n\t\treturn (\n\t\t\t<Provider store={SortStore}>\n\t\t\t\t<SortableListItemConnect ref={'analysisSort'} {...me.props} />\n\t\t\t</Provider>\n\t\t);\n\t}\n}\n\nexport default SortableListItem;\n\nSortableListItem.displayName = 'Analytics/components/OLAPOrderManager/SortableListItem/SortableListItem';", "import PropTypes from 'prop-types';\nimport React, { Component } from 'react';\nimport DraggableListItem from 'reactorCmps/src/analytics/components/DraggableListItem/DraggableListItem';\nimport sort from 'reactorCmps/src/analytics/constants/sort';\nimport { Consumer } from 'reactorCmps/src/analytics/contexts/PopoverContextProvider';\n\nconst containerStyle = {\n\theight: 33,\n\tpadding: '0 4px'\n};\n\nclass SortableListItemPresentation extends Component {\n\tconstructor(props) {\n\t\tsuper(props);\n\n\t\tthis.handleDblClick = this.handleDblClick.bind(this);\n\t\tthis.handleBeginDrag = this.handleBeginDrag.bind(this);\n\t}\n\n\thandleDblClick() {\n\t\tlet props = this.props,\n\t\t\tlist = sort.SORTED_ITEMS;\n\n\t\tif (!props.moveOnDoubleClick)\n\t\t\treturn;\n\n\t\tprops.onDrop(list, props, null);\n\t}\n\n\t/* istanbul ignore next */\n\thandleBeginDrag(popoverId) {\n\t\t/* istanbul ignore next */\n\t\t$('#' + popoverId).trigger('click');\n\t}\n\n\trender() {\n\t\tlet me = this;\n\n\t\treturn (\n\t\t\t<div onDoubleClick={me.handleDblClick}>\n\t\t\t\t<Consumer>\n\t\t\t\t\t{\n\t\t\t\t\t\tcontext => (\n\t\t\t\t\t\t\t<DraggableListItem\n\t\t\t\t\t\t\t\t{...me.props}\n\t\t\t\t\t\t\t\tonBeginDrag={me.handleBeginDrag.bind(me, context.popoverId)}\n\t\t\t\t\t\t\t\tstyle={containerStyle}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t)\n\t\t\t\t\t}\n\t\t\t\t</Consumer>\n\t\t\t</div>\n\t\t);\n\t}\n}\n\nSortableListItemPresentation.displayName = 'Analytics/components/OLAPOrderManager/SortableListItem/SortableListItemPresentation';\n\nSortableListItemPresentation.propTypes = {\n\tanalysisOid: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,\n\tmoveOnDoubleClick: PropTypes.bool\n};\n\nSortableListItemPresentation.defaultProps = {\n\tmoveOnDoubleClick: false\n};\n\nexport default SortableListItemPresentation;", "import { omit } from 'lodash';\nimport { dispatch } from 'reactor/src/store/store';\nimport SortActions from 'reactorCmps/src/analytics/actions/SortActions';\nimport SortRedux from 'reactorCmps/src/analytics/components/ScopeRedux/SortRedux';\n\nexport default {\n\tscope: {},\n\n\tgetActionsByType: function(type) {\n\t\tconst actions = {\n\t\t\tsort: SortActions\n\t\t};\n\n\t\treturn actions[type];\n\t},\n\n\taction: function(type, action, params) {\n\t\tlet actions = this.getActionsByType(type),\n\t\t\tmethod = actions[action].apply(this, params);\n\n\t\tdispatch(method);\n\t},\n\n\tsortReduxAction: function(type, params) {\n\t\tSortRedux[type].apply(this, params);\n\t},\n\n\tsendGetData: function(scopeOid) {\n\t\tconst scope = this.getScope(scopeOid);\n\n\t\tif (scope.viewScope)\n\t\t\tscope.viewScope.getData(false, false, true);\n\t\telse\n\t\t\tscope.getData(false, false, true);\n\t},\n\n\tsetScope: function(scope) {\n\t\tif (scope.useOlapEditor) {\n\t\t\tthis.scope = scope;\n\t\t\treturn;\n\t\t}\n\n\t\tthis.scope[scope.oid] = omit(scope, 'oid');\n\t},\n\n\tgetScope: function(id) {\n\t\t/* istanbul ignore else */\n\t\tif (!id || !this.scope[id])\n\t\t\treturn this.scope;\n\n\t\t/* istanbul ignore next */\n\t\treturn this.scope[id];\n\t}\n};", "export default {\n\tsetData: function(data) {\n\t\tif (this.scope.cube)\n\t\t\tthis.scope.cube.setCubeSort(data);\n\t},\n\n\tsetCubeSortOrder: function(index, direction, scopeOid) {\n\t\tconst scope = this.getScope(scopeOid);\n\n\t\tscope.cube.setCubeSortOrder(index, direction);\n\n\t\tthis.sendGetData(scopeOid);\n\t},\n\n\tmoveCubeSortItem: function(item, indexToRemove, indexToAdd, removeSort, scopeOid) {\n\t\tconst scope = this.getScope(scopeOid);\n\n\t\tscope.cube.moveCubeSortItem(item, indexToRemove, indexToAdd, removeSort);\n\n\t\tthis.sendGetData(scopeOid);\n\t},\n\n\treorderCubeSortItem: function(oldIndex, newIndex, item, scopeOid) {\n\t\tconst scope = this.getScope(scopeOid);\n\t\tlet cube = scope.cube;\n\n\t\t/* istanbul ignore if */\n\t\tif (cube.reorderCubeSortItem) {\n\t\t\tcube.reorderCubeSortItem(item, newIndex);\n\t\t} else {\n\t\t\tcube.removeCubeSort(oldIndex);\n\t\t\tcube.addCubeSort(item, newIndex);\n\t\t}\n\n\t\tthis.sendGetData(scopeOid);\n\t}\n};", "import PropTypes from 'prop-types';\nimport React, { Children, cloneElement, createRef, Fragment, PureComponent } from 'react';\nimport Image from 'reactor/src/Atomic/components/Atoms/Image/Image';\nimport MenuItem from 'reactor/src/Atomic/components/Atoms/MenuItem/MenuItem';\nimport Overlay from 'reactor/src/Atomic/components/Orgs/Overlay';\nimport Popover from 'reactor/src/Atomic/components/Orgs/PopOver/PopOver';\nimport { colors } from 'reactor/src/helpers/styleVariables';\nimport validateProp from 'reactor/src/helpers/validateProp';\n\nconst containerStyle = {\n\tcursor: 'pointer',\n\tdisplay: 'flex'\n};\n\nconst valueStyle = {\n\tflex: '1 1 auto',\n\ttextAlign: 'center'\n};\n\nconst POPOVER_SELECTOR = 'div[id*=\"olapview-card-sort\"] .popover-content';\n\nexport default class TinyDropdown extends PureComponent {\n\t/* istanbul ignore next */\n\tconstructor(props) {\n\t\tsuper(props);\n\n\t\tthis.state = {\n\t\t\tshowOverlay: false\n\t\t};\n\n\t\tthis.getPopoverReference = this.getPopoverReference.bind(this);\n\t\tthis.hideOverlay = this.hideOverlay.bind(this);\n\t\tthis.showOverlay = this.showOverlay.bind(this);\n\t\tthis.valueEl = createRef();\n\t}\n\n\tcomponentDidMount() {\n\t\tthis.setMouseDownListener();\n\t}\n\n\tcomponentDidUpdate() {\n\t\tthis.setMouseDownListener();\n\t}\n\n\tcomponentWillUnmount() {\n\t\tthis.removeMouseDownListener();\n\t}\n\n\tgetOptions() {\n\t\tconst me = this;\n\n\t\treturn Children.map(me.props.children, option => {\n\t\t\treturn cloneElement(option, {\n\t\t\t\tonClick() {\n\t\t\t\t\toption.props.onClick();\n\t\t\t\t\tme.hideOverlay();\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\t}\n\n\tgetOverlay() {\n\t\treturn (\n\t\t\t<Popover\n\t\t\t\tautoTranslateTo={this.getPopoverReference}\n\t\t\t\tcolorBorder={colors.zebraGrey}\n\t\t\t\tonMouseLeave={this.hideOverlay}\n\t\t\t\tarrow={false}\n\t\t\t\tnoStyling\n\t\t\t>\n\t\t\t\t{ this.getOptions() }\n\t\t\t</Popover>\n\t\t);\n\t}\n\n\tgetPopoverReference() {\n\t\treturn this.valueEl && this.valueEl.current;\n\t}\n\n\thideOverlay() {\n\t\tthis.setState({ showOverlay: false });\n\t}\n\n\tshowOverlay() {\n\t\tthis.setState({ showOverlay: true });\n\t}\n\n\tsetMouseDownListener() {\n\t\tthis.removeMouseDownListener();\n\n\t\t$(POPOVER_SELECTOR).on('mousedown', this.hideOverlay);\n\t}\n\n\tremoveMouseDownListener() {\n\t\t$(POPOVER_SELECTOR).off('mousedown', this.hideOverlay);\n\t}\n\n\trender() {\n\t\treturn (\n\t\t\t<Fragment>\n\t\t\t\t<div style={containerStyle} onClick={this.showOverlay}>\n\t\t\t\t\t<div style={valueStyle} ref={this.valueEl}>\n\t\t\t\t\t\t{ this.props.value }\n\t\t\t\t\t</div>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<Image icon={'seicon-down-micro'} width={'1em'} />\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<Overlay\n\t\t\t\t\tforcePlacement={'bottom'}\n\t\t\t\t\ttrigger={'click'}\n\t\t\t\t\tshow={this.state.showOverlay}\n\t\t\t\t\tonHide={this.hideOverlay}\n\t\t\t\t\ttarget={this.getPopoverReference}\n\t\t\t\t\trootClose\n\t\t\t\t>\n\t\t\t\t\t{ this.getOverlay() }\n\t\t\t\t</Overlay>\n\t\t\t</Fragment>\n\t\t);\n\t}\n}\n\nTinyDropdown.propTypes = {\n\tvalue: PropTypes.string.isRequired,\n\tchildren: PropTypes.arrayOf(validateProp.bind(null, [MenuItem.displayName], 'children'))\n};\n\nTinyDropdown.displayName = 'Analytics/components/TinyDropdown';", "export const BIGTEXT = 'BIGTEXT';\nexport const BOOLEAN = 'BOOLEAN';\nexport const CURRENCY = 'CURRENCY';\nexport const DECIMAL = 'DECIMAL';\nexport const DATE = 'DATE';\nexport const DATETIME = 'DATETIME';\nexport const INTEGER = 'INTEGER';\nexport const LONG = 'LONG';\nexport const TEXT = 'TEXT';\nexport const TIME = 'TIME';\n\nexport default {\n\tBIGTEXT,\n\tBOOLEAN,\n\tCURRENCY,\n\tDECIMAL,\n\tDATE,\n\tDATETIME,\n\tINTEGER,\n\tLONG,\n\tTEXT,\n\tTIME\n};", "export default {\n\tORDER_ITEM: 'ORDER_ITEM'\n};", "export default {\n\tDIR_ASC: 'ASC',\n\tDIR_DESC: 'DESC',\n\tAVAILABLE_ITEMS: 'availableItems',\n\tSORTED_ITEMS: 'sortedItems'\n};", "import React, { createContext, memo } from 'react';\n\nexport const PopoverContext = createContext({});\n\nexport const { Consumer } = PopoverContext;\n\nexport const PopoverContextProvider = props => {\n\tconst contextProps = {\n\t\tpopoverId: props.id\n\t};\n\n\treturn (\n\t\t<PopoverContext.Provider value={contextProps}>\n\t\t\t{ props.children }\n\t\t</PopoverContext.Provider>\n\t);\n};\n\nexport default memo(PopoverContextProvider);", "import { chartTypesThatReferToAnotherChartType } from 'reactorCmps/src/chartengine2/helpers/ChartLayoutHelper';\nimport { getCorrectViewType } from 'reactorCmps/src/chartengine2/helpers/ChartUtils';\n\nexport default {\n\tTABLE: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('109970'),\n\t\tsecubemeasures: SE.t('213717'),\n\t\tdrillEnabled: true,\n\t\tshouldShowSortIcon: true,\n\t\tfiltersEnabled: true\n\t},\n\tPIVOT_TABLE: {\n\t\tallowSort: true,\n\t\tsecubecolumns: SE.t('109970'),\n\t\tsecubemeasures: SE.t('213717'),\n\t\tsecuberows: SE.t('214874'),\n\t\tdrillEnabled: true,\n\t\tmaxElementsAllowed: 150000,\n\t\tshouldShowSortIcon: true,\n\t\tfiltersEnabled: false,\n\t\tmultiSort: false,\n\t\thideDimensions: false\n\t},\n\tCOLUMN: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('103734'),\n\t\tsecubemeasures: SE.t('213717'),\n\t\tallowSeriesAggregator: true,\n\t\tdrillEnabled: true,\n\t\tshouldShowSortIcon: true,\n\t\tfiltersEnabled: true,\n\t\thideDimensions: true\n\t},\n\tFUNNEL: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('109971'),\n\t\tsecubemeasurewithlimit: { token: SE.t('213717') },\n\t\tdrillEnabled: true,\n\t\tshouldShowSortIcon: true,\n\t\tfiltersEnabled: true,\n\t\thideDimensions: true\n\t},\n\tBUBBLE: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('109971'),\n\t\tsecubemeasuresfromxaxis: SE.t('103734'),\n\t\tsecubemeasuresfromyaxis: SE.t('103736'),\n\t\tsecubemeasuresfromzaxis: SE.t('316612'),\n\t\tallowSeriesAggregator: true,\n\t\tdrillEnabled: true,\n\t\tfiltersEnabled: true\n\t},\n\tSCATTER: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('109971'),\n\t\tsecubemeasuresfromxaxis: SE.t('103734'),\n\t\tsecubemeasuresfromyaxis: SE.t('103736'),\n\t\tallowSeriesAggregator: true,\n\t\tdrillEnabled: true,\n\t\tfiltersEnabled: true\n\t},\n\tBAR: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('103736'),\n\t\tsecubemeasures: SE.t('213717'),\n\t\tallowSeriesAggregator: true,\n\t\tdrillEnabled: true,\n\t\tshouldShowSortIcon: true,\n\t\tfiltersEnabled: true,\n\t\thideDimensions: true\n\t},\n\tLINE: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('103734'),\n\t\tsecubemeasures: SE.t('213717'),\n\t\tallowSeriesAggregator: true,\n\t\tdrillEnabled: true,\n\t\tshouldShowSortIcon: true,\n\t\tfiltersEnabled: true,\n\t\thideDimensions: true\n\t},\n\tPIE: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('214950'),\n\t\tsecubemeasures: SE.t('213717'),\n\t\tdrillEnabled: true,\n\t\tshouldShowSortIcon: true,\n\t\tfiltersEnabled: true,\n\t\thideDimensions: true\n\t},\n\tAREA: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('103734'),\n\t\tsecubemeasures: SE.t('213717'),\n\t\tallowSeriesAggregator: true,\n\t\tdrillEnabled: true,\n\t\tshouldShowSortIcon: true,\n\t\tfiltersEnabled: true,\n\t\thideDimensions: true\n\t},\n\tRADAR: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('214821'),\n\t\tsecubemeasures: SE.t('213717'),\n\t\tallowSeriesAggregator: true,\n\t\tdrillEnabled: true,\n\t\tshouldShowSortIcon: true,\n\t\tfiltersEnabled: true,\n\t\thideDimensions: true\n\t},\n\tPARETO: {\n\t\tallowSort: true,\n\t\tsecubedimensions: SE.t('103736'),\n\t\tsecubemeasures: SE.t('213717'),\n\t\tdrillEnabled: true,\n\t\tshouldShowSortIcon: true,\n\t\tfiltersEnabled: true,\n\t\thideDimensions: true\n\t},\n\tGAUGE: {\n\t\tsecubemeasures: SE.t('213717'),\n\t\tshouldShowSortIcon: false,\n\t\tfiltersEnabled: true,\n\t\thideDimensions: false\n\t},\n\tLINEAR_GAUGE: {\n\t\tsecubemeasures: SE.t('213717'),\n\t\tshouldShowSortIcon: false,\n\t\tfiltersEnabled: true,\n\t\thideDimensions: false\n\t},\n\tKPI: {\n\t\tsecubemeasures: SE.t('213717'),\n\t\tshouldShowSortIcon: false,\n\t\tfiltersEnabled: false,\n\t\thideDimensions: false\n\t},\n\tMOCK: {},\n\tget: function(viewType) {\n\t\tconst viewTypeUpper = this[viewType.toUpperCase()];\n\t\tconst correctViewTypeUpper = this[getCorrectViewType(viewType).toUpperCase()];\n\t\tconst viewTypeFromDerivedChartUpper = chartTypesThatReferToAnotherChartType[viewType];\n\n\t\treturn viewTypeUpper || correctViewTypeUpper || this[viewTypeFromDerivedChartUpper?.toUpperCase()];\n\t}\n};", "/* istanbul ignore next */\nexport default {\n\tshouldShowSort(cube) {\n\t\tif (!cube)\n\t\t\treturn true;\n\n\t\tconst olapConfig = cube.entity;\n\n\t\treturn cube.shouldShowSortIcon(olapConfig);\n\t},\n\n\tsortItemByLabel(as, bs) {\n\t\tvar a = typeof as.label === 'string' ? as.label : as;\n\t\tvar b = typeof bs.label === 'string' ? bs.label : bs;\n\n\t\treturn a.localeCompare(b);\n\t}\n};", "import cubeConfigurations from 'reactorCmps/src/analytics/helpers/Cube/cubeConfigurations';\r\n\r\nconst getData = data => {\r\n\treturn {\r\n\t\tdatatype: data.datatype,\r\n\t\tvalue: data.id,\r\n\t\tlabel: data.userLabel,\r\n\t\tuid: data.uid,\r\n\t\taggregationFunction: data.aggregationFunction || '',\r\n\t\tformula: data.formula || '',\r\n\t\toid: data.oid,\r\n\t\ttype: data.type\r\n\t};\r\n};\r\n\r\nconst getCubeSortData = (sortedItemsUid, data) => {\r\n\tsortedItemsUid.push(data.uid);\r\n\r\n\treturn {\r\n\t\tvalue: data.id,\r\n\t\tlabel: data.userLabel || data.id,\r\n\t\tdatatype: data.datatype,\r\n\t\tdirection: data.fgAsc ? 'ASC' : 'DESC',\r\n\t\tuid: data.uid,\r\n\t\taggregationFunction: data.aggregationFunction || '',\r\n\t\tformula: data.formula || '',\r\n\t\toid: data.oid,\r\n\t\ttype: data.type\r\n\t};\r\n};\r\n\r\nconst getItemByType = (type, sortedItemsUid, cube) => {\r\n\tconst cubeType = cube[type];\r\n\r\n\t/* istanbul ignore if */\r\n\tif (!cubeType)\r\n\t\treturn [];\r\n\r\n\treturn cubeType\r\n\t\t.toArray()\r\n\t\t.filter(data => !sortedItemsUid.includes(data.uid))\r\n\t\t.map(getData);\r\n};\r\n\r\nconst getItems = (cube, sortedItemsUid, configurations) => {\r\n\tlet items = [];\r\n\r\n\tif (cube.hasDimension())\r\n\t\titems = items.concat(getItemByType('dimensions', sortedItemsUid, cube));\r\n\r\n\tif (cube.isSeriesAggregator() && !sortedItemsUid.includes(cube.seriesAggregatorDimension.uid))\r\n\t\titems = items.concat(getData(cube.seriesAggregatorDimension));\r\n\r\n\tif (cube.hasMeasure())\r\n\t\titems = items.concat(getItemByType('measures', sortedItemsUid, cube));\r\n\r\n\tif (cube.hasRow() && configurations.secuberows)\r\n\t\titems = items.concat(getItemByType('rows', sortedItemsUid, cube));\r\n\r\n\treturn items;\r\n};\r\n\r\nconst getAvailableItems = (cube, sortedItemsUid, configurations) => {\r\n\tlet items = getItems(cube, sortedItemsUid, configurations);\r\n\r\n\treturn items.filter((item, pos) => items.indexOf(item) === pos);\r\n};\r\n\r\nconst getSorts = (cube, sortedItemsUid, configurations) => {\r\n\tlet sorts = cube.sorts && cube.sorts.toArray();\r\n\tlet itemsUid;\r\n\r\n\t/* istanbul ignore if */\r\n\tif (!sorts)\r\n\t\treturn [];\r\n\r\n\tif (!cube.hasRow()) {\r\n\t\titemsUid = getItems(cube, sortedItemsUid, configurations).map(item => item.uid);\r\n\r\n\t\tsorts = sorts.filter(item => itemsUid.includes(item.uid));\r\n\t}\r\n\r\n\t/* istanbul ignore if */\r\n\tif (configurations.multiSort === false && sorts[0])\r\n\t\treturn [getCubeSortData(sortedItemsUid, sorts[0])];\r\n\r\n\treturn sorts.map(getCubeSortData.bind(this, sortedItemsUid));\r\n};\r\n\r\nconst getAllItems = (cube, viewType) => {\r\n\tlet sortedItemsUid = [];\r\n\tconst configurations = cubeConfigurations.get(viewType);\r\n\tconst sortedItems = getSorts(cube, sortedItemsUid, configurations);\r\n\tconst availableItems = getAvailableItems(cube, sortedItemsUid, configurations);\r\n\r\n\treturn { availableItems, sortedItems };\r\n};\r\n\r\nexport default { getAllItems };", "import { fromJS } from 'immutable';\nimport sortConstants from 'reactorCmps/src/analytics/actions/sortConstants';\nimport sort from 'reactorCmps/src/analytics/constants/sort';\nimport ScopeRedux from 'reactorCmps/src/analytics/components/ScopeRedux/ScopeRedux';\n\nconst initialState = fromJS({\n\tanalysis: {}\n});\n\nconst findItem = (oid, item) => {\n\treturn item.get('uid') === oid;\n};\n\nconst getFixedItem = item => Object.assign(item, { direction: item.direction || sort.DIR_ASC });\n\nconst setData = (state, action) => {\n\tconst oid = action.oid.toString();\n\n\tScopeRedux.sortReduxAction('setData', [action.data.sortedItems]);\n\n\treturn state.setIn(['analysis', oid], fromJS(action.data));\n};\n\nconst setItemDirection = (state, action) => {\n\tconst oid = action.oid.toString(),\n\t\titemOid = action.itemOid.toString(),\n\t\tpath = ['analysis', oid, sort.SORTED_ITEMS],\n\t\tindex = state.getIn(path).findIndex(findItem.bind(this, itemOid)),\n\t\tdir = action.type === sortConstants.SET_ITEM_ASC ? sort.DIR_ASC : sort.DIR_DESC;\n\n\tScopeRedux.sortReduxAction('setCubeSortOrder', [index, dir, oid]);\n\n\treturn state.setIn(path.concat([index, 'direction']), dir);\n};\n\nconst removeItemFromList = (state, oid, list, itemOid) => {\n\tconst path = ['analysis', oid, list],\n\t\tindex = state.getIn(path).findIndex(findItem.bind(this, itemOid));\n\n\treturn state.removeIn(path.concat(index));\n};\n\nconst insertItemInList = (state, oid, list, index, item) => {\n\tconst path = ['analysis', oid, list],\n\t\tfixedItem = getFixedItem(item),\n\t\tdata = state.getIn(path).insert(index, fromJS(fixedItem));\n\n\treturn state.setIn(path, data);\n};\n\nconst moveItem = (state, action, listToAdd, originalListToRemove) => {\n\tconst { aboveItem, item, oid: actionOid } = action;\n\tconst oid = actionOid.toString();\n\tconst fixedItem = getFixedItem(item);\n\tconst itemOid = fixedItem.uid.toString();\n\tconst stateData = state.getIn(['analysis', oid, listToAdd]);\n\n\tlet indexToAdd = stateData.size,\n\t\tlistToRemove = originalListToRemove;\n\n\tif (state.getIn(['analysis', oid, listToAdd]).findIndex(findItem.bind(this, itemOid)) >= 0) {\n\t\tlistToRemove = listToAdd;\n\t}\n\n\tif (aboveItem) {\n\t\tindexToAdd = stateData.findIndex(findItem.bind(this, aboveItem.uid.toString()));\n\t}\n\n\t/* istanbul ignore else */\n\tif (!(listToAdd === sort.AVAILABLE_ITEMS && listToAdd === listToRemove)) {\n\t\tScopeRedux.sortReduxAction('moveCubeSortItem', [\n\t\t\tfixedItem,\n\t\t\tstate.getIn(['analysis', oid, listToRemove]).findIndex(findItem.bind(this, itemOid)),\n\t\t\tindexToAdd,\n\t\t\tlistToRemove === sort.SORTED_ITEMS,\n\t\t\toid\n\t\t]);\n\t}\n\n\treturn insertItemInList(\n\t\tremoveItemFromList(state, oid, listToRemove, itemOid),\n\t\toid, listToAdd, indexToAdd, fixedItem\n\t);\n};\n\nconst sortItem = (state, action) => {\n\tconst oid = action.oid.toString(),\n\t\titemOid = action.item.uid.toString(),\n\t\thoverOid = action.aboveItem ? action.aboveItem.uid.toString() : null,\n\t\tpath = state.getIn(['analysis', oid, sort.SORTED_ITEMS]),\n\t\titemIndex = path.findIndex(findItem.bind(this, itemOid));\n\n\tlet index = path.size;\n\n\tif (hoverOid)\n\t\tindex = path.findIndex(findItem.bind(this, hoverOid));\n\n\tif (index - itemIndex === 1) {\n\t\treturn state;\n\t}\n\n\tif (index > itemIndex) {\n\t\tindex = index - 1;\n\t}\n\n\tScopeRedux.sortReduxAction('reorderCubeSortItem', [\n\t\titemIndex,\n\t\tindex,\n\t\taction.item,\n\t\toid\n\t]);\n\n\treturn insertItemInList(\n\t\tremoveItemFromList(state, oid, sort.SORTED_ITEMS, itemOid),\n\t\toid, sort.SORTED_ITEMS, index, action.item\n\t);\n};\n\nexport default function(_state, action) {\n\tconst state = _state || initialState;\n\n\tswitch (action.type) {\n\t\tcase sortConstants.SET_DATA:\n\t\t\treturn setData(state, action);\n\n\t\tcase sortConstants.ADD_ITEM:\n\t\t\treturn moveItem(state, action, sort.SORTED_ITEMS, sort.AVAILABLE_ITEMS);\n\n\t\tcase sortConstants.REMOVE_ITEM:\n\t\t\treturn moveItem(state, action, sort.AVAILABLE_ITEMS, sort.SORTED_ITEMS);\n\n\t\tcase sortConstants.SORT_ITEM:\n\t\t\treturn sortItem(state, action);\n\n\t\tcase sortConstants.SET_ITEM_ASC:\n\t\tcase sortConstants.SET_ITEM_DESC:\n\t\t\treturn setItemDirection(state, action);\n\n\t\tdefault:\n\t\t\treturn state;\n\t}\n}", "import Store from 'reactor/src/store/store';\nimport SortReducer from 'reactorCmps/src/analytics/reducers/SortReducer';\n\nStore.injectAsyncReducer('analysisSort', SortReducer);\n\nexport default Store;", "export const BIGTEXT = 'BIGTEXT';\n\nexport const BOOLEAN = 'BOOLEAN';\n\nexport const CURRENCY = 'CURRENCY';\n\nexport const DECIMAL = 'DECIMAL';\n\nexport const DATE = 'DATE';\n\nexport const DATETIME = 'DATETIME';\n\nexport const INTEGER = 'INTEGER';\n\nexport const LONG = 'LONG';\n\nexport const TEXT = 'TEXT';\n\nexport const TIME = 'TIME';\n\nexport default { BIGTEXT, BOOLEAN, CURRENCY, DECIMAL, DATE, DATETIME, INTEGER, LONG, TEXT, TIME };", "export const YEAR = 'YEAR';\n\nexport const QUARTER = 'QUARTER';\n\nexport const MONTH = 'MONTH';\n\nexport const MONTH_YEAR = 'MONTH_YEAR';\n\nexport const WEEK = 'WEEK';\n\nexport const WEEK_MONTH = 'WEEK_MONTH';\n\nexport const DAY = 'DAY';\n\nexport const HOUR = 'HOUR';\n\nexport const MINUTE = 'MINUTE';\n\nexport const SECOND = 'SECOND';\n\nexport const STRING = 'STRING';\n\nexport const EMPTY_FORMULA = 'EMPTY_FORMULA';\n\nexport const formulas = [{\n\tvalue: YEAR,\n\tlabel: '100437'\n}, {\n\tvalue: QUARTER,\n\tlabel: '101727'\n}, {\n\tvalue: MONTH,\n\tlabel: '100436'\n}, {\n\tvalue: MONTH_YEAR,\n\tlabel: '107573'\n}, {\n\tvalue: WEEK,\n\tlabel: '100435'\n}, {\n\tvalue: WEEK_MONTH,\n\tlabel: '304499'\n}, {\n\tvalue: DAY,\n\tlabel: '100434'\n}, {\n\tvalue: HOUR,\n\tlabel: '100210'\n}, {\n\tvalue: MINUTE,\n\tlabel: '211845'\n}, {\n\tvalue: SECOND,\n\tlabel: '215903'\n}, {\n\tvalue: STRING,\n\tlabel: '100552'\n}, {\n\tvalue: EMPTY_FORMULA,\n\tlabel: '109346'\n}];\n\n/* istanbul ignore next */\nexport const getFormulaToken = (value, key = 'label') => {\n\tconst foundItem = formulas.find(formula => formula.value === value);\n\n\treturn foundItem && foundItem[key];\n};\n\nexport default { YEAR, QUARTER, MONTH, MONTH_YEAR, WEEK, WEEK_MONTH, DAY, HOUR, MINUTE, SECOND, EMPTY_FORMULA };", "// Espaço ocupado pelo card do Workspace que não é disponibilizado para os nossos cards\nconst CARD_SPACING = 51;\n\nexport const heightRules = [{\n\tmaxHeight: 173 - CARD_SPACING\n}, {\n\tminHeight: 174 - CARD_SPACING,\n\tmaxHeight: 184 - <PERSON><PERSON>_SPACING\n}, {\n\tminHeight: 185 - CARD_SPACING,\n\tmaxHeight: 275 - CARD_SPACING\n}, {\n\tminHeight: 276 - CARD_SPACING\n}];\n\nexport const widthRules = [{\n\tmaxWidth: 291 - CARD_SPACING\n}, {\n\tminWidth: 292 - CARD_SPACING,\n\tmaxWidth: 424 - CARD_SPACING\n}, {\n\tminWidth: 425 - CARD_SPACING,\n\tmaxWidth: 605 - CARD_SPACING\n}, {\n\tminWidth: 606 - CARD_SPACING\n}];\n\nexport const EXTRA_SMALL = 'XS';\n\nexport const SMALL = 'SM';\n\nexport const MEDIUM = 'MD';\n\nexport const BIG = 'LG';\n\nexport const sizes = {\n\tEXTRA_SMALL, SMALL, MEDIUM, BIG\n};\n\nexport default {\n\theightRules, widthRules\n};", "export const AREA = 'area';\n\nexport const COLUMN = 'column';\n\nexport const LINE = 'line';\n\nexport const RADAR = 'radar';\n\nexport default { AREA, COLUMN, LINE, RADAR };", "export const AREA = 'area';\n\nexport const BAR = 'bar';\n\nexport const BOXPLOT = 'boxplot';\n\nexport const LINEAR_GAUGE = 'linear_gauge';\n\nexport const COLUMN = 'column';\n\nexport const GAUGE = 'gauge';\n\nexport const KPI = 'kpi';\n\nexport const LINE = 'line';\n\nexport const PARETO = 'pareto';\n\nexport const PIE = 'pie';\n\nexport const FUNNEL = 'funnel';\n\nexport const COLUMNRANGE = 'columnrange';\n\nexport const RADAR = 'radar';\n\nexport const HISTOGRAM = 'histogram';\n\nexport const BUBBLE = 'bubble';\n\nexport const SCATTER = 'scatter';\n\nexport const FUNNEL_TYPES = [FUNNEL, COLUMNRANGE];\n\nexport const CATEGORIES_NOT_BASED_CHART = [BUBBLE, SCATTER];\n\nexport const NON_CARTESIAN_SERIES_TYPES = [\n\t'item',\n\t'pie',\n\t'funnel',\n\t'pyramid',\n\t'venn',\n\t'wordcloud',\n\t'gauge',\n\t'solidgauge',\n\t'heatmap',\n\t'treemap',\n\t'sunburst',\n\t'packedbubble',\n\t'networkgraph'\n];\n\nconst supportedTypes = {\n\tAREA, BAR, BOXPLOT, FUNNEL, COLUMNRANGE, LINEAR_GAUGE, COLUMN,\n\tGAUGE, KPI, LINE, PARETO,\n\tPIE, RADAR, HISTOGRAM, BUBBLE, SCATTER,\n\tget: function(type) {\n\t\treturn this[type.toUpperCase()];\n\t}\n};\n\nexport const chartTypes = [\n\tPIE, BAR, COLUMN, LINE, FUNNEL, COLUMNRANGE,\n\tPARETO, GAUGE, LINEAR_GAUGE, AREA,\n\tBUBBLE, HISTOGRAM, SCATTER,\n\tRADAR, BOXPLOT\n];\n\nexport const highchartsTypes = [\n\tPIE, BAR, COLUMN, LINE, COLUMNRANGE,\n\tPARETO, LINEAR_GAUGE, AREA,\n\tBUBBLE, HISTOGRAM, SCATTER,\n\tRADAR, BOXPLOT, FUNNEL\n];\n\nexport const allTypes = [\n\tKPI,\n\t...chartTypes\n];\n\nexport default supportedTypes;", "export const EMPTY_HASH = '@empty_0d9be6f7-0113-42e3-85d2-4e03013b0c2f';\n\nexport const GENERAL_SERIE = '@general_d974044a-0c1a-11eb-adc1-0242ac120002';\n\nexport const UNDEFINED_HASH = '@undefined_0019c74f-9d88-4c87-a724-3a3fee813f4c';\n\nexport default { EMPTY_HASH, GENERAL_SERIE, UNDEFINED_HASH };", "import supportedTypes from 'reactorCmps/src/chartengine2/constants/supportedTypes';\n\nconst funnelLayout = {\n\tid: supportedTypes.FUNNEL,\n\ttoken: '106450',\n\ticon: 'seicon-filtering'\n};\n\nconst columnrangeLayout = {\n\tid: supportedTypes.COLUMNRANGE,\n\ttoken: '100639',\n\ticon: 'seicon-funnel'\n};\n\nconst layoutsSupportedByEachViewType = {\n\tFUNNEL: [columnrangeLayout, funnelLayout],\n\tCOLUMNRANGE: [columnrangeLayout, funnelLayout]\n};\n\nexport const getLayoutsSupportedByEachViewType = viewType => {\n\tconst normalizedViewType = viewType.toUpperCase();\n\n\treturn layoutsSupportedByEachViewType[normalizedViewType];\n};\n\nexport const chartTypesThatReferToAnotherChartType = {\n\t[supportedTypes.COLUMNRANGE]: supportedTypes.FUNNEL\n};\n\nexport const chartTypesThatDeriveFromOtherChartTypes = [\n\tsupportedTypes.COLUMNRANGE\n];", "import { merge } from 'lodash';\nimport datatypes from 'reactorCmps/src/chartengine2/constants/datatypes';\nimport serieTypes from 'reactorCmps/src/chartengine2/constants/serieTypes';\nimport supportedTypes from 'reactorCmps/src/chartengine2/constants/supportedTypes';\nimport { createPlainObject, getValueFromObject } from 'reactorCmps/src/chartengine2/helpers/commonHelper';\n\nconst MARKER_TEXT_EXTRA_SPACING = 20;\n\nexport const getChartClassName = chart => getValueFromObject(chart, ['userOptions', 'chart', 'className'], '');\n\nexport const getCorrectViewType = (type = '') => {\n\tlet viewType = type.toLowerCase();\n\n\tif (viewType === '<unknown>' || !viewType ) {\n\t\treturn 'table';\n\t}\n\tif (viewType === 'single_number') {\n\t\treturn 'kpi';\n\t}\n\tif (viewType === 'angular') {\n\t\treturn 'gauge';\n\t}\n\tif (viewType === 'linear_horizontal' || viewType === 'bullet') {\n\t\treturn 'linear_gauge';\n\t}\n\n\treturn viewType;\n};\n\nexport function renderFooter() {\n\tif (this.props && this.props.getCEFooter)\n\t\treturn this.props.getCEFooter();\n\n\treturn null;\n}\n\nexport function renderHeader() {\n\tif (this.props && this.props.getCEHeader)\n\t\treturn this.props.getCEHeader();\n\n\treturn null;\n}\n\nexport const getElementVerticalMarginSum = element => {\n\tlet total = 0;\n\tlet style;\n\n\tif (!element)\n\t\treturn total;\n\n\tstyle = element.style;\n\n\tif (style.marginTop)\n\t\ttotal += parseFloat(style.marginTop);\n\n\tif (style.marginBottom)\n\t\ttotal += parseFloat(style.marginBottom);\n\n\treturn total;\n};\n\nexport function getHeaderHeight() {\n\tif (this.header && this.header.headerContainer)\n\t\treturn this.header.headerContainer.clientHeight;\n\n\treturn 0;\n}\n\nexport function getFooterHeight() {\n\tlet height = 0;\n\tlet footer, firstChild;\n\n\tif (this.footer && this.footer.footerContainer) {\n\t\tfooter = this.footer.footerContainer;\n\t\tfirstChild = footer.firstChild;\n\n\t\theight += footer.clientHeight;\n\n\t\t/* istanbul ignore else */\n\t\tif (firstChild)\n\t\t\theight += getElementVerticalMarginSum(firstChild);\n\n\t\treturn height;\n\t}\n\n\treturn height;\n}\n\nexport const getTooltipSymbol = symbolName => {\n\tlet symbol;\n\n\tswitch (symbolName) {\n\t\tdefault:\n\t\tcase 'arc':\n\t\t\tsymbol = '●';\n\t\t\tbreak;\n\t\tcase 'diamond':\n\t\t\tsymbol = '♦';\n\t\t\tbreak;\n\t\tcase 'square':\n\t\t\tsymbol = '■';\n\t\t\tbreak;\n\t\tcase 'triangle':\n\t\t\tsymbol = '▲';\n\t\t\tbreak;\n\t\tcase 'triangle-down':\n\t\t\tsymbol = '▼';\n\t\t\tbreak;\n\t\tcase 'pentagon':\n\t\t\tsymbol = '⬟';\n\t\t\tbreak;\n\t\tcase 'star':\n\t\t\tsymbol = '★';\n\t\t\tbreak;\n\t}\n\n\treturn symbol;\n};\n\nexport const isDataHeaderEmpty = header => {\n\tif (!header) {\n\t\treturn false;\n\t}\n\treturn header.userLabel !== '';\n};\n\nexport const isSerieTypeValid = serieType => {\n\treturn Boolean(serieType && [serieTypes.LINE, serieTypes.COLUMN, serieTypes.AREA, serieTypes.RADAR].includes(serieType.toLowerCase()));\n};\n\nexport const isTextField = datatype => [datatypes.BIGTEXT, datatypes.TEXT].includes(datatype);\n\nexport const isNumericField = datatype => {\n\treturn [datatypes.INTEGER, datatypes.DECIMAL, datatypes.CURRENCY, datatypes.LONG].includes(datatype);\n};\n\nexport const isDateField = (datatype, includeTime = true) => {\n\tlet dateTypes = [datatypes.DATE, datatypes.DATETIME];\n\n\tif (includeTime)\n\t\tdateTypes = [...dateTypes, datatypes.TIME];\n\n\treturn dateTypes.includes(datatype);\n};\n\nexport const addExportDiv = isExporting => {\n\tlet divContainer, div;\n\n\tif (!isExporting)\n\t\treturn;\n\n\tdivContainer = $('#export-div-container');\n\tdiv = document.createElement('div');\n\n\tdiv.id = 'componentRendered';\n\n\tdivContainer.empty();\n\tdivContainer.append(div);\n};\n\nexport const hideChartTooltip = function() {\n\tthis.chart.tooltip.hide();\n};\n\nexport const isWidget = viewType => {\n\treturn [supportedTypes.KPI, supportedTypes.GAUGE, supportedTypes.LINEAR_GAUGE].includes(getCorrectViewType(viewType));\n};\n\nexport const getCategoryValueByIndex = (options, categoryIndex) => {\n\tlet series;\n\n\tif (!options)\n\t\treturn null;\n\n\tif (options.xAxis && Array.isArray(options.xAxis.categories))\n\t\treturn options.xAxis.categories[categoryIndex];\n\n\tseries = options.series;\n\n\tif (Array.isArray(series) && series.length && series[0].data)\n\t\treturn series[0].data[categoryIndex].originalName;\n\n\treturn null;\n};\n\nexport const getWidgetData = data => {\n\tif (Array.isArray(data)) {\n\t\tif (Array.isArray(data[0])) {\n\t\t\treturn data[0][0];\n\t\t}\n\n\t\treturn data[0];\n\t}\n\n\treturn data;\n};\n\nexport const getTextWidth = (text, fontStyle) => {\n\tconst { fontFamily, fontSize, fontWeight } = fontStyle;\n\tconst canvas = document.createElement('canvas');\n\tconst context = canvas.getContext('2d');\n\tlet font = '';\n\n\t/* istanbul ignore if */\n\tif (fontWeight !== undefined)\n\t\tfont = `${fontWeight} `;\n\n\tfont += `${fontSize} ${fontFamily}`;\n\n\tcontext.font = font;\n\n\treturn context.measureText(text).width;\n};\n\nexport const getMarkerTextWidth = ({ text, style }) => getTextWidth(text, style) + MARKER_TEXT_EXTRA_SPACING;\n\nexport const isRadar = ({ chart }) => {\n\tif (!chart || !chart.type)\n\t\treturn false;\n\n\tif (chart.type === supportedTypes.RADAR)\n\t\treturn true;\n\n\treturn isSerieTypeValid(chart.type) && chart.polar === true;\n};\n\nexport const getChartType = config => {\n\tconst type = getValueFromObject(config, ['chart', 'type']);\n\n\tif (!type)\n\t\treturn null;\n\n\tif (isRadar(config))\n\t\treturn supportedTypes.RADAR;\n\n\treturn getCorrectViewType(type);\n};\n\nexport const isVisible = object => {\n\treturn getValueFromObject(object, 'visible', true);\n};\n\nexport const removeSerieTypes = (series = []) => {\n\treturn series.map((serie = {}) => ({ ...serie, type: undefined }));\n};\n\nexport const getColorIndex = (index, colorsLength) => index % colorsLength;\n\nexport const isCombo = (chartType, array) => {\n\tconst _chartType = String(chartType).toLowerCase();\n\tconst _array = array || [];\n\n\tif (!isSerieTypeValid(_chartType))\n\t\treturn false;\n\n\treturn _array.some(current => {\n\t\treturn current.serieType && isSerieTypeValid(current.serieType) && current.serieType.toLowerCase() !== chartType;\n\t});\n};\n\nexport const hasRightAxisOnConfig = config => {\n\tconst { series } = config;\n\n\tif (!series) {\n\t\treturn false;\n\t}\n\n\treturn series.some(serie => serie.yAxis === 1);\n};\n\nexport const isRightAxisOnly = (series = []) => {\n\tconst rightAxisSeries = series.filter(serie => serie.yAxis === 1);\n\n\treturn Boolean(series.length && rightAxisSeries.length === series.length);\n};\n\nexport const getIsConfigReady = config => Boolean(getValueFromObject(config, 'isConfigReady', false));\n\nexport const getIsStackedChart = config => {\n\t/* istanbul ignore next */\n\tconst { isStackedChart = false, plotOptions = {}, chart = {} } = config || {};\n\tconst { type } = chart;\n\n\tif (isStackedChart)\n\t\treturn true;\n\n\tif (plotOptions[type])\n\t\treturn Boolean(plotOptions[type].stacking);\n\n\tif (plotOptions.series)\n\t\treturn Boolean(plotOptions.series.stacking);\n\n\treturn false;\n};\n\nexport const checkIfChartHasPercentFormat = config => {\n\tconst { showPercentValues } = config;\n\tlet dataLabelsFormat;\n\n\tif (showPercentValues !== undefined)\n\t\treturn Boolean(showPercentValues);\n\n\tdataLabelsFormat = getValueFromObject(config, ['plotOptions', 'series', 'dataLabels', 'format'], '');\n\n\treturn dataLabelsFormat.includes('{point.percentage:,.2f}%');\n};\n\nexport const getConfigWithNewType = (config, type) => {\n\tlet newType = type;\n\n\tif (type === supportedTypes.FUNNEL) {\n\t\tnewType = supportedTypes.COLUMNRANGE;\n\t}\n\n\treturn merge({}, config, {\n\t\tchart: { type: newType }\n\t});\n};\n\nexport const getUserOptionsFromInstance = instance => {\n\tif (!instance || !instance.userOptions)\n\t\treturn {};\n\n\treturn createPlainObject(instance.userOptions);\n};\n\nexport function nonHighchartsAfterRenderEvents(ref) {\n\tconst { config, getInstance } = this.props;\n\tconst onLoad = getValueFromObject(config, ['chart', 'events', 'load']);\n\n\tgetInstance && getInstance(ref);\n\tonLoad && onLoad.call(this);\n}", "import { cloneDeep, keyBy, merge, omit } from 'lodash';\nimport { UNDEFINED_HASH } from 'reactorCmps/src/chartengine2/constants/utils';\nimport { heightRules, sizes, widthRules } from 'reactorCmps/src/chartengine2/constants/responsiveDimensions';\n\nexport const getChartSize = (chartHeight, chartWidth) => {\n\tconst sizeMap = [sizes.EXTRA_SMALL, sizes.SMALL, sizes.MEDIUM, sizes.BIG];\n\n\tfor (let i = 0; i < sizeMap.length; i++) {\n\t\tconst widthRule = widthRules[i];\n\t\tconst heightRule = heightRules[i];\n\n\t\tconst isWidthWithinRange =\n\t\t\t(widthRule.minWidth === undefined || chartWidth >= widthRule.minWidth) &&\n\t\t\t(widthRule.maxWidth === undefined || chartWidth <= widthRule.maxWidth);\n\n\t\tconst isHeightWithinRange =\n\t\t\t(heightRule.minHeight === undefined || chartHeight >= heightRule.minHeight) &&\n\t\t\t(heightRule.maxHeight === undefined || chartHeight <= heightRule.maxHeight);\n\n\t\tif (isWidthWithinRange || isHeightWithinRange) {\n\t\t\treturn sizeMap[i];\n\t\t}\n\t}\n};\n\nexport const createPlainObject = (object = {}) => {\n\treturn JSON.parse(JSON.stringify(object));\n};\n\nconst _isObject = value => value && typeof value === 'object';\n\n/* istanbul ignore next */\nconst _removeUndefinedHashFromObject = obj => {\n\tif (!obj || typeof obj !== 'object' || Array.isArray(obj))\n\t\treturn obj;\n\n\tObject.keys(obj).forEach(property => {\n\t\tif (typeof obj[property] === 'object')\n\t\t\t_removeUndefinedHashFromObject(obj[property]);\n\t\telse if (obj[property] === UNDEFINED_HASH)\n\t\t\tdelete obj[property];\n\t});\n\n\treturn obj;\n};\n\n/* istanbul ignore next */\nexport const mergeWithUndefined = (obj = {}, ...newValues) => {\n\tlet newObj = cloneDeep(obj);\n\n\tnewValues.forEach(current => {\n\t\tcurrent && Object.keys(current).forEach(property => {\n\t\t\tlet _array;\n\n\t\t\tif (!newObj || newObj === UNDEFINED_HASH)\n\t\t\t\tnewObj = {};\n\n\t\t\tif (newObj.hasOwnProperty(property)) {\n\t\t\t\tif (Array.isArray(current[property])) {\n\t\t\t\t\tif (!Array.isArray(newObj[property]))\n\t\t\t\t\t\tnewObj[property] = [];\n\n\t\t\t\t\t_array = current[property].map((current2, index) => {\n\t\t\t\t\t\tif (_isObject(current2))\n\t\t\t\t\t\t\treturn mergeWithUndefined({}, newObj[property][index], current2);\n\n\t\t\t\t\t\treturn current2;\n\t\t\t\t\t});\n\n\t\t\t\t\tnewObj[property] = _array;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (_isObject(current[property]) && _isObject(newObj[property])) {\n\t\t\t\t\tnewObj[property] = mergeWithUndefined(newObj[property], current[property]);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (current[property] === UNDEFINED_HASH) {\n\t\t\t\t\tdelete newObj[property];\n\t\t\t\t} else {\n\t\t\t\t\tnewObj[property] = current[property];\n\t\t\t\t}\n\t\t\t} else if (current[property] !== UNDEFINED_HASH) {\n\t\t\t\tnewObj[property] = _removeUndefinedHashFromObject(current[property]);\n\t\t\t}\n\t\t});\n\t});\n\n\treturn newObj;\n};\n\nexport const getValueFromObject = (object, key, defaultValue, ignoreUndefined = false) => {\n\tconst keys = !Array.isArray(key) ? [key] : key;\n\tconst keysLength = keys.length;\n\tlet noMatches = keysLength === 0;\n\tlet currentValue = object;\n\tlet currentKey, i;\n\n\tfor (i = 0; i < keysLength; i++) {\n\t\tcurrentKey = keys[i];\n\n\t\tif (currentValue && currentValue.hasOwnProperty(currentKey)) {\n\t\t\tcurrentValue = currentValue[currentKey];\n\n\t\t\tcontinue;\n\t\t}\n\n\t\tnoMatches = true;\n\n\t\tbreak;\n\t}\n\n\tif (noMatches || (ignoreUndefined && currentValue === undefined))\n\t\treturn typeof defaultValue === 'function' ? defaultValue() : defaultValue;\n\n\treturn currentValue;\n};\n\nexport const deepReset = (data, firstLevel = true) => {\n\tlet result = {};\n\tlet key, currentValue;\n\n\tif (Array.isArray(data)) {\n\t\tresult = data.map(value => (typeof value === 'object') ? deepReset(value, false) : undefined);\n\n\t\t/* istanbul ignore next */\n\t\treturn (!firstLevel && result.some(value => value === undefined)) ? UNDEFINED_HASH : result;\n\t}\n\n\t/* istanbul ignore else */\n\tif (typeof data === 'object') {\n\t\tfor (key in data) {\n\t\t\tcurrentValue = data[key];\n\n\t\t\tresult[key] = (typeof currentValue === 'object') ? deepReset(currentValue, false) : undefined;\n\t\t}\n\n\t\treturn (!firstLevel && Object.values(result).some(value => value === undefined)) ? UNDEFINED_HASH : result;\n\t}\n\n\t/* istanbul ignore next */\n\treturn data;\n};\n\n/* istanbul ignore next */\nconst _getElementBoundingClientRect = element => {\n\tlet isElementHidden = element.style.display === 'none';\n\tlet elementBoundingRect;\n\n\tif (isElementHidden)\n\t\telement.style.display = 'block';\n\n\telementBoundingRect = element.getBoundingClientRect();\n\n\tif (isElementHidden)\n\t\telement.style.display = 'none';\n\n\treturn elementBoundingRect;\n};\n\n/* istanbul ignore next */\nexport const hasOverlappingElements = (element1, element2) => {\n\tconst element1BoundingRect = _getElementBoundingClientRect(element1);\n\tconst element2BoundingRect = _getElementBoundingClientRect(element2);\n\n\treturn (\n\t\telement1BoundingRect.left < element2BoundingRect.left + element2BoundingRect.width &&\n\t\telement2BoundingRect.left < element1BoundingRect.left + element1BoundingRect.width &&\n\t\telement1BoundingRect.top < element2BoundingRect.top + element2BoundingRect.height &&\n\t\telement2BoundingRect.top < element1BoundingRect.top + element1BoundingRect.height\n\t);\n};\n\nexport const mergeArrayBy = (firstArray, secondArray, key) => {\n\tconst undefinedKey = 'c202f231-e90a-4e6c-94cb-6a5147beeeb4';\n\t/* istanbul ignore next */\n\tconst handler = current => {\n\t\tif (!current || !current[key])\n\t\t\treturn undefinedKey;\n\n\t\t/* to prevent sorting by numerical keys */\n\t\treturn `_${current[key]}_`;\n\t};\n\tconst firstObjectByUid = cloneDeep(keyBy(firstArray, handler));\n\tconst secondObjectByUid = cloneDeep(keyBy(secondArray, handler));\n\n\treturn Object.values(\n\t\tmerge(\n\t\t\t{},\n\t\t\tomit(firstObjectByUid, undefinedKey),\n\t\t\tomit(secondObjectByUid, undefinedKey)\n\t\t)\n\t);\n};\n\nexport const replaceSelectionWith = (originalText, selectionStart, selectionEnd, replacement) => {\n\treturn originalText.substring(0, selectionStart) + replacement + originalText.substring(selectionEnd);\n};", "import { DragDropContext } from \"react-dnd-old\";\nimport HTML5Backend from \"react-dnd-html5-backend\";\nimport { use } from \"suite-storage\";\n\nexport default use(\"DASHBOARD_DRAG_DROP\", () => new DragDropContext(HTML5Backend));\n", "\n      var API = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n      var domAPI = require(\"!../../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js\");\n      var insertFn = require(\"!../../../../../node_modules/style-loader/dist/runtime/insertBySelector.js\");\n      var setAttributes = require(\"!../../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\");\n      var insertStyleElement = require(\"!../../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js\");\n      var styleTagTransformFn = require(\"!../../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js\");\n      var content = require(\"!!../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./DraggableListItem.mcss\");\n      \n      content = content.__esModule ? content.default : content;\n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\noptions.insert = insertFn.bind(null, \"head\");\noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nmodule.exports = content && content.locals || {};\n", "\n      var API = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n      var domAPI = require(\"!../../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js\");\n      var insertFn = require(\"!../../../../../node_modules/style-loader/dist/runtime/insertBySelector.js\");\n      var setAttributes = require(\"!../../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\");\n      var insertStyleElement = require(\"!../../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js\");\n      var styleTagTransformFn = require(\"!../../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js\");\n      var content = require(\"!!../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./OLAPDataTypeIcon.mcss\");\n      \n      content = content.__esModule ? content.default : content;\n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\noptions.insert = insertFn.bind(null, \"head\");\noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nmodule.exports = content && content.locals || {};\n", "\n      var API = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n      var domAPI = require(\"!../../../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js\");\n      var insertFn = require(\"!../../../../../../node_modules/style-loader/dist/runtime/insertBySelector.js\");\n      var setAttributes = require(\"!../../../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\");\n      var insertStyleElement = require(\"!../../../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js\");\n      var styleTagTransformFn = require(\"!../../../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js\");\n      var content = require(\"!!../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].use[2]!./Popover.mcss\");\n      \n      content = content.__esModule ? content.default : content;\n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\noptions.insert = insertFn.bind(null, \"head\");\noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nmodule.exports = content && content.locals || {};\n"], "names": ["sortConstants", "oid", "data", "item", "aboveItem", "itemOid", "flow", "omit", "PropTypes", "React", "Component", "DragSource", "<PERSON><PERSON>arget", "DraggableListItemPresentation", "dndConstants", "itemSource", "props", "monitor", "component", "itemTarget", "DraggableListItem", "render", "_this_props", "connectDropTarget", "connectDragSource", "connect", "colors", "containerStyle", "DraggableListItemPlaceholder", "fontBold", "ListItem", "styles", "dragIconStyle", "leftContentStyle", "dragIconWidth", "getLeftContent", "combinedStyle", "Object", "leftContent", "getPlaceholder", "Label", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelStyle", "FieldsList", "get<PERSON>ontent", "me", "scrollStyle", "getContainerStyle", "style", "rightContentStyle", "titleStyle", "content", "getRightContent", "getTitlePrefix", "getTitle", "hint", "values", "memo", "useMemo", "dataType", "_obj", "TYPES", "OLAPDataTypeIcon", "param", "type", "normalizedType", "iconStyle", "DropableFieldsListPresentation", "listTarget", "DropableFieldsList", "Children", "paddingStyle", "get<PERSON><PERSON><PERSON><PERSON>nt", "PureComponent", "dispatch", "SortActions", "PopoverC<PERSON>nt", "ScopeRedux", "orderManagerHelper", "OLAPViewOrderManager", "componentDidMount", "componentDidUpdate", "setScope", "cardDataOid", "fetchData", "moveCubeSortItem", "reorderSorts", "setCubeSortOrder", "setData", "cube", "viewType", "_orderManagerHelper_getAllItems", "availableItems", "sortedItems", "method", "Provider", "Utils", "reactReduxInstanceWrapper", "PopoverContentPresentation", "sort", "SortStore", "DashboardDragNDropContext", "mapStateToProps", "state", "mapDispatchToProps", "list", "hoverItem", "PopoverContentConnect", "Image", "tokenManagerHOC", "fonts", "size", "SortableListItem", "SortTinyDropdown", "SortUtils", "getFormulaToken", "listsStyle", "dividerStyle", "leftIconStyle", "leftIndexStyle", "dropdownStyle", "rightIconStyle", "noContentStyle", "noContentTextStyle", "getLeftIcon", "getLeftIndex", "index", "removeItem", "setItemAsc", "setItemDesc", "getAvailableItems", "onDrop", "items", "getSortedItems", "_me_props", "i", "getToken", "hasItems", "getLists", "dividerHeightStyle", "getList", "term", "placeholder", "height", "String", "get<PERSON>oContent", "SortTinyDropdownPresentation", "analysisOid", "value", "direction", "SortTinyDropdownConnect", "MenuItem", "TinyDropdown", "SortableListItemPresentation", "uid", "itemObj", "SortableListItemConnect", "Consumer", "handleDblClick", "handleBeginDrag", "popoverId", "$", "context", "SortRedux", "getActionsByType", "actions", "action", "_action", "params", "sortReduxAction", "sendGetData", "scopeOid", "scope", "getScope", "id", "indexToRemove", "indexToAdd", "removeSort", "reorderCubeSortItem", "oldIndex", "newIndex", "cloneElement", "createRef", "Fragment", "Overlay", "Popover", "validateProp", "valueStyle", "POPOVER_SELECTOR", "componentWillUnmount", "getOptions", "option", "getOverlay", "getPopoverReference", "hideOverlay", "showOverlay", "setMouseDownListener", "removeMouseDownListener", "BIGTEXT", "BOOLEAN", "CURRENCY", "DECIMAL", "DATE", "DATETIME", "INTEGER", "LONG", "TEXT", "TIME", "createContext", "PopoverContext", "PopoverContextProvider", "contextProps", "chartTypesThatReferToAnotherChartType", "getCorrectViewType", "SE", "get", "viewTypeUpper", "correctViewTypeUpper", "viewTypeFromDerivedChartUpper", "olapConfig", "as", "bs", "a", "b", "cubeConfigurations", "getData", "getCubeSortData", "sortedItemsUid", "getItemByType", "cubeType", "getItems", "configurations", "pos", "getSorts", "sorts", "itemsUid", "getAllItems", "fromJS", "initialState", "findItem", "getFixedItem", "setItemDirection", "path", "dir", "removeItemFromList", "insertItemInList", "fixedItem", "moveItem", "listToAdd", "originalListToRemove", "actionOid", "stateData", "listToRemove", "sortItem", "hoverOid", "itemIndex", "_state", "Store", "SortReducer", "YEAR", "QUARTER", "MONTH", "MONTH_YEAR", "WEEK", "WEEK_MONTH", "DAY", "HOUR", "MINUTE", "SECOND", "STRING", "EMPTY_FORMULA", "formulas", "key", "foundItem", "formula", "CARD_SPACING", "heightRules", "widthRules", "EXTRA_SMALL", "SMALL", "MEDIUM", "BIG", "sizes", "AREA", "COLUMN", "LINE", "RADAR", "BAR", "BOXPLOT", "LINEAR_GAUGE", "GAUGE", "KPI", "PARETO", "PIE", "FUNNEL", "COLUMNRANGE", "HISTOGRAM", "BUBBLE", "SCATTER", "FUNNEL_TYPES", "CATEGORIES_NOT_BASED_CHART", "NON_CARTESIAN_SERIES_TYPES", "supportedTypes", "chartTypes", "highchartsTypes", "allTypes", "EMPTY_HASH", "GENERAL_SERIE", "UNDEFINED_HASH", "funnelLayout", "columnrangeLayout", "layoutsSupportedByEachViewType", "getLayoutsSupportedByEachViewType", "normalizedViewType", "chartTypesThatDeriveFromOtherChartTypes", "merge", "datatypes", "serieTypes", "createPlainObject", "getValueFromObject", "MARKER_TEXT_EXTRA_SPACING", "getChartClassName", "chart", "renderFooter", "renderHeader", "getElementVerticalMarginSum", "element", "total", "parseFloat", "getHeaderHeight", "getFooterHeight", "footer", "<PERSON><PERSON><PERSON><PERSON>", "getTooltipSymbol", "symbolName", "symbol", "isDataHeaderEmpty", "header", "isSerieTypeValid", "serieType", "Boolean", "isTextField", "datatype", "isNumericField", "isDateField", "includeTime", "dateTypes", "addExportDiv", "isExporting", "<PERSON>v<PERSON><PERSON><PERSON>", "div", "document", "hideChartTooltip", "isWidget", "getCategoryValueByIndex", "options", "categoryIndex", "series", "Array", "getWidgetData", "getTextWidth", "text", "fontStyle", "fontFamily", "fontSize", "fontWeight", "canvas", "font", "undefined", "getMarkerTextWidth", "isRadar", "getChartType", "config", "isVisible", "object", "removeSerieTypes", "serie", "getColorIndex", "colorsLength", "isCombo", "chartType", "array", "_chartType", "_array", "current", "hasRightAxisOnConfig", "isRightAxisOnly", "rightAxisSeries", "getIsConfigReady", "getIsStackedChart", "_ref", "isStackedChart", "plotOptions", "checkIfChartHasPercentFormat", "showPercentValues", "dataLabelsFormat", "getConfigWithNewType", "newType", "getUserOptionsFromInstance", "instance", "nonHighchartsAfterRenderEvents", "ref", "getInstance", "onLoad", "cloneDeep", "keyBy", "getChartSize", "chartHeight", "chartWidth", "sizeMap", "widthRule", "heightRule", "isWidthWithinRange", "isHeightWithinRange", "JSON", "_isObject", "_type_of", "_removeUndefinedHashFromObject", "obj", "property", "mergeWithUndefined", "newValues", "newObj", "current2", "defaultValue", "ignoreUndefined", "keys", "<PERSON><PERSON><PERSON><PERSON>", "noMatches", "currentValue", "current<PERSON><PERSON>", "deepReset", "firstLevel", "result", "_getElementBoundingClientRect", "isElementHidden", "elementBoundingRect", "hasOverlappingElements", "element1", "element2", "element1BoundingRect", "element2BoundingRect", "mergeArrayBy", "firstArray", "secondArray", "undefined<PERSON>ey", "handler", "firstObjectByUid", "secondObjectByUid", "replaceSelectionWith", "originalText", "selectionStart", "selectionEnd", "replacement", "DragDropContext", "HTML5Backend", "use"], "mappings": ";;;AAAA;AACA,+CAA+C,mBAAO,CAAC,oIAAqE;AAC5H,kCAAkC,mBAAO,CAAC,kHAA4D;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;;;;;ACjCd;AACA,+CAA+C,mBAAO,CAAC,oIAAqE;AAC5H,kCAAkC,mBAAO,CAAC,kHAA4D;AACtG;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,cAAc;;;;;ACbd;AACA,+CAA+C,mBAAO,CAAC,uIAAwE;AAC/H,kCAAkC,mBAAO,CAAC,qHAA+D;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;;;;;;;;;;;;ACzB6B;AACpC,gBAAgB,wDAAO;AAC9B;AACA,CAAC;AACM,eAAe,wDAAO;AAC7B;AACA,CAAC;;;;;;;;;;ACND,kDAAkD,0CAA0C;;AAE5F,4CAA4C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD;;AAE/P,8DAA8D,sEAAsE,8DAA8D;;AAEhJ;;AAElD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,qBAAqB,sDAAK;AAC1B;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,qBAAqB,wDAAO;AAC5B;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;;;;;;;;;;;;;;;;AC/CD,2CAA2C,gCAAgC,oCAAoC,oDAAoD,8DAA8D,iEAAiE,GAAG,kCAAkC;;AAEvU,iCAAiC,gBAAgB,sBAAsB,OAAO,uDAAuD,aAAa,uDAAuD,4CAA4C,KAAK,6CAA6C,6EAA6E,OAAO,iDAAiD,mFAAmF,OAAO;;AAEtgB,4CAA4C,kBAAkB,kCAAkC,oEAAoE,KAAK,OAAO,oBAAoB;;AAEpM,kDAAkD,0CAA0C;;AAE5F,4CAA4C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD;;AAE/P,8DAA8D,sEAAsE,8DAA8D;;AAE9I;AACN;AACkD;AACd;AACrC;AACG;;AAEhD;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,iEAAmB;AAChC;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,yBAAyB,kEAAoB,KAAK;;AAElD;AACA;AACA,QAAQ;;;AAGR;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA,uBAAuB,uEAAmB;;AAE1C;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,kEAAoB;AACxD;AACA;AACA;;AAEA;AACA;AACA,qDAAqD;AACrD,UAAU,eAAe;AACzB;AACA;;;AAGA,kDAAkD;;;AAGlD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,uBAAuB,uEAAmB;;AAE1C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA,WAAW,2DAAS;AACpB;AACA;AACA;AACA;AACA;AACA,wBAAwB,kEAAoB;AAC5C,SAAS;AACT;;AAEA;AACA;AACA,OAAO;;AAEP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA,sBAAsB,kEAAoB;AAC1C,OAAO;;AAEP;AACA;AACA,OAAO;;AAEP;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,QAAQ;AACR;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,sBAAsB,kEAAoB;AAC1C,OAAO;;AAEP;AACA;AACA,OAAO;;AAEP;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA,6BAA6B;AAC7B;;AAEA;AACA;AACA,QAAQ;;;AAGR;AACA;AACA,QAAQ;AACR;;;AAGA;AACA;AACA;;AAEA,uBAAuB,yDAAa;AACpC;AACA;AACA;AACA,iCAAiC,0DAAiB;AAClD,IAAI;;;AAGJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,yBAAyB;AAChC;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,gCAAgC;AACvC;AACA,GAAG;AACH;AACA;AACA;AACA,yBAAyB,yCAAW;AACpC,eAAe,yCAAW;AAC1B,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;AACA,iCAAiC,0EAAsB;AACvD;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA,yCAAyC;AACzC;AACA;AACA;;AAEA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;;;;;;;;;;ACloBD,kDAAkD,0CAA0C;;AAE5F,4CAA4C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD;;AAE/P,8DAA8D,sEAAsE,8DAA8D;;AAElM;AACA;AACA;AACA;AACA;;AAEA,4BAA4B;;AAE5B;;AAEA,oBAAoB,YAAY;AAChC;AACA;;AAEA;AACA;AACA,KAAK,GAAG;;AAER;AACA;AACA;AACA;AACA;;AAEA,qBAAqB,iBAAiB;AACtC;AACA;AACA;AACA;AACA;AACA,MAAM;;;AAGN;;AAEA,sBAAsB,sBAAsB;AAC5C;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA,iCAAiC;;AAEjC;AACA;AACA;;AAEA,sBAAsB,sBAAsB;AAC5C;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;;AAE1B;;AAEA;AACA;AACA,QAAQ;;;AAGR;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,UAAU;AACV;AACA,UAAU;AACV;AACA;AACA;;AAEA,6BAA6B;;AAE7B;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;;;;;;;;;;ACzHD,kDAAkD,0CAA0C;;AAE5F,4CAA4C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD;;AAE/P,8DAA8D,sEAAsE,8DAA8D;;AAE3L;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,+BAA+B;AAC/B;AACA,GAAG;;AAEH;AACA,CAAC;;;;;;;;;ACzEM;AACP;AACA;AACA,GAAG;AACH;AACA;;;;;;;;;;;;ACLwD;AACF;AAC/C;AACP,mBAAmB,+DAAgB,CAAC,iEAAiB;AACrD;AACA;AACA;AACO;AACP;AACA;AACA;;AAEA;AACA,qBAAqB,iEAAiB;AACtC,uBAAuB,iEAAiB;AACxC;AACA;AACA,KAAK;AACL,GAAG;AACH;;;;;;;;;;;ACnBA;;AAEA,4CAA4C,kBAAkB,kCAAkC,oEAAoE,KAAK,OAAO,oBAAoB;;AAEtJ;AACsB;AAC7D,gDAAgD,sCAAsC,8CAAgB;AAC7G;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC,uCAAuC,6CAAe;AACvD;AACA;AACA,aAAa,iFAAuB;AACpC;AACA,GAAG;AACH;AACA,CAAC,uCAAuC,8CAAgB;AACxD;AACA;AACA,aAAa,iFAAuB;AACpC;AACA,GAAG;AACH;AACA,CAAC;;;;;;;;;;;AC9BM;AACA;AACA;;;;;;;;;;;;;ACFiD;AACE;AAC1D;AACO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA,qCAAqC,2DAAS;AAC9C;;AAEA;AACA;AACA,uEAAuE;;AAEvE,MAAM,0DAAQ;AACd;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,2BAA2B,6DAAoB;AAC/C;AACA;AACA;AACA,+CAA+C;;AAE/C,QAAQ,0DAAQ;AAChB;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,2BAA2B,6DAAoB;AAC/C;AACA;AACA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AClGA,kDAAkD,0CAA0C;;AAE5F,4CAA4C,gBAAgB,kBAAkB,OAAO,2BAA2B,wDAAwD,gCAAgC,uDAAuD;;AAE/P,8DAA8D,sEAAsE,8DAA8D;;AAE3L;AACP;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;AAEH;AACA,CAAC;;;;;;;;;ACtCD;AACO;AACP;AACA;AACA,qCAAqC;AACrC;;AAEA;AACA;;;;;;;;;;;;;;ACR0C;AACG;AACG;AACzB;;AAEvB;AACA,aAAa,qDAAY;AACzB;;AAEA,6DAAe,aAAa;;;;;;;;;;;ACT5B;AACO;AACP;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,GAAG;AACH;AACO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;;;;;;;;;ACrC4E;AAE5E,6DAAe;IACd,kBAAQC,GAAG,EAAEC,IAAI;QAChB,OAAO;YACN,MAAMF,gGAAsB;YAC5BC,KAAAA;YAAKC,MAAAA;QACN;IACD;IAEA,kBAAQD,GAAG,EAAEE,IAAI,EAAEC,SAAS;QAC3B,OAAO;YACN,MAAMJ,gGAAsB;YAC5BC,KAAAA;YAAKE,MAAAA;YAAMC,WAAAA;QACZ;IACD;IAEA,mBAASH,GAAG,EAAEE,IAAI,EAAEC,SAAS;QAC5B,OAAO;YACN,MAAMJ,iGAAuB;YAC7BC,KAAAA;YAAKE,MAAAA;YAAMC,WAAAA;QACZ;IACD;IAEA,qBAAWH,GAAG,EAAEE,IAAI;QACnB,OAAO;YACN,MAAMH,mGAAyB;YAC/BC,KAAAA;YAAKE,MAAAA;QACN;IACD;IAEA,qBAAWF,GAAG,EAAEI,OAAO;QACtB,OAAO;YACN,MAAML,oGAA0B;YAChCC,KAAAA;YAAKI,SAAAA;QACN;IACD;IAEA,sBAAYJ,GAAG,EAAEI,OAAO;QACvB,OAAO;YACN,MAAML,qGAA2B;YACjCC,KAAAA;YAAKI,SAAAA;QACN;IACD;AACD,CAAC,EAAC;;;;;;;;;;AC5CF,6DAAe;IACd,UAAU;IACV,UAAU;IACV,aAAa;IACb,WAAW;IACX,cAAc;IACd,eAAe;AAChB,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPkC;AAAA;AACD;AACM;AACc;AAC0E;AACtD;AAE3E,wBAAwB,GACxB,IAAMU,aAAa;IAClB,oBAAUC,KAAK,EAAEC,OAAO,EAAEC,SAAS;QAClC,IAAIF,MAAM,WAAW,EAAE;YACtBA,MAAM,WAAW,CAACA,OAAOC,SAASC;QACnC;QAEA,OAAOF;IACR;IACA,kBAAQA,KAAK,EAAEC,OAAO,EAAEC,SAAS;QAChC,IAAIF,MAAM,SAAS,EAAE;YACpBA,MAAM,SAAS,CAACA,OAAOC,SAASC;QACjC;QAEA,OAAOF;IACR;AACD;AAEA,wBAAwB,GACxB,IAAMG,aAAa;IAClB,kBAAQH,KAAK,EAAEC,OAAO;QACrB,OAAOD,MAAM,IAAI,CAAC,GAAG,KAAKC,QAAQ,OAAO,GAAG,IAAI,CAAC,GAAG;IACrD;IACA,eAAKD,KAAK,EAAEC,OAAO;QAClBD,MAAM,MAAM,CAACA,MAAM,IAAI,EAAEC,QAAQ,OAAO,IAAID,MAAM,IAAI;IACvD;AACD;AAEA,IAAMI,kCAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACLC,KAAAA;mBAAAA,SAAAA;gBACC,IAA+CC,cAAAA,IAAI,CAAC,KAAK,EAAnDC,oBAAyCD,YAAzCC,mBAAmBC,oBAAsBF,YAAtBE,mBACxBR,QAAQT,kDAAIA,CAAC,IAAI,CAAC,KAAK,EAAE,qBAAqB;gBAE/C,OAAOgB,kBACNC,gCACC,2DAAC,2BACA,2DAACX,4HAA6BA,EAAKG;YAIvC;;;WAZKI;EAA0BV,4CAASA;AAezCU,kBAAkB,WAAW,GAAG;AAEhCA,kBAAkB,SAAS,GAAG;IAC7B,mBAAmBZ,wDAAc;IACjC,mBAAmBA,wDAAc;IACjC,QAAQA,wDAAc;IACtB,YAAYA,wDAAc;AAC3B;AAEA,6DAAeF,kDAAIA,CAAC;IACnB,IAAIM,qDAAUA,CAACE,kGAAuB,EAAEK,YAAY,SAACM,SAASR;eAAa;YAC1E,mBAAmBQ,QAAQ,UAAU;YACrC,QAAQR,QAAQ,MAAM;QACvB;;IACA,IAAIN,qDAAUA,CAACG,kGAAuB,EAAEC,YAAY,SAACU,SAASR;eAAa;YAC1E,mBAAmBQ,QAAQ,UAAU;YACrC,YAAYR,QAAQ,UAAU;QAC/B;;CACA,EAAEG,kBAAkBA,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpEmB;AACmB;AAE5D,IAAMO,iBAAiB;IACtB,iBAAiBD,iFAAiB;IAClC,cAAc;IACd,SAAS;IACT,QAAQ;IACR,OAAO;AACR;AAEA,IAAME,6CAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACLP,KAAAA;mBAAAA,SAAAA;gBACC,qBAAO,2DAAC;oBAAI,OAAOM;;YACpB;;;WAHKC;EAAqClB,4CAASA;AAMpDkB,6BAA6B,WAAW,GAAG;AAE3C,6DAAeA,4BAA4BA,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqDuE;AAxE1E;AAC6B;AACyD;AAC1D;AAC8B;AAEnG,IAAMD,iBAAiB;IACtB,cAAc;AACf;AAEA,IAAMK,gBAAgB;IACrB,OAAON,8EAAc;IACrB,UAAU;IACV,YAAYG,wEAAQA;IACpB,aAAa;AACd;AAEA,IAAMI,mBAAmB;IACxB,YAAY;IACZ,SAAS;IACT,gBAAgB;IAChB,UAAU;IACV,WAAW;AACZ;AAEA,IAAMC,gBAAgB;AAEP,IAAMrB,8CAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACpBsB,KAAAA;mBAAAA,SAAAA;gBACC,IAAIC,gBAAgBC,OAAO,MAAM,CAAC,CAAC,GAAGJ,mBACrCK,cAAc,IAAI,CAAC,KAAK,CAAC,WAAW;gBAErC,IAAIA,eAAeA,YAAY,KAAK,CAAC,KAAK,IAAIA,YAAY,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE;oBAC5EF,cAAc,KAAK,GAAGE,YAAY,KAAK,CAAC,KAAK,CAAC,KAAK,GAAGJ;gBACvD;gBAEA,qBACC,2DAAC;oBAAI,OAAOE;iCACX,2DAAC;oBAAI,WAAWL,uIAAuB;iCACtC,2DAAC;oBAAK,WAAWA,2HAAW,GAAG;oBAAgB,OAAOC;qBAErDM;YAGL;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;oBAChD,qBAAO,2DAACX,2HAA4BA;gBACrC;gBAEA,OAAO;YACR;;;YAEAP,KAAAA;mBAAAA,SAAAA;gBACC,IAAIL,QAAQ,IAAI,CAAC,KAAK;gBAEtB,qBACC,2DAAC;oBAAI,OAAOW;oBAAgB,WAAWI,2IAA2B;oBAAE;mBACjE,IAAI,CAAC,cAAc,kBACrB,2DAACD,qFAAQA;oBACR,OAAOd,MAAM,KAAK;oBAClB,aAAaA,MAAM,WAAW;oBAC9B,aAAa,IAAI,CAAC,cAAc;oBAChC,cAAcA,MAAM,YAAY;oBAChC,OAAOA,MAAM,KAAK;;YAItB;;;WA1CoBH;EAAsCH,4CAASA;AA2CnE;AAEDG,8BAA8B,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxET;AACM;AACqB;AAC0B;AAExF,IAAM6B,aAAa;IAClB,UAAU;IACV,cAAc;AACf;AAEe,IAAMC,2BAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACpBC,KAAAA;mBAAAA,SAAAA;gBACC,IAAIC,KAAK,IAAI,EACZC,cAAc;oBACb,QAAQD,GAAG,KAAK,CAAC,MAAM;oBACvB,OAAOA,GAAG,KAAK,CAAC,KAAK;oBACrB,WAAWA,GAAG,KAAK,CAAC,MAAM;oBAC1B,UAAUA,GAAG,KAAK,CAAC,KAAK;oBACxB,WAAW;gBACZ;gBAED,qBACC,2DAACJ,gGAAiBA;oBAAC,OAAOK;mBACvBD,GAAG,KAAK,CAAC,QAAQ;YAGtB;;;YAEAE,KAAAA;mBAAAA,SAAAA;gBACC,IAAIF,KAAK,IAAI,EACZG,QAAQ,CAAC;gBAEV,IAAIH,GAAG,KAAK,CAAC,KAAK,EAAE;oBACnBG,MAAM,KAAK,GAAGH,GAAG,KAAK,CAAC,KAAK;gBAC7B;gBAEA,OAAOG;YACR;;;YAEA3B,KAAAA;mBAAAA,SAAAA;gBACC,IAAIwB,KAAK,IAAI;gBAEb,qBACC,2DAAC;oBAAI,OAAOA,GAAG,iBAAiB;iCAC/B,2DAACL,kFAAKA;oBAAC,OAAOE;oBAAY,MAAMG,GAAG,KAAK,CAAC,KAAK;oBAC5CA,GAAG,UAAU;YAGlB;;;WAtCoBF;EAAmBjC,4CAASA;AAuChD;AAEDiC,WAAW,SAAS,GAAG;IACtB,QAAQnC,qEAA2B;IACnC,OAAOA,2DAAmB,CAAC;QAACA,0DAAgB;QAAEA,0DAAgB;KAAC,EAAE,UAAU;IAC3E,OAAOA,2DAAmB,CAAC;QAACA,0DAAgB;QAAEA,0DAAgB;KAAC;AAChE;AAEAmC,WAAW,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmD8B;AA5GpB;AACM;AAEzC,IAAMhB,iBAAiB;IACrB,YAAY;IACZ,OAAO;IACP,SAAS;IACT,YAAY;IACZ,OAAO;AACR,GACAM,mBAAmB;IAClB,MAAM;IACN,YAAY;IACZ,aAAa;AACd,GACAgB,oBAAoB;IACnB,MAAM;IACN,YAAY;IACZ,YAAY;AACb,GACAC,aAAa;IACZ,MAAM;IACN,YAAY;IACZ,UAAU;IACV,cAAc;IACd,YAAY;AACb;AAEc,IAAMpB,yBAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACpBK,KAAAA;mBAAAA,SAAAA;gBACC,IAAIU,KAAK,IAAI,EACZM,UAAUN,GAAG,KAAK,CAAC,WAAW;gBAE/B,IAAIM,SAAS;oBACZ,qBACC,2DAAC;wBAAI,OAAOlB;uBACTkB;gBAGL;gBAEA,OAAO;YACR;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAIP,KAAK,IAAI,EACZM,UAAUN,GAAG,KAAK,CAAC,YAAY;gBAEhC,IAAIM,SAAS;oBACZ,qBACC,2DAAC;wBAAI,OAAOF;uBACTE;gBAGL;gBAEA,OAAO;YACR;;;YAEAE,KAAAA;mBAAAA,SAAAA;gBACC,IAAIR,KAAK,IAAI;gBAEb,IAAI,CAACA,GAAG,KAAK,CAAC,WAAW,EACxB,OAAO;gBAER,OAAO,MAAOA,GAAG,KAAK,CAAC,WAAW,CAAE,WAAW,KAAK;YACrD;;;YAEAS,KAAAA;mBAAAA,SAAAA;gBACC,IAAIT,KAAK,IAAI;gBACb,IAAIU;gBAEJ,IAAI,OAAOV,GAAG,KAAK,CAAC,KAAK,KAAK,UAC7BU,OAAOV,GAAG,KAAK,CAAC,KAAK;gBAEtB,IAAIA,GAAG,KAAK,CAAC,IAAI,EAChBU,OAAOV,GAAG,KAAK,CAAC,IAAI;gBAErB,qBACC,2DAAC;oBAAI,OAAOK;oBAAY,OAAOK;mBAC5BV,GAAG,cAAc,IACjBA,GAAG,KAAK,CAAC,KAAK;YAGnB;;;YAEAxB,KAAAA;mBAAAA,SAAAA;gBACC,IAAIwB,KAAK,IAAI,EACZG,QAAQX,OAAO,MAAM,CAAC,CAAC,GAAGV,gBAAgBkB,GAAG,KAAK,CAAC,KAAK;gBAEzD,qBACC,2DAAC;oBAAI,OAAOG;mBACTH,GAAG,cAAc,IACjBA,GAAG,QAAQ,IACXA,GAAG,eAAe;YAGvB;;;WArEoBf;EAAiBpB,4CAASA;AAsE9C;AAEDoB,SAAS,SAAS,GAAG;IACpB,aAAatB,wDAAc;IAC3B,cAAcA,wDAAc;IAC5B,OAAOA,2DAAmB,CAAC;QAACA,0DAAgB;QAAEA,wDAAc;KAAC,EAAE,UAAU;IACzE,MAAMA,0DAAgB;IACtB,aAAaA,0DAAgB;AAC9B;AAEAsB,SAAS,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3De;AAjDN;AACG;AACU;AACuB;AAC6B;AAEjG,IAAMH,iBAAiB;IACtB,UAAU;IACV,YAAY;AACb;AAEA,IAAMD,SAAS;IACd,SAAS;IACT,SAAS;IACT,OAAO;IACP,UAAU;AACX;IAEckC;AAAd,IAAMC,SAAQD,WACb,iBADaA,MACZD,yFAAa,EAAG,YACjB,iBAFaC,MAEZD,4FAAgB,EAAG,YACpB,iBAHaC,MAGZD,4FAAgB,EAAG,YACpB,iBAJaC,MAIZD,4FAAgB,EAAG,YACpB,iBALaC,MAKZD,yFAAa,EAAG,YACjB,iBANaC,MAMZD,6FAAiB,EAAG,UACrB,iBAPaC,MAOZD,4FAAgB,EAAG,UACpB,iBARaC,MAQZD,yFAAa,EAAG,aACjB,iBATaC,MASZD,6FAAiB,EAAG,aACrB,iBAVaC,MAUZD,yFAAa,EAAG,aAVJC;AAad,SAASE,iBAAiBC,KAAe;QAAbC,OAAFD,MAAEC,MAAMhB,QAARe,MAAQf;IACjC,IAAMiB,iBAAiBJ,KAAK,CAACG,KAAK;IAClC,IAAME,YAAYR,8CAAOA,CAAC;eAAO,uDAC7B/B;YACH,OAAOD,MAAM,CAACuC,eAAe;YAC1BjB;OACA;QAACiB;QAAgBjB;KAAM;IAE3B,qBAAO,2DAAC;QAAE,OAAOkB;QAAW,OAAOF;QAAM,WAAY,GAAwBC,OAAtBlC,yHAAW,EAAC,YAAyB,OAAfkC;;AAC9E;AAEAH,iBAAiB,SAAS,GAAG;IAC5B,MAAMtD,uDAAe,CAACgD,oDAAMA,CAACG,oFAAQA,GAAG,UAAU;IAClD,OAAOnD,0DAAgB;AACxB;AAEAsD,iBAAiB,WAAW,GAAG;AAE/B,0EAAeL,2CAAIA,CAACK,iBAAiBA,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjDR;AACW;AACE;AAC0G;AAC1E;AAE3E,wBAAwB,GACxB,IAAMM,aAAa;IAClB,kBAAQpD,KAAK,EAAEC,OAAO;QACrB,OAAOA,QAAQ,MAAM,CAAC;YAAE,SAAS;QAAK;IACvC;IACA,eAAKD,KAAK,EAAEC,OAAO;QAClBD,MAAM,MAAM,CAACA,MAAM,IAAI,EAAEC,QAAQ,OAAO,IAAIA,QAAQ,aAAa;IAClE;AACD;AAEA,IAAMoD,mCAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACLzB,KAAAA;mBAAAA,SAAAA;gBACC,IAAIC,KAAK,IAAI,EACZ7B,QAAQT,kDAAIA,CAACsC,GAAG,KAAK,EAAE,qBAAqB,QAAQ;gBAErD,qBACC,2DAAC,2BACA,2DAACsB,+IAA8BA,EAAKnD;YAGvC;;;YAEAK,KAAAA;mBAAAA,SAAAA;gBACC,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU;YACpD;;;WAdKgD;EAA2B3D,4CAASA;AAiB1C2D,mBAAmB,WAAW,GAAG;AAEjC,6DAAe,IAAIzD,qDAAUA,CAACE,kGAAuB,EAAEsD,YAAY,SAAC3C,SAASR;IAC5E,OAAO;QACN,mBAAmBQ,QAAQ,UAAU;QACrC,eAAeR,QAAQ,MAAM,CAAC;YAAE,SAAS;QAAK;QAC9C,QAAQA,QAAQ,MAAM;QACtB,SAASA,QAAQ,OAAO;IACzB;AACD,GAAGoD,mBAAmBA,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1CO;AACK;AACgB;AAC4E;AACtD;AAEzE,IAAME,eAAe;IACpB,QAAQ;AACT;AAEe,IAAMJ,+CAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACpBK,KAAAA;mBAAAA,SAAAA;gBACC,IAAIxD,QAAQ,IAAI,CAAC,KAAK;gBAEtB,IAAIsD,iDAAc,CAACtD,MAAM,QAAQ,MAAM,KAAK,CAACA,MAAM,MAAM,EAAE;oBAC1D,OAAOA,MAAM,WAAW;gBACzB;gBAEA,OAAOA,MAAM,QAAQ;YACtB;;;YAEAK,KAAAA;mBAAAA,SAAAA;gBACC,IAAIwB,KAAK,IAAI,EACZ7B,QAAQT,kDAAIA,CAACsC,GAAG,KAAK,EAAE,UAAU,iBAAiB;gBAEnD,qBACC,2DAACF,uFAAUA,EAAK3B,OACb6B,GAAG,cAAc,IACjBA,GAAG,KAAK,CAAC,aAAa,iBAAG,2DAACjB,2HAA4BA,UAAM,MAC5DiB,GAAG,KAAK,CAAC,MAAM,IAAI,CAACA,GAAG,KAAK,CAAC,aAAa,iBAAG,2DAAC;oBAAI,OAAO0B;qBAAmB;YAGjF;;;WAtBoBJ;EAAuCzD,4CAASA;AAuBpE;AAEDyD,+BAA+B,SAAS,GAAG;IAC1C,eAAe3D,wDAAc;IAC7B,QAAQA,wDAAc;IACtB,aAAaA,wDAAc;AAC5B;AAEA2D,+BAA+B,YAAY,GAAG;IAC7C,eAAe;IACf,QAAQ;AACT;AAEAA,+BAA+B,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9CV;AACU;AACM;AACqB;AACkC;AACtB;AACE;AAEvE,IAAMY,qCAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACpBC,KAAAA;mBAAAA,SAAAA;gBACC,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,OAAO;YACb;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAI,CAAC,QAAQ;YACd;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAqF5D,cAAAA,IAAI,CAAC,KAAK,EAAvF6D,cAA6E7D,YAA7E6D,aAAaC,YAAgE9D,YAAhE8D,WAAWC,mBAAqD/D,YAArD+D,kBAAkBC,eAAmChE,YAAnCgE,cAAcC,mBAAqBjE,YAArBiE;gBAEhEV,2GAAmB,CAAC;oBACnB,SAASO;oBACT,KAAKD;oBACL,MAAM;wBACL,qBAAqBG;wBACrBD,kBAAAA;wBAAkBE,kBAAAA;oBACnB;gBACD;YACD;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAwClE,cAAAA,IAAI,CAAC,KAAK,EAA1C6D,cAAgC7D,YAAhC6D,aAAaM,OAAmBnE,YAAnBmE,MAAMC,WAAapE,YAAboE;gBAC3B,IAAwCC,kCAAAA,wGAA8B,CAACF,MAAMC,WAArEE,iBAAgCD,gCAAhCC,gBAAgBC,cAAgBF,gCAAhBE;gBACxB,IAAMC,SAASnB,mGAAyB,CAAC,IAAI,EAAE;oBAACQ;oBAAa;wBAAES,gBAAAA;wBAAgBC,aAAAA;oBAAY;iBAAE;gBAE7FnB,iEAAQA,CAACoB;YACV;;;YAEAzE,KAAAA;mBAAAA,SAAAA;gBACC,qBAAO,2DAACuD,oHAAcA;oBAAC,KAAK,IAAI,CAAC,KAAK,CAAC,WAAW;;YACnD;;;WAjCoBG;EAA6BN,gDAAaA;AAkC9D;AAEDM,qBAAqB,SAAS,GAAG;IAChC,aAAavE,qEAA2B;IACxC,MAAMA,qEAA2B;IACjC,WAAWA,mEAAyB;IACpC,kBAAkBA,mEAAyB;IAC3C,cAAcA,mEAAyB;IACvC,kBAAkBA,mEAAyB;IAC3C,UAAUA,qEAA2B;AACtC;AAEAuE,qBAAqB,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4B0B;AAAA;AAAA;AAAA;AAlF1B;AACU;AACG;AACtB;AAC4D;AACd;AAC0D;AACtE;AACM;AACkC;AAChE;AAEpC,IAAMuB,kBAAkB,SAACC,OAAOvF;IAC/B,IAAMf,MAAMe,MAAM,GAAG,CAAC,QAAQ;IAC9B,IAAMd,OAAOqG,MAAM,YAAY,CAAC,KAAK,CAAC;QAAC;QAAYtG;KAAI;IACvD,IAAI2F,iBAAiB,EAAE,EACtBC,cAAc,EAAE;IAEjB,IAAI3F,MAAM;QACT0F,iBAAiB1F,KAAK,GAAG,CAACiG,iGAAoB,EAAE,IAAI;QACpDN,cAAc3F,KAAK,GAAG,CAACiG,8FAAiB,EAAE,IAAI;IAC/C;IAEA,OAAO;QAAElG,KAAAA;QAAK2F,gBAAAA;QAAgBC,aAAAA;IAAY;AAC3C;AAEA,IAAMW,qBAAqB,SAAC9B,UAAU1D;IACrC,OAAO;QACN,qBAAWb,IAAI;YACduE,SAASC,gGAAsB,CAAC3D,MAAM,GAAG,EAAEb,KAAK,GAAG;QACpD;QACA,sBAAYA,IAAI;YACfuE,SAASC,iGAAuB,CAAC3D,MAAM,GAAG,EAAEb,KAAK,GAAG;QACrD;QACA,qBAAWA,IAAI;YACduE,SAASC,gGAAsB,CAAC3D,MAAM,GAAG,EAAEb;QAC5C;QACA,iBAAOsG,IAAI,EAAEtG,IAAI,EAAEuG,SAAS;YAC3B,IAAIZ,SAAS;YAEb,IAAIW,SAASN,iGAAoB,EAAE;gBAClCzB,SAASC,gGAAsB,CAAC3D,MAAM,GAAG,EAAEb,KAAK,IAAI;gBACpD;YACD;YAEA,IAAIsG,SAAStG,KAAK,IAAI,EAAE;gBACvB2F,SAAS;YACV;YAEApB,SAASC,qFAAW,CAACmB,OAAO,CAAC9E,MAAM,GAAG,EAAEb,KAAK,IAAI,EAAEuG;QACpD;IACD;AACD;AAEA,IAAMC,wBAAwBV,yFAAyBA,CACtDxE,oDAAOA,CAAC6E,iBAAiBE,oBAAoB,MAAM;IAAE,YAAY;AAAK,GAAGN,gIAA0BA;AAGpG,IAAMtB,+BAAN;;cAAMA;aAAAA,eAEO5D,KAAK;gCAFZ4D;;gBAGJ,kBAHIA;YAGE5D;;QAEN,MAAK,SAAS,GAAGgF,yDAAkB;;;kBAL/BpB;;YAQLvD,KAAAA;mBAAAA,SAAAA;gBACC,qBACC,2DAAC0E,iDAAQA;oBAAC,OAAOK,kFAASA;iCACzB,2DAACO;oBAAsB,IAAI,IAAI,CAAC,SAAS;oBAAE,KAAK;mBAAoB,IAAI,CAAC,KAAK;YAGjF;;;WAdK/B;EAAuBH,gDAAaA;AAiB1CG,eAAe,SAAS,GAAG;IAC1B,KAAKpE,4DAAmB,CAAC;QAACA,2DAAgB;QAAEA,2DAAgB;KAAC,EAAE,UAAU;IACzE,MAAMA,2DAAgB;AACvB;AAEAoE,eAAe,WAAW,GAAG;AAE7B,6DAAe,IAAIyB,oGAAyBA,CAACzB,eAAeA,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkKF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AApPxB;AACU;AACuB;AACyB;AACV;AACmB;AACuB;AACN;AACA;AACvB;AACpC;AACQ;AACc;AAElF,IAAMwC,aAAa;IAClB,SAAS;AACV;AAEA,IAAMC,eAAe;IACpB,YAAY,eAAe3F,kFAAiB;IAC5C,QAAQ;IACR,OAAO;AACR;AAEA,IAAM4F,gBAAgB;IACrB,WAAW;IACX,OAAO;AACR;AAEA,IAAMC,iBAAiB;IACtB,OAAO7F,8EAAa;IACpB,YAAYG,yEAAQA;IACpB,WAAW;IACX,OAAOkF,+EAAc;AACtB;AAEA,IAAM9D,oBAAoB;IACzB,OAAOvB,+EAAc;IACrB,SAAS;IACT,UAAUoF,+EAAc;IACxB,WAAW;IACX,OAAO;AACR;AAEA,IAAMU,gBAAgB;IACrB,MAAM;IACN,aAAa;AACd;AAEA,IAAMC,iBAAiB;IACtB,QAAQ;AACT;AAEA,IAAMC,iBAAiB;IACtB,YAAY;IACZ,OAAOhG,uFAAsB;IAC7B,SAAS;IACT,UAAU;IACV,SAAS;IACT,QAAQ;IACR,OAAO;AACR;AAEA,IAAMiG,qBAAqB;IAC1B,SAAS;IACT,WAAW;IACX,OAAO;AACR;AAEA,IAAMzB,2CAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACL0B,KAAAA;mBAAAA,SAAAA,YAAY5D,IAAI;gBACf,qBACC,2DAAC;oBAAI,OAAOsD;iCACX,2DAACxD,+GAAgBA;oBAAC,MAAME;;YAG3B;;;YAEA6D,KAAAA;mBAAAA,SAAAA,aAAaC,KAAK;gBACjB,qBACC,2DAAC;oBAAI,OAAOP;mBACTO;YAGL;;;YAEA1E,KAAAA;mBAAAA,SAAAA,gBAAgBjD,IAAI;gBACnB,IAAqDmB,cAAAA,IAAI,CAAC,KAAK,EAAvDrB,MAA6CqB,YAA7CrB,KAAK8H,aAAwCzG,YAAxCyG,YAAYC,aAA4B1G,YAA5B0G,YAAYC,cAAgB3G,YAAhB2G;gBAErC,qBACC,2DAAC;oBAAI,OAAOhF;iCACX,2DAAC;oBAAI,OAAOuE;iCACX,2DAACP,gIAAgBA;oBAChB,aAAahH;oBACb,SAASE,KAAK,GAAG;oBACjB,YAAY6H,WAAW,IAAI,CAAC,IAAI,EAAE7H;oBAClC,aAAa8H,YAAY,IAAI,CAAC,IAAI,EAAE9H;mCAGtC,2DAAC;oBAAI,WAAW4B,iIAAmB;iCAClC,2DAAC6E,wFAAKA;oBACL,SAASmB,WAAW,IAAI,CAAC,IAAI,EAAE5H;oBAC/B,OAAOsH;oBACP,MAAM;oBACN,OAAO;;YAKZ;;;YAEAS,KAAAA;mBAAAA,SAAAA;gBACC,IAAMrF,KAAK,IAAI;gBACf,wBAAwB,GACxB,IAA6CvB,cAAAA,IAAI,CAAC,KAAK,+BAAVA,YAArCsE,gBAAAA,yDAAiB,EAAE,+BAAE3F,MAAgBqB,YAAhBrB,KAAKkI,SAAW7G,YAAX6G;gBAClC,IAAIC,QAAQxC,eAAe,KAAK;gBAEhC,sBAAsB,GACtB,IAAI,CAACwC,MAAM,MAAM,EAChB,OAAO;gBAERA,MAAM,IAAI,CAAClB,oGAAyB;gBAEpC,OAAOkB,MAAM,GAAG,CAACjI,SAAAA;oBAChB,qBACC,2DAAC6G,gIAAgBA;wBAAC,KAAK7G,KAAK,GAAG;wBAC9B,aAAaF;wBACb,aAAa4C,GAAG,WAAW,CAAC1C,KAAK,QAAQ;wBACzC,QAAQgI;wBACR,OAAOhI,KAAK,KAAK;wBACjB,aAAa0C,GAAG,cAAc,CAAC1C;wBAC/B,MAAMgG,iGAAoB;wBAC1B,KAAKhG,KAAK,GAAG;wBACb;;gBAGH;YACD;;;YAEAkI,KAAAA;mBAAAA,SAAAA;gBACC,IAAMxF,KAAK,IAAI;gBACf,wBAAwB,GACxB,IAA0CyF,YAAAA,GAAG,KAAK,EAA1CrI,MAAkCqI,UAAlCrI,KAAKkI,SAA6BG,UAA7BH,gCAA6BG,UAArBzC,aAAAA,iDAAc,EAAE;gBACrC,IAAIuC,QAAQvC,YAAY,KAAK;gBAE7B,OAAOuC,MAAM,GAAG,CAAC,SAACjI,MAAMoI;oBACvB,qBACC,2DAAC;wBAAI,WAAWxG,kIAAoB;wBAAE,KAAK5B,KAAK,GAAG;qCAClD,2DAAC6G,gIAAgBA;wBAChB,aAAa/G;wBACb,aAAa4C,GAAG,YAAY,CAAC0F,IAAI;wBACjC,QAAQJ;wBACR,cAActF,GAAG,eAAe,CAAC1C;wBACjC,OAAOA,KAAK,KAAK;wBACjB,aAAa0C,GAAG,cAAc,CAAC1C;wBAC/B,MAAMgG,8FAAiB;wBACvB,KAAKhG,KAAK,GAAG;;gBAIjB;YACD;;;YAEAkD,KAAAA;mBAAAA,SAAAA,eAAelD,IAAI;gBAClB,IAAQqI,WAAa,IAAI,CAAC,KAAK,CAAvBA;gBAER,sBAAsB,GACtB,IAAIrI,KAAK,mBAAmB,EAC3B,OAAOA,KAAK,mBAAmB;gBAEhC,sBAAsB,GACtB,IAAIA,KAAK,OAAO,EACf,OAAOqI,SAASrB,iGAAeA,CAAChH,KAAK,OAAO;gBAE7C,OAAO;YACR;;;YAEAsI,KAAAA;mBAAAA,SAAAA;gBACC,IAAML,QAAS,qBAAG,IAAI,CAAC,KAAK,CAAC,cAAc,SAAE,qBAAG,IAAI,CAAC,KAAK,CAAC,WAAW;gBAEtE,OAAOA,SAASA,MAAM,MAAM,GAAG;YAChC;;;YAEAM,KAAAA;mBAAAA,SAAAA;gBACC,IAAMC,qBAAqBtG,OAAO,MAAM,CAAC;oBAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM;gBAAC,GAAGgF;gBAExE,qBACC,2DAAC;oBAAI,OAAOD;mBACT,IAAI,CAAC,OAAO,CAAC,QAAQjB,iGAAoB,EAAE,IAAI,CAAC,iBAAiB,mBAEnE,2DAAC;oBAAI,OAAOwC;oBAEV,IAAI,CAAC,OAAO,CAAC,QAAQxC,8FAAiB,EAAE,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC;YAGtF;;;YAEAyC,KAAAA;mBAAAA,SAAAA,QAAQC,IAAI,EAAEpC,IAAI,EAAE2B,KAAK;oBAAEU,cAAAA,iEAAc;gBACxC,IAAqCxH,cAAAA,IAAI,CAAC,KAAK,EAAvCkH,WAA6BlH,YAA7BkH,UAAUO,SAAmBzH,YAAnByH,QAAQZ,SAAW7G,YAAX6G;gBAE1B,qBACC,2DAAC9D,oIAAkBA;oBAClB,QAAQ0E;oBACR,MAAMtC;oBACN,QAAQ0B;oBACR,aAAaW;oBACb,OAAON,SAASQ,OAAOH;oBACvB,OAAO;mBAELT;YAGL;;;YAEAa,KAAAA;mBAAAA,SAAAA;oBAAaJ,OAAAA,iEAAO;gBACnB,qBACC,2DAAC;oBAAI,OAAOnB;iCACX,2DAAC;oBAAK,OAAOC;mBAAsB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAACqB,OAAOH;YAGjE;;;YAEAxH,KAAAA;mBAAAA,SAAAA;gBACC,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;YAC7D;;;WA3JK6E;EAAmCzB,gDAAaA;AA8JtDyB,2BAA2B,SAAS,GAAG;IACtC,QAAQ1F,2DAAgB;IACxB,KAAKA,4DAAmB,CAAC;QAACA,2DAAgB;QAAEA,2DAAgB;KAAC,EAAE,UAAU;IACzE,gBAAgBA,qEAA0B;IAC1C,aAAaA,qEAA0B;IACvC,YAAYA,oEAAyB;IACrC,aAAaA,oEAAyB;IACtC,YAAYA,oEAAyB;IACrC,QAAQA,oEAAyB;AAClC;AAEA0F,2BAA2B,YAAY,GAAG;IACzC,QAAQ;AACT;AAEAA,2BAA2B,WAAW,GAAG;AAEzC,6DAAeW,qGAAeA,CAACX,2BAA2BA,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7LV;AAAA;AAAA;AAAA;AAAA;AAAA;AAvDnB;AACK;AACM;AACO;AACsC;AACO;AACkD;AACnF;AACM;AAElE,IAAMI,kBAAkB,SAACC,OAAOvF;IAC/B,IAAImI,cAAcnI,MAAM,WAAW,CAAC,QAAQ,IAC3CX,UAAUW,MAAM,OAAO,CAAC,QAAQ,IAChCb,OAAOoG,MAAM,YAAY,CACvB,KAAK,CAAC;QAAC;QAAY4C;QAAahD,8FAAiB;KAAC,EAClD,IAAI,CAACoC,SAAAA;QACL,OAAOA,EAAE,GAAG,CAAC,WAAWlI;IACzB,IACD+I,QAAQpI,MAAM,QAAQ,CAAC,WACvBqI;IAED,IAAIlJ,MAAM;QACTkJ,YAAYlJ,KAAK,GAAG,CAAC;IACtB;IAEA,IAAIkJ,cAAclD,yFAAY,EAAE;QAC/BiD,QAAQpI,MAAM,QAAQ,CAAC;IACxB;IAEA,OAAOqB,OAAO,MAAM,CAAC9B,kDAAIA,CAACS,OAAO,eAAe,YAAY;QAAEoI,OAAAA;IAAM;AACrE;AAEA,IAAME,0BAA0BrD,yFAAyBA,CACxDxE,oDAAOA,CAAC6E,iBAAiB;WAAO,CAAC;GAAI,MAAM;IAAE,YAAY;AAAK,GAAG4C,4IAA4BA;AAG9F,IAAMjC,iCAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACL5F,KAAAA;mBAAAA,SAAAA;gBACC,IAAIwB,KAAK,IAAI;gBAEb,qBACC,2DAACkD,iDAAQA;oBAAC,OAAOK,kFAASA;iCACzB,2DAACkD;oBAAwB,KAAK;mBAAoBzG,GAAG,KAAK;YAG7D;;;WATKoE;EAAyBvG,4CAASA;AAYxCuG,iBAAiB,SAAS,GAAG;IAC5B,aAAazG,4DAAmB,CAAC;QAACA,2DAAgB;QAAEA,2DAAgB;KAAC,EAAE,UAAU;IACjF,SAASA,4DAAmB,CAAC;QAACA,2DAAgB;QAAEA,2DAAgB;KAAC,EAAE,UAAU;AAC9E;AAEAyG,iBAAiB,WAAW,GAAG;AAE/B,6DAAeJ,sGAAeA,CAACI,iBAAiBA,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvDd;AACU;AACgC;AACA;AAE9D,IAAMiC,6CAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACpB7H,KAAAA;mBAAAA,SAAAA;gBACC,IAAqDC,cAAAA,IAAI,CAAC,KAAK,EAAvDkH,WAA6ClH,YAA7CkH,UAAUR,aAAmC1G,YAAnC0G,YAAYC,cAAuB3G,YAAvB2G,aAAamB,QAAU9H,YAAV8H;gBAE3C,qBACC,2DAACI,yFAAYA;oBAAC,OAAOJ;iCACpB,2DAACG,8FAAQA;oBAAC,SAASvB;mBAAcQ,SAAS,0BAC1C,2DAACe,8FAAQA;oBAAC,SAAStB;mBAAeO,SAAS;YAG9C;;;WAVoBU;EAAqCzE,gDAAaA;AAWtE;AAEDyE,6BAA6B,SAAS,GAAG;IACxC,OAAO1I,qEAA2B;IAClC,YAAYA,mEAAyB;IACrC,aAAaA,mEAAyB;AACvC;AAEA0I,6BAA6B,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqB8D;AAAA;AAAA;AAAA;AAAA;AA7ChE;AACO;AACsC;AACyD;AAC7E;AAElE,IAAM5C,kBAAkB,SAACC,OAAOvF;IAC/B,IAAImI,cAAcnI,MAAM,WAAW,CAAC,QAAQ,IAC3C0I,MAAM1I,MAAM,GAAG,EACfgD,OAAOhD,MAAM,IAAI;IAElB,IAAIb,OAAOoG,MAAM,YAAY,CAC1B,KAAK,CAAC;QAAC;QAAY4C;QAAanF;KAAK,EACrC,IAAI,CAACuE,SAAAA;QACL,OAAOA,EAAE,GAAG,CAAC,WAAWmB;IACzB,IACDC,UAAU;QACT,MAAM;IACP;IAED,IAAIxJ,MAAM;QACTwJ,QAAQ,IAAI,GAAGxJ,KAAK,IAAI;IACzB;IAEA,OAAOwJ;AACR;AAEA,IAAMC,0BAA0B3D,yFAAyBA,CACxDxE,oDAAOA,CAAC6E,iBAAiB;WAAO,CAAC;GAAI,MAAM;IAAE,YAAY;AAAK,GAAGmD,2IAA4BA;AAG9F,IAAMzC,iCAAN;;cAAMA;aAAAA;gCAAAA;iCAAAA;;kBAAAA;;YACL3F,KAAAA;mBAAAA,SAAAA;gBACC,IAAIwB,KAAK,IAAI;gBAEb,qBACC,2DAACkD,iDAAQA;oBAAC,OAAOK,iFAASA;iCACzB,2DAACwD;oBAAwB,KAAK;mBAAoB/G,GAAG,KAAK;YAG7D;;;WATKmE;EAAyBtG,4CAASA;AAYxC,6DAAesG,gBAAgBA,EAAC;AAEhCA,iBAAiB,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsBa;AAnET;AACM;AACgE;AAC7C;AACyB;AAErF,IAAMrF,iBAAiB;IACtB,QAAQ;IACR,SAAS;AACV;AAEA,IAAM8H,6CAAN;;cAAMA;aAAAA,6BACOzI,KAAK;gCADZyI;;gBAEJ,kBAFIA;YAEEzI;;QAEN,MAAK,cAAc,GAAG,MAAK,cAAc,CAAC,IAAI;QAC9C,MAAK,eAAe,GAAG,MAAK,eAAe,CAAC,IAAI;;;kBAL5CyI;;YAQLK,KAAAA;mBAAAA,SAAAA;gBACC,IAAI9I,QAAQ,IAAI,CAAC,KAAK,EACrByF,OAAON,6FAAiB;gBAEzB,IAAI,CAACnF,MAAM,iBAAiB,EAC3B;gBAEDA,MAAM,MAAM,CAACyF,MAAMzF,OAAO;YAC3B;;;YAEA,wBAAwB,GACxB+I,KAAAA;mBAAAA,SAAAA,gBAAgBC,SAAS;gBACxB,wBAAwB,GACxBC,EAAE,MAAMD,WAAW,OAAO,CAAC;YAC5B;;;YAEA3I,KAAAA;mBAAAA,SAAAA;gBACC,IAAIwB,KAAK,IAAI;gBAEb,qBACC,2DAAC;oBAAI,eAAeA,GAAG,cAAc;iCACpC,2DAACgH,+FAAQA,QAEPK,SAAAA;yCACC,2DAAC9I,gHAAiBA,0CACbyB,GAAG,KAAK;wBACZ,aAAaA,GAAG,eAAe,CAAC,IAAI,CAACA,IAAIqH,QAAQ,SAAS;wBAC1D,OAAOvI;;;YAOd;;;WA1CK8H;EAAqC/I,4CAASA;AA6CpD+I,6BAA6B,WAAW,GAAG;AAE3CA,6BAA6B,SAAS,GAAG;IACxC,aAAajJ,2DAAmB,CAAC;QAACA,0DAAgB;QAAEA,0DAAgB;KAAC,EAAE,UAAU;IACjF,mBAAmBA,wDAAc;AAClC;AAEAiJ,6BAA6B,YAAY,GAAG;IAC3C,mBAAmB;AACpB;AAEA,6DAAeA,4BAA4BA,EAAC;;;;;;;;;;;;;;;;ACnEd;AACqB;AACqB;AACU;AAElF,6DAAe;IACd,OAAO,CAAC;IAERW,kBAAkB,SAAlBA,iBAA2BpG,IAAI;QAC9B,IAAMqG,UAAU;YACf,MAAM1F,qFAAWA;QAClB;QAEA,OAAO0F,OAAO,CAACrG,KAAK;IACrB;IAEAsG,QAAQ,SAARA,OAAiBtG,IAAI,EAAEuG,OAAM,EAAEC,MAAM;QACpC,IAAIH,UAAU,IAAI,CAAC,gBAAgB,CAACrG,OACnC8B,SAASuE,OAAO,CAACE,QAAO,CAAC,KAAK,CAAC,IAAI,EAAEC;QAEtC9F,iEAAQA,CAACoB;IACV;IAEA2E,iBAAiB,SAAjBA,gBAA0BzG,IAAI,EAAEwG,MAAM;QACrCL,iGAAS,CAACnG,KAAK,CAAC,KAAK,CAAC,IAAI,EAAEwG;IAC7B;IAEAE,aAAa,SAAbA,YAAsBC,QAAQ;QAC7B,IAAMC,QAAQ,IAAI,CAAC,QAAQ,CAACD;QAE5B,IAAIC,MAAM,SAAS,EAClBA,MAAM,SAAS,CAAC,OAAO,CAAC,OAAO,OAAO;aAEtCA,MAAM,OAAO,CAAC,OAAO,OAAO;IAC9B;IAEA1F,UAAU,SAAVA,SAAmB0F,KAAK;QACvB,IAAIA,MAAM,aAAa,EAAE;YACxB,IAAI,CAAC,KAAK,GAAGA;YACb;QACD;QAEA,IAAI,CAAC,KAAK,CAACA,MAAM,GAAG,CAAC,GAAGrK,kDAAIA,CAACqK,OAAO;IACrC;IAEAC,UAAU,SAAVA,SAAmBC,EAAE;QACpB,wBAAwB,GACxB,IAAI,CAACA,MAAM,CAAC,IAAI,CAAC,KAAK,CAACA,GAAG,EACzB,OAAO,IAAI,CAAC,KAAK;QAElB,wBAAwB,GACxB,OAAO,IAAI,CAAC,KAAK,CAACA,GAAG;IACtB;AACD,CAAC,EAAC;;;;;;;;;;ACrDF,6DAAe;IACdtF,SAAS,SAATA,QAAkBtF,IAAI;QACrB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAClB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAACA;IAC9B;IAEAqF,kBAAkB,SAAlBA,iBAA2BuC,KAAK,EAAEuB,SAAS,EAAEsB,QAAQ;QACpD,IAAMC,QAAQ,IAAI,CAAC,QAAQ,CAACD;QAE5BC,MAAM,IAAI,CAAC,gBAAgB,CAAC9C,OAAOuB;QAEnC,IAAI,CAAC,WAAW,CAACsB;IAClB;IAEAtF,kBAAkB,SAAlBA,iBAA2BlF,IAAI,EAAE4K,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEN,QAAQ;QAC/E,IAAMC,QAAQ,IAAI,CAAC,QAAQ,CAACD;QAE5BC,MAAM,IAAI,CAAC,gBAAgB,CAACzK,MAAM4K,eAAeC,YAAYC;QAE7D,IAAI,CAAC,WAAW,CAACN;IAClB;IAEAO,qBAAqB,SAArBA,oBAA8BC,QAAQ,EAAEC,QAAQ,EAAEjL,IAAI,EAAEwK,QAAQ;QAC/D,IAAMC,QAAQ,IAAI,CAAC,QAAQ,CAACD;QAC5B,IAAIlF,OAAOmF,MAAM,IAAI;QAErB,sBAAsB,GACtB,IAAInF,KAAK,mBAAmB,EAAE;YAC7BA,KAAK,mBAAmB,CAACtF,MAAMiL;QAChC,OAAO;YACN3F,KAAK,cAAc,CAAC0F;YACpB1F,KAAK,WAAW,CAACtF,MAAMiL;QACxB;QAEA,IAAI,CAAC,WAAW,CAACT;IAClB;AACD,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4F6D;AAAA;AAhI5B;AACuD;AACtB;AACS;AACZ;AACQ;AACb;AACA;AAE5D,IAAMhJ,iBAAiB;IACtB,QAAQ;IACR,SAAS;AACV;AAEA,IAAMgK,aAAa;IAClB,MAAM;IACN,WAAW;AACZ;AAEA,IAAMC,mBAAmB;AAEV,IAAMpC,6BAAN;;cAAMA;aAAAA,aAERxI,KAAK;gCAFGwI;;gBAGnB,kBAHmBA;YAGbxI;;QAEN,MAAK,KAAK,GAAG;YACZ,aAAa;QACd;QAEA,MAAK,mBAAmB,GAAG,MAAK,mBAAmB,CAAC,IAAI;QACxD,MAAK,WAAW,GAAG,MAAK,WAAW,CAAC,IAAI;QACxC,MAAK,WAAW,GAAG,MAAK,WAAW,CAAC,IAAI;QACxC,MAAK,OAAO,iBAAGsK,gDAASA;;;kBAZL9B;;YAepBxE,KAAAA;mBAAAA,SAAAA;gBACC,IAAI,CAAC,oBAAoB;YAC1B;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAI,CAAC,oBAAoB;YAC1B;;;YAEA4G,KAAAA;mBAAAA,SAAAA;gBACC,IAAI,CAAC,uBAAuB;YAC7B;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAMjJ,KAAK,IAAI;gBAEf,OAAOyB,+CAAY,CAACzB,GAAG,KAAK,CAAC,QAAQ,EAAEkJ,SAAAA;oBACtC,qBAAOV,mDAAYA,CAACU,QAAQ;wBAC3B;4BACCA,OAAO,KAAK,CAAC,OAAO;4BACpBlJ,GAAG,WAAW;wBACf;oBACD;gBACD;YACD;;;YAEAmJ,KAAAA;mBAAAA,SAAAA;gBACC,qBACC,2DAACP,2FAAOA;oBACP,iBAAiB,IAAI,CAAC,mBAAmB;oBACzC,aAAa/J,gFAAgB;oBAC7B,cAAc,IAAI,CAAC,WAAW;oBAC9B,OAAO;oBACP;mBAEE,IAAI,CAAC,UAAU;YAGpB;;;YAEAuK,KAAAA;mBAAAA,SAAAA;gBACC,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO;YAC5C;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAI,CAAC,QAAQ,CAAC;oBAAE,aAAa;gBAAM;YACpC;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAI,CAAC,QAAQ,CAAC;oBAAE,aAAa;gBAAK;YACnC;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAI,CAAC,uBAAuB;gBAE5BnC,EAAE2B,kBAAkB,EAAE,CAAC,aAAa,IAAI,CAAC,WAAW;YACrD;;;YAEAS,KAAAA;mBAAAA,SAAAA;gBACCpC,EAAE2B,kBAAkB,GAAG,CAAC,aAAa,IAAI,CAAC,WAAW;YACtD;;;YAEAvK,KAAAA;mBAAAA,SAAAA;gBACC,qBACC,2DAACkK,2CAAQA,sBACR,2DAAC;oBAAI,OAAO5J;oBAAgB,SAAS,IAAI,CAAC,WAAW;iCACpD,2DAAC;oBAAI,OAAOgK;oBAAY,KAAK,IAAI,CAAC,OAAO;mBACtC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAEnB,2DAAC,2BACA,2DAAC/E,wFAAKA;oBAAC,MAAM;oBAAqB,OAAO;oCAG3C,2DAAC4E,mFAAOA;oBACP,gBAAgB;oBAChB,SAAS;oBACT,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW;oBAC5B,QAAQ,IAAI,CAAC,WAAW;oBACxB,QAAQ,IAAI,CAAC,mBAAmB;oBAChC;mBAEE,IAAI,CAAC,UAAU;YAIrB;;;WAnGoBhC;EAAqB/E,gDAAaA;AAoGtD;AAED+E,aAAa,SAAS,GAAG;IACxB,OAAOhJ,qEAA2B;IAClC,UAAUA,yDAAiB,CAACkL,4EAAiB,CAAC,MAAM;QAACnC,0GAAoB;KAAC,EAAE;AAC7E;AAEAC,aAAa,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;AChIpB,IAAM8C,UAAU,UAAU;AAC1B,IAAMC,UAAU,UAAU;AAC1B,IAAMC,WAAW,WAAW;AAC5B,IAAMC,UAAU,UAAU;AAC1B,IAAMC,OAAO,OAAO;AACpB,IAAMC,WAAW,WAAW;AAC5B,IAAMC,UAAU,UAAU;AAC1B,IAAMC,OAAO,OAAO;AACpB,IAAMC,OAAO,OAAO;AACpB,IAAMC,OAAO,OAAO;AAE3B,6DAAe;IACdT,SAAAA;IACAC,SAAAA;IACAC,UAAAA;IACAC,SAAAA;IACAC,MAAAA;IACAC,UAAAA;IACAC,SAAAA;IACAC,MAAAA;IACAC,MAAAA;IACAC,MAAAA;AACD,CAAC,EAAC;;;;;;;;;;ACtBF,6DAAe;IACd,YAAY;AACb,CAAC,EAAC;;;;;;;;;;ACFF,6DAAe;IACd,SAAS;IACT,UAAU;IACV,iBAAiB;IACjB,cAAc;AACf,CAAC,EAAC;;;;;;;;;;;;;;;ACLiD;AAE5C,IAAME,+BAAiBD,oDAAaA,CAAC,CAAC,GAAG;AAEzC,IAAQnD,WAAaoD,eAAbpD,SAA4B;AAEpC,IAAMqD,yBAAyBlM,SAAAA;IACrC,IAAMmM,eAAe;QACpB,WAAWnM,MAAM,EAAE;IACpB;IAEA,qBACC,2DAACiM,eAAe,QAAQ;QAAC,OAAOE;OAC7BnM,MAAM,QAAQ;AAGnB,EAAE;AAEF,0EAAeyC,2CAAIA,CAACyJ,uBAAuBA,EAAC;;;;;;;;;;;;AClBmE;AAC1B;AAErF,6DAAe;IACd,OAAO;QACN,WAAW;QACX,kBAAkBI,GAAG,CAAC,CAAC;QACvB,gBAAgBA,GAAG,CAAC,CAAC;QACrB,cAAc;QACd,oBAAoB;QACpB,gBAAgB;IACjB;IACA,aAAa;QACZ,WAAW;QACX,eAAeA,GAAG,CAAC,CAAC;QACpB,gBAAgBA,GAAG,CAAC,CAAC;QACrB,YAAYA,GAAG,CAAC,CAAC;QACjB,cAAc;QACd,oBAAoB;QACpB,oBAAoB;QACpB,gBAAgB;QAChB,WAAW;QACX,gBAAgB;IACjB;IACA,QAAQ;QACP,WAAW;QACX,kBAAkBA,GAAG,CAAC,CAAC;QACvB,gBAAgBA,GAAG,CAAC,CAAC;QACrB,uBAAuB;QACvB,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,QAAQ;QACP,WAAW;QACX,kBAAkBA,GAAG,CAAC,CAAC;QACvB,wBAAwB;YAAE,OAAOA,GAAG,CAAC,CAAC;QAAU;QAChD,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,QAAQ;QACP,WAAW;QACX,kBAAkBA,GAAG,CAAC,CAAC;QACvB,yBAAyBA,GAAG,CAAC,CAAC;QAC9B,yBAAyBA,GAAG,CAAC,CAAC;QAC9B,yBAAyBA,GAAG,CAAC,CAAC;QAC9B,uBAAuB;QACvB,cAAc;QACd,gBAAgB;IACjB;IACA,SAAS;QACR,WAAW;QACX,kBAAkBA,GAAG,CAAC,CAAC;QACvB,yBAAyBA,GAAG,CAAC,CAAC;QAC9B,yBAAyBA,GAAG,CAAC,CAAC;QAC9B,uBAAuB;QACvB,cAAc;QACd,gBAAgB;IACjB;IACA,KAAK;QACJ,WAAW;QACX,kBAAkBA,GAAG,CAAC,CAAC;QACvB,gBAAgBA,GAAG,CAAC,CAAC;QACrB,uBAAuB;QACvB,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,MAAM;QACL,WAAW;QACX,kBAAkBA,GAAG,CAAC,CAAC;QACvB,gBAAgBA,GAAG,CAAC,CAAC;QACrB,uBAAuB;QACvB,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,KAAK;QACJ,WAAW;QACX,kBAAkBA,GAAG,CAAC,CAAC;QACvB,gBAAgBA,GAAG,CAAC,CAAC;QACrB,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,MAAM;QACL,WAAW;QACX,kBAAkBA,GAAG,CAAC,CAAC;QACvB,gBAAgBA,GAAG,CAAC,CAAC;QACrB,uBAAuB;QACvB,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,OAAO;QACN,WAAW;QACX,kBAAkBA,GAAG,CAAC,CAAC;QACvB,gBAAgBA,GAAG,CAAC,CAAC;QACrB,uBAAuB;QACvB,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,QAAQ;QACP,WAAW;QACX,kBAAkBA,GAAG,CAAC,CAAC;QACvB,gBAAgBA,GAAG,CAAC,CAAC;QACrB,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,OAAO;QACN,gBAAgBA,GAAG,CAAC,CAAC;QACrB,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,cAAc;QACb,gBAAgBA,GAAG,CAAC,CAAC;QACrB,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,KAAK;QACJ,gBAAgBA,GAAG,CAAC,CAAC;QACrB,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IACjB;IACA,MAAM,CAAC;IACPC,KAAK,SAALA,IAAc7H,QAAQ;QACrB,IAAM8H,gBAAgB,IAAI,CAAC9H,SAAS,WAAW,GAAG;QAClD,IAAM+H,uBAAuB,IAAI,CAACJ,mGAAkBA,CAAC3H,UAAU,WAAW,GAAG;QAC7E,IAAMgI,gCAAgCN,yHAAqC,CAAC1H,SAAS;QAErF,OAAO8H,iBAAiBC,wBAAwB,IAAI,CAACC,0CAAAA,oDAAAA,8BAA+B,WAAW,GAAG;IACnG;AACD,CAAC,EAAC;;;;;;;;;;AClJF,wBAAwB,GACxB,6DAAe;IACd,yBAAejI,IAAI;QAClB,IAAI,CAACA,MACJ,OAAO;QAER,IAAMkI,aAAalI,KAAK,MAAM;QAE9B,OAAOA,KAAK,kBAAkB,CAACkI;IAChC;IAEA,0BAAgBC,EAAE,EAAEC,EAAE;QACrB,IAAIC,IAAI,OAAOF,GAAG,KAAK,KAAK,WAAWA,GAAG,KAAK,GAAGA;QAClD,IAAIG,IAAI,OAAOF,GAAG,KAAK,KAAK,WAAWA,GAAG,KAAK,GAAGA;QAElD,OAAOC,EAAE,aAAa,CAACC;IACxB;AACD,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiF6B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAlG4D;AAE3F,IAAME,UAAU/N,SAAAA;IACf,OAAO;QACN,UAAUA,KAAK,QAAQ;QACvB,OAAOA,KAAK,EAAE;QACd,OAAOA,KAAK,SAAS;QACrB,KAAKA,KAAK,GAAG;QACb,qBAAqBA,KAAK,mBAAmB,IAAI;QACjD,SAASA,KAAK,OAAO,IAAI;QACzB,KAAKA,KAAK,GAAG;QACb,MAAMA,KAAK,IAAI;IAChB;AACD;AAEA,IAAMgO,kBAAkB,SAACC,gBAAgBjO;IACxCiO,eAAe,IAAI,CAACjO,KAAK,GAAG;IAE5B,OAAO;QACN,OAAOA,KAAK,EAAE;QACd,OAAOA,KAAK,SAAS,IAAIA,KAAK,EAAE;QAChC,UAAUA,KAAK,QAAQ;QACvB,WAAWA,KAAK,KAAK,GAAG,QAAQ;QAChC,KAAKA,KAAK,GAAG;QACb,qBAAqBA,KAAK,mBAAmB,IAAI;QACjD,SAASA,KAAK,OAAO,IAAI;QACzB,KAAKA,KAAK,GAAG;QACb,MAAMA,KAAK,IAAI;IAChB;AACD;AAEA,IAAMkO,gBAAgB,SAACpK,MAAMmK,gBAAgB1I;IAC5C,IAAM4I,WAAW5I,IAAI,CAACzB,KAAK;IAE3B,sBAAsB,GACtB,IAAI,CAACqK,UACJ,OAAO,EAAE;IAEV,OAAOA,SACL,OAAO,GACP,MAAM,CAACnO,SAAAA;eAAQ,CAACiO,eAAe,QAAQ,CAACjO,KAAK,GAAG;OAChD,GAAG,CAAC+N;AACP;AAEA,IAAMK,WAAW,SAAC7I,MAAM0I,gBAAgBI;IACvC,IAAInG,QAAQ,EAAE;IAEd,IAAI3C,KAAK,YAAY,IACpB2C,QAAQA,MAAM,MAAM,CAACgG,cAAc,cAAcD,gBAAgB1I;IAElE,IAAIA,KAAK,kBAAkB,MAAM,CAAC0I,eAAe,QAAQ,CAAC1I,KAAK,yBAAyB,CAAC,GAAG,GAC3F2C,QAAQA,MAAM,MAAM,CAAC6F,QAAQxI,KAAK,yBAAyB;IAE5D,IAAIA,KAAK,UAAU,IAClB2C,QAAQA,MAAM,MAAM,CAACgG,cAAc,YAAYD,gBAAgB1I;IAEhE,IAAIA,KAAK,MAAM,MAAM8I,eAAe,UAAU,EAC7CnG,QAAQA,MAAM,MAAM,CAACgG,cAAc,QAAQD,gBAAgB1I;IAE5D,OAAO2C;AACR;AAEA,IAAMF,oBAAoB,SAACzC,MAAM0I,gBAAgBI;IAChD,IAAInG,QAAQkG,SAAS7I,MAAM0I,gBAAgBI;IAE3C,OAAOnG,MAAM,MAAM,CAAC,SAACjI,MAAMqO;eAAQpG,MAAM,OAAO,CAACjI,UAAUqO;;AAC5D;AAEA,IAAMC,WAAW,SAAChJ,MAAM0I,gBAAgBI;IACvC,IAAIG,QAAQjJ,KAAK,KAAK,IAAIA,KAAK,KAAK,CAAC,OAAO;IAC5C,IAAIkJ;IAEJ,sBAAsB,GACtB,IAAI,CAACD,OACJ,OAAO,EAAE;IAEV,IAAI,CAACjJ,KAAK,MAAM,IAAI;QACnBkJ,WAAWL,SAAS7I,MAAM0I,gBAAgBI,gBAAgB,GAAG,CAACpO,SAAAA;mBAAQA,KAAK,GAAG;;QAE9EuO,QAAQA,MAAM,MAAM,CAACvO,SAAAA;mBAAQwO,SAAS,QAAQ,CAACxO,KAAK,GAAG;;IACxD;IAEA,sBAAsB,GACtB,IAAIoO,eAAe,SAAS,KAAK,SAASG,KAAK,CAAC,EAAE,EACjD,OAAO;QAACR,gBAAgBC,gBAAgBO,KAAK,CAAC,EAAE;KAAE;IAEnD,OAAOA,MAAM,GAAG,CAACR,gBAAgB,IAAI,QAAOC;AAC7C;AAEA,IAAMS,cAAc,SAACnJ,MAAMC;IAC1B,IAAIyI,iBAAiB,EAAE;IACvB,IAAMI,iBAAiBP,qGAAsB,CAACtI;IAC9C,IAAMG,cAAc4I,SAAShJ,MAAM0I,gBAAgBI;IACnD,IAAM3I,iBAAiBsC,kBAAkBzC,MAAM0I,gBAAgBI;IAE/D,OAAO;QAAE3I,gBAAAA;QAAgBC,aAAAA;IAAY;AACtC;AAEA,6DAAe;IAAE+I,aAAAA;AAAY,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2C9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AA7IkC;AACyC;AAChB;AACwB;AAEpF,IAAME,eAAeD,iDAAMA,CAAC;IAC3B,UAAU,CAAC;AACZ;AAEA,IAAME,WAAW,SAAC9O,KAAKE;IACtB,OAAOA,KAAK,GAAG,CAAC,WAAWF;AAC5B;AAEA,IAAM+O,eAAe7O,SAAAA;WAAQkC,OAAO,MAAM,CAAClC,MAAM;QAAE,WAAWA,KAAK,SAAS,IAAIgG,yFAAY;IAAC;;AAE7F,IAAMX,UAAU,SAACe,OAAO+D;IACvB,IAAMrK,MAAMqK,OAAO,GAAG,CAAC,QAAQ;IAE/BzF,mHAA0B,CAAC,WAAW;QAACyF,OAAO,IAAI,CAAC,WAAW;KAAC;IAE/D,OAAO/D,MAAM,KAAK,CAAC;QAAC;QAAYtG;KAAI,EAAE4O,iDAAMA,CAACvE,OAAO,IAAI;AACzD;AAEA,IAAM2E,mBAAmB,SAAC1I,OAAO+D;IAChC,IAAMrK,MAAMqK,OAAO,GAAG,CAAC,QAAQ,IAC9BjK,UAAUiK,OAAO,OAAO,CAAC,QAAQ,IACjC4E,OAAO;QAAC;QAAYjP;QAAKkG,8FAAiB;KAAC,EAC3C2B,QAAQvB,MAAM,KAAK,CAAC2I,MAAM,SAAS,CAACH,SAAS,IAAI,QAAO1O,WACxD8O,MAAM7E,OAAO,IAAI,KAAKtK,oGAA0B,GAAGmG,yFAAY,GAAGA,0FAAa;IAEhFtB,mHAA0B,CAAC,oBAAoB;QAACiD;QAAOqH;QAAKlP;KAAI;IAEhE,OAAOsG,MAAM,KAAK,CAAC2I,KAAK,MAAM,CAAC;QAACpH;QAAO;KAAY,GAAGqH;AACvD;AAEA,IAAMC,qBAAqB,SAAC7I,OAAOtG,KAAKwG,MAAMpG;IAC7C,IAAM6O,OAAO;QAAC;QAAYjP;QAAKwG;KAAK,EACnCqB,QAAQvB,MAAM,KAAK,CAAC2I,MAAM,SAAS,CAACH,SAAS,IAAI,QAAO1O;IAEzD,OAAOkG,MAAM,QAAQ,CAAC2I,KAAK,MAAM,CAACpH;AACnC;AAEA,IAAMuH,mBAAmB,SAAC9I,OAAOtG,KAAKwG,MAAMqB,OAAO3H;IAClD,IAAM+O,OAAO;QAAC;QAAYjP;QAAKwG;KAAK,EACnC6I,YAAYN,aAAa7O,OACzBD,OAAOqG,MAAM,KAAK,CAAC2I,MAAM,MAAM,CAACpH,OAAO+G,iDAAMA,CAACS;IAE/C,OAAO/I,MAAM,KAAK,CAAC2I,MAAMhP;AAC1B;AAEA,IAAMqP,WAAW,SAAChJ,OAAO+D,QAAQkF,WAAWC;IAC3C,IAAQrP,YAAoCkK,OAApClK,WAAWD,OAAyBmK,OAAzBnK,MAAWuP,YAAcpF,OAAnB;IACzB,IAAMrK,MAAMyP,UAAU,QAAQ;IAC9B,IAAMJ,YAAYN,aAAa7O;IAC/B,IAAME,UAAUiP,UAAU,GAAG,CAAC,QAAQ;IACtC,IAAMK,YAAYpJ,MAAM,KAAK,CAAC;QAAC;QAAYtG;QAAKuP;KAAU;IAE1D,IAAIxE,aAAa2E,UAAU,IAAI,EAC9BC,eAAeH;IAEhB,IAAIlJ,MAAM,KAAK,CAAC;QAAC;QAAYtG;QAAKuP;KAAU,EAAE,SAAS,CAACT,SAAS,IAAI,QAAO1O,aAAa,GAAG;QAC3FuP,eAAeJ;IAChB;IAEA,IAAIpP,WAAW;QACd4K,aAAa2E,UAAU,SAAS,CAACZ,SAAS,IAAI,QAAO3O,UAAU,GAAG,CAAC,QAAQ;IAC5E;IAEA,wBAAwB,GACxB,IAAI,CAAEoP,CAAAA,cAAcrJ,iGAAoB,IAAIqJ,cAAcI,YAAW,GAAI;QACxE/K,mHAA0B,CAAC,oBAAoB;YAC9CyK;YACA/I,MAAM,KAAK,CAAC;gBAAC;gBAAYtG;gBAAK2P;aAAa,EAAE,SAAS,CAACb,SAAS,IAAI,QAAO1O;YAC3E2K;YACA4E,iBAAiBzJ,8FAAiB;YAClClG;SACA;IACF;IAEA,OAAOoP,iBACND,mBAAmB7I,OAAOtG,KAAK2P,cAAcvP,UAC7CJ,KAAKuP,WAAWxE,YAAYsE;AAE9B;AAEA,IAAMO,WAAW,SAACtJ,OAAO+D;IACxB,IAAMrK,MAAMqK,OAAO,GAAG,CAAC,QAAQ,IAC9BjK,UAAUiK,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,IAClCwF,WAAWxF,OAAO,SAAS,GAAGA,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,KAAK,MAChE4E,OAAO3I,MAAM,KAAK,CAAC;QAAC;QAAYtG;QAAKkG,8FAAiB;KAAC,GACvD4J,YAAYb,KAAK,SAAS,CAACH,SAAS,IAAI,QAAO1O;IAEhD,IAAIyH,QAAQoH,KAAK,IAAI;IAErB,IAAIY,UACHhI,QAAQoH,KAAK,SAAS,CAACH,SAAS,IAAI,QAAOe;IAE5C,IAAIhI,QAAQiI,cAAc,GAAG;QAC5B,OAAOxJ;IACR;IAEA,IAAIuB,QAAQiI,WAAW;QACtBjI,QAAQA,QAAQ;IACjB;IAEAjD,mHAA0B,CAAC,uBAAuB;QACjDkL;QACAjI;QACAwC,OAAO,IAAI;QACXrK;KACA;IAED,OAAOoP,iBACND,mBAAmB7I,OAAOtG,KAAKkG,8FAAiB,EAAE9F,UAClDJ,KAAKkG,8FAAiB,EAAE2B,OAAOwC,OAAO,IAAI;AAE5C;AAEA,yBAAe,oCAAS0F,MAAM,EAAE1F,MAAM;IACrC,IAAM/D,QAAQyJ,UAAUlB;IAExB,OAAQxE,OAAO,IAAI;QAClB,KAAKtK,gGAAsB;YAC1B,OAAOwF,QAAQe,OAAO+D;QAEvB,KAAKtK,gGAAsB;YAC1B,OAAOuP,SAAShJ,OAAO+D,QAAQnE,8FAAiB,EAAEA,iGAAoB;QAEvE,KAAKnG,mGAAyB;YAC7B,OAAOuP,SAAShJ,OAAO+D,QAAQnE,iGAAoB,EAAEA,8FAAiB;QAEvE,KAAKnG,iGAAuB;YAC3B,OAAO6P,SAAStJ,OAAO+D;QAExB,KAAKtK,oGAA0B;QAC/B,KAAKA,qGAA2B;YAC/B,OAAOiP,iBAAiB1I,OAAO+D;QAEhC;YACC,OAAO/D;IACT;AACD;;;;;;;;;;;;;AC7I4C;AAC6B;AAEzE0J,iFAAwB,CAAC,gBAAgBC,sFAAWA;AAEpD,6DAAeD,gEAAKA,EAAC;;;;;;;;;;;;;;;;;;;;ACLd,IAAM3D,UAAU,UAAU;AAE1B,IAAMC,UAAU,UAAU;AAE1B,IAAMC,WAAW,WAAW;AAE5B,IAAMC,UAAU,UAAU;AAE1B,IAAMC,OAAO,OAAO;AAEpB,IAAMC,WAAW,WAAW;AAE5B,IAAMC,UAAU,UAAU;AAE1B,IAAMC,OAAO,OAAO;AAEpB,IAAMC,OAAO,OAAO;AAEpB,IAAMC,OAAO,OAAO;AAE3B,6DAAe;IAAET,SAAAA;IAASC,SAAAA;IAASC,UAAAA;IAAUC,SAAAA;IAASC,MAAAA;IAAMC,UAAAA;IAAUC,SAAAA;IAASC,MAAAA;IAAMC,MAAAA;IAAMC,MAAAA;AAAK,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiDc;AAAA;AArEzG,IAAMoD,OAAO,OAAO;AAEpB,IAAMC,UAAU,UAAU;AAE1B,IAAMC,QAAQ,QAAQ;AAEtB,IAAMC,aAAa,aAAa;AAEhC,IAAMC,OAAO,OAAO;AAEpB,IAAMC,aAAa,aAAa;AAEhC,IAAMC,MAAM,MAAM;AAElB,IAAMC,OAAO,OAAO;AAEpB,IAAMC,SAAS,SAAS;AAExB,IAAMC,SAAS,SAAS;AAExB,IAAMC,SAAS,SAAS;AAExB,IAAMC,gBAAgB,gBAAgB;AAEtC,IAAMC,WAAW;IAAC;QACxB,OAAOZ;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;IAAG;QACF,OAAOC;QACP,OAAO;IACR;CAAE,CAAC;AAEH,wBAAwB,GACjB,IAAM3J,kBAAkB,SAACiC;QAAO4H,uEAAM;IAC5C,IAAMC,YAAYF,SAAS,IAAI,CAACG,SAAAA;eAAWA,QAAQ,KAAK,KAAK9H;;IAE7D,OAAO6H,aAAaA,SAAS,CAACD,IAAI;AACnC,EAAE;AAEF,6DAAe;IAAEb,MAAAA;IAAMC,SAAAA;IAASC,OAAAA;IAAOC,YAAAA;IAAYC,MAAAA;IAAMC,YAAAA;IAAYC,KAAAA;IAAKC,MAAAA;IAAMC,QAAAA;IAAQC,QAAAA;IAAQE,eAAAA;AAAc,CAAC,EAAC;;;;;;;;;;;;;;;;;ACrEhH,uFAAuF;AACvF,IAAMK,eAAe;AAEd,IAAMC,cAAc;IAAC;QAC3B,WAAW,MAAMD;IAClB;IAAG;QACF,WAAW,MAAMA;QACjB,WAAW,MAAMA;IAClB;IAAG;QACF,WAAW,MAAMA;QACjB,WAAW,MAAMA;IAClB;IAAG;QACF,WAAW,MAAMA;IAClB;CAAE,CAAC;AAEI,IAAME,aAAa;IAAC;QAC1B,UAAU,MAAMF;IACjB;IAAG;QACF,UAAU,MAAMA;QAChB,UAAU,MAAMA;IACjB;IAAG;QACF,UAAU,MAAMA;QAChB,UAAU,MAAMA;IACjB;IAAG;QACF,UAAU,MAAMA;IACjB;CAAE,CAAC;AAEI,IAAMG,cAAc,KAAK;AAEzB,IAAMC,QAAQ,KAAK;AAEnB,IAAMC,SAAS,KAAK;AAEpB,IAAMC,MAAM,KAAK;AAEjB,IAAMC,QAAQ;IACpBJ,aAAAA;IAAaC,OAAAA;IAAOC,QAAAA;IAAQC,KAAAA;AAC7B,EAAE;AAEF,6DAAe;IACdL,aAAAA;IAAaC,YAAAA;AACd,CAAC,EAAC;;;;;;;;;;;;;;ACzCK,IAAMM,OAAO,OAAO;AAEpB,IAAMC,SAAS,SAAS;AAExB,IAAMC,OAAO,OAAO;AAEpB,IAAMC,QAAQ,QAAQ;AAE7B,6DAAe;IAAEH,MAAAA;IAAMC,QAAAA;IAAQC,MAAAA;IAAMC,OAAAA;AAAM,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwEf;AAhFvB,IAAMH,OAAO,OAAO;AAEpB,IAAMI,MAAM,MAAM;AAElB,IAAMC,UAAU,UAAU;AAE1B,IAAMC,eAAe,eAAe;AAEpC,IAAML,SAAS,SAAS;AAExB,IAAMM,QAAQ,QAAQ;AAEtB,IAAMC,MAAM,MAAM;AAElB,IAAMN,OAAO,OAAO;AAEpB,IAAMO,SAAS,SAAS;AAExB,IAAMC,MAAM,MAAM;AAElB,IAAMC,SAAS,SAAS;AAExB,IAAMC,cAAc,cAAc;AAElC,IAAMT,QAAQ,QAAQ;AAEtB,IAAMU,YAAY,YAAY;AAE9B,IAAMC,SAAS,SAAS;AAExB,IAAMC,UAAU,UAAU;AAE1B,IAAMC,eAAe;IAACL;IAAQC;CAAY,CAAC;AAE3C,IAAMK,6BAA6B;IAACH;IAAQC;CAAQ,CAAC;AAErD,IAAMG,6BAA6B;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACA,CAAC;AAEF,IAAMC,iBAAiB;IACtBnB,MAAAA;IAAMI,KAAAA;IAAKC,SAAAA;IAASM,QAAAA;IAAQC,aAAAA;IAAaN,cAAAA;IAAcL,QAAAA;IACvDM,OAAAA;IAAOC,KAAAA;IAAKN,MAAAA;IAAMO,QAAAA;IAClBC,KAAAA;IAAKP,OAAAA;IAAOU,WAAAA;IAAWC,QAAAA;IAAQC,SAAAA;IAC/BnF,KAAK,SAALA,IAAcvJ,IAAI;QACjB,OAAO,IAAI,CAACA,KAAK,WAAW,GAAG;IAChC;AACD;AAEO,IAAM+O,aAAa;IACzBV;IAAKN;IAAKH;IAAQC;IAAMS;IAAQC;IAChCH;IAAQF;IAAOD;IAAcN;IAC7Bc;IAAQD;IAAWE;IACnBZ;IAAOE;CACP,CAAC;AAEK,IAAMgB,kBAAkB;IAC9BX;IAAKN;IAAKH;IAAQC;IAAMU;IACxBH;IAAQH;IAAcN;IACtBc;IAAQD;IAAWE;IACnBZ;IAAOE;IAASM;CAChB,CAAC;AAEK,IAAMW,WAAW;IACvBd;CAEA,CAHuB,OAEvB,qBAAGY,aACF;AAEF,6DAAeD,cAAcA,EAAC;;;;;;;;;;;;;AChFvB,IAAMI,aAAa,8CAA8C;AAEjE,IAAMC,gBAAgB,gDAAgD;AAEtE,IAAMC,iBAAiB,kDAAkD;AAEhF,6DAAe;IAAEF,YAAAA;IAAYC,eAAAA;IAAeC,gBAAAA;AAAe,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;ACNsB;AAEnF,IAAMC,eAAe;IACpB,IAAIP,oGAAqB;IACzB,OAAO;IACP,MAAM;AACP;AAEA,IAAMQ,oBAAoB;IACzB,IAAIR,yGAA0B;IAC9B,OAAO;IACP,MAAM;AACP;AAEA,IAAMS,iCAAiC;IACtC,QAAQ;QAACD;QAAmBD;KAAa;IACzC,aAAa;QAACC;QAAmBD;KAAa;AAC/C;AAEO,IAAMG,oCAAoC9N,SAAAA;IAChD,IAAM+N,qBAAqB/N,SAAS,WAAW;IAE/C,OAAO6N,8BAA8B,CAACE,mBAAmB;AAC1D,EAAE;AAEK,IAAMrG,wCACZ,qBAAC0F,yGAA0B,EAAGA,oGAAqB,EAClD;AAEK,IAAMY,0CAA0C;IACtDZ,yGAA0B;CAC1B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC6SD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA5U8B;AAC0C;AACE;AACQ;AACuB;AAE1G,IAAMkB,4BAA4B;AAE3B,IAAMC,oBAAoBC,SAAAA;WAASH,sGAAkBA,CAACG,OAAO;QAAC;QAAe;QAAS;KAAY,EAAE;EAAI;AAExG,IAAM7G,qBAAqB;QAACrJ,wEAAO;IACzC,IAAI0B,WAAW1B,KAAK,WAAW;IAE/B,IAAI0B,aAAa,eAAe,CAACA,UAAW;QAC3C,OAAO;IACR;IACA,IAAIA,aAAa,iBAAiB;QACjC,OAAO;IACR;IACA,IAAIA,aAAa,WAAW;QAC3B,OAAO;IACR;IACA,IAAIA,aAAa,uBAAuBA,aAAa,UAAU;QAC9D,OAAO;IACR;IAEA,OAAOA;AACR,EAAE;AAEK,SAASyO;IACf,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EACvC,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW;IAE9B,OAAO;AACR;AAEO,SAASC;IACf,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EACvC,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW;IAE9B,OAAO;AACR;AAEO,IAAMC,8BAA8BC,SAAAA;IAC1C,IAAIC,QAAQ;IACZ,IAAIvR;IAEJ,IAAI,CAACsR,SACJ,OAAOC;IAERvR,QAAQsR,QAAQ,KAAK;IAErB,IAAItR,MAAM,SAAS,EAClBuR,SAASC,WAAWxR,MAAM,SAAS;IAEpC,IAAIA,MAAM,YAAY,EACrBuR,SAASC,WAAWxR,MAAM,YAAY;IAEvC,OAAOuR;AACR,EAAE;AAEK,SAASE;IACf,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY;IAEhD,OAAO;AACR;AAEO,SAASC;IACf,IAAI3L,SAAS;IACb,IAAI4L,QAAQC;IAEZ,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;QAC/CD,SAAS,IAAI,CAAC,MAAM,CAAC,eAAe;QACpCC,aAAaD,OAAO,UAAU;QAE9B5L,UAAU4L,OAAO,YAAY;QAE7B,wBAAwB,GACxB,IAAIC,YACH7L,UAAUsL,4BAA4BO;QAEvC,OAAO7L;IACR;IAEA,OAAOA;AACR;AAEO,IAAM8L,mBAAmBC,SAAAA;IAC/B,IAAIC;IAEJ,OAAQD;QACP;QACA,KAAK;YACJC,SAAS;YACT;QACD,KAAK;YACJA,SAAS;YACT;QACD,KAAK;YACJA,SAAS;YACT;QACD,KAAK;YACJA,SAAS;YACT;QACD,KAAK;YACJA,SAAS;YACT;QACD,KAAK;YACJA,SAAS;YACT;QACD,KAAK;YACJA,SAAS;YACT;IACF;IAEA,OAAOA;AACR,EAAE;AAEK,IAAMC,oBAAoBC,SAAAA;IAChC,IAAI,CAACA,QAAQ;QACZ,OAAO;IACR;IACA,OAAOA,OAAO,SAAS,KAAK;AAC7B,EAAE;AAEK,IAAMC,mBAAmBC,SAAAA;IAC/B,OAAOC,QAAQD,aAAa;QAACtB,+FAAe;QAAEA,iGAAiB;QAAEA,+FAAe;QAAEA,gGAAgB;KAAC,CAAC,QAAQ,CAACsB,UAAU,WAAW;AACnI,EAAE;AAEK,IAAME,cAAcC,SAAAA;WAAY;QAAC1B,iGAAiB;QAAEA,8FAAc;KAAC,CAAC,QAAQ,CAAC0B;EAAU;AAEvF,IAAMC,iBAAiBD,SAAAA;IAC7B,OAAO;QAAC1B,iGAAiB;QAAEA,iGAAiB;QAAEA,kGAAkB;QAAEA,8FAAc;KAAC,CAAC,QAAQ,CAAC0B;AAC5F,EAAE;AAEK,IAAME,cAAc,SAACF;QAAUG,+EAAc;IACnD,IAAIC,YAAY;QAAC9B,8FAAc;QAAEA,kGAAkB;KAAC;IAEpD,IAAI6B,aACHC,YAAa,qBAAGA,kBAAJ;QAAe9B,8FAAc;KAAC;IAE3C,OAAO8B,UAAU,QAAQ,CAACJ;AAC3B,EAAE;AAEK,IAAMK,eAAeC,SAAAA;IAC3B,IAAIC,cAAcC;IAElB,IAAI,CAACF,aACJ;IAEDC,eAAe5L,EAAE;IACjB6L,MAAMC,SAAS,aAAa,CAAC;IAE7BD,IAAI,EAAE,GAAG;IAETD,aAAa,KAAK;IAClBA,aAAa,MAAM,CAACC;AACrB,EAAE;AAEK,IAAME,mBAAmB;IAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI;AACxB,EAAE;AAEK,IAAMC,WAAWvQ,SAAAA;IACvB,OAAO;QAACoN,kGAAkB;QAAEA,oGAAoB;QAAEA,2GAA2B;KAAC,CAAC,QAAQ,CAACzF,mBAAmB3H;AAC5G,EAAE;AAEK,IAAMwQ,0BAA0B,SAACC,SAASC;IAChD,IAAIC;IAEJ,IAAI,CAACF,SACJ,OAAO;IAER,IAAIA,QAAQ,KAAK,IAAIG,MAAM,OAAO,CAACH,QAAQ,KAAK,CAAC,UAAU,GAC1D,OAAOA,QAAQ,KAAK,CAAC,UAAU,CAACC,cAAc;IAE/CC,SAASF,QAAQ,MAAM;IAEvB,IAAIG,MAAM,OAAO,CAACD,WAAWA,OAAO,MAAM,IAAIA,MAAM,CAAC,EAAE,CAAC,IAAI,EAC3D,OAAOA,MAAM,CAAC,EAAE,CAAC,IAAI,CAACD,cAAc,CAAC,YAAY;IAElD,OAAO;AACR,EAAE;AAEK,IAAMG,gBAAgBrW,SAAAA;IAC5B,IAAIoW,MAAM,OAAO,CAACpW,OAAO;QACxB,IAAIoW,MAAM,OAAO,CAACpW,IAAI,CAAC,EAAE,GAAG;YAC3B,OAAOA,IAAI,CAAC,EAAE,CAAC,EAAE;QAClB;QAEA,OAAOA,IAAI,CAAC,EAAE;IACf;IAEA,OAAOA;AACR,EAAE;AAEK,IAAMsW,eAAe,SAACC,MAAMC;IAClC,IAAQC,aAAqCD,UAArCC,YAAYC,WAAyBF,UAAzBE,UAAUC,aAAeH,UAAfG;IAC9B,IAAMC,SAASf,SAAS,aAAa,CAAC;IACtC,IAAM7L,UAAU4M,OAAO,UAAU,CAAC;IAClC,IAAIC,OAAO;IAEX,sBAAsB,GACtB,IAAIF,eAAeG,WAClBD,OAAQ,GAAa,OAAXF,YAAW;IAEtBE,QAAS,GAAcJ,OAAZC,UAAS,KAAc,OAAXD;IAEvBzM,QAAQ,IAAI,GAAG6M;IAEf,OAAO7M,QAAQ,WAAW,CAACuM,MAAM,KAAK;AACvC,EAAE;AAEK,IAAMQ,qBAAqB;QAAGR,aAAAA,MAAMzT,cAAAA;WAAYwT,aAAaC,MAAMzT,SAASgR;EAA0B;AAEtG,IAAMkD,UAAU;QAAGhD,cAAAA;IACzB,IAAI,CAACA,SAAS,CAACA,MAAM,IAAI,EACxB,OAAO;IAER,IAAIA,MAAM,IAAI,KAAKpB,oGAAoB,EACtC,OAAO;IAER,OAAOoC,iBAAiBhB,MAAM,IAAI,KAAKA,MAAM,KAAK,KAAK;AACxD,EAAE;AAEK,IAAMiD,eAAeC,SAAAA;IAC3B,IAAMpT,OAAO+P,sGAAkBA,CAACqD,QAAQ;QAAC;QAAS;KAAO;IAEzD,IAAI,CAACpT,MACJ,OAAO;IAER,IAAIkT,QAAQE,SACX,OAAOtE,oGAAoB;IAE5B,OAAOzF,mBAAmBrJ;AAC3B,EAAE;AAEK,IAAMqT,YAAYC,SAAAA;IACxB,OAAOvD,sGAAkBA,CAACuD,QAAQ,WAAW;AAC9C,EAAE;AAEK,IAAMC,mBAAmB;QAAClB,0EAAS,EAAE;IAC3C,OAAOA,OAAO,GAAG,CAAC;YAACmB,yEAAQ,CAAC;eAAO,wCAAKA;YAAO,MAAMR;;;AACtD,EAAE;AAEK,IAAMS,gBAAgB,SAAC3P,OAAO4P;WAAiB5P,QAAQ4P;EAAa;AAEpE,IAAMC,UAAU,SAACC,WAAWC;IAClC,IAAMC,aAAa9O,OAAO4O,WAAW,WAAW;IAChD,IAAMG,SAASF,SAAS,EAAE;IAE1B,IAAI,CAAC3C,iBAAiB4C,aACrB,OAAO;IAER,OAAOC,OAAO,IAAI,CAACC,SAAAA;QAClB,OAAOA,QAAQ,SAAS,IAAI9C,iBAAiB8C,QAAQ,SAAS,KAAKA,QAAQ,SAAS,CAAC,WAAW,OAAOJ;IACxG;AACD,EAAE;AAEK,IAAMK,uBAAuBb,SAAAA;IACnC,IAAQf,SAAWe,OAAXf;IAER,IAAI,CAACA,QAAQ;QACZ,OAAO;IACR;IAEA,OAAOA,OAAO,IAAI,CAACmB,SAAAA;eAASA,MAAM,KAAK,KAAK;;AAC7C,EAAE;AAEK,IAAMU,kBAAkB;QAAC7B,0EAAS,EAAE;IAC1C,IAAM8B,kBAAkB9B,OAAO,MAAM,CAACmB,SAAAA;eAASA,MAAM,KAAK,KAAK;;IAE/D,OAAOpC,QAAQiB,OAAO,MAAM,IAAI8B,gBAAgB,MAAM,KAAK9B,OAAO,MAAM;AACzE,EAAE;AAEK,IAAM+B,mBAAmBhB,SAAAA;WAAUhC,QAAQrB,sGAAkBA,CAACqD,QAAQ,iBAAiB;EAAQ;AAE/F,IAAMiB,oBAAoBjB,SAAAA;IAChC,wBAAwB,GACxB,IAAiEkB,OAAAA,UAAU,CAAC,yBAAXA,KAAzDC,gBAAAA,kDAAiB,gDAAwCD,KAAjCE,aAAAA,4CAAc,CAAC,mCAAkBF,KAAfpE,OAAAA,gCAAQ,CAAC;IAC3D,IAAQlQ,OAASkQ,MAATlQ;IAER,IAAIuU,gBACH,OAAO;IAER,IAAIC,WAAW,CAACxU,KAAK,EACpB,OAAOoR,QAAQoD,WAAW,CAACxU,KAAK,CAAC,QAAQ;IAE1C,IAAIwU,YAAY,MAAM,EACrB,OAAOpD,QAAQoD,YAAY,MAAM,CAAC,QAAQ;IAE3C,OAAO;AACR,EAAE;AAEK,IAAMC,+BAA+BrB,SAAAA;IAC3C,IAAQsB,oBAAsBtB,OAAtBsB;IACR,IAAIC;IAEJ,IAAID,sBAAsB1B,WACzB,OAAO5B,QAAQsD;IAEhBC,mBAAmB5E,sGAAkBA,CAACqD,QAAQ;QAAC;QAAe;QAAU;QAAc;KAAS,EAAE;IAEjG,OAAOuB,iBAAiB,QAAQ,CAAC;AAClC,EAAE;AAEK,IAAMC,uBAAuB,SAACxB,QAAQpT;IAC5C,IAAI6U,UAAU7U;IAEd,IAAIA,SAAS8O,qGAAqB,EAAE;QACnC+F,UAAU/F,0GAA0B;IACrC;IAEA,OAAOa,mDAAKA,CAAC,CAAC,GAAGyD,QAAQ;QACxB,OAAO;YAAE,MAAMyB;QAAQ;IACxB;AACD,EAAE;AAEK,IAAMC,6BAA6BC,SAAAA;IACzC,IAAI,CAACA,YAAY,CAACA,SAAS,WAAW,EACrC,OAAO,CAAC;IAET,OAAOjF,qGAAiBA,CAACiF,SAAS,WAAW;AAC9C,EAAE;AAEK,SAASC,+BAA+BC,GAAG;IACjD,IAAgC3X,cAAAA,IAAI,CAAC,KAAK,EAAlC8V,SAAwB9V,YAAxB8V,QAAQ8B,cAAgB5X,YAAhB4X;IAChB,IAAMC,SAASpF,sGAAkBA,CAACqD,QAAQ;QAAC;QAAS;QAAU;KAAO;IAErE8B,eAAeA,YAAYD;IAC3BE,UAAUA,OAAO,IAAI,CAAC,IAAI;AAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtIE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAtMqD;AAAA;AAAA;AAAA;AACuB;AAC+B;AAEtG,IAAMG,eAAe,SAACC,aAAaC;IACzC,IAAMC,UAAU;QAAC/H,2GAAiB;QAAEA,qGAAW;QAAEA,sGAAY;QAAEA,mGAAS;KAAC;IAEzE,IAAK,IAAInJ,IAAI,GAAGA,IAAIkR,QAAQ,MAAM,EAAElR,IAAK;QACxC,IAAMmR,YAAYrI,oGAAU,CAAC9I,EAAE;QAC/B,IAAMoR,aAAavI,qGAAW,CAAC7I,EAAE;QAEjC,IAAMqR,qBACJF,CAAAA,UAAU,QAAQ,KAAK1C,aAAawC,cAAcE,UAAU,QAAO,KACnEA,CAAAA,UAAU,QAAQ,KAAK1C,aAAawC,cAAcE,UAAU,QAAO;QAErE,IAAMG,sBACJF,CAAAA,WAAW,SAAS,KAAK3C,aAAauC,eAAeI,WAAW,SAAQ,KACxEA,CAAAA,WAAW,SAAS,KAAK3C,aAAauC,eAAeI,WAAW,SAAQ;QAE1E,IAAIC,sBAAsBC,qBAAqB;YAC9C,OAAOJ,OAAO,CAAClR,EAAE;QAClB;IACD;AACD,EAAE;AAEK,IAAMuL,oBAAoB;QAACwD,0EAAS,CAAC;IAC3C,OAAOwC,KAAK,KAAK,CAACA,KAAK,SAAS,CAACxC;AAClC,EAAE;AAEF,IAAMyC,YAAY3Q,SAAAA;WAASA,SAAS4Q,CAAAA,OAAO5Q,sCAAP4Q,SAAO5Q,MAAI,MAAM;;AAErD,wBAAwB,GACxB,IAAM6Q,iCAAiCC,SAAAA;IACtC,IAAI,CAACA,OAAOF,CAAAA,OAAOE,oCAAPF,SAAOE,IAAE,MAAM,YAAY5D,MAAM,OAAO,CAAC4D,MACpD,OAAOA;IAER7X,OAAO,IAAI,CAAC6X,KAAK,OAAO,CAACC,SAAAA;QACxB,IAAIH,SAAOE,GAAG,CAACC,SAAS,MAAK,UAC5BF,+BAA+BC,GAAG,CAACC,SAAS;aACxC,IAAID,GAAG,CAACC,SAAS,KAAK/G,yFAAcA,EACxC,OAAO8G,GAAG,CAACC,SAAS;IACtB;IAEA,OAAOD;AACR;AAEA,wBAAwB,GACjB,IAAME,qBAAqB;QAACF,uEAAM,CAAC;qCAAMG;QAAAA;;IAC/C,IAAIC,SAASlB,uDAASA,CAACc;IAEvBG,UAAU,OAAO,CAACrC,SAAAA;QACjBA,WAAW3V,OAAO,IAAI,CAAC2V,SAAS,OAAO,CAACmC,SAAAA;YACvC,IAAIpC;YAEJ,IAAI,CAACuC,UAAUA,WAAWlH,yFAAcA,EACvCkH,SAAS,CAAC;YAEX,IAAIA,OAAO,cAAc,CAACH,WAAW;gBACpC,IAAI7D,MAAM,OAAO,CAAC0B,OAAO,CAACmC,SAAS,GAAG;oBACrC,IAAI,CAAC7D,MAAM,OAAO,CAACgE,MAAM,CAACH,SAAS,GAClCG,MAAM,CAACH,SAAS,GAAG,EAAE;oBAEtBpC,SAASC,OAAO,CAACmC,SAAS,CAAC,GAAG,CAAC,SAACI,UAAUzS;wBACzC,IAAIiS,UAAUQ,WACb,OAAOH,mBAAmB,CAAC,GAAGE,MAAM,CAACH,SAAS,CAACrS,MAAM,EAAEyS;wBAExD,OAAOA;oBACR;oBAEAD,MAAM,CAACH,SAAS,GAAGpC;oBACnB;gBACD;gBAEA,IAAIgC,UAAU/B,OAAO,CAACmC,SAAS,KAAKJ,UAAUO,MAAM,CAACH,SAAS,GAAG;oBAChEG,MAAM,CAACH,SAAS,GAAGC,mBAAmBE,MAAM,CAACH,SAAS,EAAEnC,OAAO,CAACmC,SAAS;oBACzE;gBACD;gBAEA,IAAInC,OAAO,CAACmC,SAAS,KAAK/G,yFAAcA,EAAE;oBACzC,OAAOkH,MAAM,CAACH,SAAS;gBACxB,OAAO;oBACNG,MAAM,CAACH,SAAS,GAAGnC,OAAO,CAACmC,SAAS;gBACrC;YACD,OAAO,IAAInC,OAAO,CAACmC,SAAS,KAAK/G,yFAAcA,EAAE;gBAChDkH,MAAM,CAACH,SAAS,GAAGF,+BAA+BjC,OAAO,CAACmC,SAAS;YACpE;QACD;IACD;IAEA,OAAOG;AACR,EAAE;AAEK,IAAMvG,qBAAqB,SAACuD,QAAQtG,KAAKwJ;QAAcC,mFAAkB;IAC/E,IAAMC,OAAO,CAACpE,MAAM,OAAO,CAACtF,OAAO;QAACA;KAAI,GAAGA;IAC3C,IAAM2J,aAAaD,KAAK,MAAM;IAC9B,IAAIE,YAAYD,eAAe;IAC/B,IAAIE,eAAevD;IACnB,IAAIwD,YAAYvS;IAEhB,IAAKA,IAAI,GAAGA,IAAIoS,YAAYpS,IAAK;QAChCuS,aAAaJ,IAAI,CAACnS,EAAE;QAEpB,IAAIsS,gBAAgBA,aAAa,cAAc,CAACC,aAAa;YAC5DD,eAAeA,YAAY,CAACC,WAAW;YAEvC;QACD;QAEAF,YAAY;QAEZ;IACD;IAEA,IAAIA,aAAcH,mBAAmBI,iBAAiB7D,WACrD,OAAO,OAAOwD,iBAAiB,aAAaA,iBAAiBA;IAE9D,OAAOK;AACR,EAAE;AAEK,IAAME,YAAY,SAAC7a;QAAM8a,8EAAa;IAC5C,IAAIC,SAAS,CAAC;IACd,IAAIjK,KAAK6J;IAET,IAAIvE,MAAM,OAAO,CAACpW,OAAO;QACxB+a,SAAS/a,KAAK,GAAG,CAACkJ,SAAAA;mBAAU4Q,CAAAA,OAAO5Q,sCAAP4Q,SAAO5Q,MAAI,MAAM,WAAY2R,UAAU3R,OAAO,SAAS4N;;QAEnF,wBAAwB,GACxB,OAAQ,CAACgE,cAAcC,OAAO,IAAI,CAAC7R,SAAAA;mBAASA,UAAU4N;aAAc5D,yFAAcA,GAAG6H;IACtF;IAEA,wBAAwB,GACxB,IAAIjB,CAAAA,OAAO9Z,qCAAP8Z,SAAO9Z,KAAG,MAAM,UAAU;QAC7B,IAAK8Q,OAAO9Q,KAAM;YACjB2a,eAAe3a,IAAI,CAAC8Q,IAAI;YAExBiK,MAAM,CAACjK,IAAI,GAAIgJ,CAAAA,OAAOa,6CAAPb,SAAOa,aAAW,MAAM,WAAYE,UAAUF,cAAc,SAAS7D;QACrF;QAEA,OAAQ,CAACgE,cAAc3Y,OAAO,MAAM,CAAC4Y,QAAQ,IAAI,CAAC7R,SAAAA;mBAASA,UAAU4N;aAAc5D,yFAAcA,GAAG6H;IACrG;IAEA,wBAAwB,GACxB,OAAO/a;AACR,EAAE;AAEF,wBAAwB,GACxB,IAAMgb,gCAAgC5G,SAAAA;IACrC,IAAI6G,kBAAkB7G,QAAQ,KAAK,CAAC,OAAO,KAAK;IAChD,IAAI8G;IAEJ,IAAID,iBACH7G,QAAQ,KAAK,CAAC,OAAO,GAAG;IAEzB8G,sBAAsB9G,QAAQ,qBAAqB;IAEnD,IAAI6G,iBACH7G,QAAQ,KAAK,CAAC,OAAO,GAAG;IAEzB,OAAO8G;AACR;AAEA,wBAAwB,GACjB,IAAMC,yBAAyB,SAACC,UAAUC;IAChD,IAAMC,uBAAuBN,8BAA8BI;IAC3D,IAAMG,uBAAuBP,8BAA8BK;IAE3D,OACCC,qBAAqB,IAAI,GAAGC,qBAAqB,IAAI,GAAGA,qBAAqB,KAAK,IAClFA,qBAAqB,IAAI,GAAGD,qBAAqB,IAAI,GAAGA,qBAAqB,KAAK,IAClFA,qBAAqB,GAAG,GAAGC,qBAAqB,GAAG,GAAGA,qBAAqB,MAAM,IACjFA,qBAAqB,GAAG,GAAGD,qBAAqB,GAAG,GAAGA,qBAAqB,MAAM;AAEnF,EAAE;AAEK,IAAME,eAAe,SAACC,YAAYC,aAAa5K;IACrD,IAAM6K,eAAe;IACrB,wBAAwB,GACxB,IAAMC,UAAU9D,SAAAA;QACf,IAAI,CAACA,WAAW,CAACA,OAAO,CAAChH,IAAI,EAC5B,OAAO6K;QAER,wCAAwC,GACxC,OAAQ,IAAgB,OAAb7D,OAAO,CAAChH,IAAI,EAAC;IACzB;IACA,IAAM+K,mBAAmB3C,uDAASA,CAACC,oDAAKA,CAACsC,YAAYG;IACrD,IAAME,oBAAoB5C,uDAASA,CAACC,oDAAKA,CAACuC,aAAaE;IAEvD,OAAOzZ,OAAO,MAAM,CACnBsR,oDAAKA,CACJ,CAAC,GACDpT,mDAAIA,CAACwb,kBAAkBF,eACvBtb,mDAAIA,CAACyb,mBAAmBH;AAG3B,EAAE;AAEK,IAAMI,uBAAuB,SAACC,cAAcC,gBAAgBC,cAAcC;IAChF,OAAOH,aAAa,SAAS,CAAC,GAAGC,kBAAkBE,cAAcH,aAAa,SAAS,CAACE;AACzF,EAAE;;;;;;;;;;;;;;;ACtM8C;AACG;AACf;AAEpC,6DAAeI,kDAAGA,CAAC,uBAAuB;WAAM,IAAIF,0DAAeA,CAACC,+DAAYA;IAAG;;;;;;ACHnF,gBAAgB,mBAAO,CAAC,iKAAoF;AAC5G,mBAAmB,mBAAO,CAAC,uIAAuE;AAClG,qBAAqB,mBAAO,CAAC,iJAA4E;AACzG,0BAA0B,mBAAO,CAAC,6KAA0F;AAC5H,+BAA+B,mBAAO,CAAC,qJAA8E;AACrH,gCAAgC,mBAAO,CAAC,mJAA6E;AACrH,oBAAoB,mBAAO,CAAC,qaAA2L;AACvN;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA,cAAc;;;;;;ACtBd,gBAAgB,mBAAO,CAAC,iKAAoF;AAC5G,mBAAmB,mBAAO,CAAC,uIAAuE;AAClG,qBAAqB,mBAAO,CAAC,iJAA4E;AACzG,0BAA0B,mBAAO,CAAC,6KAA0F;AAC5H,+BAA+B,mBAAO,CAAC,qJAA8E;AACrH,gCAAgC,mBAAO,CAAC,mJAA6E;AACrH,oBAAoB,mBAAO,CAAC,kaAA0L;AACtN;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA,cAAc;;;;;;ACtBd,gBAAgB,mBAAO,CAAC,oKAAuF;AAC/G,mBAAmB,mBAAO,CAAC,0IAA0E;AACrG,qBAAqB,mBAAO,CAAC,oJAA+E;AAC5G,0BAA0B,mBAAO,CAAC,gLAA6F;AAC/H,+BAA+B,mBAAO,CAAC,wJAAiF;AACxH,gCAAgC,mBAAO,CAAC,sJAAgF;AACxH,oBAAoB,mBAAO,CAAC,oaAAuL;AACnN;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;;;AAIA,cAAc"}