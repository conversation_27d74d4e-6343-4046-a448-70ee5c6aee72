define(["react-dom","react","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","tokens!reactorCmps/tokens/general","create-react-class"], function(__WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_create_react_class__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["analytics/customFieldWizard"], {
"./src/analytics/customFieldWizard/Main.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
__webpack_require__(/*! core-js/modules/es.array.splice.js */ "../node_modules/core-js/modules/es.array.splice.js");
__webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
__webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var React = __webpack_require__(/*! react */ "react"), Button = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Button/Button */ "../reactor/src/Atomic/components/Atoms/Button/Button.jsx"), CustomFieldWizard, disabledProps = {}, classColor = 'btnWizard ', editData = {}, me = undefined;
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
__webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");
CustomFieldWizard = createReactClass({
    propTypes: {
        initialItems: PropTypes.array,
        onUpdateList: PropTypes.func,
        onButtonClick: PropTypes.func.isRequired
    },
    getInitialState: function getInitialState() {
        return {
            listItems: this.props.initialItems,
            showCreateButton: false
        };
    },
    componentDidMount: function componentDidMount() {
        var thing = $('#calcFieldList');
        var extra = 10;
        var old = $(thing).scrollTop();
        /* istanbul ignore next */ $(thing).scroll(function() {
            if ($(thing).scrollTop() < old) {
                $(thing).scrollTop($(thing).scrollTop() - extra);
            } else if ($(thing).scrollTop() > old) {
                $(thing).scrollTop($(thing).scrollTop() + extra);
            }
            old = $(thing).scrollTop();
        });
    },
    /*eslint-disable */ openModal: function openModal(index, data) {
        editData = {};
        editData.owner = this;
        /* istanbul ignore next */ if (index !== undefined) {
            editData.indexItem = index;
            editData.editData = data;
        }
        /* istanbul ignore next */ if (editData.editData && typeof OlapReact !== "undefined") editData.editData.formula = OlapReact.replaceLabelsFormula(editData.editData.formula, window.analysis.BIAnalysis.dataset.DataSetSQLQuery.fields.ArrayList);
        this.props.onButtonClick.call(this, editData);
    },
    /* eslint-enable */ add: function add(data) {
        if (data) {
            this.state.listItems.push({
                id: data.text,
                text: data.text,
                formula: data.formula,
                icon: data.type
            });
            this.forceUpdate();
            if (this.props.onUpdateList) this.props.onUpdateList();
        }
    },
    edit: function edit(index, data) {
        var newListItem;
        if (index !== undefined && data) {
            newListItem = this.state.listItems;
            newListItem[index] = {
                id: data.text,
                text: data.text,
                formula: data.formula,
                icon: data.type
            };
            this.setState({
                listItems: newListItem
            });
            this.forceUpdate();
            if (this.props.onUpdateList) this.props.onUpdateList();
        }
    },
    remove: function remove(index) {
        if (index !== undefined) {
            this.state.listItems.splice(index, 1);
            this.forceUpdate();
            if (this.props.onUpdateList) this.props.onUpdateList();
        }
    },
    enableCreateButton: function enableCreateButton() {
        this.setState({
            showCreateButton: true
        });
    },
    disableCreateButton: function disableCreateButton() {
        this.setState({
            showCreateButton: false
        });
    },
    getStore: function getStore() {
        return this.state.listItems;
    },
    render: function render() {
        me = this;
        disabledProps = {};
        if (!me.state.showCreateButton) {
            disabledProps.disabled = true;
        } else {
            classColor += 'blueWizard ';
        }
        return /*#__PURE__*/ React.createElement("div", {
            id: 'calculated-field-wizard'
        }, /*#__PURE__*/ React.createElement("span", {
            id: 'calcFieldTitle'
        }, SE.t(218335)), /*#__PURE__*/ React.createElement("div", {
            id: 'calcFieldList'
        }, me.state.listItems.map(function(value, index) {
            return /*#__PURE__*/ React.createElement("div", {
                className: 'calculatedFieldLine',
                key: value.id
            }, /*#__PURE__*/ React.createElement("div", null, /*#__PURE__*/ React.createElement("div", {
                style: {
                    width: '85%',
                    height: '35px',
                    float: 'left',
                    cursor: 'pointer',
                    paddingLeft: '15px'
                },
                onClick: me.openModal.bind(me, index, value)
            }, /*#__PURE__*/ React.createElement("div", {
                className: value.icon
            }), /*#__PURE__*/ React.createElement("div", {
                className: 'calcFieldText'
            }, value.text), /*#__PURE__*/ React.createElement("div", {
                className: 'seicon-formula'
            })), /*#__PURE__*/ React.createElement("div", {
                className: 'seicon-trash-alt',
                style: {
                    width: '15%',
                    float: 'right',
                    position: 'relative'
                },
                onClick: me.remove.bind(me, index)
            })));
        })), /*#__PURE__*/ React.createElement("div", {
            style: {
                marginTop: '31px',
                width: '100%',
                paddingLeft: '10px',
                paddingRight: '10px'
            }
        }, /*#__PURE__*/ React.createElement(Button, _object_spread_props(_object_spread({
            id: 'addCalcField',
            icon: 'seicon-plus',
            className: classColor,
            onClick: this.openModal.bind(me, undefined, undefined)
        }, disabledProps), {
            block: true
        }), SE.t(219341))));
    },
    displayName: "CustomFieldWizard"
});
/* <div className={"seicon-" + value.icon}></div> */ module.exports = {
    component: CustomFieldWizard
};


}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/analytics/customFieldWizard/Main.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=customFieldWizard.js.map