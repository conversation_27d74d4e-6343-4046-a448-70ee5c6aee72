{"version": 3, "file": "analytics/ShowFieldCheckbox.js", "sources": ["webpack://watch1749037385376/./src/analytics/dropdownOlap/showFieldCheckbox/ShowFieldCheckbox.jsx"], "sourcesContent": ["/* istanbul ignore file */\nvar PropTypes = require('prop-types');\nvar React = require('react');\nvar Checkbox = require('reactor/src/Form/components/Mols/Checkbox/Checkbox');\nvar createReactClass = require('create-react-class');\n\nvar ShowField;\n\nvar divVisible = {\n\tdisplay: 'block'\n};\n\nvar divHidden = {\n\tdisplay: 'none'\n};\n\nShowField = createReactClass({\n\tdisplayName: Checkbox.displayName,\n\n\tpropTypes: {\n\t\tscope: PropTypes.object,\n\t\tcubeType: PropTypes.string\n\t},\n\n\tgetInitialState: function() {\n\t\tvar me = this;\n\n\t\treturn {\n\t\t\tscope: me.props.scope,\n\t\t\thideField: me.props.scope.__field.hideField,\n\t\t\tviewType: me.props.scope.olapConfig.currentViewType.type,\n\t\t\tcubeType: me.props.cubeType\n\t\t};\n\t},\n\n\thandleCheckboxChange: function(checked) {\n\t\tvar me = this;\n\t\tvar _scope = me.state.scope;\n\n\t\t_scope.__field.hideField = checked;\n\n\t\tme.setState({\n\t\t\thideField: checked,\n\t\t\tscope: _scope\n\t\t});\n\t},\n\n\tshouldShowCheckbox: function(viewType, cubeType) {\n\t\tif (viewType === 'TABLE') {\n\t\t\tif (cubeType === 'dimensions' || cubeType === 'measures')\n\t\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t},\n\n\trender: function() {\n\t\tvar me = this,\n\t\t\tstate = me.state,\n\t\t\t_style = me.shouldShowCheckbox(state.viewType, state.cubeType) ? divVisible : divHidden;\n\n\t\treturn (\n\t\t\t<div style={_style}>\n\t\t\t\t<Checkbox\n\t\t\t\t\tlabel={SE.t(206966)}\n\t\t\t\t\tvalue={state.hideField}\n\t\t\t\t\tonChange={me.handleCheckboxChange}\n\t\t\t\t/>\n\t\t\t</div>\n\t\t);\n\t}\n});\n\nmodule.exports = ShowField;"], "names": ["PropTypes", "require", "React", "Checkbox", "createReactClass", "ShowField", "divVisible", "divHidden", "getInitialState", "me", "handleCheckboxChange", "checked", "_scope", "shouldShowCheckbox", "viewType", "cubeType", "render", "state", "_style", "SE", "module"], "mappings": ";;;AAAA,wBAAwB,GACxB,IAAIA,YAAYC,mBAAOA,CAAC,uDAAY;AACpC,IAAIC,QAAQD,mBAAOA,CAAC,oBAAO;AAC3B,IAAIE,WAAWF,mBAAOA,CAAC,qHAAoD;AAC3E,IAAIG,mBAAmBH,mBAAOA,CAAC,8CAAoB;AAEnD,IAAII;AAEJ,IAAIC,aAAa;IAChB,SAAS;AACV;AAEA,IAAIC,YAAY;IACf,SAAS;AACV;AAEAF,YAAYD,iBAAiB;IAC5B,aAAaD,SAAS,WAAW;IAEjC,WAAW;QACV,OAAOH,UAAU,MAAM;QACvB,UAAUA,UAAU,MAAM;IAC3B;IAEAQ,iBAAiB,SAAjBA;QACC,IAAIC,KAAK,IAAI;QAEb,OAAO;YACN,OAAOA,GAAG,KAAK,CAAC,KAAK;YACrB,WAAWA,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS;YAC3C,UAAUA,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI;YACxD,UAAUA,GAAG,KAAK,CAAC,QAAQ;QAC5B;IACD;IAEAC,sBAAsB,SAAtBA,qBAA+BC,OAAO;QACrC,IAAIF,KAAK,IAAI;QACb,IAAIG,SAASH,GAAG,KAAK,CAAC,KAAK;QAE3BG,OAAO,OAAO,CAAC,SAAS,GAAGD;QAE3BF,GAAG,QAAQ,CAAC;YACX,WAAWE;YACX,OAAOC;QACR;IACD;IAEAC,oBAAoB,SAApBA,mBAA6BC,QAAQ,EAAEC,QAAQ;QAC9C,IAAID,aAAa,SAAS;YACzB,IAAIC,aAAa,gBAAgBA,aAAa,YAC7C,OAAO;QACT;QAEA,OAAO;IACR;IAEAC,QAAQ,SAARA;QACC,IAAIP,KAAK,IAAI,EACZQ,QAAQR,GAAG,KAAK,EAChBS,SAAST,GAAG,kBAAkB,CAACQ,MAAM,QAAQ,EAAEA,MAAM,QAAQ,IAAIX,aAAaC;QAE/E,qBACC,oBAAC;YAAI,OAAOW;yBACX,oBAACf;YACA,OAAOgB,GAAG,CAAC,CAAC;YACZ,OAAOF,MAAM,SAAS;YACtB,UAAUR,GAAG,oBAAoB;;IAIrC;AACD;AAEAW,cAAc,GAAGf"}