"use strict";
define(["tokens!reactorCmps/tokens/general","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","create-react-class"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_create_react_class__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["analytics/dropdownOlap"], {
"./src/analytics/constants/dataSources.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    NO_DATA_SOURCE: 'NO_DATA_SOURCE',
    SQL_SERVER: 'SQL_SERVER',
    MYSQL: 'MYSQL',
    ORACLE: 'ORACLE',
    HSQLDB: 'HSQLDB',
    FIREBIRD: 'FIREBIRD',
    POSTGRE: 'POSTGRE',
    ODBC: 'ODBC',
    ODBC_DB2: 'ODBC_DB2',
    ODBC_SQL_SERVER: 'ODBC_SQL_SERVER',
    ODBC_ORACLE: 'ODBC_ORACLE',
    INFORMIX: 'INFORMIX',
    DB2: 'DB2',
    DERBY: 'Derby',
    INTERBASE: 'Interbase'
});


}),
"./src/analytics/dropdownOlap/DropdownOlap.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var reactor_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Link/Link */ "../reactor/src/Atomic/components/Atoms/Link/Link.jsx");
/* ESM import */var reactor_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactor_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactor/src/Atomic/components/Helpers/Language/tokenManagerHOC */ "../reactor/src/Atomic/components/Helpers/Language/tokenManagerHOC.js");
/* ESM import */var reactor_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactorCmps_src_analytics_constants_dataSources__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactorCmps/src/analytics/constants/dataSources */ "./src/analytics/constants/dataSources.js");
/* ESM import */var reactorCmps_src_analytics_dropdownOlap_customCheckboxOlap_CustomCheckbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactorCmps/src/analytics/dropdownOlap/customCheckboxOlap/CustomCheckbox */ "./src/analytics/dropdownOlap/customCheckboxOlap/CustomCheckbox.jsx");
/* ESM import */var reactorCmps_src_analytics_dropdownOlap_customPillsOlap_AxisPillsOlap__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactorCmps/src/analytics/dropdownOlap/customPillsOlap/AxisPillsOlap */ "./src/analytics/dropdownOlap/customPillsOlap/AxisPillsOlap.jsx");
/* istanbul ignore file */ function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _call_super(_this, derived, args) {
    derived = _get_prototype_of(derived);
    return _possible_constructor_return(_this, _is_native_reflect_construct() ? Reflect.construct(derived, args || [], _get_prototype_of(_this).constructor) : derived.apply(_this, args));
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _is_native_reflect_construct() {
    try {
        var result = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
    } catch (_) {}
    return (_is_native_reflect_construct = function() {
        return !!result;
    })();
}








var DropdownOlap = /*#__PURE__*/ function(Component) {
    "use strict";
    _inherits(DropdownOlap, Component);
    function DropdownOlap() {
        _class_call_check(this, DropdownOlap);
        return _call_super(this, DropdownOlap, arguments);
    }
    _create_class(DropdownOlap, [
        {
            key: "UNSAFE_componentWillMount",
            value: function UNSAFE_componentWillMount() {
                var owner = this.props.owner;
                var type = owner.olapConfig.currentViewType.type;
                var datatype = owner.__field.datatype;
                var className = owner.fieldAttributeName;
                var dataSource = this.getDataSource();
                var showAxis = this.canShowAxisPills(type, className);
                var showFacilitator = this.canShowFacilitator(type, datatype, className, dataSource);
                this.setState({
                    showAxis: showAxis,
                    showFacilitator: showFacilitator
                });
                this.handleConfiguratorOptionsLinkClick = this.handleConfiguratorOptionsLinkClick.bind(this);
            }
        },
        {
            key: "componentDidMount",
            value: function componentDidMount() {
                var _this_props = this.props, hideElement = _this_props.hideElement, showElement = _this_props.showElement;
                var _this_state = this.state, showAxis = _this_state.showAxis, showFacilitator = _this_state.showFacilitator;
                var axisSelector = '.olapReact-axis';
                var facilitatorSelector = '.olapReact-checkbox';
                if (showAxis) showElement(axisSelector);
                else hideElement(axisSelector);
                if (showFacilitator) showElement(facilitatorSelector);
                else hideElement(facilitatorSelector);
            }
        },
        {
            key: "getDataSource",
            value: function getDataSource() {
                var dataset = this.props.owner.olapConfig.analyse.dataset.DataSetSQLQuery;
                if (dataset.dataSource) return dataset.dataSource.DataSourceDatabase.fgDriverType;
                return reactorCmps_src_analytics_constants_dataSources__WEBPACK_IMPORTED_MODULE_4__["default"].NO_DATA_SOURCE;
            }
        },
        {
            key: "canShowAxisPills",
            value: function canShowAxisPills(viewType, className) {
                var owner = this.props.owner;
                var hasValidClassName, hasAxisSupport;
                /* istanbul ignore next */ if (!owner.shouldShowField()) return false;
                hasValidClassName = className === 'measure' || className === 'measures' || className === 'CubeMeasure';
                hasAxisSupport = viewType === 'COLUMN' || viewType === 'LINE' || viewType === 'AREA';
                return hasValidClassName && hasAxisSupport;
            }
        },
        {
            key: "canShowFacilitator",
            value: function canShowFacilitator(type, datatype, className, dataSource) {
                var shouldAdd = false;
                if (dataSource === reactorCmps_src_analytics_constants_dataSources__WEBPACK_IMPORTED_MODULE_4__["default"].MYSQL) return false;
                if (className === 'measure' || className === 'measures' || className === 'CubeMeasure') {
                    if (type !== 'PIE' && type !== 'PARETO' && type !== 'ANGULAR' && type !== 'SINGLE_NUMBER' && type !== 'LINEAR_HORIZONTAL') {
                        if (datatype === 'INTEGER' || datatype === 'LONG' || datatype === 'DECIMAL' || datatype === 'CURRENCY') shouldAdd = true;
                    }
                }
                return shouldAdd;
            }
        },
        {
            key: "handleConfiguratorOptionsLinkClick",
            value: function handleConfiguratorOptionsLinkClick() {
                var owner = this.props.owner;
                owner.openConfigurator(owner.__field.uid);
            }
        },
        {
            key: "renderConfiguratorOptionsLink",
            value: function renderConfiguratorOptionsLink() {
                var _this_props = this.props, getToken = _this_props.getToken, owner = _this_props.owner;
                /* istanbul ignore next */ if (!owner.shouldShowConfiguratorOptionsLink()) return null;
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_2___default()), {
                    onClick: this.handleConfiguratorOptionsLinkClick
                }, getToken(209175) + '...');
            }
        },
        {
            key: "render",
            value: function render() {
                var _this_props = this.props, defaultActiveKey = _this_props.defaultActiveKey, owner = _this_props.owner;
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
                    className: 'olapReact-checkbox'
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(reactorCmps_src_analytics_dropdownOlap_customCheckboxOlap_CustomCheckbox__WEBPACK_IMPORTED_MODULE_5__["default"], {
                    owner: owner
                })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
                    className: 'olapReact-axis'
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(reactorCmps_src_analytics_dropdownOlap_customPillsOlap_AxisPillsOlap__WEBPACK_IMPORTED_MODULE_6__["default"], {
                    owner: owner,
                    defaultActiveKey: defaultActiveKey
                })), this.renderConfiguratorOptionsLink());
            }
        }
    ]);
    return DropdownOlap;
}(react__WEBPACK_IMPORTED_MODULE_1__.Component);
DropdownOlap.propTypes = {
    owner: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().object),
    defaultActiveKey: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().number),
    hideElement: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func),
    showElement: (prop_types__WEBPACK_IMPORTED_MODULE_7___default().func)
};
DropdownOlap.displayName = 'Analytics/dropdownOlap/DropdownOlap';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reactor_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_3___default()(DropdownOlap));


}),
"./src/analytics/dropdownOlap/customCheckboxOlap/CustomCheckbox.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return CustomCheckboxOlap; }
});
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var reactor_src_Form_components_Mols_Checkbox_Checkbox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor/src/Form/components/Mols/Checkbox/Checkbox */ "../reactor/src/Form/components/Mols/Checkbox/Checkbox.jsx");
/* ESM import */var reactor_src_Form_components_Mols_Checkbox_Checkbox__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Form_components_Mols_Checkbox_Checkbox__WEBPACK_IMPORTED_MODULE_2__);
/* istanbul ignore file */ function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _call_super(_this, derived, args) {
    derived = _get_prototype_of(derived);
    return _possible_constructor_return(_this, _is_native_reflect_construct() ? Reflect.construct(derived, args || [], _get_prototype_of(_this).constructor) : derived.apply(_this, args));
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _is_native_reflect_construct() {
    try {
        var result = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
    } catch (_) {}
    return (_is_native_reflect_construct = function() {
        return !!result;
    })();
}




var checkboxLabelStyle = {
    display: 'block',
    fontSize: 13
};
var CustomCheckboxOlap = /*#__PURE__*/ function(Component) {
    "use strict";
    _inherits(CustomCheckboxOlap, Component);
    function CustomCheckboxOlap(props) {
        _class_call_check(this, CustomCheckboxOlap);
        var _this;
        _this = _call_super(this, CustomCheckboxOlap, [
            props
        ]);
        _this.state = {
            owner: props.owner,
            percentage: props.owner.__field.percentage,
            accumulated: props.owner.__field.accumulated
        };
        return _this;
    }
    _create_class(CustomCheckboxOlap, [
        {
            key: "componentDidMount",
            value: function componentDidMount() {
                this.forceDisableCheckboxOnCanAggregate();
            }
        },
        {
            key: "UNSAFE_componentWillReceiveProps",
            value: function UNSAFE_componentWillReceiveProps(nextProps) {
                this.handleCancelActions(nextProps);
            }
        },
        {
            key: "setPercentageFormatter",
            value: function setPercentageFormatter(field) {
                /* istanbul ignore else */ if (!field.formatter) field.formatter = 'NUMBER';
                /* istanbul ignore else */ if (!field.suffix) field.suffix = '%';
                field.percentage = true;
            }
        },
        {
            key: "removePercentageFormatter",
            value: function removePercentageFormatter(field) {
                /* istanbul ignore else */ if (field.suffix === '%') field.suffix = '';
                field.percentage = false;
            }
        },
        {
            key: "setAccumulatedFormatter",
            value: function setAccumulatedFormatter(field) {
                if (!field.formatter) field.formatter = 'NUMBER';
                /* istanbul ignore else */ if (!field.suffix) field.suffix = '';
            }
        },
        {
            key: "forceButtonLabelUpdate",
            value: function forceButtonLabelUpdate() {
                /* istanbul ignore next */ var order = this.state.owner.__field.order, seformatter = $('seformatter button:visible'), semeasure = $($('semeasure seformatter button').get(order - 1)), formatter = seformatter.length ? seformatter : semeasure, formatterScope = formatter && formatter.scope ? formatter.scope() : {
                    forceButtonLabelUpdate: function() {}
                };
                formatterScope.forceButtonLabelUpdate(this.state.owner.__field);
            }
        },
        {
            key: "forceDisableCheckboxOnCanAggregate",
            value: function forceDisableCheckboxOnCanAggregate() {
                var me = this, _owner = me.state.owner;
                /* istanbul ignore next */ if (!_owner.__field.canAggregate) {
                    _owner.__field.percentage = false;
                    _owner.__field.accumulated = false;
                    /* istanbul ignore next */ if (_owner.__field.formatter) _owner.__field.formatter = 'NUMBER';
                    /* istanbul ignore next */ if (_owner.__field.suffix) _owner.__field.suffix = '';
                    me.setState({
                        percentage: false,
                        accumulated: false,
                        owner: _owner
                    });
                }
            }
        },
        {
            key: "onChangePercentage",
            value: function onChangePercentage(checked) {
                var _owner = this.state.owner;
                _owner.__field.percentage = checked;
                this.setState({
                    percentage: checked,
                    owner: _owner
                });
                if (checked) this.setPercentageFormatter(this.state.owner.__field);
                else this.removePercentageFormatter(this.state.owner.__field);
                this.forceButtonLabelUpdate();
            }
        },
        {
            key: "onChangeAccumulated",
            value: function onChangeAccumulated(checked) {
                var _owner = this.state.owner;
                _owner.__field.accumulated = checked;
                this.setState({
                    accumulated: checked,
                    owner: _owner
                });
                if (checked) this.setAccumulatedFormatter(this.state.owner.__field);
                this.forceButtonLabelUpdate();
            }
        },
        {
            key: "handleCancelActions",
            value: function handleCancelActions(nextProps) {
                var me = this, state = me.state, _percent, _accumulated, _owner;
                if (nextProps.owner.__field.editCanceled) {
                    /* istanbul ignore next */ _percent = nextProps.owner.__field.percentage !== undefined ? nextProps.owner.__field.percentage : state.owner.initialPercentage;
                    /* istanbul ignore next */ _accumulated = nextProps.owner.__field.accumulated !== undefined ? nextProps.owner.__field.accumulated : state.owner.initialAccumulated;
                    _owner = state.owner;
                    _owner.__field = nextProps.owner.__field;
                    _owner.__field.editCanceled = false;
                    this.setState({
                        percentage: _percent,
                        accumulated: _accumulated,
                        owner: _owner
                    });
                    me.forceButtonLabelUpdate();
                }
            }
        },
        {
            key: "render",
            value: function render() {
                var me = this, state = me.state;
                me.forceButtonLabelUpdate();
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("label", {
                    style: checkboxLabelStyle
                }, SE.t('100414')), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
                    className: 'form-group olapPercent'
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor_src_Form_components_Mols_Checkbox_Checkbox__WEBPACK_IMPORTED_MODULE_2___default()), {
                    label: SE.t('101835'),
                    value: state.percentage,
                    onChange: me.onChangePercentage.bind(me),
                    disabled: !me.state.owner.__field.canAggregate
                })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
                    className: 'form-group olapAcumulated'
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor_src_Form_components_Mols_Checkbox_Checkbox__WEBPACK_IMPORTED_MODULE_2___default()), {
                    label: SE.t('105597'),
                    value: state.accumulated,
                    onChange: me.onChangeAccumulated.bind(me),
                    disabled: !me.state.owner.__field.canAggregate
                })));
            }
        }
    ]);
    return CustomCheckboxOlap;
}(react__WEBPACK_IMPORTED_MODULE_1__.Component);

CustomCheckboxOlap.propTypes = {
    owner: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().object)
};
CustomCheckboxOlap.displayName = 'Analytics/dropdownOlap/customCheckboxOlap/CustomCheckbox';


}),
"./src/analytics/dropdownOlap/customPillsOlap/AxisPillsOlap.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return AxisPills; }
});
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var reactor_src_Atomic_components_Orgs_Tabs_Tab__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor/src/Atomic/components/Orgs/Tabs/Tab */ "../reactor/src/Atomic/components/Orgs/Tabs/Tab.js");
/* ESM import */var reactor_src_Atomic_components_Orgs_Tabs_Tab__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Orgs_Tabs_Tab__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactor_src_Atomic_components_Orgs_Pills_Pills__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactor/src/Atomic/components/Orgs/Pills/Pills */ "../reactor/src/Atomic/components/Orgs/Pills/Pills.jsx");
/* ESM import */var reactor_src_Atomic_components_Orgs_Pills_Pills__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Orgs_Pills_Pills__WEBPACK_IMPORTED_MODULE_3__);
/* istanbul ignore file */ function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _call_super(_this, derived, args) {
    derived = _get_prototype_of(derived);
    return _possible_constructor_return(_this, _is_native_reflect_construct() ? Reflect.construct(derived, args || [], _get_prototype_of(_this).constructor) : derived.apply(_this, args));
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _is_native_reflect_construct() {
    try {
        var result = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
    } catch (_) {}
    return (_is_native_reflect_construct = function() {
        return !!result;
    })();
}





var axisLabelStyle = {
    display: 'block',
    fontSize: 13
};
var AxisPills = /*#__PURE__*/ function(Component) {
    "use strict";
    _inherits(AxisPills, Component);
    function AxisPills(props) {
        _class_call_check(this, AxisPills);
        var _this;
        _this = _call_super(this, AxisPills, [
            props
        ]);
        _this.state = _this.getDefaultState(props);
        return _this;
    }
    _create_class(AxisPills, [
        {
            key: "getDefaultState",
            value: function getDefaultState(props) {
                var _owner = props.owner, _index = props.defaultActiveKey;
                _owner.__field.rightAxis = _index === 2 ? true : false;
                return {
                    owner: _owner,
                    activeKey: _index
                };
            }
        },
        {
            key: "pillsChange",
            value: function pillsChange(index) {
                var me = this, rightAxis = index === 2 ? true : false, _owner = me.state.owner;
                _owner.__field.rightAxis = rightAxis;
                me.setState({
                    owner: _owner,
                    activeKey: index
                });
            }
        },
        {
            key: "render",
            value: function render() {
                var me = this, leftAxis = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("span", {
                    className: 'stepTabIcon seicon-left-axis'
                }, SE.t('219031')), rightAxis = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("span", {
                    className: 'stepTabIcon seicon-right-axis'
                }, SE.t('211994'));
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("label", {
                    style: axisLabelStyle
                }, SE.t('214821')), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor_src_Atomic_components_Orgs_Pills_Pills__WEBPACK_IMPORTED_MODULE_3___default()), {
                    defaultActiveKey: me.props.defaultActiveKey,
                    hasArrow: false,
                    id: 'axisPills',
                    className: 'stepTabContainer',
                    onSelect: me.pillsChange.bind(me),
                    activeKey: me.state.activeKey,
                    animation: true
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor_src_Atomic_components_Orgs_Tabs_Tab__WEBPACK_IMPORTED_MODULE_2___default()), {
                    eventKey: 1,
                    title: leftAxis
                }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor_src_Atomic_components_Orgs_Tabs_Tab__WEBPACK_IMPORTED_MODULE_2___default()), {
                    eventKey: 2,
                    title: rightAxis
                })));
            }
        }
    ]);
    return AxisPills;
}(react__WEBPACK_IMPORTED_MODULE_1__.Component);

AxisPills.propTypes = {
    owner: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().object),
    defaultActiveKey: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().number)
};
AxisPills.displayName = 'Analytics/dropdownOlap/customPillsOlap/AxisPillsOlap';


}),
"Utils": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"reactorCmps/tokens/general": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/analytics/dropdownOlap/DropdownOlap.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=dropdownOlap.js.map