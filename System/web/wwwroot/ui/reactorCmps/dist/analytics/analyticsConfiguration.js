"use strict";
define(["Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","Connector","Storage","tokens!reactorCmps/tokens/general"], function(__WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEBPACK_EXTERNAL_MODULE_Storage__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["analytics/analyticsConfiguration"], {
"./src/analytics/constants/storageKeys.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  AUTO_UPDATE: function() { return AUTO_UPDATE; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var prefix = 'Analytics';
var AUTO_UPDATE = prefix + 'AutoUpdate';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    AUTO_UPDATE: AUTO_UPDATE
});


}),
"./src/analytics/helpers/analyticsConfiguration.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ "../node_modules/core-js/modules/es.object.keys.js");
/* ESM import */var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! Connector */ "Connector");
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(Connector__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var Storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! Storage */ "Storage");
/* ESM import */var Storage__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(Storage__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactorCmps_src_analytics_constants_storageKeys__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactorCmps/src/analytics/constants/storageKeys */ "./src/analytics/constants/storageKeys.js");
/* ESM import */var reactorCmps_src_chartengine2_constants_storageKeys__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactorCmps/src/chartengine2/constants/storageKeys */ "./src/chartengine2/constants/storageKeys.js");
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
    try {
        var info = gen[key](arg);
        var value = info.value;
    } catch (error) {
        reject(error);
        return;
    }
    if (info.done) {
        resolve(value);
    } else {
        Promise.resolve(value).then(_next, _throw);
    }
}
function _async_to_generator(fn) {
    return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
            var gen = fn.apply(self, args);
            function _next(value) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
            }
            function _throw(err) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
            }
            _next(undefined);
        });
    };
}
function _ts_generator(thisArg, body) {
    var f, y, t, g, _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    };
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}





var cleanStorage = function() {
    Storage__WEBPACK_IMPORTED_MODULE_2___default().remove('AnalyticsConfiguratorType');
    Storage__WEBPACK_IMPORTED_MODULE_2___default().remove('AnalyticsNewViewConfiguratorModal');
    Storage__WEBPACK_IMPORTED_MODULE_2___default().remove('AnalyticsViewConfigurator');
    Storage__WEBPACK_IMPORTED_MODULE_2___default().remove('ChartEngineAutoUpdate');
    Storage__WEBPACK_IMPORTED_MODULE_2___default().remove('ChartEngineConfigurator');
    Storage__WEBPACK_IMPORTED_MODULE_2___default().remove('ChartEngineViewConfigurator');
    Storage__WEBPACK_IMPORTED_MODULE_2___default().remove('exporter_scn');
    Storage__WEBPACK_IMPORTED_MODULE_2___default().remove('exporter_url');
};
var storeData = function(param) {
    var deferUpdate = param.deferUpdate, standardTheme = param.standardTheme;
    if (standardTheme !== undefined) Storage__WEBPACK_IMPORTED_MODULE_2___default().set(reactorCmps_src_chartengine2_constants_storageKeys__WEBPACK_IMPORTED_MODULE_4__.TEMPLATE, {
        colors: JSON.parse(standardTheme)
    });
    if (deferUpdate !== undefined) Storage__WEBPACK_IMPORTED_MODULE_2___default().set(reactorCmps_src_analytics_constants_storageKeys__WEBPACK_IMPORTED_MODULE_3__.AUTO_UPDATE, !Boolean(deferUpdate));
};
var fetchData = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function() {
        return _ts_generator(this, function(_state) {
            return [
                2,
                Connector__WEBPACK_IMPORTED_MODULE_1___default().callKatana('chartengine/configuration').then(storeData)
            ];
        });
    });
    return function fetchData() {
        return _ref.apply(this, arguments);
    };
}();
var setAnalyticsParams = function() {
    cleanStorage();
    return fetchData();
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    cleanStorage: cleanStorage,
    setAnalyticsParams: setAnalyticsParams,
    fetch: fetchData,
    saveInStorage: storeData
});


}),
"./src/chartengine2/constants/storageKeys.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  TEMPLATE: function() { return TEMPLATE; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var prefix = 'ChartEngine';
var TEMPLATE = prefix + 'Template';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    TEMPLATE: TEMPLATE
});


}),
"Connector": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"Storage": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Storage__;

}),
"Utils": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"watch1749037385376": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"reactorCmps/tokens/general": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/analytics/helpers/analyticsConfiguration.js"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=analyticsConfiguration.js.map