{"version": 3, "file": "analytics/ScopeRedux.js", "sources": ["webpack://watch1749037385376/./src/analytics/actions/SortActions.js", "webpack://watch1749037385376/./src/analytics/actions/sortConstants.js", "webpack://watch1749037385376/./src/analytics/components/ScopeRedux/ScopeRedux.js", "webpack://watch1749037385376/./src/analytics/components/ScopeRedux/SortRedux.js"], "sourcesContent": ["import sortConstants from 'reactorCmps/src/analytics/actions/sortConstants';\n\nexport default {\n\tsetData(oid, data) {\n\t\treturn {\n\t\t\ttype: sortConstants.SET_DATA,\n\t\t\toid, data\n\t\t};\n\t},\n\n\taddItem(oid, item, aboveItem) {\n\t\treturn {\n\t\t\ttype: sortConstants.ADD_ITEM,\n\t\t\toid, item, aboveItem\n\t\t};\n\t},\n\n\tsortItem(oid, item, aboveItem) {\n\t\treturn {\n\t\t\ttype: sortConstants.SORT_ITEM,\n\t\t\toid, item, aboveItem\n\t\t};\n\t},\n\n\tremoveItem(oid, item) {\n\t\treturn {\n\t\t\ttype: sortConstants.REMOVE_ITEM,\n\t\t\toid, item\n\t\t};\n\t},\n\n\tsetItemAsc(oid, itemOid) {\n\t\treturn {\n\t\t\ttype: sortConstants.SET_ITEM_ASC,\n\t\t\toid, itemOid\n\t\t};\n\t},\n\n\tsetItemDesc(oid, itemOid) {\n\t\treturn {\n\t\t\ttype: sortConstants.SET_ITEM_DESC,\n\t\t\toid, itemOid\n\t\t};\n\t}\n};", "export default {\n\tSET_DATA: 'SE_ANALYTICS_ORDER_MANAGER_SET_DATA',\n\tADD_ITEM: 'SE_ANALYTICS_ORDER_MANAGER_ADD_ITEM',\n\tREMOVE_ITEM: 'SE_ANALYTICS_ORDER_MANAGER_REMOVE_ITEM',\n\tSORT_ITEM: 'SE_ANALYTICS_ORDER_MANAGER_SORT_ITEM',\n\tSET_ITEM_ASC: 'SE_ANALYTICS_ORDER_MANAGER_SET_ITEM_ASC',\n\tSET_ITEM_DESC: 'SE_ANALYTICS_ORDER_MANAGER_SET_ITEM_DESC'\n};", "import { omit } from 'lodash';\nimport { dispatch } from 'reactor/src/store/store';\nimport SortActions from 'reactorCmps/src/analytics/actions/SortActions';\nimport SortRedux from 'reactorCmps/src/analytics/components/ScopeRedux/SortRedux';\n\nexport default {\n\tscope: {},\n\n\tgetActionsByType: function(type) {\n\t\tconst actions = {\n\t\t\tsort: SortActions\n\t\t};\n\n\t\treturn actions[type];\n\t},\n\n\taction: function(type, action, params) {\n\t\tlet actions = this.getActionsByType(type),\n\t\t\tmethod = actions[action].apply(this, params);\n\n\t\tdispatch(method);\n\t},\n\n\tsortReduxAction: function(type, params) {\n\t\tSortRedux[type].apply(this, params);\n\t},\n\n\tsendGetData: function(scopeOid) {\n\t\tconst scope = this.getScope(scopeOid);\n\n\t\tif (scope.viewScope)\n\t\t\tscope.viewScope.getData(false, false, true);\n\t\telse\n\t\t\tscope.getData(false, false, true);\n\t},\n\n\tsetScope: function(scope) {\n\t\tif (scope.useOlapEditor) {\n\t\t\tthis.scope = scope;\n\t\t\treturn;\n\t\t}\n\n\t\tthis.scope[scope.oid] = omit(scope, 'oid');\n\t},\n\n\tgetScope: function(id) {\n\t\t/* istanbul ignore else */\n\t\tif (!id || !this.scope[id])\n\t\t\treturn this.scope;\n\n\t\t/* istanbul ignore next */\n\t\treturn this.scope[id];\n\t}\n};", "export default {\n\tsetData: function(data) {\n\t\tif (this.scope.cube)\n\t\t\tthis.scope.cube.setCubeSort(data);\n\t},\n\n\tsetCubeSortOrder: function(index, direction, scopeOid) {\n\t\tconst scope = this.getScope(scopeOid);\n\n\t\tscope.cube.setCubeSortOrder(index, direction);\n\n\t\tthis.sendGetData(scopeOid);\n\t},\n\n\tmoveCubeSortItem: function(item, indexToRemove, indexToAdd, removeSort, scopeOid) {\n\t\tconst scope = this.getScope(scopeOid);\n\n\t\tscope.cube.moveCubeSortItem(item, indexToRemove, indexToAdd, removeSort);\n\n\t\tthis.sendGetData(scopeOid);\n\t},\n\n\treorderCubeSortItem: function(oldIndex, newIndex, item, scopeOid) {\n\t\tconst scope = this.getScope(scopeOid);\n\t\tlet cube = scope.cube;\n\n\t\t/* istanbul ignore if */\n\t\tif (cube.reorderCubeSortItem) {\n\t\t\tcube.reorderCubeSortItem(item, newIndex);\n\t\t} else {\n\t\t\tcube.removeCubeSort(oldIndex);\n\t\t\tcube.addCubeSort(item, newIndex);\n\t\t}\n\n\t\tthis.sendGetData(scopeOid);\n\t}\n};"], "names": ["sortConstants", "oid", "data", "item", "aboveItem", "itemOid", "omit", "dispatch", "SortActions", "SortRedux", "getActionsByType", "type", "actions", "action", "_action", "params", "method", "sortReduxAction", "sendGetData", "scopeOid", "scope", "setScope", "getScope", "id", "setData", "setCubeSortOrder", "index", "direction", "moveCubeSortItem", "indexToRemove", "indexToAdd", "removeSort", "reorderCubeSortItem", "oldIndex", "newIndex", "cube"], "mappings": ";;;;;;;;;AAA4E;AAE5E,6DAAe;IACd,kBAAQC,GAAG,EAAEC,IAAI;QAChB,OAAO;YACN,MAAMF,gGAAsB;YAC5BC,KAAAA;YAAKC,MAAAA;QACN;IACD;IAEA,kBAAQD,GAAG,EAAEE,IAAI,EAAEC,SAAS;QAC3B,OAAO;YACN,MAAMJ,gGAAsB;YAC5BC,KAAAA;YAAKE,MAAAA;YAAMC,WAAAA;QACZ;IACD;IAEA,mBAASH,GAAG,EAAEE,IAAI,EAAEC,SAAS;QAC5B,OAAO;YACN,MAAMJ,iGAAuB;YAC7BC,KAAAA;YAAKE,MAAAA;YAAMC,WAAAA;QACZ;IACD;IAEA,qBAAWH,GAAG,EAAEE,IAAI;QACnB,OAAO;YACN,MAAMH,mGAAyB;YAC/BC,KAAAA;YAAKE,MAAAA;QACN;IACD;IAEA,qBAAWF,GAAG,EAAEI,OAAO;QACtB,OAAO;YACN,MAAML,oGAA0B;YAChCC,KAAAA;YAAKI,SAAAA;QACN;IACD;IAEA,sBAAYJ,GAAG,EAAEI,OAAO;QACvB,OAAO;YACN,MAAML,qGAA2B;YACjCC,KAAAA;YAAKI,SAAAA;QACN;IACD;AACD,CAAC,EAAC;;;;;;;;;AC5CF,6DAAe;IACd,UAAU;IACV,UAAU;IACV,aAAa;IACb,WAAW;IACX,cAAc;IACd,eAAe;AAChB,CAAC,EAAC;;;;;;;;;;;;;;;ACP4B;AACqB;AACqB;AACU;AAElF,6DAAe;IACd,OAAO,CAAC;IAERK,kBAAkB,SAAlBA,iBAA2BC,IAAI;QAC9B,IAAMC,UAAU;YACf,MAAMJ,qFAAWA;QAClB;QAEA,OAAOI,OAAO,CAACD,KAAK;IACrB;IAEAE,QAAQ,SAARA,OAAiBF,IAAI,EAAEG,OAAM,EAAEC,MAAM;QACpC,IAAIH,UAAU,IAAI,CAAC,gBAAgB,CAACD,OACnCK,SAASJ,OAAO,CAACE,QAAO,CAAC,KAAK,CAAC,IAAI,EAAEC;QAEtCR,iEAAQA,CAACS;IACV;IAEAC,iBAAiB,SAAjBA,gBAA0BN,IAAI,EAAEI,MAAM;QACrCN,iGAAS,CAACE,KAAK,CAAC,KAAK,CAAC,IAAI,EAAEI;IAC7B;IAEAG,aAAa,SAAbA,YAAsBC,QAAQ;QAC7B,IAAMC,QAAQ,IAAI,CAAC,QAAQ,CAACD;QAE5B,IAAIC,MAAM,SAAS,EAClBA,MAAM,SAAS,CAAC,OAAO,CAAC,OAAO,OAAO;aAEtCA,MAAM,OAAO,CAAC,OAAO,OAAO;IAC9B;IAEAC,UAAU,SAAVA,SAAmBD,KAAK;QACvB,IAAIA,MAAM,aAAa,EAAE;YACxB,IAAI,CAAC,KAAK,GAAGA;YACb;QACD;QAEA,IAAI,CAAC,KAAK,CAACA,MAAM,GAAG,CAAC,GAAGd,kDAAIA,CAACc,OAAO;IACrC;IAEAE,UAAU,SAAVA,SAAmBC,EAAE;QACpB,wBAAwB,GACxB,IAAI,CAACA,MAAM,CAAC,IAAI,CAAC,KAAK,CAACA,GAAG,EACzB,OAAO,IAAI,CAAC,KAAK;QAElB,wBAAwB,GACxB,OAAO,IAAI,CAAC,KAAK,CAACA,GAAG;IACtB;AACD,CAAC,EAAC;;;;;;;;;ACrDF,6DAAe;IACdC,SAAS,SAATA,QAAkBtB,IAAI;QACrB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAClB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAACA;IAC9B;IAEAuB,kBAAkB,SAAlBA,iBAA2BC,KAAK,EAAEC,SAAS,EAAER,QAAQ;QACpD,IAAMC,QAAQ,IAAI,CAAC,QAAQ,CAACD;QAE5BC,MAAM,IAAI,CAAC,gBAAgB,CAACM,OAAOC;QAEnC,IAAI,CAAC,WAAW,CAACR;IAClB;IAEAS,kBAAkB,SAAlBA,iBAA2BzB,IAAI,EAAE0B,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEZ,QAAQ;QAC/E,IAAMC,QAAQ,IAAI,CAAC,QAAQ,CAACD;QAE5BC,MAAM,IAAI,CAAC,gBAAgB,CAACjB,MAAM0B,eAAeC,YAAYC;QAE7D,IAAI,CAAC,WAAW,CAACZ;IAClB;IAEAa,qBAAqB,SAArBA,oBAA8BC,QAAQ,EAAEC,QAAQ,EAAE/B,IAAI,EAAEgB,QAAQ;QAC/D,IAAMC,QAAQ,IAAI,CAAC,QAAQ,CAACD;QAC5B,IAAIgB,OAAOf,MAAM,IAAI;QAErB,sBAAsB,GACtB,IAAIe,KAAK,mBAAmB,EAAE;YAC7BA,KAAK,mBAAmB,CAAChC,MAAM+B;QAChC,OAAO;YACNC,KAAK,cAAc,CAACF;YACpBE,KAAK,WAAW,CAAChC,MAAM+B;QACxB;QAEA,IAAI,CAAC,WAAW,CAACf;IAClB;AACD,CAAC,EAAC"}