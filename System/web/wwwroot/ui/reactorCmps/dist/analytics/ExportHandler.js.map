{"version": 3, "file": "analytics/ExportHandler.js", "sources": ["webpack://watch1749037385376/./src/analytics/constants/exportation/formats.js", "webpack://watch1749037385376/./src/analytics/constants/exportation/margins.js", "webpack://watch1749037385376/./src/analytics/constants/exportation/orientations.js", "webpack://watch1749037385376/./src/analytics/constants/exportation/paperSizes.js", "webpack://watch1749037385376/./src/analytics/helpers/ExportHandler/ExportHandler.js", "webpack://watch1749037385376/./src/analytics/helpers/ExportHandler/ExportHelper.js", "webpack://watch1749037385376/./src/analytics/services/exportation/ExportServices.js"], "sourcesContent": ["export const DOCX = 'DOCX';\n\nexport const HTML = 'HTML';\n\nexport const JRPRINT = 'JRPRINT';\n\nexport const MXLS = 'MXLS';\n\nexport const ODS = 'ODS';\n\nexport const ODT = 'ODT';\n\nexport const PDF = 'PDF';\n\nexport const PNG = 'PNG';\n\nexport const RTF = 'RTF';\n\nexport const XLS = 'XLS';\n\nexport const XLSX = 'XLSX';\n\nexport const jasperExportTypes = [\n\tDOCX,\n\tHTML,\n\tJRPRINT,\n\tODS,\n\tODT,\n\tPDF,\n\tRTF,\n\tXLS\n];\n\nexport default {\n\tDOCX,\n\tHTML,\n\tJRPRINT,\n\tMXLS,\n\tODS,\n\tODT,\n\tPDF,\n\tPNG,\n\tRTF,\n\tXLS,\n\tXLSX\n};", "export const DEFAULT = 'DEFAULT';\n\nexport const NONE = 'NONE';\n\nexport const MINIMAL = 'MINIMAL';\n\nexport default {\n\tDEFAULT,\n\tMINIMAL,\n\tNONE\n};", "export const LANDSCAPE = 'LANDSCAPE';\n\nexport const PORTRAIT = 'PORTRAIT';\n\nexport default {\n\tLANDSCAPE,\n\tPORTRAIT\n};", "export const A4 = 'A4';\n\nexport const AUTO = 'AUTO';\n\nexport const LETTER = 'LETTER';\n\nexport default {\n\tA4,\n\tAUTO,\n\tLETTER\n};", "// eslint-disable-next-line no-unused-vars\nimport jquery from 'jquery'; // FRAM-2025\nimport Utils from 'Utils';\nimport WorkspaceInfo from 'WorkspaceInfo';\nimport { PDF, PNG } from 'reactorCmps/src/analytics/constants/exportation/formats';\nimport ExportHelper from 'reactorCmps/src/analytics/helpers/ExportHandler/ExportHelper';\nimport ExportServices from 'reactorCmps/src/analytics/services/exportation/ExportServices';\n\nexport { default as formats } from 'reactorCmps/src/analytics/constants/exportation/formats';\nexport { default as margins } from 'reactorCmps/src/analytics/constants/exportation/margins';\nexport { default as orientations } from 'reactorCmps/src/analytics/constants/exportation/orientations';\nexport { default as paperSizes } from 'reactorCmps/src/analytics/constants/exportation/paperSizes';\n\nexport const DEFAULT_TIMEOUT = 300000;\n\nconst getSystemParams = () => {\n\tconst systemUrl = Utils.getSystemUrl(true);\n\tconst loginUrl = WorkspaceInfo.isExternal() ? 'external-login' : 'login';\n\n\treturn {\n\t\tdomain: `${systemUrl}/softexpert/${loginUrl}`,\n\t\thost: (new URL(systemUrl)).host,\n\t\tlanguage: WorkspaceInfo.getDefaultLanguage(),\n\t\ttimezone: WorkspaceInfo.getSystemInfo().timezone,\n\t\ttype: 'nodeParameters'\n\t};\n};\n\nconst getParams = async userParams => {\n\tconst { bearToken, token } = await ExportServices.getTokens();\n\tconst { paperSize } = await ExportServices.getExporterConfig();\n\tconst systemParams = getSystemParams();\n\n\treturn {\n\t\tformat: PNG,\n\t\ttimeout: DEFAULT_TIMEOUT,\n\t\tpaperSize, bearToken, token,\n\t\t...systemParams,\n\t\t...userParams\n\t};\n};\n\n/**\n *\n * @param {string|object} response Retorno da API de exportação\n * @param {formats} format Formato do arquivo exportado\n * @param {object} options Opções de manipulação do arquivo exportado\n * @returns {promise<void|object>}\n */\nexport const handleResponse = async(response, format, options) => {\n\tconst { action, openOnSelf } = options;\n\tconst fileData = { ...response, format };\n\n\tif (action === 'custom') {\n\t\treturn fileData;\n\t}\n\n\tif (action === 'open') {\n\t\treturn ExportHelper.openFileOnWindow(fileData, openOnSelf);\n\t}\n\n\tExportHelper.downloadPathFile(fileData.fileName);\n};\n\n/**\n *\n * @param {object} params Parâmetros da API de exportação\n * @param {?object} options Opções de manipulação do arquivo exportado\n * @returns {promise<object|void>}\n */\nexport const callExporter = async(params, options = {}) => {\n\tconst apiParams = await getParams(params);\n\tconst response = await ExportServices.callAPI(apiParams);\n\n\treturn handleResponse(response, apiParams.format, options);\n};\n\n/**\n *\n * @param {object} params Parâmetros da API de exportação\n * @param {?object} options Opções de manipulação do arquivo exportado\n * @returns {promise<object|void>}\n */\nexport const exportToPDF = async(params, options) => {\n\tconst preparedParams = {\n\t\t...params,\n\t\tformat: PDF\n\t};\n\n\treturn callExporter(preparedParams, options);\n};\n\n/**\n *\n * @param {object} params Parâmetros da API de exportação\n * @param {?object} options Opções de manipulação do arquivo exportado\n * @returns {promise<object|void>}\n */\nexport const exportToPNG = async(params, options) => {\n\tconst preparedParams = {\n\t\t...params,\n\t\tformat: PNG\n\t};\n\n\treturn callExporter(preparedParams, options);\n};", "/* istanbul ignore file */\nimport Utils from 'Utils';\n\n/**\n *\n * @param {string} link Link do arquivo a ser aberto no navegador, base64 ou diretório\n * @param {string} fileName Nome do arquivo a ser salvo\n */\nexport const downloadFile = (link, fileName) => {\n\tif (global.__karma__) {\n\t\treturn;\n\t}\n\n\tlet downloadLink = document.createElement('a');\n\n\tdocument.body.appendChild(downloadLink);\n\n\tdownloadLink.href = link;\n\tdownloadLink.target = '_self';\n\tdownloadLink.download = fileName;\n\tdownloadLink.click();\n\tdownloadLink.remove();\n};\n\n/**\n *\n * @param {string} fileName Nome do arquivo salvo na temp\n */\nexport const downloadPathFile = (fileName, downloadName) => {\n\tconst url = `${Utils.getSystemUrl()}/temp/${fileName}`;\n\n\tdownloadFile(url, downloadName || fileName);\n};\n\n/**\n *\n * @param {object} fileData Dados do arquivo exportado normalizado\n * @param {formats} format Formato do arquivo exportado\n * @param {?boolean} isBase64 Informa se o arquivo foi retornado como base64 ou está salvo na temp\n * @param {?boolean} openOnSelf Informa se o arquivo será aberto na mesma aba\n * @returns {promise<void>}\n */\nexport const openFileOnWindow = (fileData, openOnSelf) => {\n\tif (global.__karma__) {\n\t\treturn;\n\t}\n\n\tconst url = fileData.absolutePath;\n\n\tif (openOnSelf) {\n\t\treturn window.open(url, '_self');\n\t}\n\n\tUtils.openNewTab(url);\n};\n\nexport default {\n\tdownloadFile,\n\tdownloadPathFile,\n\topenFileOnWindow\n};", "import Connector from 'Connector';\nimport TokenHandler from 'Token/TokenHandler';\nimport { noop } from 'lodash';\n\nlet exportConfigPromise = null;\n\nclass ExportServices {\n\t/* istanbul ignore next */\n\tstatic async getExporterConfigPromise() {\n\t\tif (exportConfigPromise) {\n\t\t\treturn exportConfigPromise;\n\t\t}\n\n\t\texportConfigPromise = Connector.callPlatformRestRead('exporter/v1/config');\n\n\t\treturn exportConfigPromise;\n\t}\n\n\tstatic async getExporterConfig() {\n\t\tconst { paperSize, type, url } = await ExportServices.getExporterConfigPromise();\n\n\t\treturn Promise.resolve({\n\t\t\tisHTMLConverter: type === 'external',\n\t\t\turl: url.replace(/\\/?$/, '/'),\n\t\t\tpaperSize\n\t\t});\n\t}\n\n\tstatic async getTokens() {\n\t\tconst [token, bearToken] = await Promise.all([TokenHandler.getToken(), TokenHandler.getJwt(true)]);\n\n\t\treturn {\n\t\t\tbearToken: `Bearer ${bearToken}`,\n\t\t\ttoken\n\t\t};\n\t}\n\n\tstatic async callHTMLConverter(params) {\n\t\tconst { url } = await ExportServices.getExporterConfig();\n\n\t\tconst response = await fetch(`${url}export/temp`, {\n\t\t\theaders: {\n\t\t\t\taccept: 'application/json',\n\t\t\t\t'content-type': 'application/json'\n\t\t\t},\n\t\t\tbody: JSON.stringify(params),\n\t\t\tmethod: 'POST'\n\t\t});\n\n\t\t/* istanbul ignore if */\n\t\tif (!response.ok) {\n\t\t\treturn Promise.reject(response);\n\t\t}\n\n\t\treturn response.json();\n\t}\n\n\tstatic async callLegacy(params) {\n\t\treturn Connector.callPlatformRestRead('export/v1/path-legacy', JSON.stringify(params), {\n\t\t\tcontentType: 'application/json;charset=UTF-8',\n\t\t\terror: noop,\n\t\t\ttype: Connector.POST\n\t\t});\n\t}\n\n\tstatic async callAPI(params) {\n\t\tconst { isHTMLConverter } = await ExportServices.getExporterConfig();\n\n\t\tif (isHTMLConverter) {\n\t\t\treturn await ExportServices.callHTMLConverter(params);\n\t\t}\n\n\t\treturn await ExportServices.callLegacy(params);\n\t}\n}\n\nexport default ExportServices;"], "names": ["DOCX", "HTML", "JRPRINT", "MXLS", "ODS", "ODT", "PDF", "PNG", "RTF", "XLS", "XLSX", "jasperExportTypes", "DEFAULT", "NONE", "MINIMAL", "LANDSCAPE", "PORTRAIT", "A4", "AUTO", "LETTER", "j<PERSON>y", "Utils", "WorkspaceInfo", "ExportHelper", "ExportServices", "default", "formats", "margins", "orientations", "paperSizes", "DEFAULT_TIMEOUT", "getSystemParams", "systemUrl", "loginUrl", "URL", "getParams", "userParams", "_ref", "bearToken", "token", "paperSize", "systemParams", "handleResponse", "response", "format", "options", "action", "openOnSelf", "fileData", "callExporter", "params", "apiParams", "exportToPDF", "preparedParams", "exportToPNG", "downloadFile", "link", "fileName", "global", "downloadLink", "document", "downloadPathFile", "downloadName", "url", "openFileOnWindow", "window", "Connector", "TokenHandler", "noop", "exportConfigPromise", "getExporterConfigPromise", "getExporterConfig", "type", "Promise", "getTokens", "callHTMLConverter", "fetch", "JSON", "callLegacy", "callAPI", "isHTMLConverter"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAO,IAAMA,OAAO,OAAO;AAEpB,IAAMC,OAAO,OAAO;AAEpB,IAAMC,UAAU,UAAU;AAE1B,IAAMC,OAAO,OAAO;AAEpB,IAAMC,MAAM,MAAM;AAElB,IAAMC,MAAM,MAAM;AAElB,IAAMC,MAAM,MAAM;AAElB,IAAMC,MAAM,MAAM;AAElB,IAAMC,MAAM,MAAM;AAElB,IAAMC,MAAM,MAAM;AAElB,IAAMC,OAAO,OAAO;AAEpB,IAAMC,oBAAoB;IAChCX;IACAC;IACAC;IACAE;IACAC;IACAC;IACAE;IACAC;CACA,CAAC;AAEF,6DAAe;IACdT,MAAAA;IACAC,MAAAA;IACAC,SAAAA;IACAC,MAAAA;IACAC,KAAAA;IACAC,KAAAA;IACAC,KAAAA;IACAC,KAAAA;IACAC,KAAAA;IACAC,KAAAA;IACAC,MAAAA;AACD,CAAC,EAAC;;;;;;;;;;;;AC7CK,IAAME,UAAU,UAAU;AAE1B,IAAMC,OAAO,OAAO;AAEpB,IAAMC,UAAU,UAAU;AAEjC,6DAAe;IACdF,SAAAA;IACAE,SAAAA;IACAD,MAAAA;AACD,CAAC,EAAC;;;;;;;;;;;ACVK,IAAME,YAAY,YAAY;AAE9B,IAAMC,WAAW,WAAW;AAEnC,6DAAe;IACdD,WAAAA;IACAC,UAAAA;AACD,CAAC,EAAC;;;;;;;;;;;;ACPK,IAAMC,KAAK,KAAK;AAEhB,IAAMC,OAAO,OAAO;AAEpB,IAAMC,SAAS,SAAS;AAE/B,6DAAe;IACdF,IAAAA;IACAC,MAAAA;IACAC,QAAAA;AACD,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVF,0CAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAxG0B,CAAC,YAAY;AACf;AACgB;AACyC;AACK;AACG;AAEE;AACA;AACU;AACJ;AAE5F,IAAMW,kBAAkB,OAAO;AAEtC,IAAMC,kBAAkB;IACvB,IAAMC,YAAYX,yDAAkB,CAAC;IACrC,IAAMY,WAAWX,gEAAwB,KAAK,mBAAmB;IAEjE,OAAO;QACN,QAAS,GAA0BW,OAAxBD,WAAU,gBAAuB,OAATC;QACnC,MAAO,IAAIC,IAAIF,WAAY,IAAI;QAC/B,UAAUV,wEAAgC;QAC1C,UAAUA,mEAA2B,GAAG,QAAQ;QAChD,MAAM;IACP;AACD;AAEA,IAAMa;eAAY,6BAAMC;YACMC,MAArBC,WAAWC,OACXC,WACFC;;;;oBAFuBJ;;wBAAMb,gHAAwB;;;oBAA9Ba,OAAAA,eAArBC,YAAqBD,KAArBC,WAAWC,QAAUF,KAAVE;oBACG;;wBAAMf,wHAAgC;;;oBAApDgB,YAAc,cAAdA;oBACFC,eAAeV;oBAErB;;wBAAO;4BACN,QAAQxB,yFAAGA;4BACX,SAASuB;4BACTU,WAAAA;4BAAWF,WAAAA;4BAAWC,OAAAA;2BACnBE,cACAL;;;;IAEL;oBAZMD,UAAkBC;;;;AAcxB;;;;;;CAMC,GACM,IAAMM;eAAiB,6BAAMC,UAAUC,QAAQC;YAC7CC,QAAQC,YACVC;;YADEF,SAAuBD,QAAvBC,QAAQC,aAAeF,QAAfE;YACVC,WAAW,wCAAKL;gBAAUC,QAAAA;;YAEhC,IAAIE,WAAW,UAAU;gBACxB;;oBAAOE;;YACR;YAEA,IAAIF,WAAW,QAAQ;gBACtB;;oBAAOvB,sHAA6B,CAACyB,UAAUD;;YAChD;YAEAxB,sHAA6B,CAACyB,SAAS,QAAQ;;;;;IAChD;oBAbaN,eAAuBC,UAAUC,QAAQC;;;IAapD;AAEF;;;;;CAKC,GACM,IAAMI;eAAe,6BAAMC;YAAQL,SACnCM,WACAR;;;;;oBAFmCE,8EAAU,CAAC;oBAClC;;wBAAMV,UAAUe;;;oBAA5BC,YAAY;oBACD;;wBAAM3B,8GAAsB,CAAC2B;;;oBAAxCR,WAAW;oBAEjB;;wBAAOD,eAAeC,UAAUQ,UAAU,MAAM,EAAEN;;;;IACnD;oBALaI,aAAqBC;;;IAKhC;AAEF;;;;;CAKC,GACM,IAAME;eAAc,6BAAMF,QAAQL;YAClCQ;;YAAAA,iBAAiB,wCACnBH;gBACH,QAAQ5C,yFAAGA;;YAGZ;;gBAAO2C,aAAaI,gBAAgBR;;;IACrC;oBAPaO,YAAoBF,QAAQL;;;IAOvC;AAEF;;;;;CAKC,GACM,IAAMS;eAAc,6BAAMJ,QAAQL;YAClCQ;;YAAAA,iBAAiB,wCACnBH;gBACH,QAAQ3C,yFAAGA;;YAGZ;;gBAAO0C,aAAaI,gBAAgBR;;;IACrC;oBAPaS,YAAoBJ,QAAQL;;;IAOvC;;;;;;;;;;;;;;;;ACzGF,wBAAwB,GA4DtB;AA3DwB;AAE1B;;;;CAIC,GACM,IAAMU,eAAe,SAACC,MAAMC;IAClC,IAAIC,qBAAMA,CAAC,SAAS,EAAE;QACrB;IACD;IAEA,IAAIC,eAAeC,SAAS,aAAa,CAAC;IAE1CA,SAAS,IAAI,CAAC,WAAW,CAACD;IAE1BA,aAAa,IAAI,GAAGH;IACpBG,aAAa,MAAM,GAAG;IACtBA,aAAa,QAAQ,GAAGF;IACxBE,aAAa,KAAK;IAClBA,aAAa,MAAM;AACpB,EAAE;AAEF;;;CAGC,GACM,IAAME,mBAAmB,SAACJ,UAAUK;IAC1C,IAAMC,MAAO,GAA+BN,OAA7BpC,yDAAkB,IAAG,UAAiB,OAAToC;IAE5CF,aAAaQ,KAAKD,gBAAgBL;AACnC,EAAE;AAEF;;;;;;;CAOC,GACM,IAAMO,mBAAmB,SAAChB,UAAUD;IAC1C,IAAIW,qBAAMA,CAAC,SAAS,EAAE;QACrB;IACD;IAEA,IAAMK,MAAMf,SAAS,YAAY;IAEjC,IAAID,YAAY;QACf,OAAOkB,OAAO,IAAI,CAACF,KAAK;IACzB;IAEA1C,uDAAgB,CAAC0C;AAClB,EAAE;AAEF,6DAAe;IACdR,cAAAA;IACAM,kBAAAA;IACAG,kBAAAA;AACD,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgB4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA5EI;AACY;AAChB;AAE9B,IAAIK,sBAAsB;AAE1B,IAAM7C,+BAAN;;aAAMA;gCAAAA;;kBAAAA;;YAEQ8C,KAAAA;mBADb,wBAAwB,GACxB,SAAaA;uBAAb;;wBACC,IAAID,qBAAqB;4BACxB;;gCAAOA;;wBACR;wBAEAA,sBAAsBH,qEAA8B,CAAC;wBAErD;;4BAAOG;;;gBACR;;;;YAEaE,KAAAA;mBAAb,SAAaA;uBAAb;wBACkClC,MAAzBG,WAAWgC,MAAMT;;;;gCAAQ1B;;oCAb7Bb,eAakD,wBAAwB;;;gCAA7Ca,OAAAA,eAAzBG,YAAyBH,KAAzBG,WAAWgC,OAAcnC,KAAdmC,MAAMT,MAAQ1B,KAAR0B;gCAEzB;;oCAAOU,QAAQ,OAAO,CAAC;wCACtB,iBAAiBD,SAAS;wCAC1B,KAAKT,IAAI,OAAO,CAAC,QAAQ;wCACzBvB,WAAAA;oCACD;;;;gBACD;;;;YAEakC,KAAAA;mBAAb,SAAaA;uBAAb;wBAC4BrC,MAApBE,OAAOD;;;;gCAAaD;;oCAAMoC,QAAQ,GAAG;wCAAEN,kEAAqB;wCAAIA,gEAAmB,CAAC;;;;gCAAhE9B;oCAAAA;;oCAApBE,QAAoBF,SAAbC,YAAaD;gCAE3B;;oCAAO;wCACN,WAAY,UAAmB,OAAVC;wCACrBC,OAAAA;oCACD;;;;gBACD;;;;YAEaoC,KAAAA;mBAAb,SAAaA,kBAAkBzB,MAAM;uBAArC;wBACSa,KAEFpB;;;;gCAFU;;oCAhCZnB,eAgCiC,iBAAiB;;;gCAA9CuC,MAAQ,cAARA;gCAES;;oCAAMa,MAAO,GAAM,OAAJb,KAAI,gBAAc;wCACjD,SAAS;4CACR,QAAQ;4CACR,gBAAgB;wCACjB;wCACA,MAAMc,KAAK,SAAS,CAAC3B;wCACrB,QAAQ;oCACT;;;gCAPMP,WAAW;gCASjB,sBAAsB,GACtB,IAAI,CAACA,SAAS,EAAE,EAAE;oCACjB;;wCAAO8B,QAAQ,MAAM,CAAC9B;;gCACvB;gCAEA;;oCAAOA,SAAS,IAAI;;;;gBACrB;;;;YAEamC,KAAAA;mBAAb,SAAaA,WAAW5B,MAAM;uBAA9B;;wBACC;;4BAAOgB,qEAA8B,CAAC,yBAAyBW,KAAK,SAAS,CAAC3B,SAAS;gCACtF,aAAa;gCACb,OAAOkB,qDAAIA;gCACX,MAAMF,uDAAc;4BACrB;;;gBACD;;;;YAEaa,KAAAA;mBAAb,SAAaA,QAAQ7B,MAAM;uBAA3B;wBACS8B;;;;gCAAoB;;oCA5DxBxD,eA4D6C,iBAAiB;;;gCAA1DwD,kBAAoB,cAApBA;qCAEJA,iBAAAA;;;;gCACI;;oCA/DJxD,eA+DyB,iBAAiB,CAAC0B;;;gCAA9C;;oCAAO;;;gCAGD;;oCAlEH1B,eAkEwB,UAAU,CAAC0B;;;gCAAvC;;oCAAO;;;;gBACR;;;;WAnEK1B;;AAsEN,6DAAeA,cAAcA,EAAC"}