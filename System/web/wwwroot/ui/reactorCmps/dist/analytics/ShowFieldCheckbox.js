define(["Utils","create-react-class","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","tokens!reactorCmps/tokens/general","react"], function(__WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_react__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["analytics/ShowFieldCheckbox"], {
"./src/analytics/dropdownOlap/showFieldCheckbox/ShowFieldCheckbox.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
/* istanbul ignore file */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var React = __webpack_require__(/*! react */ "react");
var Checkbox = __webpack_require__(/*! reactor/src/Form/components/Mols/Checkbox/Checkbox */ "../reactor/src/Form/components/Mols/Checkbox/Checkbox.jsx");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var ShowField;
var divVisible = {
    display: 'block'
};
var divHidden = {
    display: 'none'
};
ShowField = createReactClass({
    displayName: Checkbox.displayName,
    propTypes: {
        scope: PropTypes.object,
        cubeType: PropTypes.string
    },
    getInitialState: function getInitialState() {
        var me = this;
        return {
            scope: me.props.scope,
            hideField: me.props.scope.__field.hideField,
            viewType: me.props.scope.olapConfig.currentViewType.type,
            cubeType: me.props.cubeType
        };
    },
    handleCheckboxChange: function handleCheckboxChange(checked) {
        var me = this;
        var _scope = me.state.scope;
        _scope.__field.hideField = checked;
        me.setState({
            hideField: checked,
            scope: _scope
        });
    },
    shouldShowCheckbox: function shouldShowCheckbox(viewType, cubeType) {
        if (viewType === 'TABLE') {
            if (cubeType === 'dimensions' || cubeType === 'measures') return true;
        }
        return false;
    },
    render: function render() {
        var me = this, state = me.state, _style = me.shouldShowCheckbox(state.viewType, state.cubeType) ? divVisible : divHidden;
        return /*#__PURE__*/ React.createElement("div", {
            style: _style
        }, /*#__PURE__*/ React.createElement(Checkbox, {
            label: SE.t(206966),
            value: state.hideField,
            onChange: me.handleCheckboxChange
        }));
    }
});
module.exports = ShowField;


}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/analytics/dropdownOlap/showFieldCheckbox/ShowFieldCheckbox.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=ShowFieldCheckbox.js.map