{"version": 3, "file": "analytics/analyticsConfiguration.js", "sources": ["webpack://watch1749037385376/./src/analytics/constants/storageKeys.js", "webpack://watch1749037385376/./src/analytics/helpers/analyticsConfiguration.js", "webpack://watch1749037385376/./src/chartengine2/constants/storageKeys.js"], "sourcesContent": ["const prefix = 'Analytics';\n\nexport const AUTO_UPDATE = prefix + 'AutoUpdate';\n\nexport default {\n\tAUTO_UPDATE\n};", "\nimport Connector from 'Connector';\nimport Storage from 'Storage';\nimport { AUTO_UPDATE } from 'reactorCmps/src/analytics/constants/storageKeys';\nimport { TEMPLATE } from 'reactorCmps/src/chartengine2/constants/storageKeys';\n\nconst cleanStorage = () => {\n\tStorage.remove('AnalyticsConfiguratorType');\n\tStorage.remove('AnalyticsNewViewConfiguratorModal');\n\tStorage.remove('AnalyticsViewConfigurator');\n\tStorage.remove('ChartEngineAutoUpdate');\n\tStorage.remove('ChartEngineConfigurator');\n\tStorage.remove('ChartEngineViewConfigurator');\n\tStorage.remove('exporter_scn');\n\tStorage.remove('exporter_url');\n};\n\nconst storeData = ({ deferUpdate, standardTheme }) => {\n\tif (standardTheme !== undefined)\n\t\tStorage.set(TEMPLATE, { colors: JSON.parse(standardTheme) });\n\n\tif (deferUpdate !== undefined)\n\t\tStorage.set(AUTO_UPDATE, !Boolean(deferUpdate));\n};\n\nconst fetchData = async() => {\n\treturn Connector\n\t\t.callKatana('chartengine/configuration')\n\t\t.then(storeData);\n};\n\nconst setAnalyticsParams = () => {\n\tcleanStorage();\n\n\treturn fetchData();\n};\n\nexport default {\n\tcleanStorage,\n\tsetAnalyticsParams,\n\tfetch: fetchData,\n\tsaveInStorage: storeData\n};", "const prefix = 'ChartEngine';\n\nexport const TEMPLATE = prefix + 'Template';\n\nexport default {\n\tTEMPLATE\n};"], "names": ["prefix", "AUTO_UPDATE", "Connector", "Storage", "TEMPLATE", "cleanStorage", "storeData", "deferUpdate", "standardTheme", "undefined", "JSON", "Boolean", "fetchData", "setAnalyticsParams"], "mappings": ";;;;;;;;;AAAA,IAAMA,SAAS;AAER,IAAMC,cAAcD,SAAS,aAAa;AAEjD,6DAAe;IACdC,aAAAA;AACD,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoCA;AAzCgC;AACJ;AACgD;AACA;AAE9E,IAAMI,eAAe;IACpBF,qDAAc,CAAC;IACfA,qDAAc,CAAC;IACfA,qDAAc,CAAC;IACfA,qDAAc,CAAC;IACfA,qDAAc,CAAC;IACfA,qDAAc,CAAC;IACfA,qDAAc,CAAC;IACfA,qDAAc,CAAC;AAChB;AAEA,IAAMG,YAAY;QAAGC,oBAAAA,aAAaC,sBAAAA;IACjC,IAAIA,kBAAkBC,WACrBN,kDAAW,CAACC,wFAAQA,EAAE;QAAE,QAAQM,KAAK,KAAK,CAACF;IAAe;IAE3D,IAAID,gBAAgBE,WACnBN,kDAAW,CAACF,wFAAWA,EAAE,CAACU,QAAQJ;AACpC;AAEA,IAAMK;eAAY;;YACjB;;gBAAOV,2DACK,CAAC,6BACX,IAAI,CAACI;;;IACR;oBAJMM;;;;AAMN,IAAMC,qBAAqB;IAC1BR;IAEA,OAAOO;AACR;AAEA,6DAAe;IACdP,cAAAA;IACAQ,oBAAAA;IACA,OAAOD;IACP,eAAeN;AAChB,CAAC,EAAC;;;;;;;;;;AC1CF,IAAMN,SAAS;AAER,IAAMI,WAAWJ,SAAS,WAAW;AAE5C,6DAAe;IACdI,UAAAA;AACD,CAAC,EAAC"}