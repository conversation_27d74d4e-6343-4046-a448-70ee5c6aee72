{"version": 3, "file": "analytics/ExportApiClient.js", "sources": ["webpack://watch1749037385376/./src/analytics/components/exportAPIClient/exportApiClient.js", "webpack://watch1749037385376/./src/analytics/constants/exportation/formats.js", "webpack://watch1749037385376/./src/analytics/constants/exportation/margins.js", "webpack://watch1749037385376/./src/analytics/constants/exportation/orientations.js", "webpack://watch1749037385376/./src/analytics/constants/exportation/paperSizes.js", "webpack://watch1749037385376/./src/analytics/helpers/ExportHandler/ExportHandler.js", "webpack://watch1749037385376/./src/analytics/helpers/ExportHandler/ExportHelper.js", "webpack://watch1749037385376/./src/analytics/services/exportation/ExportServices.js"], "sourcesContent": ["var Connector = require('Connector');\nvar when = require('when');\nvar formats = require('reactorCmps/src/analytics/constants/exportation/formats').default;\nvar margins = require('reactorCmps/src/analytics/constants/exportation/margins').default;\nvar orientations = require('reactorCmps/src/analytics/constants/exportation/orientations').default;\nvar paperSizes = require('reactorCmps/src/analytics/constants/exportation/paperSizes').default;\nvar ExportHandler = require('reactorCmps/src/analytics/helpers/ExportHandler/ExportHandler');\n\nvar DEFAULT_ERR = SE.t(108736);\n\nvar sizes = {\n\tDEFAULT_H: 300,\n\tDEFAULT_W: 600,\n\tMIN: 1,\n\tMAX: 10000000\n};\n\nvar options = {\n\tFILE: 'file',\n\tPATH: 'path'\n};\n\nvar ExportAnalytics = function() {\n\tvar me = this;\n\n\tme.formats = formats;\n\tme.options = options;\n\tme.margin = margins;\n\tme.orientation = orientations;\n\tme.papersize = paperSizes;\n\n\tme.getSize = function(size) {\n\t\treturn Math.min(sizes.MAX, Math.max(sizes.MIN, size));\n\t};\n\n\tme.getSizeParams = function(data) {\n\t\tvar size = data || {};\n\n\t\treturn {\n\t\t\theight: me.getSize(size.height || sizes.DEFAULT_H),\n\t\t\twidth: me.getSize(size.width || sizes.DEFAULT_W)\n\t\t};\n\t};\n\n\tme.getReqBody = function(url, format, params) {\n\t\tvar defaultParams = {\n\t\t\twaitUntil: '#componentRendered',\n\t\t\ttimeout: ExportHandler.DEFAULT_TIMEOUT,\n\t\t\tsize: {\n\t\t\t\twidth: sizes.DEFAULT_W,\n\t\t\t\theight: sizes.DEFAULT_H\n\t\t\t},\n\t\t\tscale: 1\n\t\t};\n\n\t\tvar data = Object.assign({}, defaultParams, { url, format }, params);\n\n\t\tif (format === formats.PNG)\n\t\t\tdata.size = me.getSizeParams(params.size);\n\n\t\treturn data;\n\t};\n\n\tme.getMessage = function(json) {\n\t\tif (json && json.message) {\n\t\t\treturn json.message;\n\t\t}\n\n\t\treturn DEFAULT_ERR;\n\t};\n\n\tme.handleError = function(deferred, data) {\n\t\tvar message = me.getMessage(data.responseJSON);\n\n\t\t/* istanbul ignore next */\n\t\tcurl('MessageBar', function(MessageBar) {\n\t\t\tMessageBar.showMessageError(message);\n\t\t});\n\n\t\tdeferred.reject(new Error(message));\n\t};\n\n\tme.setError = function(deferred, response) {\n\t\tdeferred.reject({\n\t\t\tsuccess: false,\n\t\t\tresponseJSON: response.responseJSON\n\t\t});\n\t};\n\n\tme.exportedFileHandler = function(deferred, trackParams, fileData) {\n\t\tdeferred.resolve(Object.assign({\n\t\t\tsuccess: true\n\t\t}, fileData));\n\t};\n\n\tme.exportFileByUrl = function(url, format = formats.PNG, params = {}) {\n\t\tvar reqBody = me.getReqBody(url, format, params);\n\t\tvar deferred = when.defer();\n\n\t\tExportHandler\n\t\t\t.callExporter(reqBody)\n\t\t\t.then(deferred.resolve, me.setError.bind(me, deferred));\n\n\t\treturn deferred.promise;\n\t};\n\n\tme.exportOlapByOid = function(oid, format = formats.PNG, params = {}, option = options.PATH, trackParams = {}) {\n\t\tvar deferred = when.defer(),\n\t\t\tsetError = me.setError.bind(me, deferred),\n\t\t\tsetDone = function(htmlData) {\n\t\t\t\tme.exportFileByUrl(htmlData.absolutePath, format, params, option)\n\t\t\t\t\t.then(me.exportedFileHandler.bind(me, deferred, trackParams), setError);\n\t\t\t};\n\n\t\tConnector\n\t\t\t.callPlatformRestRead('olapviews/' + oid + '/html', null, {\n\t\t\t\tcontentType: 'application/json; charset=UTF-8',\n\t\t\t\terror: me.handleError.bind(me, deferred)\n\t\t\t})\n\t\t\t.then(setDone, setError);\n\n\t\treturn deferred.promise;\n\t};\n\n\t/* istanbul ignore next */\n\tme.exportOlapByView = function(data, format = formats.PNG, params = {}, option = options.PATH, trackParams = {}) {\n\t\tvar deferred = when.defer();\n\t\tvar setError = me.setError.bind(me, deferred);\n\t\tvar setDone = function(htmlData) {\n\t\t\tme.exportFileByUrl(htmlData.absolutePath, format, params, option)\n\t\t\t\t.then(me.exportedFileHandler.bind(me, deferred, trackParams), setError);\n\t\t};\n\n\t\tConnector\n\t\t\t.callPlatformRestRead('olapviews/' + data.view.oid + '/htmlByJson', JSON.stringify(data), {\n\t\t\t\ttype: 'POST',\n\t\t\t\tcontentType: 'application/json; charset=UTF-8',\n\t\t\t\terror: me.handleError.bind(me, deferred)\n\t\t\t})\n\t\t\t.then(setDone, setError);\n\n\t\treturn deferred.promise;\n\t};\n\n\t/* istanbul ignore next */\n\tme.customExportOlapByView = function(data, format = formats.PNG, params = {}, option = options.PATH) {\n\t\tvar deferred = when.defer();\n\t\tvar setError = me.setError.bind(me, deferred);\n\t\tvar setDone = function(htmlData) {\n\t\t\tme.exportFileByUrl(htmlData.absolutePath, format, params, option)\n\t\t\t\t.then(me.exportedFileHandler.bind(me, deferred, params), setError);\n\t\t};\n\t\tvar config = { type: 'POST', contentType: 'application/json; charset=UTF-8' };\n\n\t\t/* istanbul ignore next */\n\t\tconfig.error = function() {};\n\n\t\tConnector\n\t\t\t.callPlatformRestRead('olapviews/htmlByView?asTable=' + Boolean(params.asTable), JSON.stringify(data), config)\n\t\t\t.then(setDone, setError);\n\n\t\treturn deferred.promise;\n\t};\n\n\treturn me;\n};\n\nmodule.exports = new ExportAnalytics();", "export const DOCX = 'DOCX';\n\nexport const HTML = 'HTML';\n\nexport const JRPRINT = 'JRPRINT';\n\nexport const MXLS = 'MXLS';\n\nexport const ODS = 'ODS';\n\nexport const ODT = 'ODT';\n\nexport const PDF = 'PDF';\n\nexport const PNG = 'PNG';\n\nexport const RTF = 'RTF';\n\nexport const XLS = 'XLS';\n\nexport const XLSX = 'XLSX';\n\nexport const jasperExportTypes = [\n\tDOCX,\n\tHTML,\n\tJRPRINT,\n\tODS,\n\tODT,\n\tPDF,\n\tRTF,\n\tXLS\n];\n\nexport default {\n\tDOCX,\n\tHTML,\n\tJRPRINT,\n\tMXLS,\n\tODS,\n\tODT,\n\tPDF,\n\tPNG,\n\tRTF,\n\tXLS,\n\tXLSX\n};", "export const DEFAULT = 'DEFAULT';\n\nexport const NONE = 'NONE';\n\nexport const MINIMAL = 'MINIMAL';\n\nexport default {\n\tDEFAULT,\n\tMINIMAL,\n\tNONE\n};", "export const LANDSCAPE = 'LANDSCAPE';\n\nexport const PORTRAIT = 'PORTRAIT';\n\nexport default {\n\tLANDSCAPE,\n\tPORTRAIT\n};", "export const A4 = 'A4';\n\nexport const AUTO = 'AUTO';\n\nexport const LETTER = 'LETTER';\n\nexport default {\n\tA4,\n\tAUTO,\n\tLETTER\n};", "// eslint-disable-next-line no-unused-vars\nimport jquery from 'jquery'; // FRAM-2025\nimport Utils from 'Utils';\nimport WorkspaceInfo from 'WorkspaceInfo';\nimport { PDF, PNG } from 'reactorCmps/src/analytics/constants/exportation/formats';\nimport ExportHelper from 'reactorCmps/src/analytics/helpers/ExportHandler/ExportHelper';\nimport ExportServices from 'reactorCmps/src/analytics/services/exportation/ExportServices';\n\nexport { default as formats } from 'reactorCmps/src/analytics/constants/exportation/formats';\nexport { default as margins } from 'reactorCmps/src/analytics/constants/exportation/margins';\nexport { default as orientations } from 'reactorCmps/src/analytics/constants/exportation/orientations';\nexport { default as paperSizes } from 'reactorCmps/src/analytics/constants/exportation/paperSizes';\n\nexport const DEFAULT_TIMEOUT = 300000;\n\nconst getSystemParams = () => {\n\tconst systemUrl = Utils.getSystemUrl(true);\n\tconst loginUrl = WorkspaceInfo.isExternal() ? 'external-login' : 'login';\n\n\treturn {\n\t\tdomain: `${systemUrl}/softexpert/${loginUrl}`,\n\t\thost: (new URL(systemUrl)).host,\n\t\tlanguage: WorkspaceInfo.getDefaultLanguage(),\n\t\ttimezone: WorkspaceInfo.getSystemInfo().timezone,\n\t\ttype: 'nodeParameters'\n\t};\n};\n\nconst getParams = async userParams => {\n\tconst { bearToken, token } = await ExportServices.getTokens();\n\tconst { paperSize } = await ExportServices.getExporterConfig();\n\tconst systemParams = getSystemParams();\n\n\treturn {\n\t\tformat: PNG,\n\t\ttimeout: DEFAULT_TIMEOUT,\n\t\tpaperSize, bearToken, token,\n\t\t...systemParams,\n\t\t...userParams\n\t};\n};\n\n/**\n *\n * @param {string|object} response Retorno da API de exportação\n * @param {formats} format Formato do arquivo exportado\n * @param {object} options Opções de manipulação do arquivo exportado\n * @returns {promise<void|object>}\n */\nexport const handleResponse = async(response, format, options) => {\n\tconst { action, openOnSelf } = options;\n\tconst fileData = { ...response, format };\n\n\tif (action === 'custom') {\n\t\treturn fileData;\n\t}\n\n\tif (action === 'open') {\n\t\treturn ExportHelper.openFileOnWindow(fileData, openOnSelf);\n\t}\n\n\tExportHelper.downloadPathFile(fileData.fileName);\n};\n\n/**\n *\n * @param {object} params Parâmetros da API de exportação\n * @param {?object} options Opções de manipulação do arquivo exportado\n * @returns {promise<object|void>}\n */\nexport const callExporter = async(params, options = {}) => {\n\tconst apiParams = await getParams(params);\n\tconst response = await ExportServices.callAPI(apiParams);\n\n\treturn handleResponse(response, apiParams.format, options);\n};\n\n/**\n *\n * @param {object} params Parâmetros da API de exportação\n * @param {?object} options Opções de manipulação do arquivo exportado\n * @returns {promise<object|void>}\n */\nexport const exportToPDF = async(params, options) => {\n\tconst preparedParams = {\n\t\t...params,\n\t\tformat: PDF\n\t};\n\n\treturn callExporter(preparedParams, options);\n};\n\n/**\n *\n * @param {object} params Parâmetros da API de exportação\n * @param {?object} options Opções de manipulação do arquivo exportado\n * @returns {promise<object|void>}\n */\nexport const exportToPNG = async(params, options) => {\n\tconst preparedParams = {\n\t\t...params,\n\t\tformat: PNG\n\t};\n\n\treturn callExporter(preparedParams, options);\n};", "/* istanbul ignore file */\nimport Utils from 'Utils';\n\n/**\n *\n * @param {string} link Link do arquivo a ser aberto no navegador, base64 ou diretório\n * @param {string} fileName Nome do arquivo a ser salvo\n */\nexport const downloadFile = (link, fileName) => {\n\tif (global.__karma__) {\n\t\treturn;\n\t}\n\n\tlet downloadLink = document.createElement('a');\n\n\tdocument.body.appendChild(downloadLink);\n\n\tdownloadLink.href = link;\n\tdownloadLink.target = '_self';\n\tdownloadLink.download = fileName;\n\tdownloadLink.click();\n\tdownloadLink.remove();\n};\n\n/**\n *\n * @param {string} fileName Nome do arquivo salvo na temp\n */\nexport const downloadPathFile = (fileName, downloadName) => {\n\tconst url = `${Utils.getSystemUrl()}/temp/${fileName}`;\n\n\tdownloadFile(url, downloadName || fileName);\n};\n\n/**\n *\n * @param {object} fileData Dados do arquivo exportado normalizado\n * @param {formats} format Formato do arquivo exportado\n * @param {?boolean} isBase64 Informa se o arquivo foi retornado como base64 ou está salvo na temp\n * @param {?boolean} openOnSelf Informa se o arquivo será aberto na mesma aba\n * @returns {promise<void>}\n */\nexport const openFileOnWindow = (fileData, openOnSelf) => {\n\tif (global.__karma__) {\n\t\treturn;\n\t}\n\n\tconst url = fileData.absolutePath;\n\n\tif (openOnSelf) {\n\t\treturn window.open(url, '_self');\n\t}\n\n\tUtils.openNewTab(url);\n};\n\nexport default {\n\tdownloadFile,\n\tdownloadPathFile,\n\topenFileOnWindow\n};", "import Connector from 'Connector';\nimport TokenHandler from 'Token/TokenHandler';\nimport { noop } from 'lodash';\n\nlet exportConfigPromise = null;\n\nclass ExportServices {\n\t/* istanbul ignore next */\n\tstatic async getExporterConfigPromise() {\n\t\tif (exportConfigPromise) {\n\t\t\treturn exportConfigPromise;\n\t\t}\n\n\t\texportConfigPromise = Connector.callPlatformRestRead('exporter/v1/config');\n\n\t\treturn exportConfigPromise;\n\t}\n\n\tstatic async getExporterConfig() {\n\t\tconst { paperSize, type, url } = await ExportServices.getExporterConfigPromise();\n\n\t\treturn Promise.resolve({\n\t\t\tisHTMLConverter: type === 'external',\n\t\t\turl: url.replace(/\\/?$/, '/'),\n\t\t\tpaperSize\n\t\t});\n\t}\n\n\tstatic async getTokens() {\n\t\tconst [token, bearToken] = await Promise.all([TokenHandler.getToken(), TokenHandler.getJwt(true)]);\n\n\t\treturn {\n\t\t\tbearToken: `Bearer ${bearToken}`,\n\t\t\ttoken\n\t\t};\n\t}\n\n\tstatic async callHTMLConverter(params) {\n\t\tconst { url } = await ExportServices.getExporterConfig();\n\n\t\tconst response = await fetch(`${url}export/temp`, {\n\t\t\theaders: {\n\t\t\t\taccept: 'application/json',\n\t\t\t\t'content-type': 'application/json'\n\t\t\t},\n\t\t\tbody: JSON.stringify(params),\n\t\t\tmethod: 'POST'\n\t\t});\n\n\t\t/* istanbul ignore if */\n\t\tif (!response.ok) {\n\t\t\treturn Promise.reject(response);\n\t\t}\n\n\t\treturn response.json();\n\t}\n\n\tstatic async callLegacy(params) {\n\t\treturn Connector.callPlatformRestRead('export/v1/path-legacy', JSON.stringify(params), {\n\t\t\tcontentType: 'application/json;charset=UTF-8',\n\t\t\terror: noop,\n\t\t\ttype: Connector.POST\n\t\t});\n\t}\n\n\tstatic async callAPI(params) {\n\t\tconst { isHTMLConverter } = await ExportServices.getExporterConfig();\n\n\t\tif (isHTMLConverter) {\n\t\t\treturn await ExportServices.callHTMLConverter(params);\n\t\t}\n\n\t\treturn await ExportServices.callLegacy(params);\n\t}\n}\n\nexport default ExportServices;"], "names": ["Connector", "require", "when", "formats", "margins", "orientations", "paperSizes", "ExportHandler", "DEFAULT_ERR", "SE", "sizes", "options", "ExportAnalytics", "me", "size", "Math", "data", "url", "format", "params", "defaultParams", "Object", "json", "deferred", "message", "curl", "MessageBar", "Error", "response", "trackParams", "fileData", "reqBody", "oid", "option", "setError", "setDone", "htmlData", "JSON", "config", "Boolean", "module", "DOCX", "HTML", "JRPRINT", "MXLS", "ODS", "ODT", "PDF", "PNG", "RTF", "XLS", "XLSX", "jasperExportTypes", "DEFAULT", "NONE", "MINIMAL", "LANDSCAPE", "PORTRAIT", "A4", "AUTO", "LETTER", "j<PERSON>y", "Utils", "WorkspaceInfo", "ExportHelper", "ExportServices", "default", "DEFAULT_TIMEOUT", "getSystemParams", "systemUrl", "loginUrl", "URL", "getParams", "userParams", "_ref", "bearToken", "token", "paperSize", "systemParams", "handleResponse", "action", "openOnSelf", "callExporter", "apiParams", "exportToPDF", "preparedParams", "exportToPNG", "downloadFile", "link", "fileName", "global", "downloadLink", "document", "downloadPathFile", "downloadName", "openFileOnWindow", "window", "TokenHandler", "noop", "exportConfigPromise", "getExporterConfigPromise", "getExporterConfig", "type", "Promise", "getTokens", "callHTMLConverter", "fetch", "callLegacy", "callAPI", "isHTMLConverter"], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAIA,YAAYC,mBAAOA,CAAC,4BAAW;AACnC,IAAIC,OAAOD,mBAAOA,CAAC,kBAAM;AACzB,IAAIE,UAAUF,oKAA0E;AACxF,IAAIG,UAAUH,oKAA0E;AACxF,IAAII,eAAeJ,8KAA+E;AAClG,IAAIK,aAAaL,0KAA6E;AAC9F,IAAIM,gBAAgBN,mBAAOA,CAAC,6HAA+D;AAE3F,IAAIO,cAAcC,GAAG,CAAC,CAAC;AAEvB,IAAIC,QAAQ;IACX,WAAW;IACX,WAAW;IACX,KAAK;IACL,KAAK;AACN;AAEA,IAAIC,UAAU;IACb,MAAM;IACN,MAAM;AACP;AAEA,IAAIC,kBAAkB;IACrB,IAAIC,KAAK,IAAI;IAEbA,GAAG,OAAO,GAAGV;IACbU,GAAG,OAAO,GAAGF;IACbE,GAAG,MAAM,GAAGT;IACZS,GAAG,WAAW,GAAGR;IACjBQ,GAAG,SAAS,GAAGP;IAEfO,GAAG,OAAO,GAAG,SAASC,IAAI;QACzB,OAAOC,KAAK,GAAG,CAACL,MAAM,GAAG,EAAEK,KAAK,GAAG,CAACL,MAAM,GAAG,EAAEI;IAChD;IAEAD,GAAG,aAAa,GAAG,SAASG,IAAI;QAC/B,IAAIF,OAAOE,QAAQ,CAAC;QAEpB,OAAO;YACN,QAAQH,GAAG,OAAO,CAACC,KAAK,MAAM,IAAIJ,MAAM,SAAS;YACjD,OAAOG,GAAG,OAAO,CAACC,KAAK,KAAK,IAAIJ,MAAM,SAAS;QAChD;IACD;IAEAG,GAAG,UAAU,GAAG,SAASI,GAAG,EAAEC,MAAM,EAAEC,MAAM;QAC3C,IAAIC,gBAAgB;YACnB,WAAW;YACX,SAASb,cAAc,eAAe;YACtC,MAAM;gBACL,OAAOG,MAAM,SAAS;gBACtB,QAAQA,MAAM,SAAS;YACxB;YACA,OAAO;QACR;QAEA,IAAIM,OAAOK,OAAO,MAAM,CAAC,CAAC,GAAGD,eAAe;YAAEH,KAAAA;YAAKC,QAAAA;QAAO,GAAGC;QAE7D,IAAID,WAAWf,QAAQ,GAAG,EACzBa,KAAK,IAAI,GAAGH,GAAG,aAAa,CAACM,OAAO,IAAI;QAEzC,OAAOH;IACR;IAEAH,GAAG,UAAU,GAAG,SAASS,IAAI;QAC5B,IAAIA,QAAQA,KAAK,OAAO,EAAE;YACzB,OAAOA,KAAK,OAAO;QACpB;QAEA,OAAOd;IACR;IAEAK,GAAG,WAAW,GAAG,SAASU,QAAQ,EAAEP,IAAI;QACvC,IAAIQ,UAAUX,GAAG,UAAU,CAACG,KAAK,YAAY;QAE7C,wBAAwB,GACxBS,KAAK,cAAc,SAASC,UAAU;YACrCA,WAAW,gBAAgB,CAACF;QAC7B;QAEAD,SAAS,MAAM,CAAC,IAAII,MAAMH;IAC3B;IAEAX,GAAG,QAAQ,GAAG,SAASU,QAAQ,EAAEK,QAAQ;QACxCL,SAAS,MAAM,CAAC;YACf,SAAS;YACT,cAAcK,SAAS,YAAY;QACpC;IACD;IAEAf,GAAG,mBAAmB,GAAG,SAASU,QAAQ,EAAEM,WAAW,EAAEC,QAAQ;QAChEP,SAAS,OAAO,CAACF,OAAO,MAAM,CAAC;YAC9B,SAAS;QACV,GAAGS;IACJ;IAEAjB,GAAG,eAAe,GAAG,SAASI,GAAG;YAAEC,SAAAA,iEAASf,QAAQ,GAAG,EAAEgB,SAAAA,iEAAS,CAAC;QAClE,IAAIY,UAAUlB,GAAG,UAAU,CAACI,KAAKC,QAAQC;QACzC,IAAII,WAAWrB,KAAK,KAAK;QAEzBK,cACE,YAAY,CAACwB,SACb,IAAI,CAACR,SAAS,OAAO,EAAEV,GAAG,QAAQ,CAAC,IAAI,CAACA,IAAIU;QAE9C,OAAOA,SAAS,OAAO;IACxB;IAEAV,GAAG,eAAe,GAAG,SAASmB,GAAG;YAAEd,SAAAA,iEAASf,QAAQ,GAAG,EAAEgB,SAAAA,iEAAS,CAAC,GAAGc,SAAAA,iEAAStB,QAAQ,IAAI,EAAEkB,cAAAA,iEAAc,CAAC;QAC3G,IAAIN,WAAWrB,KAAK,KAAK,IACxBgC,WAAWrB,GAAG,QAAQ,CAAC,IAAI,CAACA,IAAIU,WAChCY,UAAU,iBAASC,QAAQ;YAC1BvB,GAAG,eAAe,CAACuB,SAAS,YAAY,EAAElB,QAAQC,QAAQc,QACxD,IAAI,CAACpB,GAAG,mBAAmB,CAAC,IAAI,CAACA,IAAIU,UAAUM,cAAcK;QAChE;QAEDlC,UACE,oBAAoB,CAAC,eAAegC,MAAM,SAAS,MAAM;YACzD,aAAa;YACb,OAAOnB,GAAG,WAAW,CAAC,IAAI,CAACA,IAAIU;QAChC,GACC,IAAI,CAACY,SAASD;QAEhB,OAAOX,SAAS,OAAO;IACxB;IAEA,wBAAwB,GACxBV,GAAG,gBAAgB,GAAG,SAASG,IAAI;YAAEE,SAAAA,iEAASf,QAAQ,GAAG,EAAEgB,SAAAA,iEAAS,CAAC,GAAGc,SAAAA,iEAAStB,QAAQ,IAAI,EAAEkB,cAAAA,iEAAc,CAAC;QAC7G,IAAIN,WAAWrB,KAAK,KAAK;QACzB,IAAIgC,WAAWrB,GAAG,QAAQ,CAAC,IAAI,CAACA,IAAIU;QACpC,IAAIY,UAAU,iBAASC,QAAQ;YAC9BvB,GAAG,eAAe,CAACuB,SAAS,YAAY,EAAElB,QAAQC,QAAQc,QACxD,IAAI,CAACpB,GAAG,mBAAmB,CAAC,IAAI,CAACA,IAAIU,UAAUM,cAAcK;QAChE;QAEAlC,UACE,oBAAoB,CAAC,eAAegB,KAAK,IAAI,CAAC,GAAG,GAAG,eAAeqB,KAAK,SAAS,CAACrB,OAAO;YACzF,MAAM;YACN,aAAa;YACb,OAAOH,GAAG,WAAW,CAAC,IAAI,CAACA,IAAIU;QAChC,GACC,IAAI,CAACY,SAASD;QAEhB,OAAOX,SAAS,OAAO;IACxB;IAEA,wBAAwB,GACxBV,GAAG,sBAAsB,GAAG,SAASG,IAAI;YAAEE,SAAAA,iEAASf,QAAQ,GAAG,EAAEgB,SAAAA,iEAAS,CAAC,GAAGc,SAAAA,iEAAStB,QAAQ,IAAI;QAClG,IAAIY,WAAWrB,KAAK,KAAK;QACzB,IAAIgC,WAAWrB,GAAG,QAAQ,CAAC,IAAI,CAACA,IAAIU;QACpC,IAAIY,UAAU,iBAASC,QAAQ;YAC9BvB,GAAG,eAAe,CAACuB,SAAS,YAAY,EAAElB,QAAQC,QAAQc,QACxD,IAAI,CAACpB,GAAG,mBAAmB,CAAC,IAAI,CAACA,IAAIU,UAAUJ,SAASe;QAC3D;QACA,IAAII,SAAS;YAAE,MAAM;YAAQ,aAAa;QAAkC;QAE5E,wBAAwB,GACxBA,OAAO,KAAK,GAAG,YAAY;QAE3BtC,UACE,oBAAoB,CAAC,kCAAkCuC,QAAQpB,OAAO,OAAO,GAAGkB,KAAK,SAAS,CAACrB,OAAOsB,QACtG,IAAI,CAACH,SAASD;QAEhB,OAAOX,SAAS,OAAO;IACxB;IAEA,OAAOV;AACR;AAEA2B,cAAc,GAAG,IAAI5B;;;;;;;;;;;;;;;;;;;;;;ACvKd,IAAM6B,OAAO,OAAO;AAEpB,IAAMC,OAAO,OAAO;AAEpB,IAAMC,UAAU,UAAU;AAE1B,IAAMC,OAAO,OAAO;AAEpB,IAAMC,MAAM,MAAM;AAElB,IAAMC,MAAM,MAAM;AAElB,IAAMC,MAAM,MAAM;AAElB,IAAMC,MAAM,MAAM;AAElB,IAAMC,MAAM,MAAM;AAElB,IAAMC,MAAM,MAAM;AAElB,IAAMC,OAAO,OAAO;AAEpB,IAAMC,oBAAoB;IAChCX;IACAC;IACAC;IACAE;IACAC;IACAC;IACAE;IACAC;CACA,CAAC;AAEF,6DAAe;IACdT,MAAAA;IACAC,MAAAA;IACAC,SAAAA;IACAC,MAAAA;IACAC,KAAAA;IACAC,KAAAA;IACAC,KAAAA;IACAC,KAAAA;IACAC,KAAAA;IACAC,KAAAA;IACAC,MAAAA;AACD,CAAC,EAAC;;;;;;;;;;;;;AC7CK,IAAME,UAAU,UAAU;AAE1B,IAAMC,OAAO,OAAO;AAEpB,IAAMC,UAAU,UAAU;AAEjC,6DAAe;IACdF,SAAAA;IACAE,SAAAA;IACAD,MAAAA;AACD,CAAC,EAAC;;;;;;;;;;;;ACVK,IAAME,YAAY,YAAY;AAE9B,IAAMC,WAAW,WAAW;AAEnC,6DAAe;IACdD,WAAAA;IACAC,UAAAA;AACD,CAAC,EAAC;;;;;;;;;;;;;ACPK,IAAMC,KAAK,KAAK;AAEhB,IAAMC,OAAO,OAAO;AAEpB,IAAMC,SAAS,SAAS;AAE/B,6DAAe;IACdF,IAAAA;IACAC,MAAAA;IACAC,QAAAA;AACD,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVF,0CAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAxG0B,CAAC,YAAY;AACf;AACgB;AACyC;AACK;AACG;AAEE;AACA;AACU;AACJ;AAE5F,IAAMO,kBAAkB,OAAO;AAEtC,IAAMC,kBAAkB;IACvB,IAAMC,YAAYP,yDAAkB,CAAC;IACrC,IAAMQ,WAAWP,gEAAwB,KAAK,mBAAmB;IAEjE,OAAO;QACN,QAAS,GAA0BO,OAAxBD,WAAU,gBAAuB,OAATC;QACnC,MAAO,IAAIC,IAAIF,WAAY,IAAI;QAC/B,UAAUN,wEAAgC;QAC1C,UAAUA,mEAA2B,GAAG,QAAQ;QAChD,MAAM;IACP;AACD;AAEA,IAAMS;eAAY,6BAAMC;YACMC,MAArBC,WAAWC,OACXC,WACFC;;;;oBAFuBJ;;wBAAMT,gHAAwB;;;oBAA9BS,OAAAA,eAArBC,YAAqBD,KAArBC,WAAWC,QAAUF,KAAVE;oBACG;;wBAAMX,wHAAgC;;;oBAApDY,YAAc,cAAdA;oBACFC,eAAeV;oBAErB;;wBAAO;4BACN,QAAQpB,yFAAGA;4BACX,SAASmB;4BACTU,WAAAA;4BAAWF,WAAAA;4BAAWC,OAAAA;2BACnBE,cACAL;;;;IAEL;oBAZMD,UAAkBC;;;;AAcxB;;;;;;CAMC,GACM,IAAMM;eAAiB,6BAAMnD,UAAUV,QAAQP;YAC7CqE,QAAQC,YACVnD;;YADEkD,SAAuBrE,QAAvBqE,QAAQC,aAAetE,QAAfsE;YACVnD,WAAW,wCAAKF;gBAAUV,QAAAA;;YAEhC,IAAI8D,WAAW,UAAU;gBACxB;;oBAAOlD;;YACR;YAEA,IAAIkD,WAAW,QAAQ;gBACtB;;oBAAOhB,sHAA6B,CAAClC,UAAUmD;;YAChD;YAEAjB,sHAA6B,CAAClC,SAAS,QAAQ;;;;;IAChD;oBAbaiD,eAAuBnD,UAAUV,QAAQP;;;IAapD;AAEF;;;;;CAKC,GACM,IAAMuE;eAAe,6BAAM/D;YAAQR,SACnCwE,WACAvD;;;;;oBAFmCjB,8EAAU,CAAC;oBAClC;;wBAAM6D,UAAUrD;;;oBAA5BgE,YAAY;oBACD;;wBAAMlB,8GAAsB,CAACkB;;;oBAAxCvD,WAAW;oBAEjB;;wBAAOmD,eAAenD,UAAUuD,UAAU,MAAM,EAAExE;;;;IACnD;oBALauE,aAAqB/D;;;IAKhC;AAEF;;;;;CAKC,GACM,IAAMiE;eAAc,6BAAMjE,QAAQR;YAClC0E;;YAAAA,iBAAiB,wCACnBlE;gBACH,QAAQ4B,yFAAGA;;YAGZ;;gBAAOmC,aAAaG,gBAAgB1E;;;IACrC;oBAPayE,YAAoBjE,QAAQR;;;IAOvC;AAEF;;;;;CAKC,GACM,IAAM2E;eAAc,6BAAMnE,QAAQR;YAClC0E;;YAAAA,iBAAiB,wCACnBlE;gBACH,QAAQ6B,yFAAGA;;YAGZ;;gBAAOkC,aAAaG,gBAAgB1E;;;IACrC;oBAPa2E,YAAoBnE,QAAQR;;;IAOvC;;;;;;;;;;;;;;;;;ACzGF,wBAAwB,GA4DtB;AA3DwB;AAE1B;;;;CAIC,GACM,IAAM4E,eAAe,SAACC,MAAMC;IAClC,IAAIC,qBAAMA,CAAC,SAAS,EAAE;QACrB;IACD;IAEA,IAAIC,eAAeC,SAAS,aAAa,CAAC;IAE1CA,SAAS,IAAI,CAAC,WAAW,CAACD;IAE1BA,aAAa,IAAI,GAAGH;IACpBG,aAAa,MAAM,GAAG;IACtBA,aAAa,QAAQ,GAAGF;IACxBE,aAAa,KAAK;IAClBA,aAAa,MAAM;AACpB,EAAE;AAEF;;;CAGC,GACM,IAAME,mBAAmB,SAACJ,UAAUK;IAC1C,IAAM7E,MAAO,GAA+BwE,OAA7B3B,yDAAkB,IAAG,UAAiB,OAAT2B;IAE5CF,aAAatE,KAAK6E,gBAAgBL;AACnC,EAAE;AAEF;;;;;;;CAOC,GACM,IAAMM,mBAAmB,SAACjE,UAAUmD;IAC1C,IAAIS,qBAAMA,CAAC,SAAS,EAAE;QACrB;IACD;IAEA,IAAMzE,MAAMa,SAAS,YAAY;IAEjC,IAAImD,YAAY;QACf,OAAOe,OAAO,IAAI,CAAC/E,KAAK;IACzB;IAEA6C,uDAAgB,CAAC7C;AAClB,EAAE;AAEF,6DAAe;IACdsE,cAAAA;IACAM,kBAAAA;IACAE,kBAAAA;AACD,CAAC,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgB4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA5EI;AACY;AAChB;AAE9B,IAAII,sBAAsB;AAE1B,IAAMlC,+BAAN;;aAAMA;gCAAAA;;kBAAAA;;YAEQmC,KAAAA;mBADb,wBAAwB,GACxB,SAAaA;uBAAb;;wBACC,IAAID,qBAAqB;4BACxB;;gCAAOA;;wBACR;wBAEAA,sBAAsBnG,qEAA8B,CAAC;wBAErD;;4BAAOmG;;;gBACR;;;;YAEaE,KAAAA;mBAAb,SAAaA;uBAAb;wBACkC3B,MAAzBG,WAAWyB,MAAMrF;;;;gCAAQyD;;oCAb7BT,eAakD,wBAAwB;;;gCAA7CS,OAAAA,eAAzBG,YAAyBH,KAAzBG,WAAWyB,OAAc5B,KAAd4B,MAAMrF,MAAQyD,KAARzD;gCAEzB;;oCAAOsF,QAAQ,OAAO,CAAC;wCACtB,iBAAiBD,SAAS;wCAC1B,KAAKrF,IAAI,OAAO,CAAC,QAAQ;wCACzB4D,WAAAA;oCACD;;;;gBACD;;;;YAEa2B,KAAAA;mBAAb,SAAaA;uBAAb;wBAC4B9B,MAApBE,OAAOD;;;;gCAAaD;;oCAAM6B,QAAQ,GAAG;wCAAEN,kEAAqB;wCAAIA,gEAAmB,CAAC;;;;gCAAhEvB;oCAAAA;;oCAApBE,QAAoBF,SAAbC,YAAaD;gCAE3B;;oCAAO;wCACN,WAAY,UAAmB,OAAVC;wCACrBC,OAAAA;oCACD;;;;gBACD;;;;YAEa6B,KAAAA;mBAAb,SAAaA,kBAAkBtF,MAAM;uBAArC;wBACSF,KAEFW;;;;gCAFU;;oCAhCZqC,eAgCiC,iBAAiB;;;gCAA9ChD,MAAQ,cAARA;gCAES;;oCAAMyF,MAAO,GAAM,OAAJzF,KAAI,gBAAc;wCACjD,SAAS;4CACR,QAAQ;4CACR,gBAAgB;wCACjB;wCACA,MAAMoB,KAAK,SAAS,CAAClB;wCACrB,QAAQ;oCACT;;;gCAPMS,WAAW;gCASjB,sBAAsB,GACtB,IAAI,CAACA,SAAS,EAAE,EAAE;oCACjB;;wCAAO2E,QAAQ,MAAM,CAAC3E;;gCACvB;gCAEA;;oCAAOA,SAAS,IAAI;;;;gBACrB;;;;YAEa+E,KAAAA;mBAAb,SAAaA,WAAWxF,MAAM;uBAA9B;;wBACC;;4BAAOnB,qEAA8B,CAAC,yBAAyBqC,KAAK,SAAS,CAAClB,SAAS;gCACtF,aAAa;gCACb,OAAO+E,qDAAIA;gCACX,MAAMlG,uDAAc;4BACrB;;;gBACD;;;;YAEa4G,KAAAA;mBAAb,SAAaA,QAAQzF,MAAM;uBAA3B;wBACS0F;;;;gCAAoB;;oCA5DxB5C,eA4D6C,iBAAiB;;;gCAA1D4C,kBAAoB,cAApBA;qCAEJA,iBAAAA;;;;gCACI;;oCA/DJ5C,eA+DyB,iBAAiB,CAAC9C;;;gCAA9C;;oCAAO;;;gCAGD;;oCAlEH8C,eAkEwB,UAAU,CAAC9C;;;gCAAvC;;oCAAO;;;;gBACR;;;;WAnEK8C;;AAsEN,6DAAeA,cAAcA,EAAC"}