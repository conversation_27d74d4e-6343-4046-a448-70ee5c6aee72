"use strict";
define(["tokens!reactorCmps/tokens/general","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","create-react-class","suite-storage"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_suite_storage__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["analytics/OLAPDefaultFrameValidator"], {
"./src/analytics/components/OLAPDefaultFrameValidator.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Link/Link */ "../reactor2/src/Atomic/components/Atoms/Link/Link.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactor2_src_Atomic_components_Mols_Banner_Banner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactor2/src/Atomic/components/Mols/Banner/Banner */ "../reactor2/src/Atomic/components/Mols/Banner/Banner.jsx");
/* ESM import */var reactor2_src_Atomic_components_Mols_Banner_Banner__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Mols_Banner_Banner__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var reactor2_src_Atomic_components_Orgs_DefaultAlerts_DefaultAlert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactor2/src/Atomic/components/Orgs/DefaultAlerts/DefaultAlert */ "../reactor2/src/Atomic/components/Orgs/DefaultAlerts/DefaultAlert.jsx");
/* ESM import */var reactor2_src_Atomic_components_Orgs_DefaultAlerts_DefaultAlert__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Orgs_DefaultAlerts_DefaultAlert__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var reactor2_src_constants_statusConstants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactor2/src/constants/statusConstants */ "../reactor2/src/constants/statusConstants.js");
/* ESM import */var reactor2_src_helpers_zIndex__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactor2/src/helpers/zIndex */ "../reactor2/src/helpers/zIndex.js");
/* ESM import */var reactor2_src_helpers_zIndex__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_helpers_zIndex__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactor2/src/Styles/styleVariables */ "../reactor2/src/Styles/styleVariables.js");
/* ESM import */var reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_8__);
/* eslint-disable no-console */ function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}










var containerStyle = {
    position: 'absolute',
    width: '100%',
    zIndex: (reactor2_src_helpers_zIndex__WEBPACK_IMPORTED_MODULE_7___default().modal)
};
var listStyle = {
    textAlign: 'left'
};
var listItemStyle = {
    padding: reactor2_src_Styles_styleVariables__WEBPACK_IMPORTED_MODULE_8__.spacing.spacing1 + ' 0'
};
var logHeaderStyle = 'color: red; font-size: 18px; font-weight: bold;';
var title = 'Há itens a serem adequados na abertura do OLAP pela DefaultFrame';
/* istanbul ignore next: just a developer resource */ var OLAPDefaultFrameValidator = function(param) {
    var items = param.items;
    var _useState = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), showAlert = _useState[0], setAlert = _useState[1];
    var itemsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("ul", {
        style: listStyle
    }, items.map(function(item, key) {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("li", {
            key: key,
            style: listItemStyle
        }, item);
    })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_3___default()), {
        href: Utils__WEBPACK_IMPORTED_MODULE_2___default().getSystemUrl() + '/seonly/se-analytics/adequacoes.html'
    }, 'Confira a planilha de itens para mais informações.'));
    var link = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_3___default()), {
        children: 'Ver itens',
        onClick: function() {
            return setAlert(true);
        }
    });
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {
        console.log('%c' + title, logHeaderStyle);
        console.log(items);
    }, [
        items
    ]);
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
        style: containerStyle
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Mols_Banner_Banner__WEBPACK_IMPORTED_MODULE_4___default()), {
        link: link,
        text: title,
        type: reactor2_src_constants_statusConstants__WEBPACK_IMPORTED_MODULE_6__.STATUS_WARNING,
        dismissable: true
    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Orgs_DefaultAlerts_DefaultAlert__WEBPACK_IMPORTED_MODULE_5___default()), {
        body: itemsList,
        onClose: function() {
            return setAlert(false);
        },
        show: showAlert,
        type: reactor2_src_constants_statusConstants__WEBPACK_IMPORTED_MODULE_6__.STATUS_WARNING
    }));
};
OLAPDefaultFrameValidator.displayName = 'Analytics/components/OLAPDefaultFrameValidator';
OLAPDefaultFrameValidator.propTypes = {
    items: (prop_types__WEBPACK_IMPORTED_MODULE_9___default().array)
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(OLAPDefaultFrameValidator));


}),
"Utils": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"reactorCmps/tokens/general": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/analytics/components/OLAPDefaultFrameValidator.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=OLAPDefaultFrameValidator.js.map