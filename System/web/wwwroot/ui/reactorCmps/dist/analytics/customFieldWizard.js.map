{"version": 3, "file": "analytics/customFieldWizard.js", "sources": ["webpack://watch1749037385376/./src/analytics/customFieldWizard/Main.jsx"], "sourcesContent": ["var PropTypes = require('prop-types');\nvar React = require('react'),\n\tButton = require('reactor/src/Atomic/components/Atoms/Button/Button'),\n\tCustomFieldWizard,\n\tdisabledProps = {},\n\tclassColor = 'btnWizard ',\n\teditData = {},\n\tme = undefined;\n\nvar createReactClass = require('create-react-class');\n\nrequire('reactorCmps/tokens/general');\n\nCustomFieldWizard = createReactClass({\n\n\tpropTypes: {\n\t\tinitialItems: PropTypes.array,\n\t\tonUpdateList: PropTypes.func,\n\t\tonButtonClick: PropTypes.func.isRequired\n\t},\n\n\tgetInitialState: function() {\n\n\t\treturn {\n\t\t\tlistItems: this.props.initialItems,\n\t\t\tshowCreateButton: false\n\t\t};\n\t},\n\n\tcomponentDidMount: function() {\n\t\t\n\t\tvar thing = $('#calcFieldList');\n\t\tvar extra = 10;\n\t\tvar old = $(thing).scrollTop();\n\n\t\t/* istanbul ignore next */\n\t\t$(thing).scroll(function() {\n\t\t\tif ($(thing).scrollTop() < old) {\n\t\t\t\t$(thing).scrollTop($(thing).scrollTop() - extra);\n\t\t\t} else if ($(thing).scrollTop() > old) {\n\t\t\t\t$(thing).scrollTop($(thing).scrollTop() + extra);\n\t\t\t}\n\t\t\told = $(thing).scrollTop();\n\n\t\t});\n\t},\n\n\t/*eslint-disable */\n\topenModal: function( index, data ) {\n\n\t\teditData = {};\n\n\t\teditData.owner = this;\n\n\t\t/* istanbul ignore next */\n\t\tif ( index !== undefined ) {\n\t\t\teditData.indexItem = index;\n\t\t\teditData.editData  = data;\n\t\t}\n\n\t\t/* istanbul ignore next */\n\t\tif (editData.editData && typeof(OlapReact) !== \"undefined\")\n\t\t\teditData.editData.formula = OlapReact.replaceLabelsFormula(editData.editData.formula, window.analysis.BIAnalysis.dataset.DataSetSQLQuery.fields.ArrayList);\n\n\t\tthis.props.onButtonClick.call(this, editData);\n\t},\n\t/* eslint-enable */\n\n\tadd: function( data ) {\n\n\t\tif ( data ) {\n\n\t\t\tthis.state.listItems.push({\n\t\t\t\tid: data.text,\n\t\t\t\ttext: data.text,\n\t\t\t\tformula: data.formula,\n\t\t\t\ticon: data.type\n\t\t\t});\n\n\t\t\tthis.forceUpdate();\n\n\t\t\tif ( this.props.onUpdateList )\n\t\t\t\tthis.props.onUpdateList();\n\t\t}\n\t},\n\n\tedit: function( index, data ) {\n\n\t\tvar newListItem;\n\n\t\tif ( index !== undefined && data ) {\n\n\t\t\tnewListItem = this.state.listItems;\n\n\t\t\tnewListItem[ index ] = {\n\t\t\t\tid: data.text,\n\t\t\t\ttext: data.text,\n\t\t\t\tformula: data.formula,\n\t\t\t\ticon: data.type\n\t\t\t};\n\n\t\t\tthis.setState({\n\t\t\t\tlistItems: newListItem\n\t\t\t});\n\n\t\t\tthis.forceUpdate();\n\n\t\t\tif ( this.props.onUpdateList )\n\t\t\t\tthis.props.onUpdateList();\n\n\t\t}\n\t},\n\n\tremove: function( index ) {\n\n\t\tif ( index !== undefined ) {\n\n\t\t\tthis.state.listItems.splice(index, 1);\n\n\t\t\tthis.forceUpdate();\n\n\t\t\tif ( this.props.onUpdateList )\n\t\t\t\tthis.props.onUpdateList();\n\t\t}\n\t},\n\n\tenableCreateButton: function() {\n\n\t\tthis.setState({\n\t\t\tshowCreateButton: true\n\t\t});\n\t},\n\n\tdisableCreateButton: function() {\n\n\t\tthis.setState({\n\t\t\tshowCreateButton: false\n\t\t});\n\t},\n\n\tgetStore: function() {\n\n\t\treturn this.state.listItems;\n\t},\n\n\trender: function() {\n\n\t\tme = this;\n\n\t\tdisabledProps = {};\n\n\t\tif ( !me.state.showCreateButton ) {\n\t\t\tdisabledProps.disabled = true;\n\t\t} else {\n\t\t\tclassColor += 'blueWizard ';\n\t\t}\n\n\t\treturn ( \n\t\t\t<div id={'calculated-field-wizard'}>\n\t\t\t\t<span id={'calcFieldTitle'}>{ SE.t(218335) }</span>\n\t\t\t\t<div id={'calcFieldList'}>\n\t\t\t\t\t{ \n\t\t\t\t\t\tme.state.listItems.map(function(value, index) { \n\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t<div className={'calculatedFieldLine'} key={value.id}>\n\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t<div \n\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: '85%', height: '35px', float: 'left', cursor: 'pointer', paddingLeft: '15px' }} \n\t\t\t\t\t\t\t\t\t\t\tonClick={me.openModal.bind(me, index, value)}>\n\t\t\t\t\t\t\t\t\t\t\t<div className={value.icon}></div> \n\t\t\t\t\t\t\t\t\t\t\t<div className={'calcFieldText'}>{ value.text }</div> \n\t\t\t\t\t\t\t\t\t\t\t<div className={'seicon-formula'}></div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div \n\t\t\t\t\t\t\t\t\t\t\tclassName={'seicon-trash-alt'} \n\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: '15%', float: 'right', position: 'relative' }} \n\t\t\t\t\t\t\t\t\t\t\tonClick={me.remove.bind(me, index)}>\n\t\t\t\t\t\t\t\t\t\t</div> \n\t\t\t\t\t\t\t\t\t</div> \n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t})\n\t\t\t\t\t} \n\t\t\t\t</div>\n\t\t\t\t<div style={{ marginTop: '31px', width: '100%', paddingLeft: '10px', paddingRight: '10px' }}>\n\t\t\t\t\t<Button\n\t\t\t\t\t\tid={'addCalcField'}\n\t\t\t\t\t\ticon={'seicon-plus'}\n\t\t\t\t\t\tclassName={classColor}\n\t\t\t\t\t\tonClick={this.openModal.bind(me, undefined, undefined)}\n\t\t\t\t\t\t{...disabledProps}\n\t\t\t\t\t\tblock\n\t\t\t\t\t>\n\t\t\t\t\t\t{ SE.t(219341) }\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n});\n\n/* <div className={\"seicon-\" + value.icon}></div> */\n\nmodule.exports = {\n\tcomponent: CustomFieldWizard\n};"], "names": ["PropTypes", "require", "React", "<PERSON><PERSON>", "CustomFieldWizard", "disabledProps", "classColor", "editData", "me", "undefined", "createReactClass", "getInitialState", "componentDidMount", "thing", "$", "extra", "old", "openModal", "index", "data", "OlapReact", "window", "add", "edit", "newListItem", "remove", "enableCreateButton", "disable<PERSON><PERSON><PERSON><PERSON><PERSON>", "getStore", "render", "SE", "value", "module"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,IAAIA,YAAYC,mBAAOA,CAAC,uDAAY;AACpC,IAAIC,QAAQD,mBAAOA,CAAC,oBAAO,GAC1BE,SAASF,mBAAOA,CAAC,mHAAmD,GACpEG,mBACAC,gBAAgB,CAAC,GACjBC,aAAa,cACbC,WAAW,CAAC,GACZC,KAAKC;AAEN,IAAIC,mBAAmBT,mBAAOA,CAAC,8CAAoB;AAEnDA,mBAAOA,CAAC,8DAA4B;AAEpCG,oBAAoBM,iBAAiB;IAEpC,WAAW;QACV,cAAcV,UAAU,KAAK;QAC7B,cAAcA,UAAU,IAAI;QAC5B,eAAeA,UAAU,IAAI,CAAC,UAAU;IACzC;IAEAW,iBAAiB,SAAjBA;QAEC,OAAO;YACN,WAAW,IAAI,CAAC,KAAK,CAAC,YAAY;YAClC,kBAAkB;QACnB;IACD;IAEAC,mBAAmB,SAAnBA;QAEC,IAAIC,QAAQC,EAAE;QACd,IAAIC,QAAQ;QACZ,IAAIC,MAAMF,EAAED,OAAO,SAAS;QAE5B,wBAAwB,GACxBC,EAAED,OAAO,MAAM,CAAC;YACf,IAAIC,EAAED,OAAO,SAAS,KAAKG,KAAK;gBAC/BF,EAAED,OAAO,SAAS,CAACC,EAAED,OAAO,SAAS,KAAKE;YAC3C,OAAO,IAAID,EAAED,OAAO,SAAS,KAAKG,KAAK;gBACtCF,EAAED,OAAO,SAAS,CAACC,EAAED,OAAO,SAAS,KAAKE;YAC3C;YACAC,MAAMF,EAAED,OAAO,SAAS;QAEzB;IACD;IAEA,iBAAiB,GACjBI,WAAW,SAAXA,UAAqBC,KAAK,EAAEC,IAAI;QAE/BZ,WAAW,CAAC;QAEZA,SAAS,KAAK,GAAG,IAAI;QAErB,wBAAwB,GACxB,IAAKW,UAAUT,WAAY;YAC1BF,SAAS,SAAS,GAAGW;YACrBX,SAAS,QAAQ,GAAIY;QACtB;QAEA,wBAAwB,GACxB,IAAIZ,SAAS,QAAQ,IAAI,OAAOa,cAAe,aAC9Cb,SAAS,QAAQ,CAAC,OAAO,GAAGa,UAAU,oBAAoB,CAACb,SAAS,QAAQ,CAAC,OAAO,EAAEc,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS;QAE1J,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAEd;IACrC;IACA,iBAAiB,GAEjBe,KAAK,SAALA,IAAeH,IAAI;QAElB,IAAKA,MAAO;YAEX,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;gBACzB,IAAIA,KAAK,IAAI;gBACb,MAAMA,KAAK,IAAI;gBACf,SAASA,KAAK,OAAO;gBACrB,MAAMA,KAAK,IAAI;YAChB;YAEA,IAAI,CAAC,WAAW;YAEhB,IAAK,IAAI,CAAC,KAAK,CAAC,YAAY,EAC3B,IAAI,CAAC,KAAK,CAAC,YAAY;QACzB;IACD;IAEAI,MAAM,SAANA,KAAgBL,KAAK,EAAEC,IAAI;QAE1B,IAAIK;QAEJ,IAAKN,UAAUT,aAAaU,MAAO;YAElCK,cAAc,IAAI,CAAC,KAAK,CAAC,SAAS;YAElCA,WAAW,CAAEN,MAAO,GAAG;gBACtB,IAAIC,KAAK,IAAI;gBACb,MAAMA,KAAK,IAAI;gBACf,SAASA,KAAK,OAAO;gBACrB,MAAMA,KAAK,IAAI;YAChB;YAEA,IAAI,CAAC,QAAQ,CAAC;gBACb,WAAWK;YACZ;YAEA,IAAI,CAAC,WAAW;YAEhB,IAAK,IAAI,CAAC,KAAK,CAAC,YAAY,EAC3B,IAAI,CAAC,KAAK,CAAC,YAAY;QAEzB;IACD;IAEAC,QAAQ,SAARA,OAAkBP,KAAK;QAEtB,IAAKA,UAAUT,WAAY;YAE1B,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAACS,OAAO;YAEnC,IAAI,CAAC,WAAW;YAEhB,IAAK,IAAI,CAAC,KAAK,CAAC,YAAY,EAC3B,IAAI,CAAC,KAAK,CAAC,YAAY;QACzB;IACD;IAEAQ,oBAAoB,SAApBA;QAEC,IAAI,CAAC,QAAQ,CAAC;YACb,kBAAkB;QACnB;IACD;IAEAC,qBAAqB,SAArBA;QAEC,IAAI,CAAC,QAAQ,CAAC;YACb,kBAAkB;QACnB;IACD;IAEAC,UAAU,SAAVA;QAEC,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS;IAC5B;IAEAC,QAAQ,SAARA;QAECrB,KAAK,IAAI;QAETH,gBAAgB,CAAC;QAEjB,IAAK,CAACG,GAAG,KAAK,CAAC,gBAAgB,EAAG;YACjCH,cAAc,QAAQ,GAAG;QAC1B,OAAO;YACNC,cAAc;QACf;QAEA,qBACC,oBAAC;YAAI,IAAI;yBACR,oBAAC;YAAK,IAAI;WAAoBwB,GAAG,CAAC,CAAC,wBACnC,oBAAC;YAAI,IAAI;WAEPtB,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,SAASuB,KAAK,EAAEb,KAAK;YAC3C,qBACC,oBAAC;gBAAI,WAAW;gBAAuB,KAAKa,MAAM,EAAE;6BACnD,oBAAC,2BACA,oBAAC;gBACA,OAAO;oBAAE,OAAO;oBAAO,QAAQ;oBAAQ,OAAO;oBAAQ,QAAQ;oBAAW,aAAa;gBAAO;gBAC7F,SAASvB,GAAG,SAAS,CAAC,IAAI,CAACA,IAAIU,OAAOa;6BACtC,oBAAC;gBAAI,WAAWA,MAAM,IAAI;8BAC1B,oBAAC;gBAAI,WAAW;eAAmBA,MAAM,IAAI,iBAC7C,oBAAC;gBAAI,WAAW;+BAEjB,oBAAC;gBACA,WAAW;gBACX,OAAO;oBAAE,OAAO;oBAAO,OAAO;oBAAS,UAAU;gBAAW;gBAC5D,SAASvB,GAAG,MAAM,CAAC,IAAI,CAACA,IAAIU;;QAKjC,mBAGF,oBAAC;YAAI,OAAO;gBAAE,WAAW;gBAAQ,OAAO;gBAAQ,aAAa;gBAAQ,cAAc;YAAO;yBACzF,oBAACf;YACA,IAAI;YACJ,MAAM;YACN,WAAWG;YACX,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,CAACE,IAAIC,WAAWA;WACxCJ;YACJ;YAEEyB,GAAG,CAAC,CAAC;IAKZ;iBAzLD1B;AA0LA;AAEA,kDAAkD,GAElD4B,cAAc,GAAG;IAChB,WAAW5B;AACZ"}