define(["tokens!reactorCmps/tokens/general","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","create-react-class","Connector"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_Connector__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["analytics/customDBAField"], {
"../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./src/analytics/styles/codemirror.css": (function (module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "../node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../node_modules/css-loader/dist/runtime/api.js */ "../node_modules/css-loader/dist/runtime/api.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, `/* BASICS */

.CodeMirror {
  /* Set height, width, borders, and global font properties here */
  height: 300px;
  color: black;
}

/* PADDING */

.CodeMirror-lines {
  padding: 4px 0; /* Vertical padding around content */
}
.CodeMirror pre {
  padding: 0 4px; /* Horizontal padding of content */
}

.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  background-color: white; /* The little square between H and V scrollbars */
}

/* GUTTER */

.CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: #f7f7f7;
  white-space: nowrap;
}
.CodeMirror-linenumbers {}
.CodeMirror-linenumber {
  padding: 0 3px 0 5px;
  min-width: 20px;
  text-align: right;
  color: #999;
  white-space: nowrap;
}

.CodeMirror-guttermarker { color: black; }
.CodeMirror-guttermarker-subtle { color: #999; }

/* CURSOR */

.CodeMirror-cursor {
  border-left: 1px solid black;
  border-right: none;
  width: 0;
}
/* Shown when moving in bi-directional text */
.CodeMirror div.CodeMirror-secondarycursor {
  border-left: 1px solid silver;
}
.cm-fat-cursor .CodeMirror-cursor {
  width: auto;
  border: 0 !important;
  background: #7e7;
}
.cm-fat-cursor div.CodeMirror-cursors {
  z-index: 1;
}

.cm-animate-fat-cursor {
  width: auto;
  border: 0;
  -webkit-animation: blink 1.06s steps(1) infinite;
  -moz-animation: blink 1.06s steps(1) infinite;
  animation: blink 1.06s steps(1) infinite;
  background-color: #7e7;
}
@-moz-keyframes blink {
  0% {}
  50% { background-color: transparent; }
  100% {}
}
@-webkit-keyframes blink {
  0% {}
  50% { background-color: transparent; }
  100% {}
}
@keyframes blink {
  0% {}
  50% { background-color: transparent; }
  100% {}
}

/* Can style cursor different in overwrite (non-insert) mode */
.CodeMirror-overwrite .CodeMirror-cursor {}

.cm-tab { display: inline-block; text-decoration: inherit; }

.CodeMirror-rulers {
  position: absolute;
  left: 0; right: 0; top: -50px; bottom: -20px;
  overflow: hidden;
}
.CodeMirror-ruler {
  border-left: 1px solid #ccc;
  top: 0; bottom: 0;
  position: absolute;
}

/* DEFAULT THEME */

.cm-s-default .cm-header {color: blue;}
.cm-s-default .cm-quote {color: #090;}
.cm-negative {color: #d44;}
.cm-positive {color: #292;}
.cm-header, .cm-strong {font-weight: bold;}
.cm-em {font-style: italic;}
.cm-link {text-decoration: underline;}
.cm-strikethrough {text-decoration: line-through;}

.cm-s-default .cm-keyword {color: #708;}
.cm-s-default .cm-atom {color: #219;}
.cm-s-default .cm-number {color: #164;}
.cm-s-default .cm-def {color: #00f;}
.cm-s-default .cm-variable,
.cm-s-default .cm-punctuation,
.cm-s-default .cm-property,
.cm-s-default .cm-operator {}
.cm-s-default .cm-variable-2 {color: #05a;}
.cm-s-default .cm-variable-3 {color: #085;}
.cm-s-default .cm-comment {color: #a50;}
.cm-s-default .cm-string {color: #a11;}
.cm-s-default .cm-string-2 {color: #f50;}
.cm-s-default .cm-meta {color: #555;}
.cm-s-default .cm-qualifier {color: #555;}
.cm-s-default .cm-builtin {color: #30a;}
.cm-s-default .cm-bracket {color: #997;}
.cm-s-default .cm-tag {color: #170;}
.cm-s-default .cm-attribute {color: #00c;}
.cm-s-default .cm-hr {color: #999;}
.cm-s-default .cm-link {color: #00c;}

.cm-s-default .cm-error {color: #f00;}
.cm-invalidchar {color: #f00;}

.CodeMirror-composing { border-bottom: 2px solid; }

/* Default styles for common addons */

div.CodeMirror span.CodeMirror-matchingbracket {color: #0f0;}
div.CodeMirror span.CodeMirror-nonmatchingbracket {color: #f22;}
.CodeMirror-matchingtag { background: rgba(255, 150, 0, .3); }
.CodeMirror-activeline-background {background: #e8f2ff;}

/* STOP */

/* The rest of this file contains styles related to the mechanics of
   the editor. You probably shouldn't touch them. */

.CodeMirror {
  position: relative;
  overflow: hidden;
  background: white;
}

.CodeMirror-scroll {
  overflow: scroll !important; /* Things will break if this is overridden */
  /* 30px is the magic margin used to hide the element's real scrollbars */
  /* See overflow: hidden in .CodeMirror */
  margin-bottom: -30px; margin-right: -30px;
  padding-bottom: 30px;
  height: 100%;
  outline: none; /* Prevent dragging from highlighting the element */
  position: relative;
}
.CodeMirror-sizer {
  position: relative;
  border-right: 30px solid transparent;
}

/* The fake, visible scrollbars. Used to force redraw during scrolling
   before actual scrolling happens, thus preventing shaking and
   flickering artifacts. */
.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  position: absolute;
  z-index: 6;
  display: none;
}
.CodeMirror-vscrollbar {
  right: 0; top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}
.CodeMirror-hscrollbar {
  bottom: 0; left: 0;
  overflow-y: hidden;
  overflow-x: scroll;
}
.CodeMirror-scrollbar-filler {
  right: 0; bottom: 0;
}
.CodeMirror-gutter-filler {
  left: 0; bottom: 0;
}

.CodeMirror-gutters {
  position: absolute; left: 0; top: 0;
  min-height: 100%;
  z-index: 3;
}
.CodeMirror-gutter {
  white-space: normal;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: -30px;
}
.CodeMirror-gutter-wrapper {
  position: absolute;
  z-index: 4;
  background: none !important;
  border: none !important;
}
.CodeMirror-gutter-background {
  position: absolute;
  top: 0; bottom: 0;
  z-index: 4;
}
.CodeMirror-gutter-elt {
  position: absolute;
  cursor: default;
  z-index: 4;
}
.CodeMirror-gutter-wrapper {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.CodeMirror-lines {
  cursor: text;
  min-height: 1px; /* prevents collapsing before first draw */
}
.CodeMirror pre {
  /* Reset some styles that the rest of the page might have set */
  -moz-border-radius: 0; -webkit-border-radius: 0; border-radius: 0;
  border-width: 0;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre;
  word-wrap: normal;
  line-height: inherit;
  color: inherit;
  z-index: 2;
  position: relative;
  overflow: visible;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
}
.CodeMirror-wrap pre {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
}

.CodeMirror-linebackground {
  position: absolute;
  left: 0; right: 0; top: 0; bottom: 0;
  z-index: 0;
}

.CodeMirror-linewidget {
  position: relative;
  z-index: 2;
  overflow: auto;
}

.CodeMirror-widget {}

.CodeMirror-code {
  outline: none;
}

/* Force content-box sizing for the elements where we expect it */
.CodeMirror-scroll,
.CodeMirror-sizer,
.CodeMirror-gutter,
.CodeMirror-gutters,
.CodeMirror-linenumber {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}

.CodeMirror-measure {
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.CodeMirror-cursor {
  position: absolute;
  pointer-events: none;
}
.CodeMirror-measure pre { position: static; }

div.CodeMirror-cursors {
  visibility: hidden;
  position: relative;
  z-index: 3;
}
div.CodeMirror-dragcursors {
  visibility: visible;
}

.CodeMirror-focused div.CodeMirror-cursors {
  visibility: visible;
}

.CodeMirror-selected { background: #d9d9d9; }
.CodeMirror-focused .CodeMirror-selected { background: #d7d4f0; }
.CodeMirror-crosshair { cursor: crosshair; }
.CodeMirror-line::selection, .CodeMirror-line > span::selection, .CodeMirror-line > span > span::selection { background: #d7d4f0; }
.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection { background: #d7d4f0; }

.cm-searching {
  background: #ffa;
  background: rgba(255, 255, 0, .4);
}

/* Used to force a border model for a node */
.cm-force-border { padding-right: .1px; }

@media print {
  /* Hide the cursor when printing */
  .CodeMirror div.CodeMirror-cursors {
    visibility: hidden;
  }
}

/* See issue #2901 */
.cm-tab-wrap-hack:after { content: ''; }

/* Help users use markselection to safely style text background */
span.CodeMirror-selectedtext { background: none; }
`, ""]);
// Exports
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);


}),
"../node_modules/react-bootstrap/lib/Form.js": (function (module, exports, __webpack_require__) {
"use strict";


exports.__esModule = true;

var _extends2 = __webpack_require__(/*! babel-runtime/helpers/extends */ "../node_modules/babel-runtime/helpers/extends.js");

var _extends3 = _interopRequireDefault(_extends2);

var _objectWithoutProperties2 = __webpack_require__(/*! babel-runtime/helpers/objectWithoutProperties */ "../node_modules/babel-runtime/helpers/objectWithoutProperties.js");

var _objectWithoutProperties3 = _interopRequireDefault(_objectWithoutProperties2);

var _classCallCheck2 = __webpack_require__(/*! babel-runtime/helpers/classCallCheck */ "../node_modules/babel-runtime/helpers/classCallCheck.js");

var _classCallCheck3 = _interopRequireDefault(_classCallCheck2);

var _possibleConstructorReturn2 = __webpack_require__(/*! babel-runtime/helpers/possibleConstructorReturn */ "../node_modules/babel-runtime/helpers/possibleConstructorReturn.js");

var _possibleConstructorReturn3 = _interopRequireDefault(_possibleConstructorReturn2);

var _inherits2 = __webpack_require__(/*! babel-runtime/helpers/inherits */ "../node_modules/babel-runtime/helpers/inherits.js");

var _inherits3 = _interopRequireDefault(_inherits2);

var _classnames = __webpack_require__(/*! classnames */ "../node_modules/classnames/index.js");

var _classnames2 = _interopRequireDefault(_classnames);

var _react = __webpack_require__(/*! react */ "react");

var _react2 = _interopRequireDefault(_react);

var _propTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");

var _propTypes2 = _interopRequireDefault(_propTypes);

var _elementType = __webpack_require__(/*! prop-types-extra/lib/elementType */ "../node_modules/prop-types-extra/lib/elementType.js");

var _elementType2 = _interopRequireDefault(_elementType);

var _bootstrapUtils = __webpack_require__(/*! ./utils/bootstrapUtils */ "../node_modules/react-bootstrap/lib/utils/bootstrapUtils.js");

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var propTypes = {
  horizontal: _propTypes2.default.bool,
  inline: _propTypes2.default.bool,
  componentClass: _elementType2.default
};

var defaultProps = {
  horizontal: false,
  inline: false,
  componentClass: 'form'
};

var Form = function (_React$Component) {
  (0, _inherits3.default)(Form, _React$Component);

  function Form() {
    (0, _classCallCheck3.default)(this, Form);
    return (0, _possibleConstructorReturn3.default)(this, _React$Component.apply(this, arguments));
  }

  Form.prototype.render = function render() {
    var _props = this.props,
        horizontal = _props.horizontal,
        inline = _props.inline,
        Component = _props.componentClass,
        className = _props.className,
        props = (0, _objectWithoutProperties3.default)(_props, ['horizontal', 'inline', 'componentClass', 'className']);

    var _splitBsProps = (0, _bootstrapUtils.splitBsProps)(props),
        bsProps = _splitBsProps[0],
        elementProps = _splitBsProps[1];

    var classes = [];
    if (horizontal) {
      classes.push((0, _bootstrapUtils.prefix)(bsProps, 'horizontal'));
    }
    if (inline) {
      classes.push((0, _bootstrapUtils.prefix)(bsProps, 'inline'));
    }

    return _react2.default.createElement(Component, (0, _extends3.default)({}, elementProps, {
      className: (0, _classnames2.default)(className, classes)
    }));
  };

  return Form;
}(_react2.default.Component);

Form.propTypes = propTypes;
Form.defaultProps = defaultProps;

exports["default"] = (0, _bootstrapUtils.bsClass)('form', Form);
module.exports = exports["default"];

}),
"../reactor/src/Atomic/components/Orgs/Form.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var Form = __webpack_require__(/*! react-bootstrap/lib/Form */ "../node_modules/react-bootstrap/lib/Form.js");
module.exports = createReactClass({
    displayName: "Atomic/components/Orgs/Form",
    render: function render() {
        return /*#__PURE__*/ React.createElement(Form, this.props);
    }
});


}),
"./src/analytics/CodeMirror/CodeMirror.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* export default binding */ __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactorCmps_src_analytics_customDBAField_soft__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactorCmps/src/analytics/customDBAField/soft */ "./src/analytics/customDBAField/soft.js");
/* istanbul ignore file */ 

/* ESM default export */ function __WEBPACK_DEFAULT_EXPORT__(callback) {
    Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! codemirror */ "../node_modules/codemirror/lib/codemirror.js", 23)).then(function(codemirror) {
        var _codemirror = codemirror["default"] || codemirror;
        (0,reactorCmps_src_analytics_customDBAField_soft__WEBPACK_IMPORTED_MODULE_1__["default"])(_codemirror);
        if (callback) callback(_codemirror);
    });
}


}),
"./src/analytics/customDBAField/Main.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.string.substr.js */ "../node_modules/core-js/modules/es.string.substr.js");
__webpack_require__(/*! core-js/modules/es.array.last-index-of.js */ "../node_modules/core-js/modules/es.array.last-index-of.js");
__webpack_require__(/*! core-js/modules/web.timers.js */ "../node_modules/core-js/modules/web.timers.js");
__webpack_require__(/*! core-js/modules/es.parse-int.js */ "../node_modules/core-js/modules/es.parse-int.js");
__webpack_require__(/*! core-js/modules/es.string.replace.js */ "../node_modules/core-js/modules/es.string.replace.js");
__webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
__webpack_require__(/*! core-js/modules/es.json.stringify.js */ "../node_modules/core-js/modules/es.json.stringify.js");
__webpack_require__(/*! core-js/modules/es.array.find.js */ "../node_modules/core-js/modules/es.array.find.js");
__webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
__webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
__webpack_require__(/*! core-js/modules/es.array.index-of.js */ "../node_modules/core-js/modules/es.array.index-of.js");
var Connector = __webpack_require__(/*! Connector */ "Connector");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var React = __webpack_require__(/*! react */ "react");
var _require = __webpack_require__(/*! react-codemirror2 */ "../node_modules/react-codemirror2/index.js"), CodeMirror = _require.Controlled;
var ReactDOM = __webpack_require__(/*! react-dom */ "react-dom");
var Utils = __webpack_require__(/*! Utils */ "Utils");
var Form = __webpack_require__(/*! reactor/src/Atomic/components/Orgs/Form */ "../reactor/src/Atomic/components/Orgs/Form.jsx");
var FormGroup = __webpack_require__(/*! reactor/src/Form/components/Mols/FormGroup */ "../reactor/src/Form/components/Mols/FormGroup.jsx");
var FormControl = __webpack_require__(/*! reactor/src/Form/components/Atoms/FormControl/FormControl */ "../reactor/src/Form/components/Atoms/FormControl/FormControl.js");
var Button = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Button/Button */ "../reactor/src/Atomic/components/Atoms/Button/Button.jsx");
var OverlayTrigger = __webpack_require__(/*! reactor/src/Atomic/components/Orgs/OverlayTrigger */ "../reactor/src/Atomic/components/Orgs/OverlayTrigger.jsx");
var Tooltip = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Tooltip */ "../reactor/src/Atomic/components/Atoms/Tooltip.jsx");
var CustomFieldList = (__webpack_require__(/*! reactorCmps/src/analytics/customFieldList/customFieldList */ "./src/analytics/customFieldList/customFieldList.jsx")/* ["default"] */["default"]);
var codeMirrorFn = (__webpack_require__(/*! reactorCmps/src/analytics/CodeMirror/CodeMirror */ "./src/analytics/CodeMirror/CodeMirror.jsx")/* ["default"] */["default"]);
var debounceDefaults = (__webpack_require__(/*! reactorCmps/src/chartengine2/constants/debounceDefaults */ "./src/chartengine2/constants/debounceDefaults.js")/* ["default"] */["default"]);
/* istanbul ignore next */ var noop = function() {};
var CustomDBAField;
var __timeout__customDBAField;
var formulaTimeout;
/* css */ var style = {
    base: {
        backgroundColor: 'transparent',
        maxHeight: '455px',
        width: '853px'
    },
    firstRowSpace: {
        marginBottom: '10px'
    },
    input: {},
    inputError: {
        border: '1px solid red'
    },
    textarea: {
        height: '200px',
        resize: 'none'
    },
    textareaError: {
        height: '200px',
        border: '1px solid red',
        resize: 'none'
    },
    buttonBox: {
        marginTop: '20px',
        textAlign: 'right'
    },
    buttonCancel: {
        marginRight: '10px'
    },
    error: {
        border: '1px solid red'
    },
    required: {
        fontSize: '9px',
        color: '#F32C40'
    },
    required2: {
        fontSize: '9px',
        color: '#F32C40',
        display: 'inline-flex',
        margin: '1px'
    },
    gridContent: {
        display: 'flex',
        height: '197px'
    },
    gridContentLeft: {
        flex: '1 0 auto'
    },
    formulaOK: {
        color: '#0a8f55',
        margin: '20px 11px',
        display: 'block'
    },
    formulaError: {
        color: '#ad3737',
        fontWeight: 'bold',
        margin: '20px 11px',
        display: 'block'
    },
    errorMessage: {
        color: '#ad3737',
        fontSize: '13px',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        width: '100%',
        margin: '-12px 14px',
        display: 'block'
    },
    formulaModel: {
        display: 'flex',
        flexFlow: 'row',
        maxWidth: '578px'
    },
    formulaText: {
        height: '15px',
        marginTop: '10px',
        width: '100px',
        display: 'flex',
        flexDirection: 'row'
    },
    modelsDiv: {
        marginBottom: '5px',
        marginTop: '-5px',
        width: '220px',
        marginLeft: 'calc(100% - 320px)',
        display: 'flex',
        flexFlow: 'row-reverse'
    },
    subDescription: {
        fontSize: '13px',
        fontStyle: 'italic',
        color: '#777',
        paddingTop: '5px'
    },
    infoStyle: {
        fontSize: '15px',
        display: 'inline-flex'
    },
    alignDiv: {
        display: 'flex',
        flexFlow: 'row'
    },
    maxWidthDiv: {
        maxWidth: '575px'
    },
    inlineLabel: {
        display: 'inline-flex'
    },
    widthFormGroup: {
        width: '65%'
    },
    displayNone: {
        display: 'none'
    },
    displayBlock: {
        display: 'block'
    },
    displayInline: {
        display: 'inline'
    },
    divDescription: {
        fontSize: '13px',
        maxWidth: '520px'
    },
    alignDescription: {
        height: '25px',
        width: '100%'
    },
    sizeFieldList: {
        maxWidth: '275px',
        maxHeight: '285px'
    },
    sizeCustomFieldList: {
        overflow: 'auto',
        maxHeight: '225px'
    }
};
__webpack_require__(/*! reactorCmps/src/analytics/styles/codemirror.css */ "./src/analytics/styles/codemirror.css");
/* component */ CustomDBAField = createReactClass({
    propTypes: {
        cubeType: PropTypes.string,
        urlValidateFormula: PropTypes.string,
        editData: PropTypes.object,
        owner: PropTypes.object,
        dataBaseOID: PropTypes.string,
        onClose: PropTypes.func,
        onSave: PropTypes.func,
        indexItem: PropTypes.number,
        sql: PropTypes.string,
        fieldsList: PropTypes.array,
        calcFieldList: PropTypes.array,
        constantsList: PropTypes.array,
        olapDataSource: PropTypes.object,
        buildRequestParams: PropTypes.func,
        isSeriesAggregator: PropTypes.bool,
        olapReact: PropTypes.object
    },
    getDefaultProps: function() {
        return {
            olapDataSource: {}
        };
    },
    getInitialState: function getInitialState() {
        var me = this, initialData = {
            title: '',
            formula: '',
            fieldDatatype: '',
            formulaState: false,
            editing: false,
            indexItem: -1,
            sql: '',
            olapDataSource: {},
            databaseMsg: '',
            hasCodeMirror: false,
            titleState: false
        };
        if (me.props.editData && me.props.editData.text !== undefined) {
            initialData.title = me.props.editData.text;
            initialData.formula = me.props.editData.formula;
            initialData.fieldDatatype = me.props.editData.icon;
            initialData.editing = true;
            initialData.indexItem = me.props.indexItem;
            initialData.calcFieldList = me.props.calcFieldList;
        }
        if (me.props.fieldsList && me.props.fieldsList.length) {
            initialData.fieldsList = me.props.fieldsList;
        }
        if (me.props.constantsList && me.props.constantsList.length) {
            initialData.constantsList = me.props.constantsList;
        }
        return initialData;
    },
    componentDidMount: function componentDidMount() {
        var me = this, path = window.location.pathname, pathName = path.substr(path.lastIndexOf('/') + 1);
        me.setDataBaseMsgState();
        codeMirrorFn(function() {
            me.setState({
                hasCodeMirror: true
            });
        });
        /* istanbul ignore next */ setTimeout(function() {
            var cMirror = $('.ReactCodeMirror .CodeMirror');
            if (me.props.editData && me.props.editData.text !== undefined && me.props.editData.formula !== undefined) {
                me.unLockOkbutton();
            } else {
                me.lockOkbutton();
            }
            cMirror.each(function(i, el) {
                el.CodeMirror.refresh();
            });
            if (cMirror && cMirror[0] && cMirror[0].CodeMirror) {
                cMirror[0].CodeMirror.on('focus', function() {
                    $('.ReactCodeMirror').addClass('ReactCodeMirrorFocus');
                });
                cMirror[0].CodeMirror.on('blur', function() {
                    $('.ReactCodeMirror').addClass('ReactCodeMirrorFocus');
                    formulaTimeout = setTimeout(function() {
                        $('.ReactCodeMirror').removeClass('ReactCodeMirrorFocus');
                    }, 150);
                });
            }
            if (ReactDOM.findDOMNode(me.refs.formulatitle)) {
                ReactDOM.findDOMNode(me.refs.formulatitle).focus();
            }
            if (pathName === 'wizard_desktop_src.php') {
                if (typeof _gaq !== 'undefined') {
                    _gaq.push([
                        '_setAccount',
                        'UA-********-2'
                    ]);
                    _gaq.push([
                        '_trackEvent',
                        'Analytics - Campo calculado',
                        'Abriu campo calculado no Wizard',
                        me.state.formula
                    ]);
                }
            } else if (pathName === 'olap.php') {
                if (typeof _gaq !== 'undefined') {
                    _gaq.push([
                        '_setAccount',
                        'UA-********-2'
                    ]);
                    _gaq.push([
                        '_trackEvent',
                        'Analytics - Campo calculado',
                        'Abriu campo calculado no OLAP',
                        me.state.formula
                    ]);
                }
            }
        }, 150);
    },
    setDataBaseMsgState: function setDataBaseMsgState() {
        var _this = this;
        var dataBaseOID = this.props.dataBaseOID;
        var success = function(response) {
            var term = parseInt(response.results[0].term, 10);
            var msgDataBase = SE.t(219149).replace('%s', SE.t(term));
            _this.setState({
                databaseMsg: msgDataBase
            });
        };
        var error = function() {
            _this.setState({
                databaseMsg: ''
            });
        };
        var json = JSON.stringify([
            {
                String: dataBaseOID
            }
        ]);
        var params = {
            json: json
        };
        return Connector.callLogic2('DatabaseResourceLogic/getDatabaseInfo', params).then(success, error);
    },
    validate: function validate(title, formula) {
        var me = this;
        if (!title) {
            me.setState({
                titleState: 'error'
            });
            return false;
        }
        if (!formula) {
            me.setState({
                formulaState: 'error'
            });
            return false;
        }
        return true;
    },
    /*eslint-disable */ /* istanbul ignore next */ onOkClick: function onOkClick() {
        var me = this, path = window.location.pathname, pathName = path.substr(path.lastIndexOf('/') + 1);
        if (me.validate(me.state.title, me.state.formula)) {
            me.props.onSave.call(me);
            me.props.onClose.call(me);
            /* istanbul ignore next */ if (typeof _gaq !== "undefined") {
                if (pathName === "wizard_desktop_src.php") {
                    _gaq.push([
                        "_setAccount",
                        "UA-********-2"
                    ]);
                    _gaq.push([
                        "_trackEvent",
                        "Analytics - Campo calculado",
                        "OK no campo calculado no Wizard",
                        me.state.formula
                    ]);
                } else if (pathName === "olap.php") {
                    _gaq.push([
                        "_setAccount",
                        "UA-********-2"
                    ]);
                    _gaq.push([
                        "_trackEvent",
                        "Analytics - Campo calculado",
                        "OK no campo calculado no OLAP",
                        me.state.formula
                    ]);
                }
            }
        }
    },
    /* eslint-enable */ onCancelClick: function onCancelClick() {
        this.props.onClose();
    },
    handleTitleChange: function handleTitleChange(e) {
        var _this_state = this.state, formula = _this_state.formula, responseMsg = _this_state.responseMsg;
        var title = e.target.value;
        this.setState({
            title: typeof e.target.value === 'string' ? e.target.value : ''
        });
        if (formula && !responseMsg && title !== '') this.unLockOkbutton();
        else this.lockOkbutton();
    },
    /*eslint-disable */ /* istanbul ignore next */ handleFormulaChange: function handleFormulaChange(editor, data, newValue) {
        var me = this;
        clearTimeout(formulaTimeout);
        me.lockOkbutton();
        /* istanbul ignore next */ if (me.validateRequest && me.validateRequest.abort) {
            me.validateRequest.abort();
        }
        clearTimeout(__timeout__customDBAField);
        /* istanbul ignore next */ this.setState({
            formula: newValue,
            formulaState: newValue ? 'error' : false,
            titleState: !me.state.title ? 'error' : false,
            loading: true
        });
        /* istanbul ignore next */ __timeout__customDBAField = setTimeout(function() {
            me.request.call(me);
        }, debounceDefaults.VERY_SLOW);
    },
    validateRequest: undefined,
    request: function request(params) {
        /* istanbul ignore next */ var me = this, params_ = !params ? me.props.buildRequestParams.call(me) : params, errorMsg = {
            error: function error() {}
        };
        me.validateRequest = Connector.callLogicRead(me.props.urlValidateFormula, {
            _json: JSON.stringify(params_)
        }, errorMsg);
        me.validateRequest.then(function(res) {
            var datatype_ = '', results = res.results, success_ = res.success, message = '';
            if (res.message) message = Utils.stringToHtml(res.message).textContent;
            if (results && results.length > 0) {
                results = results[0];
                if (results.datatype === "LONG" || results.datatype === "INTEGER") datatype_ = "seicon-integer";
                else if (results.datatype === "TEXT" || results.datatype === "BIGTEXT") datatype_ = "seicon-varchar";
                else if (results.datatype === "DECIMAL") datatype_ = "seicon-float";
                else if (results.datatype === "DATE" || results.datatype === "DATETIME") datatype_ = "seicon-datetime";
                else datatype_ = "seicon-integer";
            }
            me.setState({
                responseStatus: success_ ? SE.t(218290) : SE.t(218293),
                responseMsg: message,
                responseStyle: success_ ? style.formulaOK : style.formulaError,
                responseIcon: success_ ? 'seicon-check' : 'seicon-cancel',
                fieldDatatype: datatype_,
                calcFieldDataType: me.getPropertyFromObject(results, 'datatype'),
                calcFieldAggregation: me.getPropertyFromObject(results, 'aggregationFunction'),
                calcFieldCanAggregate: me.getPropertyFromObject(results, 'canAggregate'),
                loading: false
            });
            if (success_) {
                if (me.state.formula && typeof me.state.title === 'string' && me.state.title) me.unLockOkbutton();
                else me.lockOkbutton();
            } else {
                if (!me.state.formula) {
                    me.setState({
                        responseStatus: '',
                        responseIcon: '',
                        responseMsg: '',
                        responseStyle: {},
                        title: ''
                    });
                }
                me.lockOkbutton();
            }
        });
    },
    /* eslint-enable */ getPropertyFromObject: function getPropertyFromObject(obj, prop) {
        if (!obj) return null;
        return obj[prop];
    },
    unLockOkbutton: function unLockOkbutton() {
        $('.sewindow.semodal').find('.buttons').find('.btn-success').removeAttr('disabled', 'disabled');
    },
    lockOkbutton: function lockOkbutton() {
        $('.sewindow.semodal').find('.buttons').find('.btn-success').attr('disabled', 'disabled');
    },
    focusSearch: function focusSearch() {
        $('#modalSearch').addClass('modalSearchFocused');
    },
    blurSearch: function blurSearch() {
        $('#modalSearch').removeClass('modalSearchFocused');
    },
    /* istanbul ignore next */ slideFields: function slideFields(fieldsItemsUp, fieldsItemsDown, constItemsUp, constItemsDown) {
        /* istanbul ignore next */ var notTdd = $('#listSearch').length ? true : false;
        /* istanbul ignore next */ if (notTdd) {
            $(fieldsItemsUp).parent().parent().slideUp();
            $(fieldsItemsDown).parent().parent().slideDown();
            $(constItemsUp).parent().parent().slideUp();
            $(constItemsDown).parent().parent().slideDown();
        }
    },
    /* istanbul ignore next */ handleSearchChange: function handleSearchChange(e, searchValue) {
        /* istanbul ignore next */ var fieldsList = this.props.fieldsList, constantsList = this.props.constantsList, search = searchValue ? searchValue.toUpperCase() : $('#listSearch').val().toUpperCase(), fieldsElements = $($('.fieldsItems')[0]).find('label'), constElements = $($('.fieldsItems')[1]).find('label'), constItemsDown = [], constItemsUp = [], fieldsItemsDown = [], fieldsItemsUp = [], fieldIndex, i;
        for(i = 0; i < fieldsList.length; i++){
            fieldIndex = fieldsList[i].name.toUpperCase().indexOf(search) > -1 ? 1 : fieldsList[i].id.toUpperCase().indexOf(search);
            if (fieldIndex > -1) {
                fieldsItemsDown.push(fieldsElements[i]);
            } else {
                fieldsItemsUp.push(fieldsElements[i]);
            }
        }
        for(i = 0; i < constantsList.length; i++){
            /* istanbul ignore next */ fieldIndex = constantsList[i].name.toUpperCase().indexOf(search) > -1 ? 1 : constantsList[i].title.toUpperCase().indexOf(search);
            if (fieldIndex > -1) {
                constItemsDown.push(constElements[i]);
            } else {
                constItemsUp.push(constElements[i]);
            }
        }
        this.slideFields(fieldsItemsUp, fieldsItemsDown, constItemsUp, constItemsDown);
    },
    onCaseClick: function onCaseClick() {
        var defaultCase = 'CASE WHEN [CONDITION]\n' + '		THEN [RETURN_IF_TRUE]\n' + '		ELSE [RETURN_IF_FALSE]\n' + 'END';
        this.props.olapReact.insertTextOnCodeMirror(defaultCase);
    },
    render: function render() {
        var me = this;
        return /*#__PURE__*/ React.createElement("div", {
            ref: 'CustomDBAFieldRef',
            style: style.base
        }, /*#__PURE__*/ React.createElement(Form, null, /*#__PURE__*/ React.createElement("div", {
            style: style.alignDiv
        }, /*#__PURE__*/ React.createElement("div", {
            style: style.maxWidthDiv
        }, /*#__PURE__*/ React.createElement(FormGroup, null, /*#__PURE__*/ React.createElement("div", null, /*#__PURE__*/ React.createElement("div", {
            style: style.firstRowSpace
        }, /*#__PURE__*/ React.createElement("div", {
            style: style.formulaText
        }, /*#__PURE__*/ React.createElement("label", {
            style: style.inlineLabel
        }, SE.t(100111)), /*#__PURE__*/ React.createElement("label", {
            style: style.required2,
            className: 'seicon-all'
        })), /*#__PURE__*/ React.createElement(FormControl, {
            ref: 'formulatitle',
            type: 'text',
            value: me.state.title,
            onChange: me.handleTitleChange
        })))), /*#__PURE__*/ React.createElement(FormGroup, null, /*#__PURE__*/ React.createElement("div", null, /*#__PURE__*/ React.createElement("div", null, /*#__PURE__*/ React.createElement("div", {
            style: style.formulaModel
        }, /*#__PURE__*/ React.createElement("div", {
            style: style.formulaText
        }, /*#__PURE__*/ React.createElement("label", {
            style: style.inlineLabel
        }, SE.t(109361)), /*#__PURE__*/ React.createElement("label", {
            style: style.required2,
            className: 'seicon-all'
        }), /*#__PURE__*/ React.createElement(OverlayTrigger, {
            placement: 'right',
            overlay: /*#__PURE__*/ React.createElement(Tooltip, {
                id: 'formulaTooltip'
            }, SE.t(219150))
        }, /*#__PURE__*/ React.createElement("label", {
            style: style.infoStyle,
            className: 'seicon-info'
        }))), /*#__PURE__*/ React.createElement("div", {
            style: style.modelsDiv
        }, /*#__PURE__*/ React.createElement("label", {
            className: 'olap-modelLabel'
        }, SE.t(218980)), /*#__PURE__*/ React.createElement(Button, {
            id: 'olap-caseBtn',
            onClick: me.onCaseClick
        }, 'CASE')))), /*#__PURE__*/ React.createElement("div", {
            style: style.gridContent
        }, /*#__PURE__*/ React.createElement("div", {
            style: style.gridContentLeft
        }, this.state.hasCodeMirror && /*#__PURE__*/ React.createElement(CodeMirror, {
            className: 'ReactCodeMirror',
            value: this.state.formula,
            onBeforeChange: this.handleFormulaChange,
            onChange: noop,
            options: {
                mode: 'text/soft-sql',
                lineWrapping: true,
                theme: 'analysis',
                placeholder: SE.t(219151)
            },
            ref: 'formula'
        }))))), /*#__PURE__*/ React.createElement(FormGroup, {
            style: style.widthFormGroup
        }, /*#__PURE__*/ React.createElement("div", {
            style: style.subDescription
        }, me.state.databaseMsg), /*#__PURE__*/ React.createElement("div", {
            style: style.divDescription
        }, /*#__PURE__*/ React.createElement("div", {
            style: style.alignDescription
        }, /*#__PURE__*/ React.createElement("label", null), /*#__PURE__*/ React.createElement("label", {
            className: me.state.responseIcon,
            style: !me.state.loading ? me.state.responseStyle : style.displayNone
        }, me.state.responseStatus), /*#__PURE__*/ React.createElement("label", {
            title: me.state.responseMsg,
            style: !me.state.loading ? style.errorMessage : style.displayNone
        }, me.state.responseMsg), /*#__PURE__*/ React.createElement("div", {
            className: 'calcFieldLoading seicon-loading',
            style: me.state.loading ? style.displayBlock : style.displayNone
        }))))), /*#__PURE__*/ React.createElement("div", {
            style: style.sizeFieldList
        }, /*#__PURE__*/ React.createElement("div", {
            className: 'calcField_list'
        }, /*#__PURE__*/ React.createElement("div", {
            id: 'searchComponent'
        }, /*#__PURE__*/ React.createElement("ul", {
            id: 'modalSearch'
        }, /*#__PURE__*/ React.createElement("li", {
            style: style.displayInline
        }, /*#__PURE__*/ React.createElement("a", {
            id: 'modalSearchIcon',
            className: 'seicon-search search-icon'
        })), /*#__PURE__*/ React.createElement("li", {
            style: style.displayInline
        }, /*#__PURE__*/ React.createElement("input", {
            type: 'text',
            id: 'listSearch',
            autoComplete: 'off',
            placeholder: SE.t(212158),
            onChange: this.handleSearchChange,
            onFocus: this.focusSearch,
            onBlur: this.blurSearch
        })))), /*#__PURE__*/ React.createElement("div", {
            style: style.sizeCustomFieldList
        }, /*#__PURE__*/ React.createElement(CustomFieldList, {
            key: 1,
            title: SE.t(202303),
            items: me.state.fieldsList,
            callbackParams: {
                CustomDBAField: this
            }
        }), /*#__PURE__*/ React.createElement(CustomFieldList, {
            key: 0,
            title: SE.t(208631),
            items: me.state.constantsList,
            callbackParams: {
                CustomDBAField: this
            }
        })))))));
    },
    displayName: "CustomDBAField"
});
module.exports = {
    component: CustomDBAField
};


}),
"./src/analytics/customDBAField/soft.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return /* export default binding */ __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.string.match.js */ "../node_modules/core-js/modules/es.string.match.js");
/* ESM import */var core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_match_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_regexp_test_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.regexp.test.js */ "../node_modules/core-js/modules/es.regexp.test.js");
/* ESM import */var core_js_modules_es_regexp_test_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_test_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.string.replace.js */ "../node_modules/core-js/modules/es.string.replace.js");
/* ESM import */var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.string.split.js */ "../node_modules/core-js/modules/es.string.split.js");
/* ESM import */var core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_4__);
/* eslint-disable */  /* eslint-enable */ 




/* ESM default export */ function __WEBPACK_DEFAULT_EXPORT__(CodeMirror) {
    "use strict";
    /* istanbul ignore next */ CodeMirror.defineMode("sql", function(config, parserConfig) {
        "use strict";
        var client = parserConfig.client || {}, atoms = parserConfig.atoms || {
            "false": true,
            "true": true,
            "null": true
        }, builtin = parserConfig.builtin || {}, keywords = parserConfig.keywords || {}, operatorChars = parserConfig.operatorChars || /^[*+\-%<>!=&|~^]/, support = parserConfig.support || {}, hooks = parserConfig.hooks || {}, dateSQL = parserConfig.dateSQL || {
            date: true,
            time: true,
            timestamp: true
        };
        function tokenBase(stream, state) {
            var ch = stream.next();
            // call hooks from the mime type
            if (hooks[ch]) {
                var result = hooks[ch](stream, state);
                if (result !== false) return result;
            }
            if (support.hexNumber == true && (ch == "0" && stream.match(/^[xX][0-9a-fA-F]+/) || (ch == "x" || ch == "X") && stream.match(/^'[0-9a-fA-F]+'/))) {
                // hex
                // ref: http://dev.mysql.com/doc/refman/5.5/en/hexadecimal-literals.html
                return "number";
            } else if (support.binaryNumber == true && ((ch == "b" || ch == "B") && stream.match(/^'[01]+'/) || ch == "0" && stream.match(/^b[01]+/))) {
                // bitstring
                // ref: http://dev.mysql.com/doc/refman/5.5/en/bit-field-literals.html
                return "number";
            } else if (ch.charCodeAt(0) > 47 && ch.charCodeAt(0) < 58) {
                // numbers
                // ref: http://dev.mysql.com/doc/refman/5.5/en/number-literals.html
                stream.match(/^[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?/);
                support.decimallessFloat == true && stream.eat('.');
                return "number";
            } else if (ch == "?" && (stream.eatSpace() || stream.eol() || stream.eat(";"))) {
                // placeholders
                return "variable-3";
            } else if (ch == "'" || ch == '"' && support.doubleQuote) {
                // strings
                // ref: http://dev.mysql.com/doc/refman/5.5/en/string-literals.html
                state.tokenize = tokenLiteral(ch);
                return state.tokenize(stream, state);
            } else if ((support.nCharCast == true && (ch == "n" || ch == "N") || support.charsetCast == true && ch == "_" && stream.match(/[a-z][a-z0-9]*/i)) && (stream.peek() == "'" || stream.peek() == '"')) {
                // charset casting: _utf8'str', N'str', n'str'
                // ref: http://dev.mysql.com/doc/refman/5.5/en/string-literals.html
                return "keyword";
            } else if (support.commentSlashSlash && ch == "/" && stream.eat("/")) {
                // 1-line comment
                stream.skipToEnd();
                return "comment";
            } else if (support.commentHash && ch == "#" || ch == "-" && stream.eat("-") && (!support.commentSpaceRequired || stream.eat(" "))) {
                // 1-line comments
                // ref: https://kb.askmonty.org/en/comment-syntax/
                stream.skipToEnd();
                return "comment";
            } else if (ch == "/" && stream.eat("*")) {
                // multi-line comments
                // ref: https://kb.askmonty.org/en/comment-syntax/
                state.tokenize = tokenComment;
                return state.tokenize(stream, state);
            } else if (ch == "<" && stream.eat("!") && stream.eat("%")) {
                // draggable constant
                // ref: https://kb.askmonty.org/en/comment-syntax/
                state.tokenize = tokenConstant;
                return state.tokenize(stream, state);
            } else if (ch == "[" && stream.match(/^[a-zA-Z0-9]+/)) {
                // draggable variavel
                state.tokenize = tokenDrag;
                return state.tokenize(stream, state);
            } else if (ch == "(" && stream.eat("[")) {
                // draggable variavel in operation
                state.tokenize = tokenDragOperation;
                return state.tokenize(stream, state);
            } else if (ch == ".") {
                // .1 for 0.1
                if (support.zerolessFloat == true && stream.match(/^(?:\d+(?:e[+-]?\d+)?)/i)) {
                    return "number";
                }
                // .table_name (ODBC)
                // // ref: http://dev.mysql.com/doc/refman/5.6/en/identifier-qualifiers.html
                if (support.ODBCdotTable == true && stream.match(/^[a-zA-Z_]+/)) {
                    return "variable-2";
                }
            } else if (operatorChars.test(ch)) {
                // operators
                stream.eatWhile(operatorChars);
                return "operator";
            } else if (ch == '{' && (stream.match(/^( )*(d|D|t|T|ts|TS)( )*'[^']*'( )*}/) || stream.match(/^( )*(d|D|t|T|ts|TS)( )*"[^"]*"( )*}/))) {
                // dates (weird ODBC syntax)
                // ref: http://dev.mysql.com/doc/refman/5.5/en/date-and-time-literals.html
                return "number";
            } else if (/^[\(\)]/.test(ch)) {
                // no highlighting
                return null;
            } else {
                stream.eatWhile(/^[_\w\d]/);
                var word = stream.current().toLowerCase();
                // dates (standard SQL syntax)
                // ref: http://dev.mysql.com/doc/refman/5.5/en/date-and-time-literals.html
                if (dateSQL.hasOwnProperty(word) && (stream.match(/^( )+'[^']*'/) || stream.match(/^( )+"[^"]*"/))) return "number";
                if (atoms.hasOwnProperty(word)) return "atom";
                if (builtin.hasOwnProperty(word)) return "builtin";
                if (keywords.hasOwnProperty(word)) return "keyword";
                if (client.hasOwnProperty(word)) return "string-2";
                return null;
            }
        }
        // 'string', with char specified in quote escaped by '\'
        function tokenLiteral(quote) {
            return function(stream, state) {
                var escaped = false, ch;
                while((ch = stream.next()) != null){
                    if (ch == quote && !escaped) {
                        state.tokenize = tokenBase;
                        break;
                    }
                    escaped = !escaped && ch == "\\";
                }
                return "string";
            };
        }
        function tokenComment(stream, state) {
            while(true){
                if (stream.skipTo("*")) {
                    stream.next();
                    if (stream.eat("/")) {
                        state.tokenize = tokenBase;
                        break;
                    }
                } else {
                    stream.skipToEnd();
                    break;
                }
            }
            return "comment";
        }
        function tokenConstant(stream, state) {
            while(true){
                if (stream.skipTo("%")) {
                    stream.next();
                    if (stream.eat(">")) {
                        state.tokenize = tokenBase;
                        break;
                    }
                } else {
                    stream.skipToEnd();
                    break;
                }
            }
            return "drag";
        }
        function tokenDrag(stream, state) {
            while(true){
                if (stream.skipTo("]")) {
                    if (stream.eat("]")) {
                        state.tokenize = tokenBase;
                        break;
                    }
                } else {
                    stream.skipToEnd();
                    break;
                }
            }
            return "drag";
        }
        function tokenDragOperation(stream, state) {
            while(true){
                if (stream.skipTo(")")) {
                    stream.next(); // Pula para proximo " xyz|"
                    state.tokenize = tokenBase; // Fecha a class logo apos ele "</div>"
                    break;
                } else {
                    stream.skipToEnd();
                    break;
                }
            }
            return "dragoperation";
        }
        function pushContext(stream, state, type) {
            state.context = {
                prev: state.context,
                indent: stream.indentation(),
                col: stream.column(),
                type: type
            };
        }
        function popContext(state) {
            state.indent = state.context.indent;
            state.context = state.context.prev;
        }
        return {
            startState: function startState() {
                return {
                    tokenize: tokenBase,
                    context: null
                };
            },
            token: function token(stream, state) {
                if (stream.sol()) {
                    if (state.context && state.context.align == null) state.context.align = false;
                }
                if (stream.eatSpace()) return null;
                var style = state.tokenize(stream, state);
                if (style == "comment") return style;
                if (state.context && state.context.align == null) state.context.align = true;
                var tok = stream.current();
                if (tok == "(") pushContext(stream, state, ")");
                else if (tok == "[") pushContext(stream, state, "]");
                else if (state.context && state.context.type == tok) popContext(state);
                return style;
            },
            indent: function indent(state, textAfter) {
                var cx = state.context;
                if (!cx) return CodeMirror.Pass;
                var closing = textAfter.charAt(0) == cx.type;
                if (cx.align) return cx.col + (closing ? 0 : 1);
                else return cx.indent + (closing ? 0 : config.indentUnit);
            },
            blockCommentStart: "/*",
            blockCommentEnd: "*/",
            lineComment: support.commentSlashSlash ? "//" : support.commentHash ? "#" : null
        };
    });
    /* istanbul ignore next */ (function(CodeMirror) {
        function clearPlaceholder(cm) {
            if (cm.state.placeholder) {
                cm.state.placeholder.parentNode.removeChild(cm.state.placeholder);
                cm.state.placeholder = null;
            }
        }
        function setPlaceholder(cm) {
            clearPlaceholder(cm);
            var elt = cm.state.placeholder = document.createElement("pre");
            elt.style.cssText = "height: 0; overflow: visible";
            elt.className = "CodeMirror-placeholder";
            var placeHolder = cm.getOption("placeholder");
            if (typeof placeHolder == "string") placeHolder = document.createTextNode(placeHolder);
            elt.appendChild(placeHolder);
            cm.display.lineSpace.insertBefore(elt, cm.display.lineSpace.firstChild);
        }
        function onBlur(cm) {
            if (isEmpty(cm)) setPlaceholder(cm);
        }
        function onChange(cm) {
            var wrapper = cm.getWrapperElement(), empty = isEmpty(cm);
            wrapper.className = wrapper.className.replace(" CodeMirror-empty", "") + (empty ? " CodeMirror-empty" : "");
            if (empty) setPlaceholder(cm);
            else clearPlaceholder(cm);
        }
        function isEmpty(cm) {
            return cm.lineCount() === 1 && cm.getLine(0) === "";
        }
        CodeMirror.defineOption("placeholder", "", function(cm, val, old) {
            var prev = old && old != CodeMirror.Init;
            if (val && !prev) {
                cm.on("blur", onBlur);
                cm.on("change", onChange);
                cm.on("swapDoc", onChange);
                onChange(cm);
            } else if (!val && prev) {
                cm.off("blur", onBlur);
                cm.off("change", onChange);
                cm.off("swapDoc", onChange);
                clearPlaceholder(cm);
                var wrapper = cm.getWrapperElement();
                wrapper.className = wrapper.className.replace(" CodeMirror-empty", "");
            }
            if (val && !cm.hasFocus()) onBlur(cm);
        });
    })(CodeMirror);
    /* istanbul ignore next */ (function() {
        "use strict";
        // `identifier`
        function hookIdentifier(stream) {
            // MySQL/MariaDB identifiers
            // ref: http://dev.mysql.com/doc/refman/5.6/en/identifier-qualifiers.html
            var ch;
            while((ch = stream.next()) != null){
                if (ch == "`" && !stream.eat("`")) return "variable-2";
            }
            stream.backUp(stream.current().length - 1);
            return stream.eatWhile(/\w/) ? "variable-2" : null;
        }
        // variable token
        function hookVar(stream) {
            // variables
            // @@prefix.varName @varName
            // varName can be quoted with ` or ' or "
            // ref: http://dev.mysql.com/doc/refman/5.5/en/user-variables.html
            if (stream.eat("@")) {
                stream.match(/^session\./);
                stream.match(/^local\./);
                stream.match(/^global\./);
            }
            if (stream.eat("'")) {
                stream.match(/^.*'/);
                return "variable-2";
            } else if (stream.eat('"')) {
                stream.match(/^.*"/);
                return "variable-2";
            } else if (stream.eat("`")) {
                stream.match(/^.*`/);
                return "variable-2";
            } else if (stream.match(/^[0-9a-zA-Z$\.\_]+/)) {
                return "variable-2";
            }
            return null;
        }
        ;
        // short client keyword token
        function hookClient(stream) {
            // \N means NULL
            // ref: http://dev.mysql.com/doc/refman/5.5/en/null-values.html
            if (stream.eat("N")) {
                return "atom";
            }
            // \g, etc
            // ref: http://dev.mysql.com/doc/refman/5.5/en/mysql-commands.html
            return stream.match(/^[a-zA-Z.#!?]/) ? "variable-2" : null;
        }
        // these keywords are used by all SQL dialects (however, a mode can still overwrite it)
        var sqlKeywords = "alter and as asc avg between by case corr count covar_pop covar_samp create cume_dist current delete dense_rank desc distinct drop else end extract first first_value from group having in insert into is join lag last last_value lead like limit listagg max min not nth_value ntile on or order over partition percent_rank percentile_cont percentile_disc preceding rank ratio_to_report regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy row row_number rows select set stddev stddev_pop stddev_samp sum table then unbounded union update values var_pop var_samp variance when where ";
        // turn a space-separated list into an array
        function set(str) {
            var obj = {}, words = str.split(" ");
            for(var i = 0; i < words.length; ++i)obj[words[i]] = true;
            return obj;
        }
        // A generic SOFT SQL Mode. It's not a standard, it just try to support what is generally supported
        CodeMirror.defineMIME("text/soft-sql", {
            name: "sql",
            keywords: set(sqlKeywords + "begin"),
            builtin: set("bool boolean bit blob enum long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision real date datetime year unsigned signed decimal numeric"),
            atoms: set("false true null unknown"),
            operatorChars: /^[*+\-%<>!=]/,
            dateSQL: set("date time timestamp"),
            support: set("ODBCdotTable doubleQuote binaryNumber hexNumber")
        });
    })();
}
;


}),
"./src/analytics/customFieldList/customFieldList.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return FieldList; }
});
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var immutable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! immutable */ "../node_modules/immutable/dist/immutable.js");
/* ESM import */var immutable__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(immutable__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactorCmps_src_analytics_customFieldListItem_customFieldListItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactorCmps/src/analytics/customFieldListItem/customFieldListItem */ "./src/analytics/customFieldListItem/customFieldListItem.jsx");
/* ESM import */var reactorCmps_tokens_general__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");
/* ESM import */var reactorCmps_tokens_general__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_tokens_general__WEBPACK_IMPORTED_MODULE_5__);
function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _call_super(_this, derived, args) {
    derived = _get_prototype_of(derived);
    return _possible_constructor_return(_this, _is_native_reflect_construct() ? Reflect.construct(derived, args || [], _get_prototype_of(_this).constructor) : derived.apply(_this, args));
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _is_native_reflect_construct() {
    try {
        var result = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
    } catch (_) {}
    return (_is_native_reflect_construct = function() {
        return !!result;
    })();
}







var styleSource = {
    CustomFieldList: {
        background: '#fff',
        color: '#444',
        fontWeight: '300',
        border: '0px solid #ccc',
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        overflowX: 'hidden'
    },
    CustomFieldListTitle: {
        marginBottom: '0px',
        fontSize: '11px',
        textTransform: 'uppercase',
        paddingLeft: '13px',
        paddingBottom: '10px',
        flex: '0 0 35px',
        maxWidth: '239px',
        maxHeight: '35px'
    },
    CustomFieldListContainer: {
        padding: '0px 20px 10px 20px',
        background: '#fff',
        flex: '1 0 auto'
    }
};
var style = immutable__WEBPACK_IMPORTED_MODULE_2___default().fromJS(styleSource);
var FieldList = /*#__PURE__*/ function(Component) {
    "use strict";
    _inherits(FieldList, Component);
    function FieldList(props) {
        _class_call_check(this, FieldList);
        var _this;
        _this = _call_super(this, FieldList, [
            props
        ]);
        _this.state = _this.getDefaultState(props);
        return _this;
    }
    _create_class(FieldList, [
        {
            key: "getDefaultState",
            value: function getDefaultState(props) {
                var initialState = {
                    title: 'default list title',
                    id: '',
                    items: [],
                    calculatedField: undefined
                };
                var instanceStyle = {};
                if (props.title) initialState.title = props.title;
                if (props.items && props.items.length) initialState.items = props.items;
                /* istanbul ignore next */ if (props.calculatedField) initialState.calculatedField = props.calculatedField;
                /* istanbul ignore next */ if (props.id) initialState.id = props.id;
                instanceStyle = style.toJS();
                initialState.style = instanceStyle;
                return initialState;
            }
        },
        {
            key: "render",
            value: function render() {
                var me = this;
                var callbackParams = me.props.callbackParams;
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3___default().createElement("div", {
                    className: 'fieldsItems',
                    style: me.state.style.CustomFieldList
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3___default().createElement("h4", {
                    style: me.state.style.CustomFieldListTitle
                }, me.state.title), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3___default().createElement("div", {
                    style: me.state.style.CustomFieldListContainer
                }, me.props && me.props.items ? me.props.items.map(function(value, index) {
                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3___default().createElement(reactorCmps_src_analytics_customFieldListItem_customFieldListItem__WEBPACK_IMPORTED_MODULE_4__["default"], {
                        key: index,
                        title: value.title,
                        icon: value.icon,
                        name: value.name,
                        id: value.id,
                        calculatedField: value.calculatedField,
                        onClick: value.onClick,
                        callbackParams: callbackParams
                    });
                }) : []));
            }
        }
    ]);
    return FieldList;
}(react__WEBPACK_IMPORTED_MODULE_3__.Component);

FieldList.propTypes = {
    title: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
    items: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().array),
    id: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
    calculatedField: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().object),
    callbackParams: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().object)
};
FieldList.displayName = 'Analytics/CustomFieldList/CustomFieldList';


}),
"./src/analytics/customFieldListItem/customFieldListItem.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return ListItem; }
});
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var immutable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! immutable */ "../node_modules/immutable/dist/immutable.js");
/* ESM import */var immutable__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(immutable__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var reactorCmps_tokens_general__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");
/* ESM import */var reactorCmps_tokens_general__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_tokens_general__WEBPACK_IMPORTED_MODULE_4__);
function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _call_super(_this, derived, args) {
    derived = _get_prototype_of(derived);
    return _possible_constructor_return(_this, _is_native_reflect_construct() ? Reflect.construct(derived, args || [], _get_prototype_of(_this).constructor) : derived.apply(_this, args));
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _is_native_reflect_construct() {
    try {
        var result = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
    } catch (_) {}
    return (_is_native_reflect_construct = function() {
        return !!result;
    })();
}






var styleSource = {
    CustomFieldListItem: {
        background: '#fff',
        padding: '0px 5px',
        cursor: 'pointer',
        borderRadius: 3,
        marginBottom: 5,
        maxWidth: 199
    },
    CustomFieldListItemTitle: {
        color: '#999',
        fontSize: 10,
        padding: '5px 5px 0px 5px',
        cursor: 'pointer'
    },
    CustomFieldListItemGrid: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        paddingBottom: 5,
        maxHeight: 18
    },
    CustomFieldListItemGridDate: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        paddingBottom: 5,
        maxHeight: 24,
        margin: '1px 0'
    },
    CustomFieldListItemIcon: {
        color: '#333',
        fontSize: 20,
        flex: '0 0 28px',
        cursor: 'pointer',
        position: 'relative',
        top: 0
    },
    CustomFieldListItemName: {
        fontSize: 12,
        paddingTop: 1,
        paddingLeft: 10,
        color: '#333',
        cursor: 'pointer',
        marginBottom: 0,
        textOverflow: 'ellipsis',
        width: '100%',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        maxWidth: 156
    },
    CustomFieldListItemIconSpan: {
        display: 'inline-block',
        fontSize: 20
    },
    ItemFormulaIcon: {
        color: '#878787',
        textDecoration: 'none'
    }
};
var style = immutable__WEBPACK_IMPORTED_MODULE_3___default().fromJS(styleSource);
var ListItem = /*#__PURE__*/ function(Component) {
    "use strict";
    _inherits(ListItem, Component);
    function ListItem(props) {
        _class_call_check(this, ListItem);
        var _this;
        _this = _call_super(this, ListItem, [
            props
        ]);
        _this.state = _this.getDefaultState(props);
        return _this;
    }
    _create_class(ListItem, [
        {
            key: "getDefaultState",
            value: function getDefaultState(props) {
                var initialState = {
                    title: '',
                    icon: '',
                    name: '',
                    id: '',
                    calculatedField: undefined
                };
                var instanceStyle = {};
                if (props.title) initialState.title = props.title;
                if (props.name) initialState.name = props.name;
                if (props.icon) initialState.icon = props.icon;
                if (props.calculatedField) initialState.calculatedField = props.calculatedField;
                if (props.id) initialState.id = props.id;
                style = immutable__WEBPACK_IMPORTED_MODULE_3___default().fromJS(styleSource);
                instanceStyle = style.toJS();
                initialState.style = instanceStyle;
                return initialState;
            }
        },
        {
            key: "getAsJson",
            value: function getAsJson() {
                return {
                    title: this.state.title,
                    icon: this.state.icon,
                    name: this.state.name,
                    id: this.state.id,
                    calculatedField: this.state.calculatedField,
                    hoverStyle: {
                        background: '#fff'
                    }
                };
            }
        },
        {
            key: "setIconColor",
            value: function setIconColor(hex) {
                this.setState({
                    style: style.setIn([
                        'CustomFieldListItemIcon',
                        'color'
                    ], hex).toJS()
                });
            }
        },
        {
            key: "hoverIn",
            value: function hoverIn() {
                this.setState({
                    style: style.setIn([
                        'CustomFieldListItem',
                        'background'
                    ], '#e9e9e9').toJS()
                });
            }
        },
        {
            key: "hoverOut",
            value: function hoverOut() {
                this.setState({
                    style: style.setIn([
                        'CustomFieldListItem',
                        'background'
                    ], '#fff').toJS()
                });
            }
        },
        {
            key: "render",
            value: function render() {
                var me = this;
                var click = me.props.onClick;
                var callbackParams = me.props.callbackParams || {};
                if (click) click = click.bind(me, callbackParams);
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement("div", {
                    style: me.state.style.CustomFieldListItem,
                    onMouseEnter: me.hoverIn.bind(me),
                    onMouseLeave: me.hoverOut.bind(me),
                    onClick: click,
                    title: !me.state.calculatedField ? me.state.id || me.state.title : me.state.id + ' - fx'
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement("div", {
                    style: me.state.style.CustomFieldListItemTitle
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement("span", null, me.props.title ? me.props.title : null)), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement("div", {
                    style: me.props.icon === 'seicon-datetime' ? me.state.style.CustomFieldListItemGridDate : me.state.style.CustomFieldListItemGrid
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement("span", {
                    style: me.state.style.CustomFieldListItemIcon
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement("span", {
                    style: me.state.style.CustomFieldListItemIconSpan,
                    className: me.props.icon
                })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement("label", {
                    style: me.state.style.CustomFieldListItemName
                }, me.props.name, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement("a", {
                    className: 'seicon-formula',
                    style: me.state.calculatedField ? me.state.style.ItemFormulaIcon : {
                        display: 'none'
                    }
                }))));
            }
        }
    ]);
    return ListItem;
}(react__WEBPACK_IMPORTED_MODULE_2__.Component);

ListItem.propTypes = {
    title: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string),
    icon: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string),
    name: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string),
    id: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string),
    calculatedField: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().object),
    onClick: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().func),
    callbackParams: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().object)
};
ListItem.displayName = 'Analytics/customFieldListItem/customFieldListItem';


}),
"./src/chartengine2/constants/debounceDefaults.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  FAST: function() { return FAST; },
  MEDIUM: function() { return MEDIUM; },
  SLOW: function() { return SLOW; },
  SUPER_SLOW: function() { return SUPER_SLOW; },
  VERY_FAST: function() { return VERY_FAST; },
  VERY_SLOW: function() { return VERY_SLOW; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var VERY_FAST = 10;
var FAST = 200;
var MEDIUM = 300;
var SLOW = 400;
var VERY_SLOW = 750;
var SUPER_SLOW = 1200;
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    FAST: FAST,
    MEDIUM: MEDIUM,
    SLOW: SLOW,
    SUPER_SLOW: SUPER_SLOW,
    VERY_FAST: VERY_FAST,
    VERY_SLOW: VERY_SLOW
});


}),
"./src/analytics/styles/codemirror.css": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js */ "../node_modules/style-loader/dist/runtime/styleDomAPI.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/insertBySelector.js */ "../node_modules/style-loader/dist/runtime/insertBySelector.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js */ "../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js */ "../node_modules/style-loader/dist/runtime/insertStyleElement.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js */ "../node_modules/style-loader/dist/runtime/styleTagTransform.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_codemirror_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! !!../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./codemirror.css */ "../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./src/analytics/styles/codemirror.css");

      
      
      
      
      
      
      
      
      

var options = {};

options.styleTagTransform = (_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default());
options.setAttributes = (_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default());
options.insert = _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default().bind(null, "head");
options.domAPI = (_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default());
options.insertStyleElement = (_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default());

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_codemirror_css__WEBPACK_IMPORTED_MODULE_6__["default"], options);




       /* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_codemirror_css__WEBPACK_IMPORTED_MODULE_6__["default"] && _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_codemirror_css__WEBPACK_IMPORTED_MODULE_6__["default"].locals ? _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_codemirror_css__WEBPACK_IMPORTED_MODULE_6__["default"].locals : undefined);


}),
"Connector": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/analytics/customDBAField/Main.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=customDBAField.js.map