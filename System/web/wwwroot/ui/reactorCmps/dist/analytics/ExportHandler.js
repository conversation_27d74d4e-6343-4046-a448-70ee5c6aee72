"use strict";
define(["Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","WorkspaceInfo","tokens!reactorCmps/tokens/general","Token/TokenHandler","Connector","jquery"], function(__WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_WorkspaceInfo__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_Token_TokenHandler__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEBPACK_EXTERNAL_MODULE_jquery__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["analytics/ExportHandler"], {
"./src/analytics/constants/exportation/formats.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  DOCX: function() { return DOCX; },
  HTML: function() { return HTML; },
  JRPRINT: function() { return JRPRINT; },
  MXLS: function() { return MXLS; },
  ODS: function() { return ODS; },
  ODT: function() { return ODT; },
  PDF: function() { return PDF; },
  PNG: function() { return PNG; },
  RTF: function() { return RTF; },
  XLS: function() { return XLS; },
  XLSX: function() { return XLSX; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; },
  jasperExportTypes: function() { return jasperExportTypes; }
});
var DOCX = 'DOCX';
var HTML = 'HTML';
var JRPRINT = 'JRPRINT';
var MXLS = 'MXLS';
var ODS = 'ODS';
var ODT = 'ODT';
var PDF = 'PDF';
var PNG = 'PNG';
var RTF = 'RTF';
var XLS = 'XLS';
var XLSX = 'XLSX';
var jasperExportTypes = [
    DOCX,
    HTML,
    JRPRINT,
    ODS,
    ODT,
    PDF,
    RTF,
    XLS
];
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    DOCX: DOCX,
    HTML: HTML,
    JRPRINT: JRPRINT,
    MXLS: MXLS,
    ODS: ODS,
    ODT: ODT,
    PDF: PDF,
    PNG: PNG,
    RTF: RTF,
    XLS: XLS,
    XLSX: XLSX
});


}),
"./src/analytics/constants/exportation/margins.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  DEFAULT: function() { return DEFAULT; },
  MINIMAL: function() { return MINIMAL; },
  NONE: function() { return NONE; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var DEFAULT = 'DEFAULT';
var NONE = 'NONE';
var MINIMAL = 'MINIMAL';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    DEFAULT: DEFAULT,
    MINIMAL: MINIMAL,
    NONE: NONE
});


}),
"./src/analytics/constants/exportation/orientations.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  LANDSCAPE: function() { return LANDSCAPE; },
  PORTRAIT: function() { return PORTRAIT; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var LANDSCAPE = 'LANDSCAPE';
var PORTRAIT = 'PORTRAIT';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    LANDSCAPE: LANDSCAPE,
    PORTRAIT: PORTRAIT
});


}),
"./src/analytics/constants/exportation/paperSizes.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  A4: function() { return A4; },
  AUTO: function() { return AUTO; },
  LETTER: function() { return LETTER; },
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
var A4 = 'A4';
var AUTO = 'AUTO';
var LETTER = 'LETTER';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    A4: A4,
    AUTO: AUTO,
    LETTER: LETTER
});


}),
"./src/analytics/helpers/ExportHandler/ExportHandler.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  DEFAULT_TIMEOUT: function() { return DEFAULT_TIMEOUT; },
  callExporter: function() { return callExporter; },
  exportToPDF: function() { return exportToPDF; },
  exportToPNG: function() { return exportToPNG; },
  formats: function() { return /* reexport safe */ reactorCmps_src_analytics_constants_exportation_formats__WEBPACK_IMPORTED_MODULE_11__["default"]; },
  handleResponse: function() { return handleResponse; },
  margins: function() { return /* reexport safe */ reactorCmps_src_analytics_constants_exportation_margins__WEBPACK_IMPORTED_MODULE_14__["default"]; },
  orientations: function() { return /* reexport safe */ reactorCmps_src_analytics_constants_exportation_orientations__WEBPACK_IMPORTED_MODULE_15__["default"]; },
  paperSizes: function() { return /* reexport safe */ reactorCmps_src_analytics_constants_exportation_paperSizes__WEBPACK_IMPORTED_MODULE_16__["default"]; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_web_url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/web.url.js */ "../node_modules/core-js/modules/web.url.js");
/* ESM import */var core_js_modules_web_url_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_url_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_web_url_to_json_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/web.url.to-json.js */ "../node_modules/core-js/modules/web.url.to-json.js");
/* ESM import */var core_js_modules_web_url_to_json_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_url_to_json_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_web_url_search_params_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/web.url-search-params.js */ "../node_modules/core-js/modules/web.url-search-params.js");
/* ESM import */var core_js_modules_web_url_search_params_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_url_search_params_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ "../node_modules/core-js/modules/es.array.iterator.js");
/* ESM import */var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ "../node_modules/core-js/modules/web.dom-collections.iterator.js");
/* ESM import */var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ "../node_modules/core-js/modules/es.string.iterator.js");
/* ESM import */var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var jquery__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! jquery */ "jquery");
/* ESM import */var jquery__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(jquery__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var reactorCmps_src_analytics_constants_exportation_formats__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactorCmps/src/analytics/constants/exportation/formats */ "./src/analytics/constants/exportation/formats.js");
/* ESM import */var reactorCmps_src_analytics_helpers_ExportHandler_ExportHelper__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! reactorCmps/src/analytics/helpers/ExportHandler/ExportHelper */ "./src/analytics/helpers/ExportHandler/ExportHelper.js");
/* ESM import */var reactorCmps_src_analytics_services_exportation_ExportServices__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactorCmps/src/analytics/services/exportation/ExportServices */ "./src/analytics/services/exportation/ExportServices.js");
/* ESM import */var reactorCmps_src_analytics_constants_exportation_margins__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactorCmps/src/analytics/constants/exportation/margins */ "./src/analytics/constants/exportation/margins.js");
/* ESM import */var reactorCmps_src_analytics_constants_exportation_orientations__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactorCmps/src/analytics/constants/exportation/orientations */ "./src/analytics/constants/exportation/orientations.js");
/* ESM import */var reactorCmps_src_analytics_constants_exportation_paperSizes__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! reactorCmps/src/analytics/constants/exportation/paperSizes */ "./src/analytics/constants/exportation/paperSizes.js");
// eslint-disable-next-line no-unused-vars
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
    try {
        var info = gen[key](arg);
        var value = info.value;
    } catch (error) {
        reject(error);
        return;
    }
    if (info.done) {
        resolve(value);
    } else {
        Promise.resolve(value).then(_next, _throw);
    }
}
function _async_to_generator(fn) {
    return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
            var gen = fn.apply(self, args);
            function _next(value) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
            }
            function _throw(err) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
            }
            _next(undefined);
        });
    };
}
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
function _ts_generator(thisArg, body) {
    var f, y, t, g, _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    };
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}








 // FRAM-2025









var DEFAULT_TIMEOUT = 300000;
var getSystemParams = function() {
    var systemUrl = Utils__WEBPACK_IMPORTED_MODULE_9___default().getSystemUrl(true);
    var loginUrl = WorkspaceInfo__WEBPACK_IMPORTED_MODULE_10___default().isExternal() ? 'external-login' : 'login';
    return {
        domain: "".concat(systemUrl, "/softexpert/").concat(loginUrl),
        host: new URL(systemUrl).host,
        language: WorkspaceInfo__WEBPACK_IMPORTED_MODULE_10___default().getDefaultLanguage(),
        timezone: WorkspaceInfo__WEBPACK_IMPORTED_MODULE_10___default().getSystemInfo().timezone,
        type: 'nodeParameters'
    };
};
var getParams = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function(userParams) {
        var _ref, bearToken, token, paperSize, systemParams;
        return _ts_generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        reactorCmps_src_analytics_services_exportation_ExportServices__WEBPACK_IMPORTED_MODULE_13__["default"].getTokens()
                    ];
                case 1:
                    _ref = _state.sent(), bearToken = _ref.bearToken, token = _ref.token;
                    return [
                        4,
                        reactorCmps_src_analytics_services_exportation_ExportServices__WEBPACK_IMPORTED_MODULE_13__["default"].getExporterConfig()
                    ];
                case 2:
                    paperSize = _state.sent().paperSize;
                    systemParams = getSystemParams();
                    return [
                        2,
                        _object_spread({
                            format: reactorCmps_src_analytics_constants_exportation_formats__WEBPACK_IMPORTED_MODULE_11__.PNG,
                            timeout: DEFAULT_TIMEOUT,
                            paperSize: paperSize,
                            bearToken: bearToken,
                            token: token
                        }, systemParams, userParams)
                    ];
            }
        });
    });
    return function getParams(userParams) {
        return _ref.apply(this, arguments);
    };
}();
/**
 *
 * @param {string|object} response Retorno da API de exportação
 * @param {formats} format Formato do arquivo exportado
 * @param {object} options Opções de manipulação do arquivo exportado
 * @returns {promise<void|object>}
 */ var handleResponse = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function(response, format, options) {
        var action, openOnSelf, fileData;
        return _ts_generator(this, function(_state) {
            action = options.action, openOnSelf = options.openOnSelf;
            fileData = _object_spread_props(_object_spread({}, response), {
                format: format
            });
            if (action === 'custom') {
                return [
                    2,
                    fileData
                ];
            }
            if (action === 'open') {
                return [
                    2,
                    reactorCmps_src_analytics_helpers_ExportHandler_ExportHelper__WEBPACK_IMPORTED_MODULE_12__["default"].openFileOnWindow(fileData, openOnSelf)
                ];
            }
            reactorCmps_src_analytics_helpers_ExportHandler_ExportHelper__WEBPACK_IMPORTED_MODULE_12__["default"].downloadPathFile(fileData.fileName);
            return [
                2
            ];
        });
    });
    return function handleResponse(response, format, options) {
        return _ref.apply(this, arguments);
    };
}();
/**
 *
 * @param {object} params Parâmetros da API de exportação
 * @param {?object} options Opções de manipulação do arquivo exportado
 * @returns {promise<object|void>}
 */ var callExporter = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function(params) {
        var options, apiParams, response;
        var _arguments = arguments;
        return _ts_generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    options = _arguments.length > 1 && _arguments[1] !== void 0 ? _arguments[1] : {};
                    return [
                        4,
                        getParams(params)
                    ];
                case 1:
                    apiParams = _state.sent();
                    return [
                        4,
                        reactorCmps_src_analytics_services_exportation_ExportServices__WEBPACK_IMPORTED_MODULE_13__["default"].callAPI(apiParams)
                    ];
                case 2:
                    response = _state.sent();
                    return [
                        2,
                        handleResponse(response, apiParams.format, options)
                    ];
            }
        });
    });
    return function callExporter(params) {
        return _ref.apply(this, arguments);
    };
}();
/**
 *
 * @param {object} params Parâmetros da API de exportação
 * @param {?object} options Opções de manipulação do arquivo exportado
 * @returns {promise<object|void>}
 */ var exportToPDF = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function(params, options) {
        var preparedParams;
        return _ts_generator(this, function(_state) {
            preparedParams = _object_spread_props(_object_spread({}, params), {
                format: reactorCmps_src_analytics_constants_exportation_formats__WEBPACK_IMPORTED_MODULE_11__.PDF
            });
            return [
                2,
                callExporter(preparedParams, options)
            ];
        });
    });
    return function exportToPDF(params, options) {
        return _ref.apply(this, arguments);
    };
}();
/**
 *
 * @param {object} params Parâmetros da API de exportação
 * @param {?object} options Opções de manipulação do arquivo exportado
 * @returns {promise<object|void>}
 */ var exportToPNG = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function(params, options) {
        var preparedParams;
        return _ts_generator(this, function(_state) {
            preparedParams = _object_spread_props(_object_spread({}, params), {
                format: reactorCmps_src_analytics_constants_exportation_formats__WEBPACK_IMPORTED_MODULE_11__.PNG
            });
            return [
                2,
                callExporter(preparedParams, options)
            ];
        });
    });
    return function exportToPNG(params, options) {
        return _ref.apply(this, arguments);
    };
}();


}),
"./src/analytics/helpers/ExportHandler/ExportHelper.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; },
  downloadFile: function() { return downloadFile; },
  downloadPathFile: function() { return downloadPathFile; },
  openFileOnWindow: function() { return openFileOnWindow; }
});
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_1__);
/* istanbul ignore file */ 

/**
 *
 * @param {string} link Link do arquivo a ser aberto no navegador, base64 ou diretório
 * @param {string} fileName Nome do arquivo a ser salvo
 */ var downloadFile = function(link, fileName) {
    if (__webpack_require__.g.__karma__) {
        return;
    }
    var downloadLink = document.createElement('a');
    document.body.appendChild(downloadLink);
    downloadLink.href = link;
    downloadLink.target = '_self';
    downloadLink.download = fileName;
    downloadLink.click();
    downloadLink.remove();
};
/**
 *
 * @param {string} fileName Nome do arquivo salvo na temp
 */ var downloadPathFile = function(fileName, downloadName) {
    var url = "".concat(Utils__WEBPACK_IMPORTED_MODULE_1___default().getSystemUrl(), "/temp/").concat(fileName);
    downloadFile(url, downloadName || fileName);
};
/**
 *
 * @param {object} fileData Dados do arquivo exportado normalizado
 * @param {formats} format Formato do arquivo exportado
 * @param {?boolean} isBase64 Informa se o arquivo foi retornado como base64 ou está salvo na temp
 * @param {?boolean} openOnSelf Informa se o arquivo será aberto na mesma aba
 * @returns {promise<void>}
 */ var openFileOnWindow = function(fileData, openOnSelf) {
    if (__webpack_require__.g.__karma__) {
        return;
    }
    var url = fileData.absolutePath;
    if (openOnSelf) {
        return window.open(url, '_self');
    }
    Utils__WEBPACK_IMPORTED_MODULE_1___default().openNewTab(url);
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    downloadFile: downloadFile,
    downloadPathFile: downloadPathFile,
    openFileOnWindow: openFileOnWindow
});


}),
"./src/analytics/services/exportation/ExportServices.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.string.replace.js */ "../node_modules/core-js/modules/es.string.replace.js");
/* ESM import */var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ "../node_modules/core-js/modules/es.array.iterator.js");
/* ESM import */var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ "../node_modules/core-js/modules/web.dom-collections.iterator.js");
/* ESM import */var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ "../node_modules/core-js/modules/es.string.iterator.js");
/* ESM import */var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.json.stringify.js */ "../node_modules/core-js/modules/es.json.stringify.js");
/* ESM import */var core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_json_stringify_js__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! Connector */ "Connector");
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(Connector__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var Token_TokenHandler__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! Token/TokenHandler */ "Token/TokenHandler");
/* ESM import */var Token_TokenHandler__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(Token_TokenHandler__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var lodash_noop__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lodash/noop */ "../node_modules/lodash/noop.js");
/* ESM import */var lodash_noop__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(lodash_noop__WEBPACK_IMPORTED_MODULE_10__);
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
    try {
        var info = gen[key](arg);
        var value = info.value;
    } catch (error) {
        reject(error);
        return;
    }
    if (info.done) {
        resolve(value);
    } else {
        Promise.resolve(value).then(_next, _throw);
    }
}
function _async_to_generator(fn) {
    return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
            var gen = fn.apply(self, args);
            function _next(value) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
            }
            function _throw(err) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
            }
            _next(undefined);
        });
    };
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
function _ts_generator(thisArg, body) {
    var f, y, t, g, _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    };
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}











var exportConfigPromise = null;
var ExportServices = /*#__PURE__*/ function() {
    "use strict";
    function ExportServices() {
        _class_call_check(this, ExportServices);
    }
    _create_class(ExportServices, null, [
        {
            key: "getExporterConfigPromise",
            value: /* istanbul ignore next */ function getExporterConfigPromise() {
                return _async_to_generator(function() {
                    return _ts_generator(this, function(_state) {
                        if (exportConfigPromise) {
                            return [
                                2,
                                exportConfigPromise
                            ];
                        }
                        exportConfigPromise = Connector__WEBPACK_IMPORTED_MODULE_8___default().callPlatformRestRead('exporter/v1/config');
                        return [
                            2,
                            exportConfigPromise
                        ];
                    });
                })();
            }
        },
        {
            key: "getExporterConfig",
            value: function getExporterConfig() {
                return _async_to_generator(function() {
                    var _ref, paperSize, type, url;
                    return _ts_generator(this, function(_state) {
                        switch(_state.label){
                            case 0:
                                return [
                                    4,
                                    ExportServices.getExporterConfigPromise()
                                ];
                            case 1:
                                _ref = _state.sent(), paperSize = _ref.paperSize, type = _ref.type, url = _ref.url;
                                return [
                                    2,
                                    Promise.resolve({
                                        isHTMLConverter: type === 'external',
                                        url: url.replace(/\/?$/, '/'),
                                        paperSize: paperSize
                                    })
                                ];
                        }
                    });
                })();
            }
        },
        {
            key: "getTokens",
            value: function getTokens() {
                return _async_to_generator(function() {
                    var _ref, token, bearToken;
                    return _ts_generator(this, function(_state) {
                        switch(_state.label){
                            case 0:
                                return [
                                    4,
                                    Promise.all([
                                        Token_TokenHandler__WEBPACK_IMPORTED_MODULE_9___default().getToken(),
                                        Token_TokenHandler__WEBPACK_IMPORTED_MODULE_9___default().getJwt(true)
                                    ])
                                ];
                            case 1:
                                _ref = _sliced_to_array.apply(void 0, [
                                    _state.sent(),
                                    2
                                ]), token = _ref[0], bearToken = _ref[1];
                                return [
                                    2,
                                    {
                                        bearToken: "Bearer ".concat(bearToken),
                                        token: token
                                    }
                                ];
                        }
                    });
                })();
            }
        },
        {
            key: "callHTMLConverter",
            value: function callHTMLConverter(params) {
                return _async_to_generator(function() {
                    var url, response;
                    return _ts_generator(this, function(_state) {
                        switch(_state.label){
                            case 0:
                                return [
                                    4,
                                    ExportServices.getExporterConfig()
                                ];
                            case 1:
                                url = _state.sent().url;
                                return [
                                    4,
                                    fetch("".concat(url, "export/temp"), {
                                        headers: {
                                            accept: 'application/json',
                                            'content-type': 'application/json'
                                        },
                                        body: JSON.stringify(params),
                                        method: 'POST'
                                    })
                                ];
                            case 2:
                                response = _state.sent();
                                /* istanbul ignore if */ if (!response.ok) {
                                    return [
                                        2,
                                        Promise.reject(response)
                                    ];
                                }
                                return [
                                    2,
                                    response.json()
                                ];
                        }
                    });
                })();
            }
        },
        {
            key: "callLegacy",
            value: function callLegacy(params) {
                return _async_to_generator(function() {
                    return _ts_generator(this, function(_state) {
                        return [
                            2,
                            Connector__WEBPACK_IMPORTED_MODULE_8___default().callPlatformRestRead('export/v1/path-legacy', JSON.stringify(params), {
                                contentType: 'application/json;charset=UTF-8',
                                error: (lodash_noop__WEBPACK_IMPORTED_MODULE_10___default()),
                                type: (Connector__WEBPACK_IMPORTED_MODULE_8___default().POST)
                            })
                        ];
                    });
                })();
            }
        },
        {
            key: "callAPI",
            value: function callAPI(params) {
                return _async_to_generator(function() {
                    var isHTMLConverter;
                    return _ts_generator(this, function(_state) {
                        switch(_state.label){
                            case 0:
                                return [
                                    4,
                                    ExportServices.getExporterConfig()
                                ];
                            case 1:
                                isHTMLConverter = _state.sent().isHTMLConverter;
                                if (!isHTMLConverter) return [
                                    3,
                                    3
                                ];
                                return [
                                    4,
                                    ExportServices.callHTMLConverter(params)
                                ];
                            case 2:
                                return [
                                    2,
                                    _state.sent()
                                ];
                            case 3:
                                return [
                                    4,
                                    ExportServices.callLegacy(params)
                                ];
                            case 4:
                                return [
                                    2,
                                    _state.sent()
                                ];
                        }
                    });
                })();
            }
        }
    ]);
    return ExportServices;
}();
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ExportServices);


}),
"Connector": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"Token/TokenHandler": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Token_TokenHandler__;

}),
"Utils": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"WorkspaceInfo": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_WorkspaceInfo__;

}),
"jquery": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_jquery__;

}),
"watch1749037385376": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"reactorCmps/tokens/general": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/analytics/helpers/ExportHandler/ExportHandler.js"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=ExportHandler.js.map