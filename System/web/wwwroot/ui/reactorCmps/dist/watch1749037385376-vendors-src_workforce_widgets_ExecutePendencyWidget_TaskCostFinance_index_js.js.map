{"version": 3, "file": "watch1749037385376-vendors-src_workforce_widgets_ExecutePendencyWidget_TaskCostFinance_index_js.js", "sources": ["webpack://watch1749037385376/./src/workforce/widgets/ExecutePendencyWidget/TaskCostFinance/index.js"], "sourcesContent": ["import React from 'react';\nimport Utils from 'Utils';\n\nimport Button from 'reactor2/src/Atomic/components/Atoms/Button/Button';\nimport { SIZE_SMALL} from 'reactor2/src/constants/sizeConstants';\nimport Row from 'reactor2/src/Atomic/components/Layout/Row';\nimport Col from 'reactor2/src/Atomic/components/Layout/Col';\nimport TextView from 'reactor2/src/Atomic/components/Mols/TextView';\nimport { watchWindow } from 'reactorCmps/src/executependency/widgets/ExecutePendencyWidget/helpers/Utils';\nimport { getTitle } from '../helpers/Utils';\n\nexport { getTitle };\n\nexport const getExtraInfo = ({ record, getToken }) => {\n\tconst { idtipocusto, nmcusto } = record.toJS();\n\n\treturn (\n\t\t<Row nested>\n\t\t\t<Col xs={4} sm={4} md={4} lg={4}>\n\t\t\t\t<TextView title={getToken(111532)} value={idtipocusto} />\n\t\t\t</Col>\n\t\t\t<Col xs={4} sm={4} md={4} lg={4}>\n\t\t\t\t<TextView title={getToken(108963)} value={nmcusto} />\n\t\t\t</Col>\n\t\t</Row>\t\t\n\t);\n};\n\nconst onClickView = (props) => {\n\tconst { cdativi, typeatividade } = props.record.toJS();\n\n\tconst url = `${Utils.getBaseclassUrl()}/project/pr_project/project_data.php`,\n\t\tparams =  {\n\t\t\ttypeview: typeatividade,\n\t\t\tclass_name: 'nonprojectviewreadonly',\n\t\t\tcdtask: cdativi,\n\t\t\tfgtasktype: typeatividade\n\t\t};\n\n\tUtils.openStealthPopUp(url, params, null, null, true);\n};\n\nconst onClickExecute = (props) => {\n\tconst { cdativi, numerocost } = props.record.toJS();\n\tconst { fetchData } = props;\n\n\tconst url = `${Utils.getBaseclassUrl()}/exp/project/finance/cost_revenue_data.php`,\n\t\tparams  =  {\n\t\t\tcdTask: cdativi,\n\t\t\tnrCostRevenue: numerocost,\n\t\t\tpopup: 1,\n\t\t\texecution: 1,\n\t\t\tpendency: 1,\n\t\t\ttype: 'cost'\n\t\t};\n\n\twatchWindow(Utils.openStealthPopUp(url, params, 700, 600), fetchData);\n};\n\nexport const getButtonsToolbar = (props) => {\n\tconst { getToken } = props;\n\n\treturn (\n\t\t[\n\t\t\t<Button\n\t\t\t\tsize={SIZE_SMALL}\n\t\t\t\tkey={1}\n\t\t\t\tonClick={onClickView.bind(this, props)}\n\t\t\t\ticon={\"seicon-eye\"}\n\t\t\t\ttooltip={getToken(200633)}\n\t\t\t/>,\n\t\t\t<Button\n\t\t\t\tsize={SIZE_SMALL}\n\t\t\t\tkey={2}\n\t\t\t\tonClick={onClickExecute.bind(this, props)}\n\t\t\t\ticon={\"seicon-play\"}\n\t\t\t\ttooltip={getToken(102616)}\n\t\t\t/>\n\t\t]\n\t);\n};\n\nexport const sortObjects = () => {\n\treturn [\n\t\t{ field: 'nmidtask', token: 100528 },\n\t\t{ field: 'nmtask', token: 100111 },\n\t\t{ field: 'nmcusto', token: 108963 },\n\t\t{ field: 'idtipocusto', token: 111532 }\n\t];\n};\n\nexport const getPrimaryKey = () => 'cdativi';\n"], "names": ["React", "Utils", "<PERSON><PERSON>", "SIZE_SMALL", "Row", "Col", "TextView", "watchWindow", "getTitle", "getExtraInfo", "record", "getToken", "_record_toJS", "idtipocusto", "nmcusto", "onClickView", "props", "_props_record_toJS", "cdativi", "typeatividade", "url", "params", "onClickExecute", "numerocost", "fetchData", "getButtonsToolbar", "sortObjects", "getPrimaryKey"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2F6C;AAAA;;AA3FnB;AACA;AAE8C;AACP;AACL;AACA;AACQ;AACsC;AAC9D;AAExB;AAEb,IAAMS,eAAe;QAAGC,eAAAA,QAAQC,iBAAAA;IACtC,IAAiCC,eAAAA,OAAO,IAAI,IAApCC,cAAyBD,aAAzBC,aAAaC,UAAYF,aAAZE;IAErB,qBACC,2DAACV,kFAAGA;QAAC;qBACJ,2DAACC,kFAAGA;QAAC,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG,IAAI;qBAC7B,2DAACC,qFAAQA;QAAC,OAAOK,SAAS;QAAS,OAAOE;uBAE3C,2DAACR,kFAAGA;QAAC,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG,IAAI;qBAC7B,2DAACC,qFAAQA;QAAC,OAAOK,SAAS;QAAS,OAAOG;;AAI9C,EAAE;AAEF,IAAMC,cAAc,SAACC;IACpB,IAAmCC,qBAAAA,MAAM,MAAM,CAAC,IAAI,IAA5CC,UAA2BD,mBAA3BC,SAASC,gBAAkBF,mBAAlBE;IAEjB,IAAMC,MAAO,GAA0B,OAAxBnB,4DAAqB,IAAG,yCACtCoB,SAAU;QACT,UAAUF;QACV,YAAY;QACZ,QAAQD;QACR,YAAYC;IACb;IAEDlB,6DAAsB,CAACmB,KAAKC,QAAQ,MAAM,MAAM;AACjD;AAEA,IAAMC,iBAAiB,SAACN;IACvB,IAAgCC,qBAAAA,MAAM,MAAM,CAAC,IAAI,IAAzCC,UAAwBD,mBAAxBC,SAASK,aAAeN,mBAAfM;IACjB,IAAQC,YAAcR,MAAdQ;IAER,IAAMJ,MAAO,GAA0B,OAAxBnB,4DAAqB,IAAG,+CACtCoB,SAAW;QACV,QAAQH;QACR,eAAeK;QACf,OAAO;QACP,WAAW;QACX,UAAU;QACV,MAAM;IACP;IAEDhB,wHAAWA,CAACN,6DAAsB,CAACmB,KAAKC,QAAQ,KAAK,MAAMG;AAC5D;AAEO,IAAMC,oBAAoB,SAACT;IACjC,IAAQL,WAAaK,MAAbL;IAER,OACC;sBACC,2DAACT,2FAAMA;YACN,MAAMC,4EAAUA;YAChB,KAAK;YACL,SAASY,YAAY,IAAI,QAAOC;YAChC,MAAM;YACN,SAASL,SAAS;;sBAEnB,2DAACT,2FAAMA;YACN,MAAMC,4EAAUA;YAChB,KAAK;YACL,SAASmB,eAAe,IAAI,QAAON;YACnC,MAAM;YACN,SAASL,SAAS;;KAEnB;AAEH,EAAE;AAEK,IAAMe,cAAc;IAC1B,OAAO;QACN;YAAE,OAAO;YAAY,OAAO;QAAO;QACnC;YAAE,OAAO;YAAU,OAAO;QAAO;QACjC;YAAE,OAAO;YAAW,OAAO;QAAO;QAClC;YAAE,OAAO;YAAe,OAAO;QAAO;KACtC;AACF,EAAE;AAEK,IAAMC,gBAAgB;WAAM;EAAU"}