{"version": 3, "file": "document/DocumentList.js", "sources": ["webpack://watch1749037385376/../reactor/src/Form/components/Mols/InputGroup/InputGroup.css", "webpack://watch1749037385376/./src/document/components/DocumentList.css", "webpack://watch1749037385376/../reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeeedback.scss", "webpack://watch1749037385376/../reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeedback.jsx", "webpack://watch1749037385376/../reactor/src/Form/components/Mols/InputGroup/InputGroup.jsx", "webpack://watch1749037385376/./src/document/collection/DocumentListCategoriesCollection.js", "webpack://watch1749037385376/./src/document/collection/DocumentListCollection.js", "webpack://watch1749037385376/./src/document/components/DocumentList.jsx", "webpack://watch1749037385376/./src/document/components/DocumentListConfigurationModal.jsx", "webpack://watch1749037385376/./src/document/components/DocumentListRow.jsx", "webpack://watch1749037385376/../reactor/src/Form/components/Mols/InputGroup/InputGroup.css?b720", "webpack://watch1749037385376/./src/document/components/DocumentList.css?31b6", "webpack://watch1749037385376/../reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeeedback.scss?1008"], "sourcesContent": ["// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.rctInputGroup .rctValidationMessage {\n\tfloat: right;\n}`, \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.showOnHover:hover{\r\n\topacity: 0.5;\r\n}\r\n\r\n.categorySpan{\r\n\tcolor: rgba(0, 0, 0, 0.45);\r\n}\r\n\r\n.categoryItalic{\r\n\tfont-style: italic;\r\n}\r\n\r\n.greenLightText{\r\n\tcolor: #7ac2a3;\r\n}\r\n\r\n.updatedDate, .updatedUser {\r\n\tcolor: #37954d;\r\n}\r\n\r\n.TextStyle {\r\n\tpadding-top: 10px;\r\n\tpadding-bottom: 10px;\r\n}\r\n\r\n.overrideBorder {\r\n\tborder: 1px !important;\r\n}\r\n\r\n.row {\r\n\tborder: 1px !important;\r\n}`, \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "// Imports\nimport ___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___ from \"../../../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js\";\nimport ___CSS_LOADER_API_IMPORT___ from \"../../../../../../node_modules/css-loader/dist/runtime/api.js\";\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(___CSS_LOADER_API_NO_SOURCEMAP_IMPORT___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, `.rctModal .rctModalFeedback .modalAlertIcon {\n  min-width: 90px;\n}\n\n.rctModal .rctModalFeedback .mobileEmptyMessage {\n  word-break: break-word;\n}`, \"\"]);\n// Exports\nexport default ___CSS_LOADER_EXPORT___;\n", "var PropTypes = require('prop-types');\nvar React = require('react');\nvar createReactClass = require('create-react-class');\nvar Modal = require('reactor/src/Atomic/components/Orgs/Modal/Modal.jsx');\nvar FeedbackMsg = require('reactor/src/Atomic/components/Atoms/FeedbackMsg');\nvar Button = require('reactor/src/Atomic/components/Atoms/Button/Button');\nvar componentsThatShouldNotBeInLayoutCalc = require('reactor/src/helpers/componentsThatShouldNotBeInLayoutCalc');\nvar modalBtnBox = {\n\tmarginLeft: 104,\n\tpaddingTop: 4\n};\n\nvar modalBtnSpaces = {\n\tdisplay: 'inline',\n\tmarginRight: 5\n};\n\nvar modalCustomStyle = {\n\tbackgroundColor: \"#FFFFFF\",\n\tborderRadius: 0,\n\twidth: 400\n};\n\nvar buttonsType = {\n\tALERT: 'alert',\n\tCONFIRM: 'confirm'\n};\n\nrequire(\"./ModalFeeedback.scss\");\nrequire(\"reactorCmps/tokens/general\");\n\nmodule.exports = createReactClass({\n\tdisplayName: \"Atomic/components/Orgs/ModalFeedback\",\n\n\tpropTypes: {\n\t\t/**\n\t\t * Define se está visivel\n\t\t */\n\t\tshow: PropTypes.bool,\n\t\t/**\n\t\t * Define o tipo do modal a ser mostrado\n\t\t */\n\t\ttype: PropTypes.oneOf(['error', 'success', 'info', 'alert', 'idea']),\n\t\t/**\n\t\t * Título do modal\n\t\t */\n\t\ttitle: PropTypes.string,\n\t\t/**\n\t\t * Texto do corpo do modal\n\t\t */\n\t\tbody: PropTypes.string,\n\t\t/**\n\t\t * Botões que serão exibidos\n\t\t *\n\t\t * @customPropType(oneOf([buttonsType.ALERT, buttonsType.CONFIRM]), PropTypes.array)\n\t\t */\n\t\tbuttons: PropTypes.oneOfType([\n\t\t\tPropTypes.oneOf([buttonsType.ALERT, buttonsType.CONFIRM]),\n\t\t\tPropTypes.array\n\t\t]),\n\t\t/**\n\t\t * Callback do botão \"Ok\"\n\t\t */\n\t\tonOk: PropTypes.func,\n\t\t/**\n\t\t * Callback do botão \"Cancelar\"\n\t\t */\n\t\tonCancel: PropTypes.func,\n\t\t/**\n\t\t * Callback da ação de fechar a modal (clique fora e ESC)\n\t\t */\n\t\tonClose: PropTypes.func,\n\t\t/**\n\t\t * @private\n\t\t */\n\t\theight: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n\t},\n\n\tgetDefaultProps: function() {\n\t\treturn ({\n\t\t\ttype: \"info\",\n\t\t\theight: 210\n\t\t});\n\t},\n\n\tgetButtonsByType: function() {\n\t\tvar me = this, props = me.props;\n\n\t\tvar buttonOk = (<Button\n\t\t\tkey=\"btnOk\"\n\t\t\tclassName={\"rctModalAlertBtn-confirm\"}\n\t\t\tcolor=\"primary\"\n\t\t\ttext={SE.t(100094)}\n\t\t\tonClick={me.handleOk}\n\t\t/>);\n\n\t\tvar buttonCancel = (<Button\n\t\t\tkey=\"btnCancel\"\n\t\t\tclassName={\"rctModalAlertBtn-cancel\"}\n\t\t\tcolor=\"default\"\n\t\t\ttext={SE.t(100095)}\n\t\t\tonClick={me.handleCancel}\n\t\t/>);\n\n\t\tvar buttons = props.buttons;\n\n\t\tif (!props.buttons || props.buttons === buttonsType.ALERT) {\n\t\t\tbuttons = [buttonOk];\n\t\t}\n\n\t\tif (props.buttons === buttonsType.CONFIRM) {\n\t\t\tbuttons = [buttonOk, buttonCancel];\n\t\t}\n\n\t\treturn buttons.map(function(button) {\n\t\t\tvar key = 'container-' + button.key;\n\n\t\t\treturn (\n\t\t\t\t<div style={modalBtnSpaces} key={key}>\n\t\t\t\t\t{button}\n\t\t\t\t</div>);\n\t\t});\n\t},\n\n\tgetButtons: function() {\n\t\tvar me = this, buttons = me.getButtonsByType();\n\n\t\treturn (\n\t\t\t<div className=\"rctModalAlertButton\" style={modalBtnBox}>\n\t\t\t\t{buttons}\n\t\t\t</div>\n\t\t);\n\t},\n\n\tgetType: function(type) {\n\t\tvar feedBackType =\n\t\t\t{\n\t\t\t\terror: 0,\n\t\t\t\tsuccess: 1,\n\t\t\t\talert: 2,\n\t\t\t\tinfo: 3,\n\t\t\t\tidea: 8\n\t\t\t};\n\n\t\treturn feedBackType[type];\n\t},\n\n\thandleClose: function() {\n\t\tvar me = this, props = me.props;\n\n\t\tif (props.buttons === buttonsType.CONFIRM) {\n\t\t\tme.handleCancel();\n\t\t\treturn;\n\t\t}\n\n\t\tif (props.buttons === buttonsType.ALERT) {\n\t\t\tme.handleOk();\n\t\t\treturn;\n\t\t}\n\n\t\tif (props.onClose) {\n\t\t\tprops.onClose();\n\t\t}\n\t},\n\n\thandleOk: function() {\n\t\tvar me = this, props = me.props;\n\n\t\tif (props.onOk) {\n\t\t\tprops.onOk();\n\t\t}\n\n\t\tif (props.onClose) {\n\t\t\tprops.onClose();\n\t\t}\n\t},\n\n\thandleCancel: function() {\n\t\tvar me = this, props = me.props;\n\n\t\tif (props.onCancel) {\n\t\t\tprops.onCancel();\n\t\t}\n\n\t\tif (props.onClose) {\n\t\t\tprops.onClose();\n\t\t}\n\t},\n\n\trender: function() {\n\t\tvar me = this, props = me.props, body;\n\n\t\tbody = (\n\t\t\t<div>\n\t\t\t\t<FeedbackMsg\n\t\t\t\t\ttitle={props.title}\n\t\t\t\t\tcontent={props.body}\n\t\t\t\t\ttype={me.getType(props.type)}\n\t\t\t\t/>\n\t\t\t\t{me.getButtons()}\n\t\t\t</div>\n\t\t);\n\n\t\treturn (\n\t\t\t<Modal\n\t\t\t\t{...props}\n\t\t\t\thasBtnClose={false}\n\t\t\t\tstyle={modalCustomStyle}\n\t\t\t\tclassName=\"rctModalFeedback\"\n\t\t\t\tforceDesktop\n\t\t\t\ttitle={null}\n\t\t\t\tfooter={null}\n\t\t\t\theight={me.props.height}\n\t\t\t\tonClose={me.handleClose}\n\t\t\t\tonOk={me.handleOk}\n\t\t\t\tonCancel={me.handleCancel}\n\t\t\t\tBody={body}\n\t\t\t/>\n\t\t);\n\t}\n});\n\nmodule.exports.ALERT = buttonsType.ALERT;\nmodule.exports.CONFIRM = buttonsType.CONFIRM;\n\ncomponentsThatShouldNotBeInLayoutCalc.add(module.exports.displayName);", "var React = require(\"react\");\nvar createReactClass = require('create-react-class');\nvar InputGroup = require(\"react-bootstrap/lib/InputGroup\");\n\nvar style = {\n\tmarginBottom: \"15px\"\n};\n\nrequire(\"./InputGroup.css\");\n\nmodule.exports = createReactClass({\n\tdisplayName: \"Form/components/Mols/InputGroup/InputGroup\",\n\n\trender: function() {\n\t\treturn (\n\t\t\t<InputGroup {...this.props} style={style} className={\"rctInputGroup\"} />\n\t\t);\n\t}\n});", "var createCollection = require('SG2/collection/Factory');\r\n\r\nmodule.exports = createCollection({\r\n\tmodel: {\r\n\t\tprimaryKey: 'cdcategory',\r\n\t\t\r\n\t\tproxyCfg: {\r\n\t\t\troute: '/document/request/documentListCategories.php'\r\n\t\t}\r\n\t},\r\n\tcollection: {\r\n\t\tdefaultOrder: {\r\n\t\t\tnmcategory: 'ASC'\r\n\t\t}\r\n\t}\r\n});", "var createCollection = require('SG2/collection/Factory');\r\n\r\nmodule.exports = createCollection({\r\n\tmodel: {\r\n\t\tprimaryKey: 'cddocument',\r\n\t\t\r\n\t\tproxyCfg: {\r\n\t\t\troute: '/document/request/documentListData.php'\r\n\t\t}\r\n\t},\r\n\tcollection: {\r\n\t\tdefaultOrder: {\r\n\t\t\tiddocument: 'ASC'\r\n\t\t}\r\n\t}\r\n});", "var React = require('react');\nvar createReactClass = require('create-react-class');\n\n// O modal de configuração\nvar DocumentListConfigurationModal = require('./DocumentListConfigurationModal').default;\nvar tokenManagerHOC = require('reactor/src/Atomic/components/Helpers/Language/tokenManagerHOC');\n\n// Componentes da grid - easyGrid e cada Row da easyGrid\nvar easyGrid = require('reactor/src/FlexGrid/components/Helpers/EasyGrid');\nvar DocumentListRow = require('reactorCmps/src/document/components/DocumentListRow.jsx').default;\nvar DocumentListCollection = require('reactorCmps/src/document/collection/DocumentListCollection');\nvar FeedbackMsg = require('reactor/src/Atomic/components/Atoms/FeedbackMsg');\n\n// Componente de Busca acima do componente\nvar FormGroup = require('reactor/src/Form/components/Mols/FormGroup');\nvar InputAddon = require('reactor/src/Form/components/Atoms/InputAddon');\nvar InputGroup = require('reactor/src/Form/components/Mols/InputGroup/InputGroup');\nvar FormControl = require('reactor/src/Form/components/Atoms/FormControl/FormControl');\nvar keyConstants = require('reactor/src/helpers/keyConstants.js');\n\n// Úteis\nvar workspaceInfo = require('WorkspaceInfo');\nvar when = require('when');\nconst Connector = require('Connector');\n\nvar cursorPointer = {\n\tcursor: 'pointer'\n};\n\nvar DocumentList = createReactClass({\n\tdisplayName: 'DocumentList',\n\n\tgetDefaultProps() {\n\t\treturn {\n\t\t\tDocumentListCollection: new DocumentListCollection()\n\t\t};\n\t},\n\n\tgetInitialState: function() {\n\t\treturn ({\n\t\t\tloading: true,\n\t\t\tshowModal: false\n\t\t});\n\t},\n\n\tUNSAFE_componentWillMount() {\n\t\tvar me = this;\n\n\t\tvar DocumentListGrid = easyGrid({\n\t\t\tgridName: 'DocumentListGrid_' + me.props.oid,\n\t\t\tRow: DocumentListRow,\n\t\t\tprimaryKey: 'cddocument',\n\t\t\tselectType: 0,\n\t\t\tshowPagination: true,\n\t\t\tcollectionConfig: {\n\t\t\t\tinstance: me.props.DocumentListCollection,\n\t\t\t\tparams: { oidWidget: me.props.oid, cduser: workspaceInfo.getCDUser() }\n\t\t\t} \n\t\t});\n\t\t\n\t\tDocumentListGrid.fetchCollection({ oidWidget: me.props.oid, cduser: workspaceInfo.getCDUser() });\n\t\tme.DocumentListGrid = DocumentListGrid;\n\t},\n\n\tcomponentDidMount() {\n\t\tvar me = this;\n\t\tvar hasBeenConfigured;\n\t\tvar hasBeenConfiguredPromise = me.hasBeenConfigured();\n\n\t\thasBeenConfiguredPromise.then(function(value) {\n\t\t\thasBeenConfigured = (value === 'true');\n\n\t\t\t!me.isCancelled && me.setState({\n\t\t\t\tloading: false,\n\t\t\t\thasBeenConfigured\n\t\t\t});\n\t\t});\n\n\t},\n\n\tcomponentWillUnmount() {\n\t\tvar me = this;\n\n\t\tme.isCancelled = true;\n\t},\n\n\thasBeenConfigured: function() {\n\t\tvar deferred = when.defer();\n\t\tvar me = this;\n\n\t\tConnector.callBaseclass('document/request/documentListLoadedData.php', {\n\t\t\tdataType: 'json',\n\t\t\tdata: {\n\t\t\t\toid: me.props.oid,\n\t\t\t\twillCheckIfConfigured: true\n\t\t\t},\n\t\t\tcomplete: function(response) {\n\t\t\t\tdeferred.resolve(response);\n\t\t\t}\n\t\t});\n\n\t\treturn deferred.promise;\n\t},\n\n\tupdateGrid(searchTerm) {\n\t\tvar me = this;\n\n\t\tme.DocumentListGrid.fetchCollection({ oidWidget: me.props.oid, searchTerm, cduser: workspaceInfo.getCDUser() });\n\t},\n\n\tupdateGridWithoutSearch() {\n\t\tvar me = this;\n\n\t\tme.DocumentListGrid.fetchCollection({ oidWidget: me.props.oid, cduser: workspaceInfo.getCDUser() });\n\t},\n\n\tsetConfigurationState() {\n\t\tvar me = this;\n\n\t\tme.setState({ hasBeenConfigured: true });\n\t},\n\n\topenModal: function() {\n\t\tvar me = this;\n\n\t\tme.refs['DocumentListConfigurationModal' + me.props.oid].setModalState();\n\t\tme.setState({ showModal: true });\n\t},\n\n\thideModal: function() {\n\t\tvar me = this;\n\t\t\n\t\tme.setState({ showModal: false });\n\t},\n\n\tupdateDocumentListGrid(e) {\n\t\tvar me = this;\n\t\tvar isMouseClick = false;\n\t\tvar searchTerm = '';\n\n\t\t/* istanbul ignore next */\n\t\tif ('buttons' in e.nativeEvent && e.nativeEvent.which === keyConstants.MOUSE_LEFT_BUTTON) {\n\t\t\tisMouseClick = true;\n\t\t\tsearchTerm = this.searchInput.value;\n\t\t} else if (e.target) {\n\t\t\tsearchTerm = e.target.value;\n\t\t}\n\n\t\t/* istanbul ignore if */\n\t\tif (isMouseClick || e.nativeEvent.keyCode === keyConstants.ENTER_KEY) {\t\n\t\t\tme.updateGrid(searchTerm);\n\t\t}\n\t},\n\n\tgetDocumentListWidth: function() {\n\t\tvar me = this;\n\n\t\treturn me.refs['DocumentList' + me.props.oid].offsetWidth;\n\t},\n\n\tgetSearchFilter: function() {\n\t\tvar me = this;\n\n\t\treturn (\n\t\t\t<div>\n\t\t\t\t<FormGroup>\n\t\t\t\t\t<InputGroup>\n\t\t\t\t\t\t<InputAddon \n\t\t\t\t\t\t\tid={'InputAddon_' + me.props.oid}\n\t\t\t\t\t\t\tonClick={me.updateDocumentListGrid}\n\t\t\t\t\t\t\tstyle={cursorPointer}\n\t\t\t\t\t\t\tclassName={'seicon-search'}\n\t\t\t\t\t\t\ttitle={me.props.getToken(214653)}>\n\t\t\t\t\t\t</InputAddon>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<FormControl\n\t\t\t\t\t\t\t\tinputRef={ref => { me.searchInput = ref; }}\n\t\t\t\t\t\t\t\tonKeyPress={me.updateDocumentListGrid}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</InputGroup>\n\t\t\t\t</FormGroup>\n\t\t\t</div>\n\t\t);\n\t},\n\n\tgetGrid: function() {\n\t\tlet me = this;\n\t\tlet msgToken = me.props.getToken(306906).split('\\\\\"').join('\"');\n\n\t\treturn (\n\t\t\t<FeedbackMsg\n\t\t\t\ttitle={msgToken} \n\t\t\t\ticonColor={'#ACACAC'}\n\t\t\t\ttextBaseColor={'gray'}\n\t\t\t\tcenter\n\t\t\t\ttype={2}\n\t\t\t/>\n\t\t);\n\t},\n\n\trender: function() {\n\t\tvar me = this;\n\n\t\treturn (\n\t\t\t<div id={'DocumentList' + me.props.oid} ref={'DocumentList' + me.props.oid}>\n\t\t\t\t<DocumentListConfigurationModal updateGridWithoutSearch={me.updateGridWithoutSearch}\n\t\t\t\t\tsetConfigurationState={me.setConfigurationState} \n\t\t\t\t\tref={'DocumentListConfigurationModal' + me.props.oid} \n\t\t\t\t\tshowModal={me.state.showModal} \n\t\t\t\t\topenModal={me.openModal}\n\t\t\t\t\thideModal={me.hideModal}\n\t\t\t\t\t{...me.props}/>\n\t\t\t\t{me.getGrid()}\n\t\t\t</div>\n\t\t);\n\t}\n});\n\nmodule.exports = tokenManagerHOC(DocumentList);\nmodule.exports.PureDocumentList = DocumentList;\n\nrequire('reactorCmps/tokens/general');", "import React from 'react';\r\nimport createReactClass from 'create-react-class';\r\nimport Modal from 'reactor/src/Atomic/components/Orgs/Modal/Modal';\r\nimport Text from 'reactor/src/Form/components/Mols/Text';\r\nimport Button from 'reactor/src/Atomic/components/Atoms/Button/Button.jsx';\r\nimport ButtonRadio from 'reactor/src/Atomic/components/Atoms/ButtonRadio';\r\nimport ButtonRadioGroup from 'reactor/src/Atomic/components/Mols/ButtonRadioGroup';\r\nimport Lookup from 'reactor/src/Form/components/Mols/Lookup';\r\nimport DocumentListCategoriesCollection from 'reactorCmps/src/document/collection/DocumentListCategoriesCollection';\r\nimport Label from 'reactor/src/Atomic/components/Atoms/Label';\r\nimport FormGroup from 'reactor/src/Form/components/Mols/FormGroup';\r\nimport FormControl from 'reactor/src/Form/components/Atoms/FormControl/FormControl';\r\nimport ReactDOM from 'react-dom';\r\n\r\n// Arquivos auxiliares, do workspace\r\nimport when from 'when';\r\n\r\nimport workspaceInfo from 'WorkspaceInfo';\r\n\r\n//\tConector para as requisições ajax\r\nimport Connector from 'Connector';\r\n\r\n// Layout\r\nimport Row from 'reactor/src/Atomic/components/Layout/Row';\r\n\r\nimport Col from 'reactor/src/Atomic/components/Layout/Col';\r\n\r\nimport './DocumentList.css';\r\n\r\n// Collection das categorias apresentadas no Lookup\r\nvar CategoryCollection = new DocumentListCategoriesCollection();\r\n\r\nvar rowStyle = {\r\n\tpaddingBottom: '10px',\r\n\tpaddingTop: '2px'\r\n};\r\n\r\nvar divStyle = {\r\n\tdisplay: 'block',\r\n\tpaddingTop: '14px',\r\n\tfontSize: '11px',\r\n\tlineHeight: '11px',\r\n\tfontWeight: 400,\r\n\ttextAlign: 'left',\r\n\tfontFamily: 'Arial, sans-serif',\r\n\tcolor: 'rgba(0, 0, 0, 0.6)'\r\n};\r\n\r\nexport default createReactClass({\r\n\tdisplayName: 'DocumentList',\r\n\r\n\tgetInitialState: function() {\r\n\t\tvar categories = null;\r\n\t\tvar me = this;\r\n\t\tvar savedSearchesData = [];\r\n\t\tvar loadedCategories = [];\r\n\t\tvar titleState = '';\r\n\r\n\t\tme.isCancelled = false; \r\n\r\n\t\treturn ({\r\n\t\t\tselected: 'category',\r\n\t\t\tcategoryLookupValue: loadedCategories,\r\n\t\t\tsavedSearchesData,\r\n\t\t\tmodalHeight: 570,\r\n\t\t\tcategories,\r\n\t\t\tlookupValidationState: null,\r\n\t\t\ttitleValidationState: null,\r\n\t\t\ttitle: titleState,\r\n\t\t\tloading: true\r\n\t\t});\r\n\t},\r\n\r\n\tcomponentWillUnmount() {\r\n\t\tvar me = this;\r\n\r\n\t\tme.isCancelled = true;\r\n\t},\r\n\r\n\tsetModalState: function() {\r\n\t\tvar categories = null;\r\n\t\tvar me = this;\r\n\t\tvar allCategoriesPromise = me.getCategories();\r\n\t\tvar loadedDataPromise = me.getLoadedData();\r\n\t\tvar savedSearchesPromise = me.getSavedSearches();\r\n\r\n\t\tvar savedSearchesData = [];\r\n\t\tvar loadedCategories = [];\r\n\t\tvar loadedSavedSearch = [];\r\n\t\tvar titleState = '';\r\n\r\n\t\tme.isCancelled = false; \r\n\t\t\t\r\n\t\tallCategoriesPromise.then(function(value) {\r\n\t\t\tcategories = value.map(function(obj) {\r\n\t\t\t\treturn Object.keys(obj).sort().map(function(key) { \r\n\t\t\t\t\treturn obj[key];\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t});\r\n\r\n\t\tloadedDataPromise.then(function(value) {\r\n\t\t\tvar counter;\r\n\t\t\tvar fgtype = (value[0].hasOwnProperty('notConfigured')) ? -1 : parseInt(value[0].fgtype, 10);\r\n\r\n\t\t\tif (value[0].notConfigured) {\r\n\t\t\t\t!me.isCancelled && me.setState({\r\n\t\t\t\t\tloadedSavedSearch,\r\n\t\t\t\t\tcategoryLookupValue: loadedCategories,\r\n\t\t\t\t\ttitle: titleState\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t// Category / Categoria\r\n\t\t\tif (fgtype === 1) {\r\n\t\t\t\tfor (counter = 0; counter < value.length; counter++) {\r\n\t\t\t\t\tloadedCategories.push({ oid: value[counter].cdcategory, \r\n\t\t\t\t\t\tcdcategory: value[counter].cdcategory,\r\n\t\t\t\t\t\tcattitle: value[counter].cattitle });\r\n\t\t\t\t\ttitleState = value[counter].nmwidget;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Consulta Salva / Saved Search\r\n\t\t\tif (fgtype === 2) {\r\n\t\t\t\tloadedSavedSearch = value[0].oidsavedsearch;\r\n\t\t\t\ttitleState = value[0].nmwidget;\r\n\t\t\t}\r\n\r\n\t\t\t!me.isCancelled && me.setState({\r\n\t\t\t\tloadedSavedSearch,\r\n\t\t\t\tcategoryLookupValue: loadedCategories,\r\n\t\t\t\ttitle: titleState,\r\n\t\t\t\tfgtype,\r\n\t\t\t\tselected: (fgtype === 2) ? 'savedSearch' : 'category',\r\n\t\t\t\tmodalHeight: (fgtype === 2) ? 350 : 570\r\n\t\t\t});\r\n\t\t});\r\n\r\n\t\tsavedSearchesPromise.then(function(value) {\r\n\t\t\tvar counter;\r\n\r\n\t\t\tfor (counter = 0; counter < value.length; counter++) {\r\n\t\t\t\tsavedSearchesData.push({ oidconsult: value[counter].oid,\r\n\t\t\t\t\tnmconsult: value[counter].nmconsult });\r\n\t\t\t}\r\n\r\n\t\t\t!me.isCancelled && me.setState({\r\n\t\t\t\tsavedSearchesData\r\n\t\t\t});\r\n\t\t});\r\n\r\n\t\tme.setState({\r\n\t\t\tselected: 'category',\r\n\t\t\tcategoryLookupValue: loadedCategories,\r\n\t\t\tsavedSearchesData,\r\n\t\t\tmodalHeight: 570,\r\n\t\t\tcategories,\r\n\t\t\tlookupValidationState: null,\r\n\t\t\ttitleValidationState: null,\r\n\t\t\ttitle: titleState\r\n\t\t});\r\n\t},\r\n\r\n\tgetLoadedData: function() {\r\n\t\tvar deferred = when.defer();\r\n\t\tvar me = this;\r\n\r\n\t\tConnector.callBaseclass('document/request/documentListLoadedData.php', {\r\n\t\t\tdataType: 'json',\r\n\t\t\tdata: {\r\n\t\t\t\toid: me.props.oid,\r\n\t\t\t\twillGetLoadedData: true\r\n\t\t\t},\r\n\t\t\tcomplete: function(response) {\r\n\t\t\t\tdeferred.resolve(response);\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn deferred.promise;\r\n\t},\r\n\r\n\tgetSavedSearches: function() {\r\n\t\tvar deferred = when.defer();\r\n\r\n\t\tConnector.callBaseclass('document/request/documentListSavedSearches.php', {\r\n\t\t\tdataType: 'json',\r\n\t\t\tdata: {\r\n\t\t\t\tcduser: workspaceInfo.getCDUser(),\r\n\t\t\t\twillGetSavedSearches: true\r\n\t\t\t},\r\n\t\t\tcomplete: function(response) {\r\n\t\t\t\tdeferred.resolve(response);\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\treturn deferred.promise;\r\n\t},\r\n\r\n\tgetCategories: function() {\r\n\t\tvar deferred = when.defer();\r\n\r\n\t\tCategoryCollection.fetch({ cduser: workspaceInfo.getCDUser() }).then(\r\n\t\t\tfunction(results) {\r\n\t\t\t\tdeferred.resolve(results);\r\n\t\t\t},\r\n\t\t\tdeferred.reject\r\n\t\t);\r\n\r\n\t\treturn deferred.promise;\r\n\t},\r\n\r\n\tgetSavedSearchesOptions: function() {\r\n\t\tvar me = this;\r\n\t\tvar savedSearches = me.state.savedSearchesData;\r\n\t\tvar savedSearchesOptions = [];\r\n\r\n\t\tsavedSearches.map(function(value, index) {\r\n\t\t\tsavedSearchesOptions.push(<option style={{ maxWidth: '100%' }} key={index} value={value.oidconsult}>{value.nmconsult}</option>);\r\n\t\t});\r\n\r\n\t\treturn savedSearchesOptions;\r\n\t},\r\n\r\n\thandleChange: function(i, selected) {\r\n\t\tvar me = this;\r\n\t\tvar newModalHeight;\r\n\r\n\t\tif (selected === 'category') {\r\n\t\t\tnewModalHeight = 570;\r\n\t\t} else {\r\n\t\t\tnewModalHeight = 350;\r\n\t\t}\r\n\r\n\t\tme.setState({ selected,\r\n\t\t\tmodalHeight: newModalHeight });\r\n\t},\r\n\r\n\tonCategoryLookupValueChange: function(value) {\r\n\t\tvar me = this;\r\n\r\n\t\tme.setState({ categoryLookupValue: value,\r\n\t\t\tlookupValidationState: null\r\n\t\t});\r\n\t\t\r\n\t\treturn value;\r\n\t},\r\n\r\n\tonSavedSearchSelection: function(value) {\r\n\t\tvar me = this;\r\n\t\tvar select = ReactDOM.findDOMNode(me.refs.savedSearch);\r\n\r\n\t\tme.setState({ chosenSavedSearch: select.value,\r\n\t\t\tlookupValidationState: null\r\n\t\t});\r\n\t\t\r\n\t\treturn value;\r\n\t},\r\n\r\n\tsearchCategories: function(term, page) {\r\n\t\tvar deferred = when.defer();\r\n\r\n\t\tCategoryCollection.search({ searchTerm: term, cduser: workspaceInfo.getCDUser() }, page).then(\r\n\t\t\tfunction() {\r\n\t\t\t\tdeferred.resolve({\r\n\t\t\t\t\tdata: CategoryCollection.getAttributes(),\r\n\t\t\t\t\ttotal: CategoryCollection.getState().size\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tdeferred.reject\r\n\t\t);\r\n\r\n\t\treturn deferred.promise;\r\n\t},\r\n\r\n\tgetChoice: function() {\r\n\t\tvar me = this;\r\n\t\tvar savedSearchValue;\r\n\r\n\t\tif (me.state.loadedSavedSearch && (!me.state.chosenSavedSearch)) {\r\n\t\t\tsavedSearchValue = me.state.loadedSavedSearch ? me.state.loadedSavedSearch : '';\r\n\t\t} else {\r\n\t\t\tsavedSearchValue = me.state.chosenSavedSearch ? me.state.chosenSavedSearch : '';\r\n\t\t}\r\n\r\n\t\tif (me.state.selected === 'category') {\r\n\t\t\treturn (<div>\r\n\t\t\t\t<Lookup\r\n\t\t\t\t\tid={'Lookup' + me.props.oid}\r\n\t\t\t\t\tref={'categoryLookup'}\r\n\t\t\t\t\trequired \r\n\t\t\t\t\trequiredIndicator \r\n\t\t\t\t\tvalidationState={me.state.lookupValidationState}\r\n\t\t\t\t\ttitle={SE.t(105287)}\r\n\t\t\t\t\tdata={me.state.categories}\r\n\t\t\t\t\ttextField={'cattitle'}\r\n\t\t\t\t\tvalueField={'cdcategory'}\r\n\t\t\t\t\tsearchFn={me.searchCategories}\r\n\t\t\t\t\tvalue={me.state.categoryLookupValue}\r\n\t\t\t\t\tonChange={value => me.onCategoryLookupValueChange(value)}>\r\n\t\t\t\t</Lookup>\r\n\t\t\t</div>);\t\r\n\t\t} \r\n\r\n\t\treturn (<div>\r\n\t\t\t<div className={'labelContainer'}>\r\n\t\t\t\t<Label text={SE.t(300586)} required requiredIndicator/>\r\n\t\t\t</div>\r\n\t\t\t<div style={{ maxWidth: '100%', width: '100%' }}>\r\n\t\t\t\t<FormGroup style={{ maxWidth: '100%', width: '100%' }}>\r\n\t\t\t\t\t<FormControl id={'savedSearchForm' + me.props.oid}\r\n\t\t\t\t\t\tref={'savedSearch'}\r\n\t\t\t\t\t\tvalue={(savedSearchValue.length > 0) ? savedSearchValue : 0}\r\n\t\t\t\t\t\tcomponentClass={'select'} \r\n\t\t\t\t\t\tplaceholder={SE.t(300586)}\r\n\t\t\t\t\t\tonChange={value => me.onSavedSearchSelection(value)} \r\n\t\t\t\t\t\tstyle={{ maxWidth: '100%', width: '100%' }}>\r\n\t\t\t\t\t\t{me.getSavedSearchesOptions()}\r\n\t\t\t\t\t</FormControl>\r\n\t\t\t\t</FormGroup>\r\n\t\t\t</div>\r\n\t\t</div>);\r\n\t},\r\n\r\n\tsaveConfigurationToDatabase: function(selected) {\r\n\t\tvar me = this;\r\n\t\tvar title = me.refs.TitleInput.props.value;\r\n\t\tvar select = ReactDOM.findDOMNode(me.refs.savedSearch);\r\n\t\tvar data = [];\r\n\r\n\t\tif (selected === 'category') {\r\n\t\t\tdata = {\r\n\t\t\t\tcategoriesCodes: me.refs.categoryLookup.props.value.map(function(index) { return index.cdcategory; }),\r\n\t\t\t\taddCategory: true,\r\n\t\t\t\toidWidget: me.props.oid\r\n\t\t\t};\r\n\t\t} else {\r\n\t\t\tdata = {\r\n\t\t\t\taddSavedSearch: true,\r\n\t\t\t\toidWidget: me.props.oid,\r\n\t\t\t\toidSavedSearch: select.value\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\tConnector.callBaseclass('document/request/ajaxCrudDocumentList.php', {\r\n\t\t\tdata,\r\n\t\t\tcomplete: function() {\r\n\t\t\t\tme.afterSaved(title);\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\r\n\tafterSaved: function(title) {\r\n\t\tvar me = this;\r\n\r\n\t\tcurl('MessageBar', function(MessageBar) {\r\n\t\t\tMessageBar.showMessageSuccess(SE.t(114547), {\r\n\t\t\t\ttimeOut: 3000\r\n\t\t\t});\r\n\t\t});\r\n\r\n\t\tme.props.setTitle(title);\r\n\t\tme.props.serverSave();\r\n\t\tme.props.updateGridWithoutSearch();\r\n\t\tme.props.setConfigurationState();\r\n\t},\r\n\r\n\tsaveConfiguration: function() {\r\n\t\tvar me = this;\r\n\r\n\t\tif (!me.requiredFieldsAreFilled()) {\r\n\t\t\tcurl('MessageBar', function(MessageBar) {\r\n\t\t\t\tMessageBar.showMessageError(SE.t(113870), {\r\n\t\t\t\t\ttimeOut: 3000\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t\treturn;\r\n\t\t} \r\n\r\n\t\tme.saveConfigurationToDatabase(me.state.selected);\r\n\r\n\t\tme.props.hideModal();\r\n\t},\r\n\r\n\trequiredFieldsAreFilled: function() {\r\n\t\tvar me = this;\r\n\t\tvar titleValidationState = null;\r\n\t\tvar lookupValidationState = null;\r\n\t\tvar requiredFieldsAreFilled = true;\r\n\r\n\t\tif (!me.refs.TitleInput.props.value) {\r\n\t\t\ttitleValidationState = 'error';\r\n\t\t\trequiredFieldsAreFilled = false;\r\n\t\t}\r\n\r\n\t\tif (me.state.selected === 'category' && (!(me.refs.categoryLookup.props.value) || (me.refs.categoryLookup.props.value.length === 0))) {\r\n\t\t\tlookupValidationState = 'error';\r\n\t\t\trequiredFieldsAreFilled = false;\r\n\t\t}\r\n\r\n\t\tme.setState({ titleValidationState,\r\n\t\t\tlookupValidationState\r\n\t\t});\r\n\r\n\t\treturn requiredFieldsAreFilled;\r\n\t},\r\n\r\n\tonTitleChange: function(newTitle) {\r\n\t\tvar me = this;\r\n\r\n\t\tme.setState({ title: newTitle,\r\n\t\t\ttitleValidationState: null\r\n\t\t});\r\n\t},\r\n\r\n\tgetBody: function() {\r\n\t\tvar me = this;\r\n\t\tvar title = String(SE.t(301062)).toUpperCase();\r\n\r\n\t\treturn (<div id={'ModalBodyDiv' + me.props.oid}>\r\n\t\t\t<Text\r\n\t\t\t\tclassName={'TextStyle'}\r\n\t\t\t\tref={'TitleInput'}\r\n\t\t\t\ttitle={SE.t(100380)}\r\n\t\t\t\tvalue={me.state.title}\r\n\t\t\t\trequired\r\n\t\t\t\trequiredIndicator\r\n\t\t\t\tcleanAll\r\n\t\t\t\tvalidationState={me.state.titleValidationState}\r\n\t\t\t\tonChange={me.onTitleChange}/>\r\n\t\t\t<div style={divStyle}>{title}</div>\r\n\t\t\t<Row style={rowStyle}\r\n\t\t\t\tclassName={'RowStyle'}>\r\n\t\t\t\t<Col xs={8} sm={8} md={8} lg={8}>\r\n\t\t\t\t\t<ButtonRadioGroup\r\n\t\t\t\t\t\tclassName={'ButRadioGroup'}\r\n\t\t\t\t\t\tvalue={me.state.selected}\r\n\t\t\t\t\t\tonChange={me.handleChange.bind(me, 1)}>\r\n\t\t\t\t\t\t<ButtonRadio id={'category'}>\r\n\t\t\t\t\t\t\t{SE.t(102041)}\r\n\t\t\t\t\t\t</ButtonRadio>\r\n\t\t\t\t\t\t<ButtonRadio id={'savedSearch'}>\r\n\t\t\t\t\t\t\t{SE.t(300586)}\r\n\t\t\t\t\t\t</ButtonRadio>\r\n\t\t\t\t\t</ButtonRadioGroup>\r\n\t\t\t\t</Col>\r\n\t\t\t\t<Col xs={6} sm={6} md={6} lg={6}>\r\n\t\t\t\t</Col>\r\n\t\t\t</Row>\r\n\t\t\t<span>\r\n\t\t\t</span>\r\n\t\t\t{me.getChoice()}\r\n\t\t</div>);\r\n\t},\r\n\r\n\trender: function() {\r\n\t\tvar me = this;\r\n\t\t\r\n\t\treturn (\r\n\t\t\t<Modal \r\n\t\t\t\tkey={'Modal' + me.props.oid}\r\n\t\t\t\twidth={560}\r\n\t\t\t\theight={me.state.modalHeight}\r\n\t\t\t\tshow={me.props.showModal}\r\n\t\t\t\tonClose={me.props.hideModal}\r\n\t\t\t\tref={'SelectionModal' + me.props.oid}\r\n\t\t\t\thasCloseBtn\r\n\t\t\t\ttitle={SE.t(301060)}\r\n\t\t\t\tBody={me.getBody()}\r\n\t\t\t\tFooter={<Button onClick={me.saveConfiguration.bind(me, null)} tooltip={SE.t(100026)} id={'close'} text={SE.t(100026)}/>}\r\n\t\t\t/>\r\n\t\t);\r\n\t}\r\n});", "import React from 'react';\r\nimport createReactClass from 'create-react-class';\r\n\r\n// Componentes de layout\r\nimport Row from 'reactor/src/FlexGrid/components/Layout/Row/RowPresentation.jsx';\r\n\r\nimport Col from 'reactor/src/FlexGrid/components/Layout/Col';\r\nimport Image from 'reactor/src/Atomic/components/Atoms/Image/Image';\r\nimport ToolBarMenu from 'reactor/src/FlexGrid/components/Mols/ToolbarMenu/ToolBarMenu';\r\nimport Button from 'reactor/src/Atomic/components/Atoms/Button/Button';\r\nimport Link from 'reactor/src/Atomic/components/Atoms/Link/Link';\r\nimport DateFormatter from 'Formatters/Date';\r\nimport OverlayTrigger from 'reactor/src/Atomic/components/Orgs/OverlayTrigger';\r\nimport Tooltip from 'reactor/src/Atomic/components/Atoms/Tooltip';\r\nimport ModalFeedback from 'reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeedback';\r\nimport Utils from 'Utils';\r\nimport workspaceInfo from 'WorkspaceInfo';\r\nimport Connector from 'Connector';\r\n\r\nimport './DocumentList.css';\r\n\r\nvar baseURL = Utils.getSystemUrl();\r\n\r\nexport default createReactClass({\r\n\r\n\tgetInitialState: function() {\r\n\t\tvar me = this;\r\n\r\n\t\tvar documentListWidth = me.props.getDocumentListWidth();\r\n\r\n\t\treturn me.getDynamicComponents(documentListWidth);\r\n\t},\r\n\r\n\tcomponentWillUnmount: function() {\r\n\t\tvar element;\r\n\r\n\t\tif (document.getElementById('jnlpiframe') !== null) {\r\n\t\t\telement = document.getElementById('jnlpiframe');\r\n\t\t\telement.parentNode.removeChild(element);\r\n\t\t}\r\n\t},\r\n\r\n\tresizeRow: function() {\r\n\t\tvar me = this;\r\n\r\n\t\tme.setState(me.getDynamicComponents(me.props.getDocumentListWidth()));\r\n\t},\r\n\r\n\tgetDynamicComponents: function(widgetWidth) {\r\n\t\tvar dynamicLayout;\r\n\r\n\t\tdynamicLayout = {\r\n\t\t\ttoolbarButtonSize: 'medium',\r\n\t\t\ticonWidth: 1,\r\n\t\t\ttextWidth: 7,\r\n\t\t\ticonSize: 'products55x55',\r\n\t\t\ttitleMaxLength: 33,\r\n\t\t\ttitleFontSize: 16,\r\n\t\t\tsecondaryFontSize: 12,\r\n\t\t\trowLeftMargin: 14,\r\n\t\t\tbuttonsIn: false\r\n\t\t};\r\n\r\n\t\tif (widgetWidth < 400 ) {\r\n\t\t\tdynamicLayout = {\r\n\t\t\t\ttoolbarButtonSize: 'small',\r\n\t\t\t\ticonWidth: 2,\r\n\t\t\t\ttextWidth: 6,\r\n\t\t\t\ticonSize: 'products32x32',\r\n\t\t\t\ttitleMaxLength: 14,\r\n\t\t\t\ttitleFontSize: 14,\r\n\t\t\t\tsecondaryFontSize: 10,\r\n\t\t\t\trowLeftMargin: 0,\r\n\t\t\t\tbuttonsIn: true\r\n\t\t\t};\r\n\t\t} else if (widgetWidth < 600) {\r\n\t\t\tdynamicLayout = {\r\n\t\t\t\ttoolbarButtonSize: 'small',\r\n\t\t\t\ticonWidth: 2,\r\n\t\t\t\ttextWidth: 8,\r\n\t\t\t\ticonSize: 'products32x32',\r\n\t\t\t\ttitleMaxLength: 21,\r\n\t\t\t\ttitleFontSize: 15,\r\n\t\t\t\tsecondaryFontSize: 11,\r\n\t\t\t\trowLeftMargin: -10,\r\n\t\t\t\tbuttonsIn: true\r\n\t\t\t};\r\n\t\t} else if (widgetWidth > 1200) {\r\n\t\t\tdynamicLayout = {\r\n\t\t\t\ttoolbarButtonSize: 'medium',\r\n\t\t\t\ticonWidth: 1,\r\n\t\t\t\ttextWidth: 10,\r\n\t\t\t\ticonSize: 'products55x55',\r\n\t\t\t\ttitleMaxLength: 116,\r\n\t\t\t\ttitleFontSize: 17,\r\n\t\t\t\tsecondaryFontSize: 13,\r\n\t\t\t\trowLeftMargin: -30,\r\n\t\t\t\tbuttonsIn: false\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\tdynamicLayout.showFeedbackModal = false;\r\n\r\n\t\treturn dynamicLayout;\r\n\t},\r\n\r\n\tviewDocumentData: function() {\r\n\t\tvar me = this;\r\n\t\tvar record = me.props.record.toJS();\r\n\t\tvar params = { cddocument: record.cddocument, action: 4 };\r\n\r\n\t\tUtils.openStealthPopUp(baseURL + '/document/dc_document/document_ribbon.php', params, null, null, true);\r\n\t},\r\n\r\n\tjnlpIframeExists: function() {\r\n\t\treturn window.top.document.getElementById('jnlpiframe');\r\n\t},\r\n\r\n\tcreateJnlpIframe: function() {\r\n\t\tvar jnlpIframe = window.top.document.createElement('iframe');\r\n\r\n\t\tjnlpIframe.id = 'jnlpiframe';\r\n\t\tjnlpIframe.name = 'jnlpiframe';\r\n\t\tjnlpIframe.style.display = 'none';\r\n\t\twindow.top.document.getElementsByTagName('body')[0].appendChild(jnlpIframe);\r\n\t},\r\n\r\n\tcheckFgStatus: function() {\r\n\t\tvar me = this;\r\n\t\tvar record = me.props.record.toJS();\r\n\r\n\t\tswitch (parseInt(record.fgstatusdoc, 10)) {\r\n\t\t\tcase 1:\r\n\t\t\t\tcurl('MessageBar', function(MessageBar) {\r\n\t\t\t\t\tMessageBar.showMessageError(SE.t(104865) + ' ' + SE.t(110816), {\r\n\t\t\t\t\t\ttimeOut: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\tcase 4:\r\n\t\t\t\tme.openFeedbackModal();\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tme.viewElectronicFile();\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t},\r\n\r\n\tviewElectronicFile: function() {\r\n\t\tvar me = this;\r\n\t\tvar record = me.props.record.toJS();\r\n\r\n\t\tif (!me.jnlpIframeExists())\r\n\t\t\tme.createJnlpIframe();\r\n\r\n\t\tif (record.filecount === 1) {\r\n\t\t\twindow.open(baseURL +\r\n\t\t\t\t\t\t'/generic/gn_eletronicfile_view/1.1/view_eletronic_file.php?'\r\n\t\t\t\t\t\t+ 'resource=document'\r\n\t\t\t\t\t\t+ '&cdisosystem=21&action=4&cdfile='\r\n\t\t\t\t\t\t+ record.cdfile + '&cddocument='\r\n\t\t\t\t\t\t+ record.cddocument + '&view=1&permission=2', 'jnlpiframe');\r\n\t\t} else {\r\n\t\t\twindow.open(baseURL +\r\n\t\t\t\t\t\t'/generic/gn_eletronicfile_view/1.1/view_eletronic_file.php?'\r\n\t\t\t\t\t\t+ 'resource=document'\r\n\t\t\t\t\t\t+ '&cdisosystem=21&action=4&cddocument=' + record.cddocument + '&view=1&permission=2', 'jnlpiframe');\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t},\r\n\r\n\tdownloadElectronicFiles: function() {\r\n\t\tvar me = this;\r\n\t\tvar record = me.props.record.toJS();\r\n\t\tvar data = '';\r\n\r\n\t\tif (!me.jnlpIframeExists())\r\n\t\t\tme.createJnlpIframe();\r\n\r\n\t\tConnector.callBaseclass('document/request/ajaxOpenGnScreen.php?getDownloadParam=1', {\r\n\t\t\tdata,\r\n\t\t\tcomplete: function(response) {\r\n\t\t\t\twindow.open(baseURL +\r\n\t\t\t\t\t\t\t'/generic/gn_eletronicfile_view/1.1/view_eletronic_download.php?'\r\n\t\t\t\t\t\t\t+ 'resource=document'\r\n\t\t\t\t\t\t\t+ '&action=4&cdisosystem=21&cddocument=' + record.cddocument\r\n\t\t\t\t\t\t\t+ '&saveas=1&mainframe=1&cdrevision=' + record.cdrevision + response.responseText\r\n\t\t\t\t\t\t\t+ '&cduser=' + workspaceInfo.getCDUser(), 'jnlpiframe');\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\r\n\tgetToolbar: function() {\r\n\t\tvar me = this;\r\n\t\tlet renderedRow;\r\n\r\n\t\tif (me.state.buttonsIn) {\r\n\t\t\trenderedRow = (<ToolBarMenu ref={'ToolBarMenu' + me.props.oid}\r\n\t\t\t\titemsIn={me.getToolbarItems()}\r\n\t\t\t/>);\r\n\t\t} else {\r\n\t\t\trenderedRow = (<ToolBarMenu ref={'ToolBarMenu' + me.props.oid}\r\n\t\t\t\titemsOut={me.getToolbarItems()}\r\n\t\t\t/>);\r\n\t\t}\r\n\r\n\t\treturn (\r\n\t\t\trenderedRow\r\n\t\t);\r\n\t},\r\n\r\n\tgetToolbarItems: function() {\r\n\t\tvar me = this;\r\n\t\tvar record = me.props.record.toJS();\r\n\t\tvar ToolbarItems = [];\r\n\r\n\t\tToolbarItems.push(<Button\r\n\t\t\tid={'btn2_' + me.props.oid + '_' + record.counter}\r\n\t\t\tkey={'btn2_' + me.props.oid + '_' + record.counter}\r\n\t\t\tsize={me.state.toolbarButtonSize}\r\n\t\t\ticon={'seicon-file'}\r\n\t\t\tonClick={me.viewDocumentData}\r\n\t\t\ttooltip={SE.t(104636)}/>);\r\n\r\n\t\tToolbarItems.push(<Button\r\n\t\t\tid={'btn1_' + me.props.oid + '_' + record.counter}\r\n\t\t\tkey={'btn1_' + me.props.oid + '_' + record.counter}\r\n\t\t\tsize={me.state.toolbarButtonSize}\r\n\t\t\ticon={'seicon-eye'}\r\n\t\t\tonClick={me.checkFgStatus}\r\n\t\t\ttooltip={SE.t(104634)}/>);\r\n\r\n\t\tToolbarItems.push(<Button\r\n\t\t\tid={'btn3_' + me.props.oid + '_' + record.counter}\r\n\t\t\tkey={'btn3_' + me.props.oid + '_' + record.counter}\r\n\t\t\tsize={me.state.toolbarButtonSize}\r\n\t\t\ticon={'seicon-download'}\r\n\t\t\tonClick={me.downloadElectronicFiles}\r\n\t\t\ttooltip={SE.t(111257)}/>);\r\n\r\n\t\treturn (ToolbarItems);\r\n\t},\r\n\r\n\tgetDocumentTitle: function() {\r\n\t\tvar me = this;\r\n\t\tvar record = me.props.record.toJS();\r\n\r\n\t\tif (record.title === null) {\r\n\t\t\treturn '';\r\n\t\t}\r\n\r\n\t\tif (record.title && (record.title.length > me.state.titleMaxLength)) {\r\n\t\t\treturn record.title.substr(0, me.state.titleMaxLength) + '...';\r\n\t\t}\r\n\r\n\t\treturn record.title;\r\n\t},\r\n\r\n\topenFeedbackModal: function() {\r\n\t\tvar me = this;\r\n\r\n\t\tme.setState({ showFeedbackModal: true });\r\n\t},\r\n\r\n\tcloseFeedbackModal: function() {\r\n\t\tvar me = this;\r\n\r\n\t\tme.setState({ showFeedbackModal: false });\r\n\t},\r\n\r\n\tgetStatusLabel: function(style) {\r\n\t\tvar me = this;\r\n\t\tvar record = me.props.record.toJS();\r\n\t\tvar nameIsNotSet = (record.nmuserupd === null);\r\n\t\tvar dateIsNotSet = (record.dtupdate === null);\r\n\t\tvar dateAux = new Date(record.dtupdate);\r\n\t\tvar timezoneOffset = dateAux.getTimezoneOffset() >= -660 ? dateAux.getTimezoneOffset() : 0;\r\n\t\tvar date = new DateFormatter().format(dateAux.getTime() + Math.abs(timezoneOffset * 60000));\r\n\r\n\t\tif (nameIsNotSet && dateIsNotSet) {\r\n\t\t\treturn ('');\r\n\t\t}\r\n\r\n\t\tif (nameIsNotSet) {\r\n\t\t\treturn (<div style={style}>\r\n\t\t\t\t<span className={'greenLightText'}>{SE.t(100239) + ' '}</span>\r\n\t\t\t\t<span className={'updatedDate'}>{date}</span>\r\n\t\t\t</div>);\r\n\t\t}\r\n\r\n\t\tif (dateIsNotSet) {\r\n\t\t\treturn (<div style={style}>\r\n\t\t\t\t<span className={'greenLightText'}>{SE.t(102130) + ' '}</span>\r\n\t\t\t\t<span className={'updatedUser'}>{record.nmuserupd}</span>\r\n\t\t\t</div>);\r\n\t\t}\r\n\r\n\t\treturn (<div style={style}>\r\n\t\t\t<span className={'greenLightText'}>{SE.t(100239) + ' '}</span>\r\n\t\t\t<span className={'updatedDate'}>{date}</span>\r\n\t\t\t<span className={'greenLightText'}>{' ' + SE.t(101179) + ' '}</span>\r\n\t\t\t<span className={'updatedUser'}>{record.nmuserupd}</span>\r\n\t\t</div>);\r\n\t},\r\n\r\n\trender: function() {\r\n\t\tvar me = this, record = me.props.record.toJS();\r\n\r\n\t\tvar documentNameLink = {\r\n\t\t\tfontSize: me.state.titleFontSize + 'px',\r\n\t\t\tfontWeight: 'normal',\r\n\t\t\tfontFamily: '\"arial\", sans-serif',\r\n\t\t\tmarginLeft: me.state.rowLeftMargin + 'px'\r\n\t\t};\r\n\r\n\t\tvar secondaryTextStyle = {\r\n\t\t\tmarginLeft: me.state.rowLeftMargin + 'px',\r\n\t\t\tfontSize: me.state.secondaryFontSize + 'px'\r\n\t\t};\r\n\r\n\t\tvar statusLabel = me.getStatusLabel(secondaryTextStyle);\r\n\r\n\t\treturn (<div>\r\n\t\t\t<ModalFeedback\r\n\t\t\t\tshow={me.state.showFeedbackModal}\r\n\t\t\t\ttitle={SE.t(105336)}\r\n\t\t\t\tbody={SE.t(105167)}\r\n\t\t\t\tbuttons={ModalFeedback.CONFIRM}\r\n\t\t\t\tonClose={me.closeFeedbackModal}\r\n\t\t\t\tonOk={me.viewElectronicFile}\r\n\t\t\t\tonCancel={me.closeFeedbackModal}\r\n\t\t\t\ttype={'info'}/>\r\n\t\t\t<Row className={'overrideBorder'} ref={'Row_' + record.counter + '_' + me.props.oid}\r\n\t\t\t\ttoolbar={me.getToolbar()}>\r\n\t\t\t\t<Col xs={me.state.iconWidth}\r\n\t\t\t\t\tsm={me.state.iconWidth}\r\n\t\t\t\t\tmd={me.state.iconWidth}\r\n\t\t\t\t\tlg={me.state.iconWidth}\r\n\t\t\t\t\tstyle={{ paddingBottom: 10, paddingTop: 16 }}>\r\n\t\t\t\t\t<Image\r\n\t\t\t\t\t\tsrc={baseURL + '/ui/desktop/lite/resources/images/' + me.state.iconSize + '/product-73.png'}>\r\n\t\t\t\t\t</Image>\r\n\t\t\t\t</Col>\r\n\t\t\t\t<Col xs={me.state.textWidth}\r\n\t\t\t\t\tsm={me.state.textWidth}\r\n\t\t\t\t\tmd={me.state.textWidth}\r\n\t\t\t\t\tlg={me.state.textWidth}\r\n\t\t\t\t\tstyle={{ paddingBottom: 10, paddingTop: (statusLabel === '') ? 24 : 14 }}>\r\n\t\t\t\t\t<OverlayTrigger\r\n\t\t\t\t\t\tplacement={'bottom'}\r\n\t\t\t\t\t\toverlay={(<Tooltip id={'Tooltip' + record.counter}>{record.title}</Tooltip>)}>\r\n\t\t\t\t\t\t<Link\r\n\t\t\t\t\t\t\tonClick={me.checkFgStatus}\r\n\t\t\t\t\t\t\tstyle={documentNameLink}>{me.getDocumentTitle()}</Link>\r\n\t\t\t\t\t</OverlayTrigger>\r\n\t\t\t\t\t<div id={'Row_' + record.counter + '_' + me.props.oid + 'div'} style={secondaryTextStyle}>\r\n\t\t\t\t\t\t<span className={'categorySpan'}>{SE.t(102041) + ' - '}</span>\r\n\t\t\t\t\t\t<span className={'categoryItalic'}>{record.nmcategory}</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t{statusLabel}\r\n\t\t\t\t</Col>\r\n\t\t\t</Row>\r\n\t\t</div>);\r\n\t}\r\n});\r\n\r\nrequire('reactorCmps/tokens/general');", "\n      import API from \"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./InputGroup.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\noptions.insert = insertFn.bind(null, \"head\");\noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./InputGroup.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./DocumentList.css\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\noptions.insert = insertFn.bind(null, \"head\");\noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./DocumentList.css\";\n       export default content && content.locals ? content.locals : undefined;\n", "\n      import API from \"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\";\n      import domAPI from \"!../../../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js\";\n      import insertFn from \"!../../../../../../node_modules/style-loader/dist/runtime/insertBySelector.js\";\n      import setAttributes from \"!../../../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js\";\n      import insertStyleElement from \"!../../../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js\";\n      import styleTagTransformFn from \"!../../../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js\";\n      import content, * as namedExport from \"!!../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[2]!./ModalFeeedback.scss\";\n      \n      \n\nvar options = {};\n\noptions.styleTagTransform = styleTagTransformFn;\noptions.setAttributes = setAttributes;\noptions.insert = insertFn.bind(null, \"head\");\noptions.domAPI = domAPI;\noptions.insertStyleElement = insertStyleElement;\n\nvar update = API(content, options);\n\n\n\nexport * from \"!!../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[2]!./ModalFeeedback.scss\";\n       export default content && content.locals ? content.locals : undefined;\n"], "names": ["PropTypes", "require", "React", "createReactClass", "Modal", "Feedback<PERSON>g", "<PERSON><PERSON>", "componentsThatShouldNotBeInLayoutCalc", "modalBtnBox", "modalBtnSpaces", "modalCustomStyle", "buttonsType", "module", "getDefaultProps", "getButtonsByType", "me", "props", "buttonOk", "SE", "buttonCancel", "buttons", "button", "key", "getButtons", "getType", "type", "feedBackType", "handleClose", "handleOk", "handleCancel", "render", "body", "InputGroup", "style", "createCollection", "DocumentListConfigurationModal", "tokenManagerHOC", "easyGrid", "DocumentListRow", "DocumentListCollection", "FormGroup", "InputAddon", "FormControl", "keyConstants", "workspaceInfo", "when", "Connector", "cursorPointer", "DocumentList", "getInitialState", "DocumentListGrid", "hasBeenConfigured", "hasBeenConfiguredPromise", "value", "deferred", "complete", "response", "searchTerm", "openModal", "hideModal", "e", "isMouseClick", "getDocumentListWidth", "getSearchFilter", "ref", "get<PERSON><PERSON>", "msgToken", "Text", "ButtonRadio", "ButtonRadioGroup", "Lookup", "DocumentListCategoriesCollection", "Label", "ReactDOM", "Row", "Col", "CategoryCollection", "rowStyle", "divStyle", "categories", "savedSearchesData", "loadedCategories", "titleState", "setModalState", "allCategoriesPromise", "loadedDataPromise", "savedSearchesPromise", "loadedSavedSearch", "obj", "Object", "counter", "fgtype", "parseInt", "getLoadedData", "getSavedSearches", "getCategories", "results", "getSavedSearchesOptions", "savedSearches", "savedSearchesOptions", "index", "handleChange", "i", "selected", "newModalHeight", "onCategoryLookupValueChange", "onSavedSearchSelection", "select", "searchCategories", "term", "page", "getChoice", "savedSearchValue", "saveConfigurationToDatabase", "title", "data", "afterSaved", "curl", "MessageBar", "saveConfiguration", "requiredFields<PERSON><PERSON><PERSON>illed", "titleValidationState", "lookupValidationState", "onTitleChange", "newTitle", "getBody", "String", "Image", "ToolBarMenu", "Link", "Date<PERSON><PERSON><PERSON><PERSON>", "OverlayTrigger", "<PERSON><PERSON><PERSON>", "ModalFeedback", "Utils", "baseURL", "documentListWidth", "componentWillUnmount", "element", "document", "resizeRow", "getDynamicComponents", "widgetWidth", "dynamicLayout", "viewDocumentData", "record", "params", "jnlpIframeExists", "window", "createJnlpIframe", "jnlpIframe", "checkFgStatus", "viewElectronicFile", "downloadElectronicFiles", "getToolbar", "renderedRow", "getToolbarItems", "ToolbarItems", "getDocumentTitle", "openFeedbackModal", "closeFeedbackModal", "getStatusLabel", "nameIsNotSet", "dateIsNotSet", "dateAux", "Date", "timezoneOffset", "date", "Math", "documentNameLink", "secondaryTextStyle", "statusLabel"], "mappings": ";;;;;;;;;;;;AAAA;AAC8H;AACtB;AACxG,8BAA8B,mFAA2B,CAAC,8FAAwC;AAClG;AACA;AACA;AACA,CAAC;AACD;AACA,6DAAe,uBAAuB,EAAC;;;;;;;;;;;;;;ACTvC;AACwH;AACtB;AAClG,8BAA8B,mFAA2B,CAAC,8FAAwC;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,6DAAe,uBAAuB,EAAC;;;;;;;;;;;;;;ACtCvC;AAC8H;AACtB;AACxG,8BAA8B,mFAA2B,CAAC,8FAAwC;AAClG;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;AACA,6DAAe,uBAAuB,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACbvC;AAAA,IAAIA,YAAYC,mBAAOA,CAAC,uDAAY;AACpC,IAAIC,QAAQD,mBAAOA,CAAC,oBAAO;AAC3B,IAAIE,mBAAmBF,mBAAOA,CAAC,8CAAoB;AACnD,IAAIG,QAAQH,mBAAOA,CAAC,iHAAoD;AACxE,IAAII,cAAcJ,mBAAOA,CAAC,+GAAiD;AAC3E,IAAIK,SAASL,mBAAOA,CAAC,mHAAmD;AACxE,IAAIM,wCAAwCN,mBAAOA,CAAC,kIAA2D;AAC/G,IAAIO,cAAc;IACjB,YAAY;IACZ,YAAY;AACb;AAEA,IAAIC,iBAAiB;IACpB,SAAS;IACT,aAAa;AACd;AAEA,IAAIC,mBAAmB;IACtB,iBAAiB;IACjB,cAAc;IACd,OAAO;AACR;AAEA,IAAIC,cAAc;IACjB,OAAO;IACP,SAAS;AACV;AAEAV,mBAAOA,CAAC,sGAAuB;AAC/BA,mBAAOA,CAAC,8DAA4B;AAEpCW,cAAc,GAAGT,iBAAiB;IACjC,aAAa;IAEb,WAAW;QACV;;GAEC,GACD,MAAMH,UAAU,IAAI;QACpB;;GAEC,GACD,MAAMA,UAAU,KAAK,CAAC;YAAC;YAAS;YAAW;YAAQ;YAAS;SAAO;QACnE;;GAEC,GACD,OAAOA,UAAU,MAAM;QACvB;;GAEC,GACD,MAAMA,UAAU,MAAM;QACtB;;;;GAIC,GACD,SAASA,UAAU,SAAS,CAAC;YAC5BA,UAAU,KAAK,CAAC;gBAACW,YAAY,KAAK;gBAAEA,YAAY,OAAO;aAAC;YACxDX,UAAU,KAAK;SACf;QACD;;GAEC,GACD,MAAMA,UAAU,IAAI;QACpB;;GAEC,GACD,UAAUA,UAAU,IAAI;QACxB;;GAEC,GACD,SAASA,UAAU,IAAI;QACvB;;GAEC,GACD,QAAQA,UAAU,SAAS,CAAC;YAACA,UAAU,MAAM;YAAEA,UAAU,MAAM;SAAC;IACjE;IAEAa,iBAAiB,SAAjBA;QACC,OAAQ;YACP,MAAM;YACN,QAAQ;QACT;IACD;IAEAC,kBAAkB,SAAlBA;QACC,IAAIC,KAAK,IAAI,EAAEC,QAAQD,GAAG,KAAK;QAE/B,IAAIE,yBAAY,oBAACX;YAChB,KAAI;YACJ,WAAW;YACX,OAAM;YACN,MAAMY,GAAG,CAAC,CAAC;YACX,SAASH,GAAG,QAAQ;;QAGrB,IAAII,6BAAgB,oBAACb;YACpB,KAAI;YACJ,WAAW;YACX,OAAM;YACN,MAAMY,GAAG,CAAC,CAAC;YACX,SAASH,GAAG,YAAY;;QAGzB,IAAIK,UAAUJ,MAAM,OAAO;QAE3B,IAAI,CAACA,MAAM,OAAO,IAAIA,MAAM,OAAO,KAAKL,YAAY,KAAK,EAAE;YAC1DS,UAAU;gBAACH;aAAS;QACrB;QAEA,IAAID,MAAM,OAAO,KAAKL,YAAY,OAAO,EAAE;YAC1CS,UAAU;gBAACH;gBAAUE;aAAa;QACnC;QAEA,OAAOC,QAAQ,GAAG,CAAC,SAASC,MAAM;YACjC,IAAIC,MAAM,eAAeD,OAAO,GAAG;YAEnC,qBACC,oBAAC;gBAAI,OAAOZ;gBAAgB,KAAKa;eAC/BD;QAEJ;IACD;IAEAE,YAAY,SAAZA;QACC,IAAIR,KAAK,IAAI,EAAEK,UAAUL,GAAG,gBAAgB;QAE5C,qBACC,oBAAC;YAAI,WAAU;YAAsB,OAAOP;WAC1CY;IAGJ;IAEAI,SAAS,SAATA,QAAkBC,IAAI;QACrB,IAAIC,eACH;YACC,OAAO;YACP,SAAS;YACT,OAAO;YACP,MAAM;YACN,MAAM;QACP;QAED,OAAOA,YAAY,CAACD,KAAK;IAC1B;IAEAE,aAAa,SAAbA;QACC,IAAIZ,KAAK,IAAI,EAAEC,QAAQD,GAAG,KAAK;QAE/B,IAAIC,MAAM,OAAO,KAAKL,YAAY,OAAO,EAAE;YAC1CI,GAAG,YAAY;YACf;QACD;QAEA,IAAIC,MAAM,OAAO,KAAKL,YAAY,KAAK,EAAE;YACxCI,GAAG,QAAQ;YACX;QACD;QAEA,IAAIC,MAAM,OAAO,EAAE;YAClBA,MAAM,OAAO;QACd;IACD;IAEAY,UAAU,SAAVA;QACC,IAAIb,KAAK,IAAI,EAAEC,QAAQD,GAAG,KAAK;QAE/B,IAAIC,MAAM,IAAI,EAAE;YACfA,MAAM,IAAI;QACX;QAEA,IAAIA,MAAM,OAAO,EAAE;YAClBA,MAAM,OAAO;QACd;IACD;IAEAa,cAAc,SAAdA;QACC,IAAId,KAAK,IAAI,EAAEC,QAAQD,GAAG,KAAK;QAE/B,IAAIC,MAAM,QAAQ,EAAE;YACnBA,MAAM,QAAQ;QACf;QAEA,IAAIA,MAAM,OAAO,EAAE;YAClBA,MAAM,OAAO;QACd;IACD;IAEAc,QAAQ,SAARA;QACC,IAAIf,KAAK,IAAI,EAAEC,QAAQD,GAAG,KAAK,EAAEgB;QAEjCA,qBACC,oBAAC,2BACA,oBAAC1B;YACA,OAAOW,MAAM,KAAK;YAClB,SAASA,MAAM,IAAI;YACnB,MAAMD,GAAG,OAAO,CAACC,MAAM,IAAI;YAE3BD,GAAG,UAAU;QAIhB,qBACC,oBAACX,+CACIY;YACJ,aAAa;YACb,OAAON;YACP,WAAU;YACV;YACA,OAAO;YACP,QAAQ;YACR,QAAQK,GAAG,KAAK,CAAC,MAAM;YACvB,SAASA,GAAG,WAAW;YACvB,MAAMA,GAAG,QAAQ;YACjB,UAAUA,GAAG,YAAY;YACzB,MAAMgB;;IAGT;AACD;AAEAnB,oBAAoB,GAAGD,YAAY,KAAK;AACxCC,sBAAsB,GAAGD,YAAY,OAAO;AAE5CJ,sCAAsC,GAAG,CAACK,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjOpE,IAAIV,QAAQD,mBAAOA,CAAC,oBAAO;AAC3B,IAAIE,mBAAmBF,mBAAOA,CAAC,8CAAoB;AACnD,IAAI+B,aAAa/B,mBAAOA,CAAC,yFAAgC;AAEzD,IAAIgC,QAAQ;IACX,cAAc;AACf;AAEAhC,mBAAOA,CAAC,uFAAkB;AAE1BW,cAAc,GAAGT,iBAAiB;IACjC,aAAa;IAEb2B,QAAQ,SAARA;QACC,qBACC,oBAACE,oDAAe,IAAI,CAAC,KAAK;YAAE,OAAOC;YAAO,WAAW;;IAEvD;AACD;;;;;AClBA,IAAIC,mBAAmBjC,mBAAOA,CAAC,sDAAwB;AAEvDW,cAAc,GAAGsB,iBAAiB;IACjC,OAAO;QACN,YAAY;QAEZ,UAAU;YACT,OAAO;QACR;IACD;IACA,YAAY;QACX,cAAc;YACb,YAAY;QACb;IACD;AACD;;;;;ACfA,IAAIA,mBAAmBjC,mBAAOA,CAAC,sDAAwB;AAEvDW,cAAc,GAAGsB,iBAAiB;IACjC,OAAO;QACN,YAAY;QAEZ,UAAU;YACT,OAAO;QACR;IACD;IACA,YAAY;QACX,cAAc;YACb,YAAY;QACb;IACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACfA;AAAA;AAAA;AAAA,IAAIhC,QAAQD,mBAAOA,CAAC,oBAAO;AAC3B,IAAIE,mBAAmBF,mBAAOA,CAAC,8CAAoB;AAEnD,0BAA0B;AAC1B,IAAIkC,iCAAiClC,yJAAmD;AACxF,IAAImC,kBAAkBnC,mBAAOA,CAAC,4IAAgE;AAE9F,wDAAwD;AACxD,IAAIoC,WAAWpC,mBAAOA,CAAC,gHAAkD;AACzE,IAAIqC,kBAAkBrC,iKAA0E;AAChG,IAAIsC,yBAAyBtC,mBAAOA,CAAC,uHAA4D;AACjG,IAAII,cAAcJ,mBAAOA,CAAC,+GAAiD;AAE3E,0CAA0C;AAC1C,IAAIuC,YAAYvC,mBAAOA,CAAC,qGAA4C;AACpE,IAAIwC,aAAaxC,mBAAOA,CAAC,yGAA8C;AACvE,IAAI+B,aAAa/B,mBAAOA,CAAC,6HAAwD;AACjF,IAAIyC,cAAczC,mBAAOA,CAAC,kIAA2D;AACrF,IAAI0C,eAAe1C,mBAAOA,CAAC,mFAAqC;AAEhE,QAAQ;AACR,IAAI2C,gBAAgB3C,mBAAOA,CAAC,oCAAe;AAC3C,IAAI4C,OAAO5C,mBAAOA,CAAC,kBAAM;AACzB,IAAM6C,YAAY7C,mBAAOA,CAAC,4BAAW;AAErC,IAAI8C,gBAAgB;IACnB,QAAQ;AACT;AAEA,IAAIC,eAAe7C,iBAAiB;IACnC,aAAa;IAEb;QACC,OAAO;YACN,wBAAwB,IAAIoC;QAC7B;IACD;IAEAU,iBAAiB,SAAjBA;QACC,OAAQ;YACP,SAAS;YACT,WAAW;QACZ;IACD;IAEA;QACC,IAAIlC,KAAK,IAAI;QAEb,IAAImC,mBAAmBb,SAAS;YAC/B,UAAU,sBAAsBtB,GAAG,KAAK,CAAC,GAAG;YAC5C,KAAKuB;YACL,YAAY;YACZ,YAAY;YACZ,gBAAgB;YAChB,kBAAkB;gBACjB,UAAUvB,GAAG,KAAK,CAAC,sBAAsB;gBACzC,QAAQ;oBAAE,WAAWA,GAAG,KAAK,CAAC,GAAG;oBAAE,QAAQ6B,cAAc,SAAS;gBAAG;YACtE;QACD;QAEAM,iBAAiB,eAAe,CAAC;YAAE,WAAWnC,GAAG,KAAK,CAAC,GAAG;YAAE,QAAQ6B,cAAc,SAAS;QAAG;QAC9F7B,GAAG,gBAAgB,GAAGmC;IACvB;IAEA;QACC,IAAInC,KAAK,IAAI;QACb,IAAIoC;QACJ,IAAIC,2BAA2BrC,GAAG,iBAAiB;QAEnDqC,yBAAyB,IAAI,CAAC,SAASC,KAAK;YAC3CF,oBAAqBE,UAAU;YAE/B,CAACtC,GAAG,WAAW,IAAIA,GAAG,QAAQ,CAAC;gBAC9B,SAAS;gBACToC,mBAAAA;YACD;QACD;IAED;IAEA;QACC,IAAIpC,KAAK,IAAI;QAEbA,GAAG,WAAW,GAAG;IAClB;IAEAoC,mBAAmB,SAAnBA;QACC,IAAIG,WAAWT,KAAK,KAAK;QACzB,IAAI9B,KAAK,IAAI;QAEb+B,UAAU,aAAa,CAAC,+CAA+C;YACtE,UAAU;YACV,MAAM;gBACL,KAAK/B,GAAG,KAAK,CAAC,GAAG;gBACjB,uBAAuB;YACxB;YACAwC,UAAU,SAAVA,SAAmBC,QAAQ;gBAC1BF,SAAS,OAAO,CAACE;YAClB;QACD;QAEA,OAAOF,SAAS,OAAO;IACxB;IAEA,qBAAWG,UAAU;QACpB,IAAI1C,KAAK,IAAI;QAEbA,GAAG,gBAAgB,CAAC,eAAe,CAAC;YAAE,WAAWA,GAAG,KAAK,CAAC,GAAG;YAAE0C,YAAAA;YAAY,QAAQb,cAAc,SAAS;QAAG;IAC9G;IAEA;QACC,IAAI7B,KAAK,IAAI;QAEbA,GAAG,gBAAgB,CAAC,eAAe,CAAC;YAAE,WAAWA,GAAG,KAAK,CAAC,GAAG;YAAE,QAAQ6B,cAAc,SAAS;QAAG;IAClG;IAEA;QACC,IAAI7B,KAAK,IAAI;QAEbA,GAAG,QAAQ,CAAC;YAAE,mBAAmB;QAAK;IACvC;IAEA2C,WAAW,SAAXA;QACC,IAAI3C,KAAK,IAAI;QAEbA,GAAG,IAAI,CAAC,mCAAmCA,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,aAAa;QACtEA,GAAG,QAAQ,CAAC;YAAE,WAAW;QAAK;IAC/B;IAEA4C,WAAW,SAAXA;QACC,IAAI5C,KAAK,IAAI;QAEbA,GAAG,QAAQ,CAAC;YAAE,WAAW;QAAM;IAChC;IAEA,iCAAuB6C,CAAC;QACvB,IAAI7C,KAAK,IAAI;QACb,IAAI8C,eAAe;QACnB,IAAIJ,aAAa;QAEjB,wBAAwB,GACxB,IAAI,aAAaG,EAAE,WAAW,IAAIA,EAAE,WAAW,CAAC,KAAK,KAAKjB,aAAa,iBAAiB,EAAE;YACzFkB,eAAe;YACfJ,aAAa,IAAI,CAAC,WAAW,CAAC,KAAK;QACpC,OAAO,IAAIG,EAAE,MAAM,EAAE;YACpBH,aAAaG,EAAE,MAAM,CAAC,KAAK;QAC5B;QAEA,sBAAsB,GACtB,IAAIC,gBAAgBD,EAAE,WAAW,CAAC,OAAO,KAAKjB,aAAa,SAAS,EAAE;YACrE5B,GAAG,UAAU,CAAC0C;QACf;IACD;IAEAK,sBAAsB,SAAtBA;QACC,IAAI/C,KAAK,IAAI;QAEb,OAAOA,GAAG,IAAI,CAAC,iBAAiBA,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW;IAC1D;IAEAgD,iBAAiB,SAAjBA;QACC,IAAIhD,KAAK,IAAI;QAEb,qBACC,oBAAC,2BACA,oBAACyB,+BACA,oBAACR,gCACA,oBAACS;YACA,IAAI,gBAAgB1B,GAAG,KAAK,CAAC,GAAG;YAChC,SAASA,GAAG,sBAAsB;YAClC,OAAOgC;YACP,WAAW;YACX,OAAOhC,GAAG,KAAK,CAAC,QAAQ,CAAC;0BAE1B,oBAAC,2BACA,oBAAC2B;YACA,UAAUsB,SAAAA;gBAASjD,GAAG,WAAW,GAAGiD;YAAK;YACzC,YAAYjD,GAAG,sBAAsB;;IAO5C;IAEAkD,SAAS,SAATA;QACC,IAAIlD,KAAK,IAAI;QACb,IAAImD,WAAWnD,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,KAAK,CAAC,OAAO,IAAI,CAAC;QAE3D,qBACC,oBAACV;YACA,OAAO6D;YACP,WAAW;YACX,eAAe;YACf;YACA,MAAM;;IAGT;IAEApC,QAAQ,SAARA;QACC,IAAIf,KAAK,IAAI;QAEb,qBACC,oBAAC;YAAI,IAAI,iBAAiBA,GAAG,KAAK,CAAC,GAAG;YAAE,KAAK,iBAAiBA,GAAG,KAAK,CAAC,GAAG;yBACzE,oBAACoB;YAA+B,yBAAyBpB,GAAG,uBAAuB;YAClF,uBAAuBA,GAAG,qBAAqB;YAC/C,KAAK,mCAAmCA,GAAG,KAAK,CAAC,GAAG;YACpD,WAAWA,GAAG,KAAK,CAAC,SAAS;YAC7B,WAAWA,GAAG,SAAS;YACvB,WAAWA,GAAG,SAAS;WACnBA,GAAG,KAAK,IACZA,GAAG,OAAO;IAGd;AACD;AAEAH,cAAc,GAAGwB,gBAAgBY;AACjCpC,+BAA+B,GAAGoC;AAElC/C,mBAAOA,CAAC,8DAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2PjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAzduB;AACwB;AACiB;AACV;AACkB;AACD;AACS;AACtB;AACuD;AACtD;AACK;AACiB;AACnD;AAEjC,oCAAoC;AACZ;AAEkB;AAE1C,oCAAoC;AACF;AAElC,SAAS;AACkD;AAEA;AAE/B;AAE5B,mDAAmD;AACnD,IAAI2E,qBAAqB,IAAIL,8GAAgCA;AAE7D,IAAIM,WAAW;IACd,eAAe;IACf,YAAY;AACb;AAEA,IAAIC,WAAW;IACd,SAAS;IACT,YAAY;IACZ,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,YAAY;IACZ,OAAO;AACR;AAEA,6DAAe3E,yDAAgBA,CAAC;IAC/B,aAAa;IAEb8C,iBAAiB,SAAjBA;QACC,IAAI8B,aAAa;QACjB,IAAIhE,KAAK,IAAI;QACb,IAAIiE,oBAAoB,EAAE;QAC1B,IAAIC,mBAAmB,EAAE;QACzB,IAAIC,aAAa;QAEjBnE,GAAG,WAAW,GAAG;QAEjB,OAAQ;YACP,UAAU;YACV,qBAAqBkE;YACrBD,mBAAAA;YACA,aAAa;YACbD,YAAAA;YACA,uBAAuB;YACvB,sBAAsB;YACtB,OAAOG;YACP,SAAS;QACV;IACD;IAEA;QACC,IAAInE,KAAK,IAAI;QAEbA,GAAG,WAAW,GAAG;IAClB;IAEAoE,eAAe,SAAfA;QACC,IAAIJ,aAAa;QACjB,IAAIhE,KAAK,IAAI;QACb,IAAIqE,uBAAuBrE,GAAG,aAAa;QAC3C,IAAIsE,oBAAoBtE,GAAG,aAAa;QACxC,IAAIuE,uBAAuBvE,GAAG,gBAAgB;QAE9C,IAAIiE,oBAAoB,EAAE;QAC1B,IAAIC,mBAAmB,EAAE;QACzB,IAAIM,oBAAoB,EAAE;QAC1B,IAAIL,aAAa;QAEjBnE,GAAG,WAAW,GAAG;QAEjBqE,qBAAqB,IAAI,CAAC,SAAS/B,KAAK;YACvC0B,aAAa1B,MAAM,GAAG,CAAC,SAASmC,GAAG;gBAClC,OAAOC,OAAO,IAAI,CAACD,KAAK,IAAI,GAAG,GAAG,CAAC,SAASlE,GAAG;oBAC9C,OAAOkE,GAAG,CAAClE,IAAI;gBAChB;YACD;QACD;QAEA+D,kBAAkB,IAAI,CAAC,SAAShC,KAAK;YACpC,IAAIqC;YACJ,IAAIC,SAAUtC,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,mBAAoB,CAAC,IAAIuC,SAASvC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE;YAEzF,IAAIA,KAAK,CAAC,EAAE,CAAC,aAAa,EAAE;gBAC3B,CAACtC,GAAG,WAAW,IAAIA,GAAG,QAAQ,CAAC;oBAC9BwE,mBAAAA;oBACA,qBAAqBN;oBACrB,OAAOC;gBACR;YACD;YAEA,uBAAuB;YACvB,IAAIS,WAAW,GAAG;gBACjB,IAAKD,UAAU,GAAGA,UAAUrC,MAAM,MAAM,EAAEqC,UAAW;oBACpDT,iBAAiB,IAAI,CAAC;wBAAE,KAAK5B,KAAK,CAACqC,QAAQ,CAAC,UAAU;wBACrD,YAAYrC,KAAK,CAACqC,QAAQ,CAAC,UAAU;wBACrC,UAAUrC,KAAK,CAACqC,QAAQ,CAAC,QAAQ;oBAAC;oBACnCR,aAAa7B,KAAK,CAACqC,QAAQ,CAAC,QAAQ;gBACrC;YACD;YAEA,gCAAgC;YAChC,IAAIC,WAAW,GAAG;gBACjBJ,oBAAoBlC,KAAK,CAAC,EAAE,CAAC,cAAc;gBAC3C6B,aAAa7B,KAAK,CAAC,EAAE,CAAC,QAAQ;YAC/B;YAEA,CAACtC,GAAG,WAAW,IAAIA,GAAG,QAAQ,CAAC;gBAC9BwE,mBAAAA;gBACA,qBAAqBN;gBACrB,OAAOC;gBACPS,QAAAA;gBACA,UAAWA,WAAW,IAAK,gBAAgB;gBAC3C,aAAcA,WAAW,IAAK,MAAM;YACrC;QACD;QAEAL,qBAAqB,IAAI,CAAC,SAASjC,KAAK;YACvC,IAAIqC;YAEJ,IAAKA,UAAU,GAAGA,UAAUrC,MAAM,MAAM,EAAEqC,UAAW;gBACpDV,kBAAkB,IAAI,CAAC;oBAAE,YAAY3B,KAAK,CAACqC,QAAQ,CAAC,GAAG;oBACtD,WAAWrC,KAAK,CAACqC,QAAQ,CAAC,SAAS;gBAAC;YACtC;YAEA,CAAC3E,GAAG,WAAW,IAAIA,GAAG,QAAQ,CAAC;gBAC9BiE,mBAAAA;YACD;QACD;QAEAjE,GAAG,QAAQ,CAAC;YACX,UAAU;YACV,qBAAqBkE;YACrBD,mBAAAA;YACA,aAAa;YACbD,YAAAA;YACA,uBAAuB;YACvB,sBAAsB;YACtB,OAAOG;QACR;IACD;IAEAW,eAAe,SAAfA;QACC,IAAIvC,WAAWT,kDAAU;QACzB,IAAI9B,KAAK,IAAI;QAEb+B,+DAAuB,CAAC,+CAA+C;YACtE,UAAU;YACV,MAAM;gBACL,KAAK/B,GAAG,KAAK,CAAC,GAAG;gBACjB,mBAAmB;YACpB;YACAwC,UAAU,SAAVA,SAAmBC,QAAQ;gBAC1BF,SAAS,OAAO,CAACE;YAClB;QACD;QAEA,OAAOF,SAAS,OAAO;IACxB;IAEAwC,kBAAkB,SAAlBA;QACC,IAAIxC,WAAWT,kDAAU;QAEzBC,+DAAuB,CAAC,kDAAkD;YACzE,UAAU;YACV,MAAM;gBACL,QAAQF,+DAAuB;gBAC/B,sBAAsB;YACvB;YACAW,UAAU,SAAVA,SAAmBC,QAAQ;gBAC1BF,SAAS,OAAO,CAACE;YAClB;QACD;QAEA,OAAOF,SAAS,OAAO;IACxB;IAEAyC,eAAe,SAAfA;QACC,IAAIzC,WAAWT,kDAAU;QAEzB+B,mBAAmB,KAAK,CAAC;YAAE,QAAQhC,+DAAuB;QAAG,GAAG,IAAI,CACnE,SAASoD,OAAO;YACf1C,SAAS,OAAO,CAAC0C;QAClB,GACA1C,SAAS,MAAM;QAGhB,OAAOA,SAAS,OAAO;IACxB;IAEA2C,yBAAyB,SAAzBA;QACC,IAAIlF,KAAK,IAAI;QACb,IAAImF,gBAAgBnF,GAAG,KAAK,CAAC,iBAAiB;QAC9C,IAAIoF,uBAAuB,EAAE;QAE7BD,cAAc,GAAG,CAAC,SAAS7C,KAAK,EAAE+C,KAAK;YACtCD,qBAAqB,IAAI,eAAC,2DAAC;gBAAO,OAAO;oBAAE,UAAU;gBAAO;gBAAG,KAAKC;gBAAO,OAAO/C,MAAM,UAAU;eAAGA,MAAM,SAAS;QACrH;QAEA,OAAO8C;IACR;IAEAE,cAAc,SAAdA,aAAuBC,CAAC,EAAEC,QAAQ;QACjC,IAAIxF,KAAK,IAAI;QACb,IAAIyF;QAEJ,IAAID,aAAa,YAAY;YAC5BC,iBAAiB;QAClB,OAAO;YACNA,iBAAiB;QAClB;QAEAzF,GAAG,QAAQ,CAAC;YAAEwF,UAAAA;YACb,aAAaC;QAAe;IAC9B;IAEAC,6BAA6B,SAA7BA,4BAAsCpD,KAAK;QAC1C,IAAItC,KAAK,IAAI;QAEbA,GAAG,QAAQ,CAAC;YAAE,qBAAqBsC;YAClC,uBAAuB;QACxB;QAEA,OAAOA;IACR;IAEAqD,wBAAwB,SAAxBA,uBAAiCrD,KAAK;QACrC,IAAItC,KAAK,IAAI;QACb,IAAI4F,SAASlC,6DAAoB,CAAC1D,GAAG,IAAI,CAAC,WAAW;QAErDA,GAAG,QAAQ,CAAC;YAAE,mBAAmB4F,OAAO,KAAK;YAC5C,uBAAuB;QACxB;QAEA,OAAOtD;IACR;IAEAuD,kBAAkB,SAAlBA,iBAA2BC,IAAI,EAAEC,IAAI;QACpC,IAAIxD,WAAWT,kDAAU;QAEzB+B,mBAAmB,MAAM,CAAC;YAAE,YAAYiC;YAAM,QAAQjE,+DAAuB;QAAG,GAAGkE,MAAM,IAAI,CAC5F;YACCxD,SAAS,OAAO,CAAC;gBAChB,MAAMsB,mBAAmB,aAAa;gBACtC,OAAOA,mBAAmB,QAAQ,GAAG,IAAI;YAC1C;QACD,GACAtB,SAAS,MAAM;QAGhB,OAAOA,SAAS,OAAO;IACxB;IAEAyD,WAAW,SAAXA;QACC,IAAIhG,KAAK,IAAI;QACb,IAAIiG;QAEJ,IAAIjG,GAAG,KAAK,CAAC,iBAAiB,IAAK,CAACA,GAAG,KAAK,CAAC,iBAAiB,EAAG;YAChEiG,mBAAmBjG,GAAG,KAAK,CAAC,iBAAiB,GAAGA,GAAG,KAAK,CAAC,iBAAiB,GAAG;QAC9E,OAAO;YACNiG,mBAAmBjG,GAAG,KAAK,CAAC,iBAAiB,GAAGA,GAAG,KAAK,CAAC,iBAAiB,GAAG;QAC9E;QAEA,IAAIA,GAAG,KAAK,CAAC,QAAQ,KAAK,YAAY;YACrC,qBAAQ,2DAAC,2BACR,2DAACuD,iFAAMA;gBACN,IAAI,WAAWvD,GAAG,KAAK,CAAC,GAAG;gBAC3B,KAAK;gBACL;gBACA;gBACA,iBAAiBA,GAAG,KAAK,CAAC,qBAAqB;gBAC/C,OAAOG,GAAG,CAAC,CAAC;gBACZ,MAAMH,GAAG,KAAK,CAAC,UAAU;gBACzB,WAAW;gBACX,YAAY;gBACZ,UAAUA,GAAG,gBAAgB;gBAC7B,OAAOA,GAAG,KAAK,CAAC,mBAAmB;gBACnC,UAAUsC,SAAAA;2BAAStC,GAAG,2BAA2B,CAACsC;;;QAGrD;QAEA,qBAAQ,2DAAC,2BACR,2DAAC;YAAI,WAAW;yBACf,2DAACmB,mFAAKA;YAAC,MAAMtD,GAAG,CAAC,CAAC;YAAS;YAAS;2BAErC,2DAAC;YAAI,OAAO;gBAAE,UAAU;gBAAQ,OAAO;YAAO;yBAC7C,2DAACsB,oFAASA;YAAC,OAAO;gBAAE,UAAU;gBAAQ,OAAO;YAAO;yBACnD,2DAACE,mGAAWA;YAAC,IAAI,oBAAoB3B,GAAG,KAAK,CAAC,GAAG;YAChD,KAAK;YACL,OAAQiG,iBAAiB,MAAM,GAAG,IAAKA,mBAAmB;YAC1D,gBAAgB;YAChB,aAAa9F,GAAG,CAAC,CAAC;YAClB,UAAUmC,SAAAA;uBAAStC,GAAG,sBAAsB,CAACsC;;YAC7C,OAAO;gBAAE,UAAU;gBAAQ,OAAO;YAAO;WACxCtC,GAAG,uBAAuB;IAKhC;IAEAkG,6BAA6B,SAA7BA,4BAAsCV,QAAQ;QAC7C,IAAIxF,KAAK,IAAI;QACb,IAAImG,QAAQnG,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK;QAC1C,IAAI4F,SAASlC,6DAAoB,CAAC1D,GAAG,IAAI,CAAC,WAAW;QACrD,IAAIoG,OAAO,EAAE;QAEb,IAAIZ,aAAa,YAAY;YAC5BY,OAAO;gBACN,iBAAiBpG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,SAASqF,KAAK;oBAAI,OAAOA,MAAM,UAAU;gBAAE;gBACnG,aAAa;gBACb,WAAWrF,GAAG,KAAK,CAAC,GAAG;YACxB;QACD,OAAO;YACNoG,OAAO;gBACN,gBAAgB;gBAChB,WAAWpG,GAAG,KAAK,CAAC,GAAG;gBACvB,gBAAgB4F,OAAO,KAAK;YAC7B;QACD;QAEA7D,+DAAuB,CAAC,6CAA6C;YACpEqE,MAAAA;YACA5D,UAAU,SAAVA;gBACCxC,GAAG,UAAU,CAACmG;YACf;QACD;IACD;IAEAE,YAAY,SAAZA,WAAqBF,KAAK;QACzB,IAAInG,KAAK,IAAI;QAEbsG,KAAK,cAAc,SAASC,UAAU;YACrCA,WAAW,kBAAkB,CAACpG,GAAG,CAAC,CAAC,SAAS;gBAC3C,SAAS;YACV;QACD;QAEAH,GAAG,KAAK,CAAC,QAAQ,CAACmG;QAClBnG,GAAG,KAAK,CAAC,UAAU;QACnBA,GAAG,KAAK,CAAC,uBAAuB;QAChCA,GAAG,KAAK,CAAC,qBAAqB;IAC/B;IAEAwG,mBAAmB,SAAnBA;QACC,IAAIxG,KAAK,IAAI;QAEb,IAAI,CAACA,GAAG,uBAAuB,IAAI;YAClCsG,KAAK,cAAc,SAASC,UAAU;gBACrCA,WAAW,gBAAgB,CAACpG,GAAG,CAAC,CAAC,SAAS;oBACzC,SAAS;gBACV;YACD;YACA;QACD;QAEAH,GAAG,2BAA2B,CAACA,GAAG,KAAK,CAAC,QAAQ;QAEhDA,GAAG,KAAK,CAAC,SAAS;IACnB;IAEAyG,yBAAyB,SAAzBA;QACC,IAAIzG,KAAK,IAAI;QACb,IAAI0G,uBAAuB;QAC3B,IAAIC,wBAAwB;QAC5B,IAAIF,0BAA0B;QAE9B,IAAI,CAACzG,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE;YACpC0G,uBAAuB;YACvBD,0BAA0B;QAC3B;QAEA,IAAIzG,GAAG,KAAK,CAAC,QAAQ,KAAK,cAAe,EAAEA,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,IAAMA,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAI;YACrI2G,wBAAwB;YACxBF,0BAA0B;QAC3B;QAEAzG,GAAG,QAAQ,CAAC;YAAE0G,sBAAAA;YACbC,uBAAAA;QACD;QAEA,OAAOF;IACR;IAEAG,eAAe,SAAfA,cAAwBC,QAAQ;QAC/B,IAAI7G,KAAK,IAAI;QAEbA,GAAG,QAAQ,CAAC;YAAE,OAAO6G;YACpB,sBAAsB;QACvB;IACD;IAEAC,SAAS,SAATA;QACC,IAAI9G,KAAK,IAAI;QACb,IAAImG,QAAQY,OAAO5G,GAAG,CAAC,CAAC,SAAS,WAAW;QAE5C,qBAAQ,2DAAC;YAAI,IAAI,iBAAiBH,GAAG,KAAK,CAAC,GAAG;yBAC7C,2DAACoD,+EAAIA;YACJ,WAAW;YACX,KAAK;YACL,OAAOjD,GAAG,CAAC,CAAC;YACZ,OAAOH,GAAG,KAAK,CAAC,KAAK;YACrB;YACA;YACA;YACA,iBAAiBA,GAAG,KAAK,CAAC,oBAAoB;YAC9C,UAAUA,GAAG,aAAa;0BAC3B,2DAAC;YAAI,OAAO+D;WAAWoC,sBACvB,2DAACxC,kFAAGA;YAAC,OAAOG;YACX,WAAW;yBACX,2DAACF,kFAAGA;YAAC,IAAI;YAAG,IAAI;YAAG,IAAI;YAAG,IAAI;yBAC7B,2DAACN,6FAAgBA;YAChB,WAAW;YACX,OAAOtD,GAAG,KAAK,CAAC,QAAQ;YACxB,UAAUA,GAAG,YAAY,CAAC,IAAI,CAACA,IAAI;yBACnC,2DAACqD,yFAAWA;YAAC,IAAI;WACflD,GAAG,CAAC,CAAC,wBAEP,2DAACkD,yFAAWA;YAAC,IAAI;WACflD,GAAG,CAAC,CAAC,0BAIT,2DAACyD,kFAAGA;YAAC,IAAI;YAAG,IAAI;YAAG,IAAI;YAAG,IAAI;2BAG/B,2DAAC,eAEA5D,GAAG,SAAS;IAEf;IAEAe,QAAQ,SAARA;QACC,IAAIf,KAAK,IAAI;QAEb,qBACC,2DAACX,uFAAKA;YACL,KAAK,UAAUW,GAAG,KAAK,CAAC,GAAG;YAC3B,OAAO;YACP,QAAQA,GAAG,KAAK,CAAC,WAAW;YAC5B,MAAMA,GAAG,KAAK,CAAC,SAAS;YACxB,SAASA,GAAG,KAAK,CAAC,SAAS;YAC3B,KAAK,mBAAmBA,GAAG,KAAK,CAAC,GAAG;YACpC;YACA,OAAOG,GAAG,CAAC,CAAC;YACZ,MAAMH,GAAG,OAAO;YAChB,sBAAQ,2DAACT,+FAAMA;gBAAC,SAASS,GAAG,iBAAiB,CAAC,IAAI,CAACA,IAAI;gBAAO,SAASG,GAAG,CAAC,CAAC;gBAAS,IAAI;gBAAS,MAAMA,GAAG,CAAC,CAAC;;;IAGhH;AACD,EAAE,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxGmC;AAAA;AAAA;AAAA;AAjXZ;AACwB;AAElD,wBAAwB;AACyD;AAEpB;AACO;AACmB;AAChB;AACN;AACrB;AACmC;AACb;AACyB;AACjE;AACgB;AACR;AAEN;AAE5B,IAAIqH,UAAUD,0DAAkB;AAEhC,6DAAenI,yDAAgBA,CAAC;IAE/B8C,iBAAiB,SAAjBA;QACC,IAAIlC,KAAK,IAAI;QAEb,IAAIyH,oBAAoBzH,GAAG,KAAK,CAAC,oBAAoB;QAErD,OAAOA,GAAG,oBAAoB,CAACyH;IAChC;IAEAC,sBAAsB,SAAtBA;QACC,IAAIC;QAEJ,IAAIC,SAAS,cAAc,CAAC,kBAAkB,MAAM;YACnDD,UAAUC,SAAS,cAAc,CAAC;YAClCD,QAAQ,UAAU,CAAC,WAAW,CAACA;QAChC;IACD;IAEAE,WAAW,SAAXA;QACC,IAAI7H,KAAK,IAAI;QAEbA,GAAG,QAAQ,CAACA,GAAG,oBAAoB,CAACA,GAAG,KAAK,CAAC,oBAAoB;IAClE;IAEA8H,sBAAsB,SAAtBA,qBAA+BC,WAAW;QACzC,IAAIC;QAEJA,gBAAgB;YACf,mBAAmB;YACnB,WAAW;YACX,WAAW;YACX,UAAU;YACV,gBAAgB;YAChB,eAAe;YACf,mBAAmB;YACnB,eAAe;YACf,WAAW;QACZ;QAEA,IAAID,cAAc,KAAM;YACvBC,gBAAgB;gBACf,mBAAmB;gBACnB,WAAW;gBACX,WAAW;gBACX,UAAU;gBACV,gBAAgB;gBAChB,eAAe;gBACf,mBAAmB;gBACnB,eAAe;gBACf,WAAW;YACZ;QACD,OAAO,IAAID,cAAc,KAAK;YAC7BC,gBAAgB;gBACf,mBAAmB;gBACnB,WAAW;gBACX,WAAW;gBACX,UAAU;gBACV,gBAAgB;gBAChB,eAAe;gBACf,mBAAmB;gBACnB,eAAe,CAAC;gBAChB,WAAW;YACZ;QACD,OAAO,IAAID,cAAc,MAAM;YAC9BC,gBAAgB;gBACf,mBAAmB;gBACnB,WAAW;gBACX,WAAW;gBACX,UAAU;gBACV,gBAAgB;gBAChB,eAAe;gBACf,mBAAmB;gBACnB,eAAe,CAAC;gBAChB,WAAW;YACZ;QACD;QAEAA,cAAc,iBAAiB,GAAG;QAElC,OAAOA;IACR;IAEAC,kBAAkB,SAAlBA;QACC,IAAIjI,KAAK,IAAI;QACb,IAAIkI,SAASlI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI;QACjC,IAAImI,SAAS;YAAE,YAAYD,OAAO,UAAU;YAAE,QAAQ;QAAE;QAExDX,8DAAsB,CAACC,UAAU,6CAA6CW,QAAQ,MAAM,MAAM;IACnG;IAEAC,kBAAkB,SAAlBA;QACC,OAAOC,OAAO,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC;IAC3C;IAEAC,kBAAkB,SAAlBA;QACC,IAAIC,aAAaF,OAAO,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC;QAEnDE,WAAW,EAAE,GAAG;QAChBA,WAAW,IAAI,GAAG;QAClBA,WAAW,KAAK,CAAC,OAAO,GAAG;QAC3BF,OAAO,GAAG,CAAC,QAAQ,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAACE;IACjE;IAEAC,eAAe,SAAfA;QACC,IAAIxI,KAAK,IAAI;QACb,IAAIkI,SAASlI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI;QAEjC,OAAQ6E,SAASqD,OAAO,WAAW,EAAE;YACpC,KAAK;gBACJ5B,KAAK,cAAc,SAASC,UAAU;oBACrCA,WAAW,gBAAgB,CAACpG,GAAG,CAAC,CAAC,UAAU,MAAMA,GAAG,CAAC,CAAC,SAAS;wBAC9D,SAAS;oBACV;gBACD;gBACA,OAAO;YACR,KAAK;gBACJH,GAAG,iBAAiB;gBACpB;YACD;gBACCA,GAAG,kBAAkB;gBACrB;QACF;QAEA,OAAO;IACR;IAEAyI,oBAAoB,SAApBA;QACC,IAAIzI,KAAK,IAAI;QACb,IAAIkI,SAASlI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI;QAEjC,IAAI,CAACA,GAAG,gBAAgB,IACvBA,GAAG,gBAAgB;QAEpB,IAAIkI,OAAO,SAAS,KAAK,GAAG;YAC3BG,OAAO,IAAI,CAACb,UACT,gEACE,sBACA,qCACAU,OAAO,MAAM,GAAG,iBAChBA,OAAO,UAAU,GAAG,wBAAwB;QAClD,OAAO;YACNG,OAAO,IAAI,CAACb,UACT,gEACE,sBACA,yCAAyCU,OAAO,UAAU,GAAG,wBAAwB;QAC3F;QAEA,OAAO;IACR;IAEAQ,yBAAyB,SAAzBA;QACC,IAAI1I,KAAK,IAAI;QACb,IAAIkI,SAASlI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI;QACjC,IAAIoG,OAAO;QAEX,IAAI,CAACpG,GAAG,gBAAgB,IACvBA,GAAG,gBAAgB;QAEpB+B,+DAAuB,CAAC,4DAA4D;YACnFqE,MAAAA;YACA5D,UAAU,SAAVA,SAAmBC,QAAQ;gBAC1B4F,OAAO,IAAI,CAACb,UACT,oEACE,sBACA,yCAAyCU,OAAO,UAAU,GAC1D,sCAAsCA,OAAO,UAAU,GAAGzF,SAAS,YAAY,GAC/E,aAAaZ,+DAAuB,IAAI;YAC9C;QACD;IACD;IAEA8G,YAAY,SAAZA;QACC,IAAI3I,KAAK,IAAI;QACb,IAAI4I;QAEJ,IAAI5I,GAAG,KAAK,CAAC,SAAS,EAAE;YACvB4I,4BAAe,2DAAC3B,qGAAWA;gBAAC,KAAK,gBAAgBjH,GAAG,KAAK,CAAC,GAAG;gBAC5D,SAASA,GAAG,eAAe;;QAE7B,OAAO;YACN4I,4BAAe,2DAAC3B,qGAAWA;gBAAC,KAAK,gBAAgBjH,GAAG,KAAK,CAAC,GAAG;gBAC5D,UAAUA,GAAG,eAAe;;QAE9B;QAEA,OACC4I;IAEF;IAEAC,iBAAiB,SAAjBA;QACC,IAAI7I,KAAK,IAAI;QACb,IAAIkI,SAASlI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI;QACjC,IAAI8I,eAAe,EAAE;QAErBA,aAAa,IAAI,eAAC,2DAACvJ,2FAAMA;YACxB,IAAI,UAAUS,GAAG,KAAK,CAAC,GAAG,GAAG,MAAMkI,OAAO,OAAO;YACjD,KAAK,UAAUlI,GAAG,KAAK,CAAC,GAAG,GAAG,MAAMkI,OAAO,OAAO;YAClD,MAAMlI,GAAG,KAAK,CAAC,iBAAiB;YAChC,MAAM;YACN,SAASA,GAAG,gBAAgB;YAC5B,SAASG,GAAG,CAAC,CAAC;;QAEf2I,aAAa,IAAI,eAAC,2DAACvJ,2FAAMA;YACxB,IAAI,UAAUS,GAAG,KAAK,CAAC,GAAG,GAAG,MAAMkI,OAAO,OAAO;YACjD,KAAK,UAAUlI,GAAG,KAAK,CAAC,GAAG,GAAG,MAAMkI,OAAO,OAAO;YAClD,MAAMlI,GAAG,KAAK,CAAC,iBAAiB;YAChC,MAAM;YACN,SAASA,GAAG,aAAa;YACzB,SAASG,GAAG,CAAC,CAAC;;QAEf2I,aAAa,IAAI,eAAC,2DAACvJ,2FAAMA;YACxB,IAAI,UAAUS,GAAG,KAAK,CAAC,GAAG,GAAG,MAAMkI,OAAO,OAAO;YACjD,KAAK,UAAUlI,GAAG,KAAK,CAAC,GAAG,GAAG,MAAMkI,OAAO,OAAO;YAClD,MAAMlI,GAAG,KAAK,CAAC,iBAAiB;YAChC,MAAM;YACN,SAASA,GAAG,uBAAuB;YACnC,SAASG,GAAG,CAAC,CAAC;;QAEf,OAAQ2I;IACT;IAEAC,kBAAkB,SAAlBA;QACC,IAAI/I,KAAK,IAAI;QACb,IAAIkI,SAASlI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI;QAEjC,IAAIkI,OAAO,KAAK,KAAK,MAAM;YAC1B,OAAO;QACR;QAEA,IAAIA,OAAO,KAAK,IAAKA,OAAO,KAAK,CAAC,MAAM,GAAGlI,GAAG,KAAK,CAAC,cAAc,EAAG;YACpE,OAAOkI,OAAO,KAAK,CAAC,MAAM,CAAC,GAAGlI,GAAG,KAAK,CAAC,cAAc,IAAI;QAC1D;QAEA,OAAOkI,OAAO,KAAK;IACpB;IAEAc,mBAAmB,SAAnBA;QACC,IAAIhJ,KAAK,IAAI;QAEbA,GAAG,QAAQ,CAAC;YAAE,mBAAmB;QAAK;IACvC;IAEAiJ,oBAAoB,SAApBA;QACC,IAAIjJ,KAAK,IAAI;QAEbA,GAAG,QAAQ,CAAC;YAAE,mBAAmB;QAAM;IACxC;IAEAkJ,gBAAgB,SAAhBA,eAAyBhI,KAAK;QAC7B,IAAIlB,KAAK,IAAI;QACb,IAAIkI,SAASlI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI;QACjC,IAAImJ,eAAgBjB,OAAO,SAAS,KAAK;QACzC,IAAIkB,eAAgBlB,OAAO,QAAQ,KAAK;QACxC,IAAImB,UAAU,IAAIC,KAAKpB,OAAO,QAAQ;QACtC,IAAIqB,iBAAiBF,QAAQ,iBAAiB,MAAM,CAAC,MAAMA,QAAQ,iBAAiB,KAAK;QACzF,IAAIG,OAAO,IAAIrC,yDAAaA,GAAG,MAAM,CAACkC,QAAQ,OAAO,KAAKI,KAAK,GAAG,CAACF,iBAAiB;QAEpF,IAAIJ,gBAAgBC,cAAc;YACjC,OAAQ;QACT;QAEA,IAAID,cAAc;YACjB,qBAAQ,2DAAC;gBAAI,OAAOjI;6BACnB,2DAAC;gBAAK,WAAW;eAAmBf,GAAG,CAAC,CAAC,UAAU,oBACnD,2DAAC;gBAAK,WAAW;eAAgBqJ;QAEnC;QAEA,IAAIJ,cAAc;YACjB,qBAAQ,2DAAC;gBAAI,OAAOlI;6BACnB,2DAAC;gBAAK,WAAW;eAAmBf,GAAG,CAAC,CAAC,UAAU,oBACnD,2DAAC;gBAAK,WAAW;eAAgB+H,OAAO,SAAS;QAEnD;QAEA,qBAAQ,2DAAC;YAAI,OAAOhH;yBACnB,2DAAC;YAAK,WAAW;WAAmBf,GAAG,CAAC,CAAC,UAAU,oBACnD,2DAAC;YAAK,WAAW;WAAgBqJ,qBACjC,2DAAC;YAAK,WAAW;WAAmB,MAAMrJ,GAAG,CAAC,CAAC,UAAU,oBACzD,2DAAC;YAAK,WAAW;WAAgB+H,OAAO,SAAS;IAEnD;IAEAnH,QAAQ,SAARA;QACC,IAAIf,KAAK,IAAI,EAAEkI,SAASlI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI;QAE5C,IAAI0J,mBAAmB;YACtB,UAAU1J,GAAG,KAAK,CAAC,aAAa,GAAG;YACnC,YAAY;YACZ,YAAY;YACZ,YAAYA,GAAG,KAAK,CAAC,aAAa,GAAG;QACtC;QAEA,IAAI2J,qBAAqB;YACxB,YAAY3J,GAAG,KAAK,CAAC,aAAa,GAAG;YACrC,UAAUA,GAAG,KAAK,CAAC,iBAAiB,GAAG;QACxC;QAEA,IAAI4J,cAAc5J,GAAG,cAAc,CAAC2J;QAEpC,qBAAQ,2DAAC,2BACR,2DAACrC,wGAAaA;YACb,MAAMtH,GAAG,KAAK,CAAC,iBAAiB;YAChC,OAAOG,GAAG,CAAC,CAAC;YACZ,MAAMA,GAAG,CAAC,CAAC;YACX,SAASmH,gHAAqB;YAC9B,SAAStH,GAAG,kBAAkB;YAC9B,MAAMA,GAAG,kBAAkB;YAC3B,UAAUA,GAAG,kBAAkB;YAC/B,MAAM;0BACP,2DAAC2D,uGAAGA;YAAC,WAAW;YAAkB,KAAK,SAASuE,OAAO,OAAO,GAAG,MAAMlI,GAAG,KAAK,CAAC,GAAG;YAClF,SAASA,GAAG,UAAU;yBACtB,2DAAC4D,mFAAGA;YAAC,IAAI5D,GAAG,KAAK,CAAC,SAAS;YAC1B,IAAIA,GAAG,KAAK,CAAC,SAAS;YACtB,IAAIA,GAAG,KAAK,CAAC,SAAS;YACtB,IAAIA,GAAG,KAAK,CAAC,SAAS;YACtB,OAAO;gBAAE,eAAe;gBAAI,YAAY;YAAG;yBAC3C,2DAACgH,wFAAKA;YACL,KAAKQ,UAAU,uCAAuCxH,GAAG,KAAK,CAAC,QAAQ,GAAG;2BAG5E,2DAAC4D,mFAAGA;YAAC,IAAI5D,GAAG,KAAK,CAAC,SAAS;YAC1B,IAAIA,GAAG,KAAK,CAAC,SAAS;YACtB,IAAIA,GAAG,KAAK,CAAC,SAAS;YACtB,IAAIA,GAAG,KAAK,CAAC,SAAS;YACtB,OAAO;gBAAE,eAAe;gBAAI,YAAa4J,gBAAgB,KAAM,KAAK;YAAG;yBACvE,2DAACxC,2FAAcA;YACd,WAAW;YACX,uBAAU,2DAACC,qFAAOA;gBAAC,IAAI,YAAYa,OAAO,OAAO;eAAGA,OAAO,KAAK;yBAChE,2DAAChB,uFAAIA;YACJ,SAASlH,GAAG,aAAa;YACzB,OAAO0J;WAAmB1J,GAAG,gBAAgB,oBAE/C,2DAAC;YAAI,IAAI,SAASkI,OAAO,OAAO,GAAG,MAAMlI,GAAG,KAAK,CAAC,GAAG,GAAG;YAAO,OAAO2J;yBACrE,2DAAC;YAAK,WAAW;WAAiBxJ,GAAG,CAAC,CAAC,UAAU,sBACjD,2DAAC;YAAK,WAAW;WAAmB+H,OAAO,UAAU,IAErD0B;IAIL;;AACD,EAAE,EAAC;AAEH1K,mBAAOA,CAAC,8DAA4B;;;;;;;;;;;;;;;;;;;;;;;;AChXpC,MAA8G;AAC9G,MAAoG;AACpG,MAA2G;AAC3G,MAA8H;AAC9H,MAAuH;AACvH,MAAuH;AACvH,MAAmJ;AACnJ;AACA;;AAEA;;AAEA,4BAA4B,qGAAmB;AAC/C,wBAAwB,kHAAa;AACrC,iBAAiB,uGAAa;AAC9B,iBAAiB,+FAAM;AACvB,6BAA6B,sGAAkB;;AAE/C,aAAa,0GAAG,CAAC,mHAAO;;;;AAI6F;AACrH,OAAO,6DAAe,mHAAO,IAAI,0HAAc,GAAG,0HAAc,YAAY,EAAC;;;;;;;;;;;;;;;;;;;;;;;;ACvB7E,MAAwG;AACxG,MAA8F;AAC9F,MAAqG;AACrG,MAAwH;AACxH,MAAiH;AACjH,MAAiH;AACjH,MAA+I;AAC/I;AACA;;AAEA;;AAEA,4BAA4B,qGAAmB;AAC/C,wBAAwB,kHAAa;AACrC,iBAAiB,uGAAa;AAC9B,iBAAiB,+FAAM;AACvB,6BAA6B,sGAAkB;;AAE/C,aAAa,0GAAG,CAAC,qHAAO;;;;AAIyF;AACjH,OAAO,6DAAe,qHAAO,IAAI,4HAAc,GAAG,4HAAc,YAAY,EAAC;;;;;;;;;;;;;;;;;;;;;;;;ACvB7E,MAA8G;AAC9G,MAAoG;AACpG,MAA2G;AAC3G,MAA8H;AAC9H,MAAuH;AACvH,MAAuH;AACvH,MAA2O;AAC3O;AACA;;AAEA;;AAEA,4BAA4B,qGAAmB;AAC/C,wBAAwB,kHAAa;AACrC,iBAAiB,uGAAa;AAC9B,iBAAiB,+FAAM;AACvB,6BAA6B,sGAAkB;;AAE/C,aAAa,0GAAG,CAAC,qLAAO;;;;AAIqL;AAC7M,OAAO,6DAAe,qLAAO,IAAI,4LAAc,GAAG,4LAAc,YAAY,EAAC"}