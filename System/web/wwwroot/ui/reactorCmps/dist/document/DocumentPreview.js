define(["tokens!reactorCmps/tokens/general","when","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","WorkspaceInfo","SG2/collection/Factory","create-react-class","Connector","suite-storage"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_when__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_WorkspaceInfo__, __WEBPACK_EXTERNAL_MODULE_SG2_collection_Factory__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEBPACK_EXTERNAL_MODULE_suite_storage__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["document/DocumentPreview"], {
"./src/document/collection/PreviewDocumentCollection.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: 'cddocument',
        proxyCfg: {
            route: '/document/request/previewWidgetDocuments.php'
        }
    },
    collection: {
        defaultOrder: {
            nmfile: 'ASC'
        },
        quantity: 25
    }
});


}),
"./src/document/collection/PreviewDocumentFileCollection.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: 'cdfile',
        proxyCfg: {
            route: '/document/request/previewWidgetFiles.php'
        }
    },
    collection: {
        defaultOrder: {
            nmfile: 'ASC'
        }
    }
});


}),
"./src/document/collection/PreviewWidgetDataCollection.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: 'oid',
        proxyCfg: {
            route: '/document/request/previewWidgetData.php'
        }
    },
    collection: {
        defaultOrder: {
            nmfile: 'ASC'
        }
    }
});


}),
"./src/document/components/DocumentPreview.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
__webpack_require__(/*! core-js/modules/es.object.keys.js */ "../node_modules/core-js/modules/es.object.keys.js");
__webpack_require__(/*! core-js/modules/es.array.sort.js */ "../node_modules/core-js/modules/es.array.sort.js");
__webpack_require__(/*! core-js/modules/es.string.search.js */ "../node_modules/core-js/modules/es.string.search.js");
__webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
__webpack_require__(/*! core-js/modules/web.btoa.js */ "../node_modules/core-js/modules/web.btoa.js");
__webpack_require__(/*! core-js/modules/web.dom-exception.constructor.js */ "../node_modules/core-js/modules/web.dom-exception.constructor.js");
__webpack_require__(/*! core-js/modules/web.dom-exception.stack.js */ "../node_modules/core-js/modules/web.dom-exception.stack.js");
__webpack_require__(/*! core-js/modules/web.dom-exception.to-string-tag.js */ "../node_modules/core-js/modules/web.dom-exception.to-string-tag.js");
__webpack_require__(/*! core-js/modules/es.error.to-string.js */ "../node_modules/core-js/modules/es.error.to-string.js");
__webpack_require__(/*! core-js/modules/web.timers.js */ "../node_modules/core-js/modules/web.timers.js");
__webpack_require__(/*! core-js/modules/es.parse-int.js */ "../node_modules/core-js/modules/es.parse-int.js");
__webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
var React = __webpack_require__(/*! react */ "react");
var Connector = __webpack_require__(/*! Connector */ "Connector");
var ReactDOM = __webpack_require__(/*! react-dom */ "react-dom");
var Modal = __webpack_require__(/*! reactor/src/Atomic/components/Orgs/Modal/Modal */ "../reactor/src/Atomic/components/Orgs/Modal/Modal.jsx");
var Lookup = __webpack_require__(/*! reactor/src/Form/components/Mols/Lookup */ "../reactor/src/Form/components/Mols/Lookup.jsx");
var ComboBox = __webpack_require__(/*! reactor/src/Form/components/Mols/ComboBox */ "../reactor/src/Form/components/Mols/ComboBox.jsx");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var Button = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Button/Button.jsx */ "../reactor/src/Atomic/components/Atoms/Button/Button.jsx");
var DocumentPreviewWebviewer = __webpack_require__(/*! reactorCmps/src/document/components/DocumentPreviewWebviewer.jsx */ "./src/document/components/DocumentPreviewWebviewer.jsx");
// Helpers
var when = __webpack_require__(/*! when */ "when");
// Utilities from the workspace project
var workspaceInfo = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
var Utils = __webpack_require__(/*! Utils */ "Utils");
// Collections
var DocumentCollection = __webpack_require__(/*! reactorCmps/src/document/collection/PreviewDocumentCollection */ "./src/document/collection/PreviewDocumentCollection.js");
var FileCollection = __webpack_require__(/*! reactorCmps/src/document/collection/PreviewDocumentFileCollection */ "./src/document/collection/PreviewDocumentFileCollection.js");
var WidgetCollection = __webpack_require__(/*! reactorCmps/src/document/collection/PreviewWidgetDataCollection */ "./src/document/collection/PreviewWidgetDataCollection.js");
/* global gn_tooltip */ // Para que o JSLint entenda que a função existe
curl(Utils.getBaseclassUrl() + '/generic/common/js/gn_tooltip.js');
module.exports = createReactClass({
    displayName: 'DocumentPreview',
    getDefaultProps: function() {
        return {
            DocumentCollection: new DocumentCollection(),
            FileCollection: new FileCollection(),
            WidgetCollection: new WidgetCollection()
        };
    },
    getInitialState: function getInitialState() {
        var me = this;
        var documentData = null;
        var promise = me.getDocuments();
        me.isCancelled = false;
        promise.then(function(value) {
            var documents = value.map(function(obj) {
                return Object.keys(obj).sort().map(function(key) {
                    return obj[key];
                });
            });
            documentData = documents;
        });
        return {
            showModal: false,
            documentData: documentData,
            filesData: [],
            chosenDocumentCode: -1,
            loading: true,
            triggerReload: false
        };
    },
    UNSAFE_componentWillMount: function UNSAFE_componentWillMount() {
        var me = this;
        me.setDocumentLicensePermission();
        window.globalVar = function() {};
        window.globalVar.callback = function(cdfile, nmfile) {
            me.setState({
                tooltipCdFile: cdfile,
                tooltipNmFile: nmfile,
                triggerReload: !me.state.triggerReload
            });
        };
    },
    componentDidMount: function componentDidMount() {
        var me = this;
        me.getConfiguredFiles();
    },
    componentWillUnmount: function() {
        var me = this;
        me.isCancelled = true;
    },
    getConfiguredFiles: function getConfiguredFiles() {
        var me = this;
        var promiseThatBringsWidgetData = me.getWidgetData();
        var promiseThatBringsFileData = null;
        var loadedLookupValue = [];
        var loadedComboBoxValue = {};
        var counter = 0;
        var node = '';
        promiseThatBringsWidgetData.then(function(widgetData) {
            if (widgetData.length > 0) {
                promiseThatBringsFileData = me.getFiles(widgetData[0].cddocument);
                me.props.setTitle(widgetData[0].title + ' - ' + widgetData[0].nmfile);
                loadedLookupValue.push({
                    oid: widgetData[0].cddocument,
                    cddocument: widgetData[0].cddocument,
                    iddocument: widgetData[0].iddocument,
                    title: widgetData[0].title
                });
                promiseThatBringsFileData.then(function(FileData) {
                    for(counter = 0; counter < FileData.length; counter++){
                        if (FileData[counter].nmfile === widgetData[0].nmfile) {
                            loadedComboBoxValue.oid = FileData[counter].oid;
                            loadedComboBoxValue.nmfile = FileData[counter].nmfile;
                            loadedComboBoxValue.cdfile = FileData[counter].cdfile;
                            break;
                        }
                    }
                    if (FileData.length > 0) {
                        !me.isCancelled && me.setState({
                            filesData: FileData,
                            documentLookupValue: loadedLookupValue,
                            loadedLookupValue: loadedLookupValue,
                            fileComboBoxValue: loadedComboBoxValue,
                            loadedComboBoxValue: loadedComboBoxValue,
                            loadedNmFile: widgetData[0].nmfile,
                            hasLoadedConfig: true,
                            chosenDocumentCode: widgetData[0].cddocument,
                            loadedDocumentCode: widgetData[0].cddocument,
                            chosenDocumentId: widgetData[0].iddocument,
                            cdfile: widgetData[0].cdfile,
                            loading: false
                        });
                    }
                });
            } else if (me.props.cardData.inEditModeCallback()) {
                if (document.getElementById('ModalBodyDiv' + me.props.oid)) {
                    node = ReactDOM.findDOMNode(document.getElementById('ModalBodyDiv' + me.props.oid));
                    node.closest('#rctModal').remove();
                }
                !me.isCancelled && me.setState({
                    loading: false,
                    showModal: true
                });
            } else {
                !me.isCancelled && me.setState({
                    loading: false
                });
            }
        });
    },
    getWidgetData: function getWidgetData() {
        var deferred = when.defer();
        var me = this;
        me.props.WidgetCollection.fetch({
            oid: me.props.oid
        }).then(function(results) {
            deferred.resolve(results);
        }, deferred.reject);
        return deferred.promise;
    },
    onDocumentLookupValueChange: function onDocumentLookupValueChange(value) {
        var me = this;
        var promise = null;
        // Seta o state/estado com o códigos do documento
        if (value.length > 0) {
            promise = me.getFiles(value[0].oid);
            // Promise que retorna os valores do arquivo do documento
            promise.then(function(innerValue) {
                !me.isCancelled && me.setState({
                    chosenDocumentCode: value[0].oid,
                    filesData: innerValue,
                    documentLookupValue: value,
                    fileComboBoxValue: ''
                });
            });
        } else {
            !me.isCancelled && me.setState({
                chosenDocumentCode: '',
                documentLookupValue: value,
                fileComboBoxValue: value
            });
        }
        return value;
    },
    hideModal: function hideModal() {
        var me = this;
        if (!me.state.hasLoadedConfig) {
            me.setState({
                showModal: false,
                documentLookupValue: [],
                chosenDocumentCode: '',
                fileComboBoxValue: []
            });
        } else {
            me.setState({
                showModal: false
            });
        }
    },
    showModal: function showModal() {
        var me = this;
        me.setState({
            showModal: true
        });
    },
    openModal: function openModal() {
        var me = this;
        if (me.state.hasLoadedConfig) {
            me.setState({
                fileComboBoxValue: me.state.loadedComboBoxValue,
                chosenDocumentCode: me.state.loadedDocumentCode,
                documentLookupValue: me.state.loadedLookupValue
            });
        } else {
            me.setState({
                documentLookupValue: [],
                chosenDocumentCode: '',
                fileComboBoxValue: []
            });
        }
        me.showModal();
    },
    searchDocumentFunction: function searchDocumentFunction(term, page) {
        var deferred = when.defer();
        var me = this;
        me.props.DocumentCollection.search({
            iddocument: term,
            cduser: workspaceInfo.getCDUser()
        }, page).then(function() {
            deferred.resolve({
                data: me.props.DocumentCollection.getAttributes(),
                total: me.props.DocumentCollection.getState().size
            });
        }, deferred.reject);
        return deferred.promise;
    },
    getDocuments: function getDocuments() {
        var deferred = when.defer();
        var me = this;
        me.props.DocumentCollection.fetch({
            cduser: workspaceInfo.getCDUser()
        }).then(function(results) {
            deferred.resolve(results);
        }, deferred.reject);
        return deferred.promise;
    },
    getFiles: function getFiles(cdDocument) {
        var deferred = when.defer();
        var me = this;
        me.props.FileCollection.fetch({
            cddocument: cdDocument
        }).then(function(results) {
            deferred.resolve(results);
        }, deferred.reject);
        return deferred.promise;
    },
    checkFieldsAndSave: function checkFieldsAndSave() {
        var me = this;
        var data = null;
        if (!me.state.documentLookupValue || me.state.documentLookupValue.length === 0 || !me.state.fileComboBoxValue) {
            curl('MessageBar', function(MessageBar) {
                MessageBar.showMessageError(SE.t(113870), {
                    timeOut: 3000
                });
            });
            return;
        }
        data = {
            cddocument: me.state.chosenDocumentCode,
            nmfile: me.state.fileComboBoxValue.nmfile,
            cdfile: me.state.fileComboBoxValue.cdfile,
            addParam: true,
            oid: me.props.oid
        };
        Connector.callBaseclass('/document/request/ajaxPreviewWidget.php', {
            dataType: 'json',
            data: data,
            success: function success() {
                me.getConfiguredFiles();
                me.hideModal();
            }
        });
    },
    openToolTip: /* istanbul ignore next */ function openToolTip() {
        var me = this;
        var Gn_tooltip = gn_tooltip;
        var tooltip = new Gn_tooltip(Utils.getBaseclassUrl() + '/');
        var place = null;
        var documentcomplexfilecont = null;
        var clickFunction = null;
        var base64ClickFunction = null;
        // Necessário para que o botão do tooltip não desapareça
        document.getElementById('fileSelection' + me.props.oid).style.visibility = 'visible';
        function showToolTip(elmQtip, codes, link) {
            tooltip.setPosition('bottom center,top center');
            tooltip.setTitle(SE.t(111798));
            // parametros para o ajax
            tooltip.setAjax(Utils.getBaseclassUrl() + '/document/dc_document/document_multfile_ajax.php', {
                cdprod: 21,
                codes: codes,
                link: link,
                path: Utils.getBaseclassUrl() + '/'
            });
            tooltip.show(elmQtip);
        }
        // Imagem onde renderizará o tooltip
        place = document.getElementById('fileSelection' + me.props.oid);
        // Código do documento
        documentcomplexfilecont = me.state.filesData[0].cdcomplexfilecont;
        // Função do clique do mouse
        clickFunction = 'globalVar.callback(cdfile, this.innerText);';
        // Link encodado em 64 da função.
        base64ClickFunction = btoa(clickFunction);
        showToolTip(place, documentcomplexfilecont, base64ClickFunction);
        setTimeout(function() {
            // Adiciona um eventListener para que o botão que habilita o tooltip desapareça ao fechar o tooltip.  
            var el = document.getElementsByClassName('qtip-close')[0];
            el.addEventListener('click', function() {
                document.getElementById('fileSelection' + me.props.oid).style.visibility = '';
            }, false);
        }, 500);
    },
    getWebviewer: function getWebviewer() {
        var me = this;
        var webviewerComponent = null;
        var fileName = '';
        var cdFile = -1;
        var loading = me.state.loading;
        var noLicense = me.state.documentAccess === 'false';
        if (me.state.tooltipNmFile) {
            fileName = me.state.tooltipNmFile;
            cdFile = me.state.tooltipCdFile;
        } else if (me.state.fileComboBoxValue) {
            fileName = me.state.fileComboBoxValue.nmfile;
            cdFile = me.state.fileComboBoxValue.cdfile;
        } else if (me.state.loadedDocumentCode && me.state.filesData.length === 0) {
            fileName = me.state.loadedNmFile;
            cdFile = me.state.loadedComboBoxValue.cdfile;
        }
        if (me.state.hasLoadedConfig || me.state.filesData.length !== 0) {
            webviewerComponent = /*#__PURE__*/ React.createElement(DocumentPreviewWebviewer, {
                ref: 'documentPreviewWebviewer' + me.props.oid,
                oid: me.props.oid,
                filesData: me.state.filesData,
                fileName: fileName,
                cdFile: cdFile,
                loading: loading,
                noLicense: noLicense,
                documentCode: parseInt(me.state.loadedDocumentCode, 10) || parseInt(me.state.chosenDocumentCode, 10)
            });
        } else {
            webviewerComponent = /*#__PURE__*/ React.createElement(DocumentPreviewWebviewer, {
                ref: 'documentPreviewWebviewer' + me.props.oid,
                oid: me.props.oid,
                filesData: me.state.filesData,
                fileName: fileName,
                cdFile: cdFile,
                noLicense: noLicense,
                documentCode: parseInt(me.state.loadedDocumentCode, 10) || parseInt(me.state.chosenDocumentCode, 10),
                hasNotBeenConfigured: true
            });
        }
        return webviewerComponent;
    },
    setFileComboBoxValue: function setFileComboBoxValue(value) {
        var me = this;
        me.setState({
            fileComboBoxValue: value
        });
    },
    getComboBox: function getComboBox() {
        var me = this;
        var chosenDocumentCode = me.state.chosenDocumentCode;
        var fileComboBox = null;
        if (!chosenDocumentCode) return null;
        if (chosenDocumentCode > 0 && chosenDocumentCode !== -1) {
            fileComboBox = /*#__PURE__*/ React.createElement(ComboBox, {
                title: SE.t('104657'),
                ref: 'fileComboBox' + me.props.oid,
                data: me.state.filesData,
                required: true,
                requiredIndicator: true,
                valueField: 'cdfile',
                textField: 'nmfile',
                filter: 'contains',
                value: me.state.fileComboBoxValue,
                onChange: function(value) {
                    return me.setFileComboBoxValue(value);
                }
            });
        }
        return fileComboBox;
    },
    setDocumentLicensePermission: function setDocumentLicensePermission() {
        Connector.callBaseclass('document/request/licenseAccess.php', {
            method: 'POST',
            data: {
                checkDocumentAccess: 'true'
            },
            success: (function(response) {
                var answer = JSON.parse(response);
                this.setState({
                    documentAccess: answer
                });
            }).bind(this),
            error: function error() {}
        });
    },
    render: function render() {
        var me = this;
        return /*#__PURE__*/ React.createElement("div", {
            id: 'DocumentPreviewDiv' + me.props.oid,
            style: {
                width: '100%',
                height: '100%'
            }
        }, me.getWebviewer(), /*#__PURE__*/ React.createElement(Modal, {
            key: 'Modal' + me.props.oid,
            width: 430,
            height: 430,
            show: me.state.showModal && parseInt(me.props.oid, 10) !== -1,
            onClose: me.hideModal,
            ref: 'documentSelectionModal' + me.props.oid,
            hasCloseBtn: true,
            title: SE.t('200960'),
            Body: /*#__PURE__*/ React.createElement("div", {
                id: 'ModalBodyDiv' + me.props.oid,
                style: {
                    marginBottom: '15px'
                }
            }, /*#__PURE__*/ React.createElement(Lookup, {
                style: {
                    marginBottom: '10px'
                },
                ref: 'documentLookup' + me.props.oid,
                limit: 1,
                required: true,
                requiredIndicator: true,
                data: me.state.documentData,
                title: SE.t('208351'),
                textField: 'title',
                valueField: 'cddocument',
                searchFn: me.searchDocumentFunction,
                value: me.state.documentLookupValue || [],
                onChange: function(value) {
                    return me.onDocumentLookupValueChange(value);
                }
            }), me.getComboBox()),
            Footer: [
                /*#__PURE__*/ React.createElement(Button, {
                    key: 'saveButton',
                    color: 'primary',
                    text: SE.t(100299),
                    id: 'saveAndExit',
                    onClick: me.checkFieldsAndSave
                })
            ]
        }));
    }
});
__webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");


}),
"./src/document/components/DocumentPreviewWebviewer.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
__webpack_require__(/*! core-js/modules/es.string.substr.js */ "../node_modules/core-js/modules/es.string.substr.js");
__webpack_require__(/*! core-js/modules/es.array.last-index-of.js */ "../node_modules/core-js/modules/es.array.last-index-of.js");
__webpack_require__(/*! core-js/modules/es.array.index-of.js */ "../node_modules/core-js/modules/es.array.index-of.js");
var React = __webpack_require__(/*! react */ "react");
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var Utils = __webpack_require__(/*! Utils */ "Utils");
var when = __webpack_require__(/*! when */ "when");
var WorkspaceInfo = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
var Connector = __webpack_require__(/*! Connector */ "Connector");
module.exports = createReactClass({
    displayName: 'DocumentPreviewWebviewer',
    propTypes: {
        documentCode: PropTypes.number,
        filesData: PropTypes.array,
        loading: PropTypes.bool,
        hasNotBeenConfigured: PropTypes.bool,
        fileName: PropTypes.string,
        cdFile: PropTypes.number,
        oid: PropTypes.string,
        fgLanguage: PropTypes.number
    },
    getInitialState: function getInitialState() {
        var me = this;
        me.isCancelled = false;
        return {
            loading: true
        };
    },
    componentDidMount: function componentDidMount() {
        var me = this;
        me.adaptToPermission();
    },
    componentWillUnmount: function() {
        var me = this;
        me.isCancelled = true;
    },
    adaptToPermission: function adaptToPermission() {
        var me = this;
        var permissionPromise = me.checkPermissionsAndFiles();
        permissionPromise.then(function(value) {
            if (value.permissionDenied) {
                !me.isCancelled && me.setState({
                    loading: false,
                    permissionDenied: true
                });
            } else {
                !me.isCancelled && me.setState({
                    loading: false
                });
            }
        });
    },
    checkPermissionsAndFiles: function checkPermissionsAndFiles() {
        var deferred = when.defer();
        var me = this;
        var data = {
            cduser: WorkspaceInfo.getCDUser(),
            oid: me.props.oid,
            checkFileParam: true
        };
        Connector.callBaseclass('/document/request/ajaxPreviewWidget.php', {
            dataType: 'json',
            data: data,
            success: function success(value) {
                deferred.resolve(value);
            }
        });
        return deferred.promise;
    },
    render: function render() {
        var me = this;
        var src = '';
        var srcParams = '';
        var fgpdfconverted = '';
        var nmfile = '';
        var extension = '';
        var counter = 0;
        var foundFileNameMatch = false;
        var acceptedExtensions = [
            '.pdf',
            '.png',
            '.jpg',
            '.jpeg',
            '.docx',
            '.xlsx',
            '.pptx'
        ];
        if (me.state.loading) {
            src = Utils.getBaseclassUrl() + '/object/common/loading.php';
        } else if (me.props.noLicense) {
            src = Utils.getBaseclassUrl() + '/document/dc_view_document/keylock.php?termo=' + SE.t(308860);
        } else if (me.state.permissionDenied) {
            src = Utils.getBaseclassUrl() + '/document/dc_view_document/keylock.php?termo=' + SE.t(104601);
        } else if (me.props.hasNotBeenConfigured) {
            src = Utils.getBaseclassUrl() + '/document/dc_view_document/keylock.php?termo=' + SE.t(220465);
        } else if (me.props.loading || me.props.filesData.length === 0) {
            src = Utils.getBaseclassUrl() + '/object/common/loading.php';
        } else {
            for(counter = 0; counter < me.props.filesData.length; counter++){
                if (me.props.filesData[counter].nmfile === me.props.fileName && me.props.filesData[counter].cdfile === me.props.cdFile) {
                    foundFileNameMatch = true;
                    break;
                }
            }
            if (!foundFileNameMatch) {
                src = Utils.getBaseclassUrl() + '/document/dc_view_document/keylock.php?termo=' + SE.t(220465);
                return /*#__PURE__*/ React.createElement("div", {
                    style: {
                        width: '100%',
                        height: '100%'
                    }
                }, /*#__PURE__*/ React.createElement("iframe", {
                    id: 'webviewerIFrame' + me.props.oid,
                    style: {
                        width: '100%',
                        height: '100%',
                        border: '1px solid #CCC',
                        borderRadius: '5px',
                        overflow: 'hidden'
                    },
                    src: src
                }));
            }
            fgpdfconverted = me.props.filesData && me.props.filesData[counter].fgpdfconverted ? '&pdfconverted=' + me.props.filesData[counter].fgpdfconverted : '';
            nmfile = me.props.filesData && me.props.filesData[counter].nmfile ? me.props.filesData[counter].nmfile : '';
            extension = nmfile.substr(nmfile.lastIndexOf('.'));
            extension = extension.toLowerCase();
            if (acceptedExtensions.indexOf(extension) === -1 && me.props.filesData[counter].fgpdfconverted !== 1 && me.props.filesData[counter].fgpdfconverted !== 5 && me.props.filesData[counter].fgpdfconverted !== true) {
                src = Utils.getBaseclassUrl() + '/document/dc_view_document/keylock.php?termo=' + SE.t(215529);
            } else {
                var _me_props;
                src = Utils.getBaseclassUrl() + '/document/webviewer/webviewer.php';
                srcParams = '?cddocument=' + me.props.documentCode;
                srcParams += '&cdcomplexfilecont=' + me.props.filesData[counter].cdcomplexfilecont;
                srcParams += '&cdfile=' + me.props.filesData[counter].cdfile;
                srcParams += '&cdeletronicfilecfg=' + me.props.filesData[counter].cdeletronicfilecfg;
                srcParams += '&fgstatus=' + me.props.filesData[counter].fgstatus;
                srcParams += '&iframe=frviewfile_preview';
                srcParams += '&isDrawer=' + ((_me_props = me.props) === null || _me_props === void 0 ? void 0 : _me_props.isDrawer);
                srcParams += '&hideLanguageSelector=1';
                srcParams += fgpdfconverted;
                if (me.props.noHeader) {
                    srcParams += '&noHeader=true';
                }
            }
            if (this.props.fgLanguage !== 0) {
                srcParams += '&fglanguage=' + me.props.fgLanguage;
            }
        }
        return /*#__PURE__*/ React.createElement("div", {
            style: {
                width: '100%',
                height: '100%'
            }
        }, /*#__PURE__*/ React.createElement("iframe", {
            id: 'webviewerIFrame' + me.props.oid,
            style: {
                width: '100%',
                height: '100%',
                border: '1px solid #CCC',
                borderRadius: '5px',
                overflow: 'hidden'
            },
            src: src + srcParams
        }));
    }
});
__webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");


}),
"Connector": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"SG2/collection/Factory": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_SG2_collection_Factory__;

}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"WorkspaceInfo": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_WorkspaceInfo__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"when": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_when__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/document/components/DocumentPreview.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=DocumentPreview.js.map