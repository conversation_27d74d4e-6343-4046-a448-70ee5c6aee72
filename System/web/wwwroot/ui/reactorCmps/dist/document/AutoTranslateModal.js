"use strict";
define(["tokens!reactorCmps/tokens/general","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","WorkspaceInfo","create-react-class","Connector","suite-storage"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_WorkspaceInfo__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEB<PERSON><PERSON><PERSON>_EXTERNAL_MODULE_suite_storage__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["document/AutoTranslateModal"], {
"./src/document/components/AutoTranslation/Modal/index.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.filter.js */ "../node_modules/core-js/modules/es.array.filter.js");
/* ESM import */var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.parse-int.js */ "../node_modules/core-js/modules/es.parse-int.js");
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.includes.js */ "../node_modules/core-js/modules/es.array.includes.js");
/* ESM import */var core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.string.includes.js */ "../node_modules/core-js/modules/es.string.includes.js");
/* ESM import */var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_19__);
/* ESM import */var lodash_noop__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash/noop */ "../node_modules/lodash/noop.js");
/* ESM import */var lodash_noop__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_noop__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC */ "../reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC.js");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var reactor2_src_constants_sizeConstants__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactor2/src/constants/sizeConstants */ "../reactor2/src/constants/sizeConstants.js");
/* ESM import */var reactor2_src_Atomic_components_Orgs_Modal_Modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! reactor2/src/Atomic/components/Orgs/Modal/Modal */ "../reactor2/src/Atomic/components/Orgs/Modal/Modal.jsx");
/* ESM import */var reactor2_src_Atomic_components_Orgs_Modal_Modal__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Orgs_Modal_Modal__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Button/Button */ "../reactor2/src/Atomic/components/Atoms/Button/Button.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_13__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_CircularLoading_CircularLoading__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/CircularLoading/CircularLoading */ "../reactor2/src/Atomic/components/Helpers/CircularLoading/CircularLoading.jsx");
/* ESM import */var reactor2_src_Atomic_components_Helpers_CircularLoading_CircularLoading__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Helpers_CircularLoading_CircularLoading__WEBPACK_IMPORTED_MODULE_14__);
/* ESM import */var reactor2_src_Atomic_components_Mols_ImageTextView__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactor2/src/Atomic/components/Mols/ImageTextView */ "../reactor2/src/Atomic/components/Mols/ImageTextView.jsx");
/* ESM import */var reactor2_src_Atomic_components_Mols_ImageTextView__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Mols_ImageTextView__WEBPACK_IMPORTED_MODULE_15__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Headings_H5__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Headings/H5 */ "../reactor2/src/Atomic/components/Atoms/Headings/H5.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Headings_H5__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Headings_H5__WEBPACK_IMPORTED_MODULE_16__);
/* ESM import */var reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! reactorCmps/src/document/components/AutoTranslation/constants/steps */ "./src/document/components/AutoTranslation/constants/steps.js");
/* ESM import */var reactorCmps_src_document_components_AutoTranslation_helper_action__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! reactorCmps/src/document/components/AutoTranslation/helper/action */ "./src/document/components/AutoTranslation/helper/action.js");
function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
    try {
        var info = gen[key](arg);
        var value = info.value;
    } catch (error) {
        reject(error);
        return;
    }
    if (info.done) {
        resolve(value);
    } else {
        Promise.resolve(value).then(_next, _throw);
    }
}
function _async_to_generator(fn) {
    return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
            var gen = fn.apply(self, args);
            function _next(value) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
            }
            function _throw(err) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
            }
            _next(undefined);
        });
    };
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
function _ts_generator(thisArg, body) {
    var f, y, t, g, _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    };
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}




















var divLanguageStyle = {
    display: 'flex',
    alignItems: 'center',
    columnGap: '0.5em'
};
var AutoTranslateModal = function(param) {
    var show = param.show, disabled = param.disabled, cdFile = param.cdFile, fgLanguage = param.fgLanguage, onClose = param.onClose, getToken = param.getToken, callbackSuccess = param.callbackSuccess;
    var _useState = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(show), 2), shouldShow = _useState[0], setShouldShow = _useState[1];
    var _useState1 = _sliced_to_array((0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT_LOADING), 2), step = _useState1[0], setStep = _useState1[1];
    var setTranslationLoad = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(function() {
        return setStep(reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT_TRANSLATION);
    }, []);
    var callStepTranslation = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(function() {
        return setStep(reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT_NOT_EXISTS);
    }, []);
    var closeModal = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(function() {
        setStep(reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT_LOADING);
        setShouldShow(false);
        onClose();
    }, [
        onClose
    ]);
    var callCallback = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/ _async_to_generator(function() {
        return _ts_generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        callbackSuccess()
                    ];
                case 1:
                    _state.sent();
                    closeModal();
                    return [
                        2
                    ];
            }
        });
    }), [
        callbackSuccess,
        closeModal
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function() {
        var isMounted = true;
        /* istanbul ignore else */ if (show) {
            var isDocumentExists = /*#__PURE__*/ function() {
                var _ref = _async_to_generator(function() {
                    return _ts_generator(this, function(_state) {
                        return [
                            2,
                            (0,reactorCmps_src_document_components_AutoTranslation_helper_action__WEBPACK_IMPORTED_MODULE_18__.autoTranslateDocExists)({
                                cdFile: cdFile,
                                fgLanguage: fgLanguage,
                                okCallback: callCallback,
                                notFoundCallback: callStepTranslation,
                                errorCallback: closeModal
                            })
                        ];
                    });
                });
                return function isDocumentExists() {
                    return _ref.apply(this, arguments);
                };
            }();
            /* istanbul ignore else */ if (!Boolean(fgLanguage)) {
                return callCallback();
            }
            /* istanbul ignore else */ if (isMounted) {
                isDocumentExists();
            }
        }
        setShouldShow(show);
        return function() {
            isMounted = false;
        };
    }, [
        callStepTranslation,
        cdFile,
        fgLanguage,
        closeModal,
        callCallback,
        show,
        setShouldShow
    ]);
    var translateDocument = /*#__PURE__*/ function() {
        var _ref = _async_to_generator(function() {
            return _ts_generator(this, function(_state) {
                setTranslationLoad();
                (0,reactorCmps_src_document_components_AutoTranslation_helper_action__WEBPACK_IMPORTED_MODULE_18__.autoTranslateDocument)({
                    cdFile: cdFile,
                    fgLanguage: fgLanguage,
                    okCallback: callCallback,
                    errorCallback: closeModal
                });
                return [
                    2
                ];
            });
        });
        return function translateDocument() {
            return _ref.apply(this, arguments);
        };
    }();
    /* istanbul ignore next */ var _WorkspaceInfo_getLanguageList_filter = _sliced_to_array(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_9___default().getLanguageList().filter(function(language) {
        return parseInt(language.value, 10) === parseInt(fgLanguage, 10);
    }), 1), item = _WorkspaceInfo_getLanguageList_filter[0];
    var getBody = function() {
        switch(step){
            case reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT_LOADING:
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Atomic_components_Helpers_CircularLoading_CircularLoading__WEBPACK_IMPORTED_MODULE_14___default()), {
                    absolute: true
                });
            case reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT_TRANSLATION:
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Atomic_components_Helpers_CircularLoading_CircularLoading__WEBPACK_IMPORTED_MODULE_14___default()), {
                    subtitle: getToken('315760'),
                    absolute: true
                });
            case reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT_NOT_EXISTS:
            default:
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((react__WEBPACK_IMPORTED_MODULE_6___default().Fragment), null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement("p", null, disabled === false ? getToken('315763') : getToken('315902')), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement("div", {
                    style: divLanguageStyle
                }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Atomic_components_Mols_ImageTextView__WEBPACK_IMPORTED_MODULE_15___default()), {
                    tooltip: getToken(item === null || item === void 0 ? void 0 : item.term),
                    src: "".concat(Utils__WEBPACK_IMPORTED_MODULE_8___default().getSystemUrl(), "/ui/desktop/lite/resources/images/flagIcons/32x32/").concat(item === null || item === void 0 ? void 0 : item.abrev, ".png"),
                    size: reactor2_src_constants_sizeConstants__WEBPACK_IMPORTED_MODULE_11__.SIZE_SMALL
                }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Atomic_components_Atoms_Headings_H5__WEBPACK_IMPORTED_MODULE_16___default()), {
                    value: getToken(item === null || item === void 0 ? void 0 : item.term)
                })));
        }
    };
    var modalProps = {
        show: shouldShow,
        title: getToken('315759'),
        width: reactor2_src_constants_sizeConstants__WEBPACK_IMPORTED_MODULE_11__.SIZE_SMALL,
        Body: getBody(),
        onClose: closeModal,
        hideFooter: step === reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT_LOADING,
        testSelector: 'AUTOTRANSLATEMODAL'
    };
    /* istanbul ignore else */ if (disabled === false) {
        modalProps.primaryButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_13___default()), {
            text: getToken(104642),
            onClick: translateDocument,
            disabled: [
                reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT_LOADING,
                reactorCmps_src_document_components_AutoTranslation_constants_steps__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT_TRANSLATION
            ].includes(step)
        });
    }
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement((reactor2_src_Atomic_components_Orgs_Modal_Modal__WEBPACK_IMPORTED_MODULE_12___default()), modalProps);
};
AutoTranslateModal.propTypes = {
    show: (prop_types__WEBPACK_IMPORTED_MODULE_19___default().bool),
    disabled: (prop_types__WEBPACK_IMPORTED_MODULE_19___default().bool),
    cdFile: prop_types__WEBPACK_IMPORTED_MODULE_19___default().oneOfType([
        (prop_types__WEBPACK_IMPORTED_MODULE_19___default().string),
        (prop_types__WEBPACK_IMPORTED_MODULE_19___default().number)
    ]),
    fgLanguage: prop_types__WEBPACK_IMPORTED_MODULE_19___default().oneOfType([
        (prop_types__WEBPACK_IMPORTED_MODULE_19___default().string),
        (prop_types__WEBPACK_IMPORTED_MODULE_19___default().number)
    ]),
    callbackSuccess: (prop_types__WEBPACK_IMPORTED_MODULE_19___default().func),
    onClose: (prop_types__WEBPACK_IMPORTED_MODULE_19___default().func)
};
AutoTranslateModal.defaultProps = {
    show: false,
    disabled: true,
    onClose: (lodash_noop__WEBPACK_IMPORTED_MODULE_7___default())
};
AutoTranslateModal.displayName = 'document/components/AutoTranslation/Modal';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reactor2_src_Atomic_components_Helpers_Language_tokenManagerHOC__WEBPACK_IMPORTED_MODULE_10___default()(AutoTranslateModal));


}),
"./src/document/components/AutoTranslation/constants/responseStatus.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_object_freeze_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.object.freeze.js */ "../node_modules/core-js/modules/es.object.freeze.js");
/* ESM import */var core_js_modules_es_object_freeze_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_freeze_js__WEBPACK_IMPORTED_MODULE_0__);

/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Object.freeze({
    OK: 200,
    NOT_FOUND: 404
}));


}),
"./src/document/components/AutoTranslation/constants/steps.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_object_freeze_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.object.freeze.js */ "../node_modules/core-js/modules/es.object.freeze.js");
/* ESM import */var core_js_modules_es_object_freeze_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_freeze_js__WEBPACK_IMPORTED_MODULE_0__);

/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Object.freeze({
    // Verificando se existe uma tradução para o idioma selecionado
    DOCUMENT_LOADING: 1,
    // Quando não existir um tradução para o idioma selecionado
    DOCUMENT_NOT_EXISTS: 2,
    // Traduzindo o documento para o idioma selecionado
    DOCUMENT_TRANSLATION: 3
}));


}),
"./src/document/components/AutoTranslation/helper/action.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  autoTranslateDocExists: function() { return autoTranslateDocExists; },
  autoTranslateDocument: function() { return autoTranslateDocument; },
  getFgLanguages: function() { return getFgLanguages; }
});
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.parse-int.js */ "../node_modules/core-js/modules/es.parse-int.js");
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! Connector */ "Connector");
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(Connector__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactorCmps_src_document_components_AutoTranslation_constants_responseStatus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactorCmps/src/document/components/AutoTranslation/constants/responseStatus */ "./src/document/components/AutoTranslation/constants/responseStatus.js");
/* ESM import */var reactorCmps_src_document_utils_DocumentUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactorCmps/src/document/utils/DocumentUtils */ "./src/document/utils/DocumentUtils.js");
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
    try {
        var info = gen[key](arg);
        var value = info.value;
    } catch (error) {
        reject(error);
        return;
    }
    if (info.done) {
        resolve(value);
    } else {
        Promise.resolve(value).then(_next, _throw);
    }
}
function _async_to_generator(fn) {
    return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
            var gen = fn.apply(self, args);
            function _next(value) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
            }
            function _throw(err) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
            }
            _next(undefined);
        });
    };
}
function _object_without_properties(source, excluded) {
    if (source == null) return {};
    var target = _object_without_properties_loose(source, excluded);
    var key, i;
    if (Object.getOwnPropertySymbols) {
        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
        for(i = 0; i < sourceSymbolKeys.length; i++){
            key = sourceSymbolKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
            target[key] = source[key];
        }
    }
    return target;
}
function _object_without_properties_loose(source, excluded) {
    if (source == null) return {};
    var target = {};
    var sourceKeys = Object.keys(source);
    var key, i;
    for(i = 0; i < sourceKeys.length; i++){
        key = sourceKeys[i];
        if (excluded.indexOf(key) >= 0) continue;
        target[key] = source[key];
    }
    return target;
}
function _ts_generator(thisArg, body) {
    var f, y, t, g, _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    };
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}





var toast = (0,reactorCmps_src_document_utils_DocumentUtils__WEBPACK_IMPORTED_MODULE_4__.getToast)();
var documentRpc = '/document/dc_document/document_rpc.php';
var autoTranslateDocExists = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function(param) {
        var cdFile, fgLanguage, okCallback, notFoundCallback, errorCallback;
        return _ts_generator(this, function(_state) {
            cdFile = param.cdFile, fgLanguage = param.fgLanguage, okCallback = param.okCallback, notFoundCallback = param.notFoundCallback, errorCallback = param.errorCallback;
            return [
                2,
                Connector__WEBPACK_IMPORTED_MODULE_2___default().callBaseclass(documentRpc, {
                    method: (Connector__WEBPACK_IMPORTED_MODULE_2___default().GET),
                    data: {
                        cdfile: cdFile,
                        fglanguage: fgLanguage,
                        autoTranslateDocExists: true
                    }
                }).then(function(response) {
                    var statusCode = parseInt(response, 10);
                    if (statusCode === reactorCmps_src_document_components_AutoTranslation_constants_responseStatus__WEBPACK_IMPORTED_MODULE_3__["default"].OK) {
                        return okCallback instanceof Function && okCallback();
                    }
                    if (statusCode === reactorCmps_src_document_components_AutoTranslation_constants_responseStatus__WEBPACK_IMPORTED_MODULE_3__["default"].NOT_FOUND) {
                        return notFoundCallback instanceof Function && notFoundCallback();
                    }
                    toast.error({
                        message: 216225
                    });
                    errorCallback instanceof Function && errorCallback();
                })
            ];
        });
    });
    return function autoTranslateDocExists(_) {
        return _ref.apply(this, arguments);
    };
}();
var autoTranslateDocument = /*#__PURE__*/ function() {
    var _ref = _async_to_generator(function(param) {
        var cdFile, fgLanguage, okCallback, errorCallback;
        return _ts_generator(this, function(_state) {
            cdFile = param.cdFile, fgLanguage = param.fgLanguage, okCallback = param.okCallback, errorCallback = param.errorCallback;
            return [
                2,
                Connector__WEBPACK_IMPORTED_MODULE_2___default().callBaseclass(documentRpc, {
                    method: (Connector__WEBPACK_IMPORTED_MODULE_2___default().POST),
                    data: {
                        cdfile: cdFile,
                        fglanguage: fgLanguage,
                        autoTranslateDocument: true
                    }
                }).then(function(response) {
                    var statusCode = parseInt(response, 10);
                    if (statusCode === reactorCmps_src_document_components_AutoTranslation_constants_responseStatus__WEBPACK_IMPORTED_MODULE_3__["default"].OK) {
                        return okCallback instanceof Function && okCallback();
                    }
                    toast.error({
                        message: 216225
                    });
                    errorCallback instanceof Function && errorCallback();
                })
            ];
        });
    });
    return function autoTranslateDocument(_) {
        return _ref.apply(this, arguments);
    };
}();
var getFgLanguages = function(_param) {
    var setResults = _param.setResults, params = _object_without_properties(_param, [
        "setResults"
    ]);
    return Connector__WEBPACK_IMPORTED_MODULE_2___default().callBaseclass("/document/request/documentFgLanguages.php?".concat((0,reactorCmps_src_document_utils_DocumentUtils__WEBPACK_IMPORTED_MODULE_4__.handleQueryParameters)(params)), {
        method: (Connector__WEBPACK_IMPORTED_MODULE_2___default().GET)
    }).then(function(response) {
        if (response.success && setResults instanceof Function) {
            setResults(response.results);
        }
    });
};


}),
"./src/document/utils/DocumentUtils.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  create: function() { return create; },
  createAndSubmitForm: function() { return createAndSubmitForm; },
  createJnlpIframe: function() { return createJnlpIframe; },
  extractProperty: function() { return extractProperty; },
  generateKey: function() { return generateKey; },
  getToast: function() { return getToast; },
  getTokenManager: function() { return getTokenManager; },
  handleQueryParameters: function() { return handleQueryParameters; },
  jnlpIframeExists: function() { return jnlpIframeExists; },
  removeJnlpIframeIfExists: function() { return removeJnlpIframeIfExists; },
  utf8decode: function() { return utf8decode; }
});
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_web_url_search_params_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/web.url-search-params.js */ "../node_modules/core-js/modules/web.url-search-params.js");
/* ESM import */var core_js_modules_web_url_search_params_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_url_search_params_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.array.iterator.js */ "../node_modules/core-js/modules/es.array.iterator.js");
/* ESM import */var core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_iterator_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/web.dom-collections.iterator.js */ "../node_modules/core-js/modules/web.dom-collections.iterator.js");
/* ESM import */var core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_iterator_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.string.iterator.js */ "../node_modules/core-js/modules/es.string.iterator.js");
/* ESM import */var core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_iterator_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.object.to-string.js */ "../node_modules/core-js/modules/es.object.to-string.js");
/* ESM import */var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.error.to-string.js */ "../node_modules/core-js/modules/es.error.to-string.js");
/* ESM import */var core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_error_to_string_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! core-js/modules/es.date.to-string.js */ "../node_modules/core-js/modules/es.date.to-string.js");
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! core-js/modules/es.regexp.to-string.js */ "../node_modules/core-js/modules/es.regexp.to-string.js");
/* ESM import */var core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_to_string_js__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! core-js/modules/es.string.replace.js */ "../node_modules/core-js/modules/es.string.replace.js");
/* ESM import */var core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_replace_js__WEBPACK_IMPORTED_MODULE_11__);
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_13__);
/* ESM import */var reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactor2/src/helpers/ToasterSystem/ToasterSystem */ "../reactor2/src/helpers/ToasterSystem/ToasterSystem.js");
/* ESM import */var reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_tokenManager__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/tokenManager */ "../reactor2/src/Atomic/components/Helpers/Language/tokenManager.js");
/* ESM import */var reactor2_src_Common_components_ActionSystem_StandaloneActions__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! reactor2/src/Common/components/ActionSystem/StandaloneActions */ "../reactor2/src/Common/components/ActionSystem/StandaloneActions.js");
/* ESM import */var reactorCmps_src_generic_helpers_ProductConstants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! reactorCmps/src/generic/helpers/ProductConstants */ "./src/generic/helpers/ProductConstants.js");


















/**
 * Extract value of array
 * ```javascript
 * const selected = [
 *   { cdDocument: 1302, cdRevision: 1912 },
 *   { cdDocument: 4532, cdRevision: 1105 }
 * ];
 * console.log(extractProperty(selected, 'cdDocument')); // [ 1302, 4532 ]
 *  ```
 * @param {array} data
 * @param {string} propName
 * @returns {array} values extracted
*/ var extractProperty = function(data, propName) {
    return data.map(function(data) {
        return data[propName];
    });
};
/**
 * Handle parameters for sending in request GET
 * ```javascript
 * const params = {
 *   cdDocument: 1302,
 *   cdRevision: 1912
 * };
 * console.log(handleQueryParameters(params)); // 'cdDocument=1302&cdRevision=1912'
 *  ```
 * @param {object} object
 * @returns {string} values handled
*/ var handleQueryParameters = function(object) {
    return new URLSearchParams(object).toString();
};
/**
 * Generates a string with the given prefix
 * ```javascript
 * console.log(generateKey(`confirmAlert`); // 'confirmAlert-**********'
 *  ```
 * @param {string} preFixed
 * @returns {string} value generated
*/ var generateKey = function(preFixed) {
    return "".concat(preFixed, "-").concat(Utils__WEBPACK_IMPORTED_MODULE_13___default().generateHash());
};
/**
 * Returns a getToken provider for **pure javascript files**
 * ```javascript
 * const getToken = getTokenManager();
 * console.log(getToken(100357)) // Documento
 *  ```
 * @returns {tokenManager}
*/ var getTokenManager = function() {
    return (0,reactor2_src_Atomic_components_Helpers_Language_tokenManager__WEBPACK_IMPORTED_MODULE_15__["default"])(reactorCmps_src_generic_helpers_ProductConstants__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT);
};
/**
 * Returns helpers to render components
 * @returns {object}
*/ var create = function() {
    return (0,reactor2_src_Common_components_ActionSystem_StandaloneActions__WEBPACK_IMPORTED_MODULE_16__["default"])({
        cdProduct: reactorCmps_src_generic_helpers_ProductConstants__WEBPACK_IMPORTED_MODULE_17__["default"].DOCUMENT
    });
};
/**
 * Returns a toast provider with standardized properties
 * ```javascript
 * const toast = getToast();
 * toast.success({ message: 100051 });
 *  ```
 * @returns {ToasterSystem}
*/ var getToast = function() {
    var getToken = getTokenManager();
    var info = function(param) {
        var message = param.message;
        reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14___default().INFO), getToken(message), null, 5000, true);
    };
    var warning = function(param) {
        var message = param.message;
        reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14___default().WARNING), getToken(message), null, 5000, true);
    };
    var error = function(param) {
        var message = param.message;
        reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14___default().ERROR), getToken(message), null, 5000, true);
    };
    var success = function(param) {
        var message = param.message;
        reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_14___default().SUCCESS), getToken(message), null, 5000, true);
    };
    return {
        info: info,
        warning: warning,
        error: error,
        success: success
    };
};
var jnlpIframeExists = function() {
    return window.top.document.getElementById('jnlpiframe');
};
var removeJnlpIframeIfExists = function() {
    if (jnlpIframeExists()) {
        var iframe = window.top.document.getElementById('jnlpiframe');
        iframe.parentNode.removeChild(iframe);
    }
};
var createJnlpIframe = function() {
    var jnlpIframe = window.top.document.createElement('iframe');
    jnlpIframe.id = 'jnlpiframe';
    jnlpIframe.name = 'jnlpiframe';
    jnlpIframe.style.display = 'none';
    window.top.document.getElementsByTagName('body')[0].appendChild(jnlpIframe);
};
var utf8decode = function(value) {
    return value.replace(/[\u00e0-\u00ef][\u0080-\u00bf][\u0080-\u00bf]/g, function(c) {
        return String.fromCharCode((c.charCodeAt(0) & 0x0f) << 12 | (c.charCodeAt(1) & 0x3f) << 6 | c.charCodeAt(2) & 0x3f);
    }).replace(/[\u00c0-\u00df][\u0080-\u00bf]/g, function(c) {
        return String.fromCharCode((c.charCodeAt(0) & 0x1f) << 6 | c.charCodeAt(1) & 0x3f);
    });
};
function createAndSubmitForm(params, param) {
    var _param_target = param.target, target = _param_target === void 0 ? 'jnlpiframe' : _param_target, _param_id = param.id, id = _param_id === void 0 ? 'eformDownload' : _param_id, action = param.action;
    var form = document.createElement('form');
    form.method = 'post';
    form.action = action;
    form.id = id;
    form.name = id;
    form.target = target;
    for(var key in params){
        var input = document.createElement('input');
        input.id = key;
        input.name = key;
        input.type = 'hidden';
        input.value = params[key];
        form.appendChild(input);
    }
    document.body.appendChild(form);
    document.getElementById(id).submit();
    document.body.removeChild(form);
}


}),
"./src/generic/helpers/ProductConstants.js": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    AUDIT: 13,
    DOCUMENT: 21,
    STRATEGY: 16,
    DRIVE: 307,
    OKR: 303,
    ADMINISTRATION: 153,
    TRAINING: 26,
    FORM: 49
});


}),
"Connector": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"Utils": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"WorkspaceInfo": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_WorkspaceInfo__;

}),
"create-react-class": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"reactorCmps/tokens/general": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/document/components/AutoTranslation/Modal/index.js"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=AutoTranslateModal.js.map