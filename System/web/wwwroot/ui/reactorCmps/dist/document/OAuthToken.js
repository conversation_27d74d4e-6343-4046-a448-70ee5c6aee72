"use strict";
define(["tokens!reactorCmps/tokens/general","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","create-react-class","suite-storage"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_suite_storage__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["document/OAuthToken"], {
"./src/document/components/OAuthToken/InstructionsList.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.includes.js */ "../node_modules/core-js/modules/es.array.includes.js");
/* ESM import */var core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_includes_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.string.includes.js */ "../node_modules/core-js/modules/es.string.includes.js");
/* ESM import */var core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_includes_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.string.split.js */ "../node_modules/core-js/modules/es.string.split.js");
/* ESM import */var core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_split_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_array_splice_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.array.splice.js */ "../node_modules/core-js/modules/es.array.splice.js");
/* ESM import */var core_js_modules_es_array_splice_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_splice_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.array.concat.js */ "../node_modules/core-js/modules/es.array.concat.js");
/* ESM import */var core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_concat_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/useTokenManager */ "../reactor2/src/Atomic/components/Helpers/Language/useTokenManager.js");









var listStyle = {
    marginTop: 8,
    paddingInlineStart: 20
};
var listItemStyle = {
    lineHeight: 2
};
var TUTORIAL_STEPS = [
    '311798',
    '312841',
    '312842'
];
var getListItemText = function(item, key, getToken) {
    var text;
    if (getToken(item).includes('%s')) {
        var arr = getToken(item).split('%s');
        arr.splice(1, 0, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("strong", {
            key: "".concat(key, "-CM010")
        }, 'CM010'));
        text = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("span", null, arr);
    } else {
        text = getToken(item);
    }
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("li", {
        key: "".concat(key, "-").concat(text),
        style: listItemStyle
    }, text);
};
function InstructionsList() {
    var getToken = (0,reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_8__["default"])();
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("ol", {
        style: listStyle
    }, TUTORIAL_STEPS.map(function(item, key) {
        return getListItemText(item, key, getToken);
    }));
}
InstructionsList.displayName = 'services/components/OAuthToken/InstructionsList';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InstructionsList);


}),
"./src/document/components/OAuthToken/OAuthToken.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var reactorCmps_src_services_components_OAuthToken_Title__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactorCmps/src/services/components/OAuthToken/Title */ "./src/services/components/OAuthToken/Title.jsx");
/* ESM import */var reactorCmps_src_services_components_OAuthToken_InstructionsTitle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactorCmps/src/services/components/OAuthToken/InstructionsTitle */ "./src/services/components/OAuthToken/InstructionsTitle.jsx");
/* ESM import */var reactorCmps_src_document_components_OAuthToken_InstructionsList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactorCmps/src/document/components/OAuthToken/InstructionsList */ "./src/document/components/OAuthToken/InstructionsList.jsx");
/* ESM import */var reactorCmps_src_services_components_OAuthToken_CodeInstructions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactorCmps/src/services/components/OAuthToken/CodeInstructions */ "./src/services/components/OAuthToken/CodeInstructions.jsx");






var containerStyle = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%'
};
var contentContainer = {
    padding: '36px 8px',
    borderColor: '#DFE1E4',
    borderWidth: 1,
    borderStyle: 'solid',
    width: '400px',
    borderRadius: 5
};
var titleContainerStyle = {
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
    alignContent: 'center'
};
var contentContainerStyle = {
    width: 360,
    marginTop: 22,
    marginLeft: 12
};
var instructionsContainerStyle = {
    display: 'flex',
    flexDirection: 'column'
};
var codeSectionContainerStyle = {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 8,
    flexDirection: 'column',
    alignContent: 'center'
};
var getCodePageParam = function() {
    return Utils__WEBPACK_IMPORTED_MODULE_1___default().getURLParameter('code');
};
function OAuthToken() {
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
        style: containerStyle
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
        style: contentContainer
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
        style: titleContainerStyle
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(reactorCmps_src_services_components_OAuthToken_Title__WEBPACK_IMPORTED_MODULE_2__["default"], null)), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
        style: contentContainerStyle
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
        style: instructionsContainerStyle
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(reactorCmps_src_services_components_OAuthToken_InstructionsTitle__WEBPACK_IMPORTED_MODULE_3__["default"], null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(reactorCmps_src_document_components_OAuthToken_InstructionsList__WEBPACK_IMPORTED_MODULE_4__["default"], null)), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
        style: codeSectionContainerStyle
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(reactorCmps_src_services_components_OAuthToken_CodeInstructions__WEBPACK_IMPORTED_MODULE_5__["default"], {
        code: getCodePageParam()
    })))));
}
OAuthToken.displayName = 'document/components/OAuthToken/OAuthToken';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OAuthToken);


}),
"./src/services/components/OAuthToken/CodeInstructions.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var clipboard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clipboard */ "../node_modules/clipboard/dist/clipboard.js");
/* ESM import */var clipboard__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(clipboard__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
/* ESM import */var prop_types__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Button/Button */ "../reactor2/src/Atomic/components/Atoms/Button/Button.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/useTokenManager */ "../reactor2/src/Atomic/components/Helpers/Language/useTokenManager.js");
/* ESM import */var reactor2_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! reactor2/src/Form/components/Mols/Text */ "../reactor2/src/Form/components/Mols/Text.jsx");
/* ESM import */var reactor2_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! reactor2/src/helpers/ToasterSystem/ToasterSystem */ "../reactor2/src/helpers/ToasterSystem/ToasterSystem.js");
/* ESM import */var reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5__);







var codeTitleStyle = {
    fontWeight: 'bold',
    fontSize: 14
};
var codeInstructionsTextStyle = {
    lineHeight: 2
};
var codeContainerStyle = {
    display: 'flex',
    marginTop: 8
};
var codeInputStyle = {
    flexGrow: 1
};
var codeClipboardBtnStyle = {
    marginLeft: 4
};
function CodeInstructions(param) {
    var code = param.code;
    var getToken = (0,reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_3__["default"])();
    var getSuccessShareTerm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {
        return getToken('217338');
    }, [
        getToken
    ]);
    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {
        var clipboard = new (clipboard__WEBPACK_IMPORTED_MODULE_0___default())('.rctOAuthButton.clipboard');
        clipboard.on('success', function() {
            reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().addNotification((reactor2_src_helpers_ToasterSystem_ToasterSystem__WEBPACK_IMPORTED_MODULE_5___default().SUCCESS), getSuccessShareTerm());
        });
        return function() {
            return clipboard.destroy();
        };
    }, [
        getSuccessShareTerm
    ]);
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("span", {
        style: codeTitleStyle
    }, getToken('311802')), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("span", {
        style: codeInstructionsTextStyle
    }, getToken('311803')), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
        style: codeContainerStyle
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
        style: codeInputStyle
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_4___default()), {
        disabled: true,
        value: code
    })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement("div", {
        style: codeClipboardBtnStyle
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement((reactor2_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_2___default()), {
        className: 'rctOAuthButton clipboard',
        icon: 'seicon-floppy',
        "data-clipboard-text": code,
        tooltip: getToken('311804')
    }))));
}
CodeInstructions.propTypes = {
    code: prop_types__WEBPACK_IMPORTED_MODULE_6__.string.isRequired
};
CodeInstructions.displayName = 'services/components/OAuthToken/CodeInstructions';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CodeInstructions);


}),
"./src/services/components/OAuthToken/CompanyLogo.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var reactor2_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor2/src/Atomic/components/Atoms/Image/Image */ "../reactor2/src/Atomic/components/Atoms/Image/Image.jsx");
/* ESM import */var reactor2_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(reactor2_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_2__);



var LOGO_URL = '/ui/desktop/lite/resources/images/suiteLogos/softexpert.svg';
function CompanyLogo() {
    var logoUrl = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {
        return Utils__WEBPACK_IMPORTED_MODULE_1___default().getSystemUrl() + LOGO_URL;
    }, []);
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement((reactor2_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_2___default()), {
        src: logoUrl,
        height: 50,
        width: 200
    });
}
CompanyLogo.displayName = 'services/components/OAuthToken/CompanyLogo';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompanyLogo);


}),
"./src/services/components/OAuthToken/InstructionsTitle.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/useTokenManager */ "../reactor2/src/Atomic/components/Helpers/Language/useTokenManager.js");


var instructionsTitleStyle = {
    fontWeight: 'bold',
    fontSize: 14,
    textAlign: 'center'
};
function InstructionsTitle() {
    var getToken = (0,reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_1__["default"])();
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
        style: instructionsTitleStyle
    }, getToken('311797'));
}
InstructionsTitle.displayName = 'services/components/OAuthToken/InstructionsTitle';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InstructionsTitle);


}),
"./src/services/components/OAuthToken/Title.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var reactorCmps_src_services_components_OAuthToken_CompanyLogo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! reactorCmps/src/services/components/OAuthToken/CompanyLogo */ "./src/services/components/OAuthToken/CompanyLogo.jsx");
/* ESM import */var reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reactor2/src/Atomic/components/Helpers/Language/useTokenManager */ "../reactor2/src/Atomic/components/Helpers/Language/useTokenManager.js");



var titleTextStyle = {
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center'
};
function Title() {
    var getToken = (0,reactor2_src_Atomic_components_Helpers_Language_useTokenManager__WEBPACK_IMPORTED_MODULE_2__["default"])();
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(reactorCmps_src_services_components_OAuthToken_CompanyLogo__WEBPACK_IMPORTED_MODULE_1__["default"], null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
        style: titleTextStyle
    }, getToken('311796')));
}
Title.displayName = 'services/components/OAuthToken/Title';
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Title);


}),
"Utils": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"create-react-class": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"reactorCmps/tokens/general": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/document/components/OAuthToken/OAuthToken.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=OAuthToken.js.map