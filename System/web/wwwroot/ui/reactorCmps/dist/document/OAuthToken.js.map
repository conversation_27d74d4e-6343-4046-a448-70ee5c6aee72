{"version": 3, "file": "document/OAuthToken.js", "sources": ["webpack://watch1749037385376/./src/document/components/OAuthToken/InstructionsList.jsx", "webpack://watch1749037385376/./src/document/components/OAuthToken/OAuthToken.jsx", "webpack://watch1749037385376/./src/services/components/OAuthToken/CodeInstructions.jsx", "webpack://watch1749037385376/./src/services/components/OAuthToken/CompanyLogo.jsx", "webpack://watch1749037385376/./src/services/components/OAuthToken/InstructionsTitle.jsx", "webpack://watch1749037385376/./src/services/components/OAuthToken/Title.jsx"], "sourcesContent": ["import React from 'react';\nimport useTokenManager from 'reactor2/src/Atomic/components/Helpers/Language/useTokenManager';\n\nconst listStyle = {\n\tmarginTop: 8,\n\tpaddingInlineStart: 20\n};\n\nconst listItemStyle = {\n\tlineHeight: 2\n};\n\nconst TUTORIAL_STEPS = ['311798', '312841', '312842'];\n\nconst getListItemText = (item, key, getToken) => {\n\tlet text;\n\n\tif (getToken(item).includes('%s')) {\n\t\tconst arr = getToken(item).split('%s');\n\n\t\tarr.splice(1, 0, (<strong key={`${key}-CM010`}>{'CM010'}</strong>));\n\n\t\ttext = (\n\t\t\t<span>\n\t\t\t\t{arr}\n\t\t\t</span>\n\t\t);\n\t} else {\n\t\ttext = getToken(item);\n\t}\n\n\treturn (\n\t\t<li key={`${key}-${text}`} style={listItemStyle}>\n\t\t\t{text}\n\t\t</li>\n\t);\n};\n\nfunction InstructionsList() {\n\tconst getToken = useTokenManager();\n\n\treturn (\n\t\t<ol style={listStyle}>\n\t\t\t{TUTORIAL_STEPS.map((item, key) => getListItemText(item, key, getToken))}\n\t\t</ol>\n\t);\n}\n\nInstructionsList.displayName = 'services/components/OAuthToken/InstructionsList';\n\nexport default InstructionsList;", "import React from 'react';\nimport Utils from 'Utils';\nimport Title from 'reactorCmps/src/services/components/OAuthToken/Title';\nimport InstructionsTitle from 'reactorCmps/src/services/components/OAuthToken/InstructionsTitle';\nimport InstructionsList from 'reactorCmps/src/document/components/OAuthToken/InstructionsList';\nimport CodeInstructions from 'reactorCmps/src/services/components/OAuthToken/CodeInstructions';\n\nconst containerStyle = {\n\tdisplay: 'flex',\n\tjustifyContent: 'center',\n\talignItems: 'center',\n\theight: '100%'\n};\n\nconst contentContainer = {\n\tpadding: '36px 8px',\n\tborderColor: '#DFE1E4',\n\tborderWidth: 1,\n\tborderStyle: 'solid',\n\twidth: '400px',\n\tborderRadius: 5\n};\n\nconst titleContainerStyle = {\n\tdisplay: 'flex',\n\tjustifyContent: 'center',\n\tflexDirection: 'column',\n\talignContent: 'center'\n};\n\nconst contentContainerStyle = {\n\twidth: 360,\n\tmarginTop: 22,\n\tmarginLeft: 12\n};\n\nconst instructionsContainerStyle = {\n\tdisplay: 'flex',\n\tflexDirection: 'column'\n};\n\nconst codeSectionContainerStyle = {\n\tdisplay: 'flex',\n\tjustifyContent: 'center',\n\tmarginTop: 8,\n\tflexDirection: 'column',\n\talignContent: 'center'\n};\n\nconst getCodePageParam = () => Utils.getURLParameter('code');\n\nfunction OAuthToken() {\n\treturn (\n\t\t<div style={containerStyle}>\n\t\t\t<div style={contentContainer}>\n\t\t\t\t<div style={titleContainerStyle}>\n\t\t\t\t\t<Title/>\n\t\t\t\t</div>\n\t\t\t\t<div style={contentContainerStyle}>\n\t\t\t\t\t<div style={instructionsContainerStyle}>\n\t\t\t\t\t\t<InstructionsTitle/>\n\t\t\t\t\t\t<InstructionsList/>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div style={codeSectionContainerStyle}>\n\t\t\t\t\t\t<CodeInstructions code={getCodePageParam()}/>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\nOAuthToken.displayName = 'document/components/OAuthToken/OAuthToken';\n\nexport default OAuthToken;", "import Clipboard from 'clipboard';\nimport { string } from 'prop-types';\nimport React, { useEffect, useCallback } from 'react';\nimport Button from 'reactor2/src/Atomic/components/Atoms/Button/Button';\nimport useTokenManager from 'reactor2/src/Atomic/components/Helpers/Language/useTokenManager';\nimport Text from 'reactor2/src/Form/components/Mols/Text';\nimport ToasterSystem from 'reactor2/src/helpers/ToasterSystem/ToasterSystem';\n\nconst codeTitleStyle = {\n\tfontWeight: 'bold',\n\tfontSize: 14\n};\n\nconst codeInstructionsTextStyle = {\n\tlineHeight: 2\n};\n\nconst codeContainerStyle = {\n\tdisplay: 'flex',\n\tmarginTop: 8\n};\n\nconst codeInputStyle = {\n\tflexGrow: 1\n};\n\nconst codeClipboardBtnStyle = {\n\tmarginLeft: 4\n};\n\nfunction CodeInstructions({ code }) {\n\tconst getToken = useTokenManager();\n\tconst getSuccessShareTerm = useCallback(() => getToken('217338'), [getToken]);\n\n\tuseEffect(() => {\n\t\tconst clipboard = new Clipboard('.rctOAuthButton.clipboard');\n\n\t\tclipboard.on('success', () => {\n\t\t\tToasterSystem.addNotification(ToasterSystem.SUCCESS, getSuccessShareTerm());\n\t\t});\n\n\t\treturn () => clipboard.destroy();\n\t}, [getSuccessShareTerm]);\n\n\treturn (\n\t\t<>\n\t\t\t<span style={codeTitleStyle}>\n\t\t\t\t{getToken('311802')}\n\t\t\t</span>\n\n\t\t\t<span style={codeInstructionsTextStyle}>\n\t\t\t\t{getToken('311803')}\n\t\t\t</span>\n\n\t\t\t<div style={codeContainerStyle}>\n\t\t\t\t<div style={codeInputStyle}>\n\t\t\t\t\t<Text disabled value={code}/>\n\t\t\t\t</div>\n\t\t\t\t<div style={codeClipboardBtnStyle}>\n\t\t\t\t\t<Button\n\t\t\t\t\t\tclassName={'rctOAuthButton clipboard'}\n\t\t\t\t\t\ticon={'seicon-floppy'}\n\t\t\t\t\t\tdata-clipboard-text={code}\n\t\t\t\t\t\ttooltip={getToken('311804')}\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</>\n\t);\n}\n\nCodeInstructions.propTypes = {\n\tcode: string.isRequired\n};\n\nCodeInstructions.displayName = 'services/components/OAuthToken/CodeInstructions';\n\nexport default CodeInstructions;", "import React, { useMemo } from 'react';\nimport Utils from 'Utils';\nimport Image from 'reactor2/src/Atomic/components/Atoms/Image/Image';\n\nconst LOGO_URL = '/ui/desktop/lite/resources/images/suiteLogos/softexpert.svg';\n\nfunction CompanyLogo() {\n\tconst logoUrl = useMemo(() => Utils.getSystemUrl() + LOGO_URL, []);\n  \n\treturn (\n\t\t<Image \n\t\t\tsrc={logoUrl}\n\t\t\theight={50}\n\t\t\twidth={200}\n\t\t/>\n\t);\n}\n\nCompanyLogo.displayName = 'services/components/OAuthToken/CompanyLogo';\n\nexport default CompanyLogo;", "import React from 'react';\nimport useTokenManager from 'reactor2/src/Atomic/components/Helpers/Language/useTokenManager';\n\nconst instructionsTitleStyle = {\n\tfontWeight: 'bold',\n\tfontSize: 14,\n\ttextAlign: 'center'\n};\n\nfunction InstructionsTitle() {\n\tconst getToken = useTokenManager();\n\n\treturn (\n\t\t<span style={instructionsTitleStyle}>\n\t\t\t{getToken('311797')}\n\t\t</span>\n\t);\n}\n\nInstructionsTitle.displayName = 'services/components/OAuthToken/InstructionsTitle';\n\nexport default InstructionsTitle;", "import React from 'react';\nimport CompanyLogo from 'reactorCmps/src/services/components/OAuthToken/CompanyLogo';\nimport useTokenManager from 'reactor2/src/Atomic/components/Helpers/Language/useTokenManager';\n\nconst titleTextStyle = {\n\tfontWeight: 'bold',\n\tfontSize: 16,\n\ttextAlign: 'center'\n};\n\nfunction Title() {\n\tconst getToken = useTokenManager();\n\n\treturn (\n\t\t<>\n\t\t\t<CompanyLogo/>\n\t\t\t<span style={titleTextStyle}>\n\t\t\t\t{getToken('311796')}\n\t\t\t</span>\n\t\t</>\n\t);\n}\n\nTitle.displayName = 'services/components/OAuthToken/Title';\n\nexport default Title;"], "names": ["React", "useTokenManager", "listStyle", "listItemStyle", "TUTORIAL_STEPS", "getListItemText", "item", "key", "getToken", "text", "arr", "InstructionsList", "Utils", "Title", "InstructionsTitle", "CodeInstructions", "containerStyle", "contentContainer", "titleContainerStyle", "contentContainerStyle", "instructionsContainerStyle", "codeSectionContainerStyle", "getCodePageParam", "OAuthToken", "Clipboard", "string", "useEffect", "useCallback", "<PERSON><PERSON>", "Text", "ToasterSystem", "codeTitleStyle", "codeInstructionsTextStyle", "codeContainerStyle", "codeInputStyle", "codeClipboardBtnStyle", "param", "code", "getSuccessShareTerm", "clipboard", "useMemo", "Image", "LOGO_URL", "CompanyLogo", "logoUrl", "instructionsTitleStyle", "titleTextStyle"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAkDgC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAlDN;AACoE;AAE9F,IAAME,YAAY;IACjB,WAAW;IACX,oBAAoB;AACrB;AAEA,IAAMC,gBAAgB;IACrB,YAAY;AACb;AAEA,IAAMC,iBAAiB;IAAC;IAAU;IAAU;CAAS;AAErD,IAAMC,kBAAkB,SAACC,MAAMC,KAAKC;IACnC,IAAIC;IAEJ,IAAID,SAASF,MAAM,QAAQ,CAAC,OAAO;QAClC,IAAMI,MAAMF,SAASF,MAAM,KAAK,CAAC;QAEjCI,IAAI,MAAM,CAAC,GAAG,iBAAI,2DAAC;YAAO,KAAM,GAAM,OAAJH,KAAI;WAAU;QAEhDE,qBACC,2DAAC,cACCC;IAGJ,OAAO;QACND,OAAOD,SAASF;IACjB;IAEA,qBACC,2DAAC;QAAG,KAAM,GAASG,OAAPF,KAAI,KAAQ,OAALE;QAAQ,OAAON;OAChCM;AAGJ;AAEA,SAASE;IACR,IAAMH,WAAWP,2GAAeA;IAEhC,qBACC,2DAAC;QAAG,OAAOC;OACTE,eAAe,GAAG,CAAC,SAACE,MAAMC;eAAQF,gBAAgBC,MAAMC,KAAKC;;AAGjE;AAEAG,iBAAiB,WAAW,GAAG;AAE/B,6DAAeA,gBAAgBA,EAAC;;;;;;;;;;;;;;;;;AClDN;AACA;AAC+C;AACwB;AACF;AACA;AAE/F,IAAMK,iBAAiB;IACtB,SAAS;IACT,gBAAgB;IAChB,YAAY;IACZ,QAAQ;AACT;AAEA,IAAMC,mBAAmB;IACxB,SAAS;IACT,aAAa;IACb,aAAa;IACb,aAAa;IACb,OAAO;IACP,cAAc;AACf;AAEA,IAAMC,sBAAsB;IAC3B,SAAS;IACT,gBAAgB;IAChB,eAAe;IACf,cAAc;AACf;AAEA,IAAMC,wBAAwB;IAC7B,OAAO;IACP,WAAW;IACX,YAAY;AACb;AAEA,IAAMC,6BAA6B;IAClC,SAAS;IACT,eAAe;AAChB;AAEA,IAAMC,4BAA4B;IACjC,SAAS;IACT,gBAAgB;IAChB,WAAW;IACX,eAAe;IACf,cAAc;AACf;AAEA,IAAMC,mBAAmB;WAAMV,4DAAqB,CAAC;;AAErD,SAASW;IACR,qBACC,2DAAC;QAAI,OAAOP;qBACX,2DAAC;QAAI,OAAOC;qBACX,2DAAC;QAAI,OAAOC;qBACX,2DAACL,4FAAKA,wBAEP,2DAAC;QAAI,OAAOM;qBACX,2DAAC;QAAI,OAAOC;qBACX,2DAACN,wGAAiBA,uBAClB,2DAACH,uGAAgBA,wBAElB,2DAAC;QAAI,OAAOU;qBACX,2DAACN,uGAAgBA;QAAC,MAAMO;;AAM9B;AAEAC,WAAW,WAAW,GAAG;AAEzB,6DAAeA,UAAUA,EAAC;;;;;;;;;;;;;;;;;;;;;;AC1EQ;AACE;AACkB;AACkB;AACsB;AACpC;AACmB;AAE7E,IAAMQ,iBAAiB;IACtB,YAAY;IACZ,UAAU;AACX;AAEA,IAAMC,4BAA4B;IACjC,YAAY;AACb;AAEA,IAAMC,qBAAqB;IAC1B,SAAS;IACT,WAAW;AACZ;AAEA,IAAMC,iBAAiB;IACtB,UAAU;AACX;AAEA,IAAMC,wBAAwB;IAC7B,YAAY;AACb;AAEA,SAASpB,iBAAiBqB,KAAQ;QAANC,OAAFD,MAAEC;IAC3B,IAAM7B,WAAWP,2GAAeA;IAChC,IAAMqC,sBAAsBX,kDAAWA,CAAC;eAAMnB,SAAS;OAAW;QAACA;KAAS;IAE5EkB,gDAASA,CAAC;QACT,IAAMa,YAAY,IAAIf,kDAASA,CAAC;QAEhCe,UAAU,EAAE,CAAC,WAAW;YACvBT,uGAA6B,CAACA,iGAAqB,EAAEQ;QACtD;QAEA,OAAO;mBAAMC,UAAU,OAAO;;IAC/B,GAAG;QAACD;KAAoB;IAExB,qBACC,wIACC,2DAAC;QAAK,OAAOP;OACXvB,SAAS,0BAGX,2DAAC;QAAK,OAAOwB;OACXxB,SAAS,0BAGX,2DAAC;QAAI,OAAOyB;qBACX,2DAAC;QAAI,OAAOC;qBACX,2DAACL,+EAAIA;QAAC;QAAS,OAAOQ;uBAEvB,2DAAC;QAAI,OAAOF;qBACX,2DAACP,2FAAMA;QACN,WAAW;QACX,MAAM;QACN,uBAAqBS;QACrB,SAAS7B,SAAS;;AAMxB;AAEAO,iBAAiB,SAAS,GAAG;IAC5B,MAAMU,yDAAiB;AACxB;AAEAV,iBAAiB,WAAW,GAAG;AAE/B,6DAAeA,gBAAgBA,EAAC;;;;;;;;;;;;;;;AC7EO;AACb;AAC2C;AAErE,IAAM2B,WAAW;AAEjB,SAASC;IACR,IAAMC,UAAUJ,8CAAOA,CAAC;eAAM5B,yDAAkB,KAAK8B;OAAU,EAAE;IAEjE,qBACC,2DAACD,yFAAKA;QACL,KAAKG;QACL,QAAQ;QACR,OAAO;;AAGV;AAEAD,YAAY,WAAW,GAAG;AAE1B,6DAAeA,WAAWA,EAAC;;;;;;;;;;;;ACpBD;AACoE;AAE9F,IAAME,yBAAyB;IAC9B,YAAY;IACZ,UAAU;IACV,WAAW;AACZ;AAEA,SAAS/B;IACR,IAAMN,WAAWP,2GAAeA;IAEhC,qBACC,2DAAC;QAAK,OAAO4C;OACXrC,SAAS;AAGb;AAEAM,kBAAkB,WAAW,GAAG;AAEhC,6DAAeA,iBAAiBA,EAAC;;;;;;;;;;;;;ACrBP;AAC2D;AACS;AAE9F,IAAMgC,iBAAiB;IACtB,YAAY;IACZ,UAAU;IACV,WAAW;AACZ;AAEA,SAASjC;IACR,IAAML,WAAWP,2GAAeA;IAEhC,qBACC,wIACC,2DAAC0C,kGAAWA,uBACZ,2DAAC;QAAK,OAAOG;OACXtC,SAAS;AAId;AAEAK,MAAM,WAAW,GAAG;AAEpB,6DAAeA,KAAKA,EAAC"}