define(["tokens!reactorCmps/tokens/general","Formatters/Date","when","react","react-dom","Utils","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","WorkspaceInfo","SG2/collection/Factory","create-react-class","Connector","suite-storage"], function(__WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_Formatters_Date__, __WEBPACK_EXTERNAL_MODULE_when__, __WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__, __WEBPACK_EXTERNAL_MODULE_Utils__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPA<PERSON><PERSON>_EXTERNAL_MODULE_WorkspaceInfo__, __WEBPACK_EXTERNAL_MODULE_SG2_collection_Factory__, __WEBPACK_EXTERNAL_MODULE_create_react_class__, __WEBPACK_EXTERNAL_MODULE_Connector__, __WEBPACK_EXTERNAL_MODULE_suite_storage__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["document/DocumentList"], {
"../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!../reactor/src/Form/components/Mols/InputGroup/InputGroup.css": (function (module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "../node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../../../node_modules/css-loader/dist/runtime/api.js */ "../node_modules/css-loader/dist/runtime/api.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, `.rctInputGroup .rctValidationMessage {
	float: right;
}`, ""]);
// Exports
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);


}),
"../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./src/document/components/DocumentList.css": (function (module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "../node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../node_modules/css-loader/dist/runtime/api.js */ "../node_modules/css-loader/dist/runtime/api.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, `.showOnHover:hover{
	opacity: 0.5;
}

.categorySpan{
	color: rgba(0, 0, 0, 0.45);
}

.categoryItalic{
	font-style: italic;
}

.greenLightText{
	color: #7ac2a3;
}

.updatedDate, .updatedUser {
	color: #37954d;
}

.TextStyle {
	padding-top: 10px;
	padding-bottom: 10px;
}

.overrideBorder {
	border: 1px !important;
}

.row {
	border: 1px !important;
}`, ""]);
// Exports
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);


}),
"../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[2]!../reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeeedback.scss": (function (module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../../../node_modules/css-loader/dist/runtime/noSourceMaps.js */ "../node_modules/css-loader/dist/runtime/noSourceMaps.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../../../node_modules/css-loader/dist/runtime/api.js */ "../node_modules/css-loader/dist/runtime/api.js");
/* ESM import */var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_noSourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, `.rctModal .rctModalFeedback .modalAlertIcon {
  min-width: 90px;
}

.rctModal .rctModalFeedback .mobileEmptyMessage {
  word-break: break-word;
}`, ""]);
// Exports
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);


}),
"../reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeedback.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
__webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var Modal = __webpack_require__(/*! reactor/src/Atomic/components/Orgs/Modal/Modal.jsx */ "../reactor/src/Atomic/components/Orgs/Modal/Modal.jsx");
var FeedbackMsg = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/FeedbackMsg */ "../reactor/src/Atomic/components/Atoms/FeedbackMsg.jsx");
var Button = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Button/Button */ "../reactor/src/Atomic/components/Atoms/Button/Button.jsx");
var componentsThatShouldNotBeInLayoutCalc = __webpack_require__(/*! reactor/src/helpers/componentsThatShouldNotBeInLayoutCalc */ "../reactor/src/helpers/componentsThatShouldNotBeInLayoutCalc.js");
var modalBtnBox = {
    marginLeft: 104,
    paddingTop: 4
};
var modalBtnSpaces = {
    display: 'inline',
    marginRight: 5
};
var modalCustomStyle = {
    backgroundColor: "#FFFFFF",
    borderRadius: 0,
    width: 400
};
var buttonsType = {
    ALERT: 'alert',
    CONFIRM: 'confirm'
};
__webpack_require__(/*! ./ModalFeeedback.scss */ "../reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeeedback.scss");
__webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");
module.exports = createReactClass({
    displayName: "Atomic/components/Orgs/ModalFeedback",
    propTypes: {
        /**
		 * Define se está visivel
		 */ show: PropTypes.bool,
        /**
		 * Define o tipo do modal a ser mostrado
		 */ type: PropTypes.oneOf([
            'error',
            'success',
            'info',
            'alert',
            'idea'
        ]),
        /**
		 * Título do modal
		 */ title: PropTypes.string,
        /**
		 * Texto do corpo do modal
		 */ body: PropTypes.string,
        /**
		 * Botões que serão exibidos
		 *
		 * @customPropType(oneOf([buttonsType.ALERT, buttonsType.CONFIRM]), PropTypes.array)
		 */ buttons: PropTypes.oneOfType([
            PropTypes.oneOf([
                buttonsType.ALERT,
                buttonsType.CONFIRM
            ]),
            PropTypes.array
        ]),
        /**
		 * Callback do botão "Ok"
		 */ onOk: PropTypes.func,
        /**
		 * Callback do botão "Cancelar"
		 */ onCancel: PropTypes.func,
        /**
		 * Callback da ação de fechar a modal (clique fora e ESC)
		 */ onClose: PropTypes.func,
        /**
		 * @private
		 */ height: PropTypes.oneOfType([
            PropTypes.number,
            PropTypes.string
        ])
    },
    getDefaultProps: function getDefaultProps() {
        return {
            type: "info",
            height: 210
        };
    },
    getButtonsByType: function getButtonsByType() {
        var me = this, props = me.props;
        var buttonOk = /*#__PURE__*/ React.createElement(Button, {
            key: "btnOk",
            className: "rctModalAlertBtn-confirm",
            color: "primary",
            text: SE.t(100094),
            onClick: me.handleOk
        });
        var buttonCancel = /*#__PURE__*/ React.createElement(Button, {
            key: "btnCancel",
            className: "rctModalAlertBtn-cancel",
            color: "default",
            text: SE.t(100095),
            onClick: me.handleCancel
        });
        var buttons = props.buttons;
        if (!props.buttons || props.buttons === buttonsType.ALERT) {
            buttons = [
                buttonOk
            ];
        }
        if (props.buttons === buttonsType.CONFIRM) {
            buttons = [
                buttonOk,
                buttonCancel
            ];
        }
        return buttons.map(function(button) {
            var key = 'container-' + button.key;
            return /*#__PURE__*/ React.createElement("div", {
                style: modalBtnSpaces,
                key: key
            }, button);
        });
    },
    getButtons: function getButtons() {
        var me = this, buttons = me.getButtonsByType();
        return /*#__PURE__*/ React.createElement("div", {
            className: "rctModalAlertButton",
            style: modalBtnBox
        }, buttons);
    },
    getType: function getType(type) {
        var feedBackType = {
            error: 0,
            success: 1,
            alert: 2,
            info: 3,
            idea: 8
        };
        return feedBackType[type];
    },
    handleClose: function handleClose() {
        var me = this, props = me.props;
        if (props.buttons === buttonsType.CONFIRM) {
            me.handleCancel();
            return;
        }
        if (props.buttons === buttonsType.ALERT) {
            me.handleOk();
            return;
        }
        if (props.onClose) {
            props.onClose();
        }
    },
    handleOk: function handleOk() {
        var me = this, props = me.props;
        if (props.onOk) {
            props.onOk();
        }
        if (props.onClose) {
            props.onClose();
        }
    },
    handleCancel: function handleCancel() {
        var me = this, props = me.props;
        if (props.onCancel) {
            props.onCancel();
        }
        if (props.onClose) {
            props.onClose();
        }
    },
    render: function render() {
        var me = this, props = me.props, body;
        body = /*#__PURE__*/ React.createElement("div", null, /*#__PURE__*/ React.createElement(FeedbackMsg, {
            title: props.title,
            content: props.body,
            type: me.getType(props.type)
        }), me.getButtons());
        return /*#__PURE__*/ React.createElement(Modal, _object_spread_props(_object_spread({}, props), {
            hasBtnClose: false,
            style: modalCustomStyle,
            className: "rctModalFeedback",
            forceDesktop: true,
            title: null,
            footer: null,
            height: me.props.height,
            onClose: me.handleClose,
            onOk: me.handleOk,
            onCancel: me.handleCancel,
            Body: body
        }));
    }
});
module.exports.ALERT = buttonsType.ALERT;
module.exports.CONFIRM = buttonsType.CONFIRM;
componentsThatShouldNotBeInLayoutCalc.add(module.exports.displayName);


}),
"../reactor/src/Form/components/Mols/InputGroup/InputGroup.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
function ownKeys(object, enumerableOnly) {
    var keys = Object.keys(object);
    if (Object.getOwnPropertySymbols) {
        var symbols = Object.getOwnPropertySymbols(object);
        if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
        }
        keys.push.apply(keys, symbols);
    }
    return keys;
}
function _object_spread_props(target, source) {
    source = source != null ? source : {};
    if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
        ownKeys(Object(source)).forEach(function(key) {
            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
        });
    }
    return target;
}
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
var InputGroup = __webpack_require__(/*! react-bootstrap/lib/InputGroup */ "../node_modules/react-bootstrap/lib/InputGroup.js");
var style = {
    marginBottom: "15px"
};
__webpack_require__(/*! ./InputGroup.css */ "../reactor/src/Form/components/Mols/InputGroup/InputGroup.css");
module.exports = createReactClass({
    displayName: "Form/components/Mols/InputGroup/InputGroup",
    render: function render() {
        return /*#__PURE__*/ React.createElement(InputGroup, _object_spread_props(_object_spread({}, this.props), {
            style: style,
            className: "rctInputGroup"
        }));
    }
});


}),
"./src/document/collection/DocumentListCategoriesCollection.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: 'cdcategory',
        proxyCfg: {
            route: '/document/request/documentListCategories.php'
        }
    },
    collection: {
        defaultOrder: {
            nmcategory: 'ASC'
        }
    }
});


}),
"./src/document/collection/DocumentListCollection.js": (function (module, __unused_webpack_exports, __webpack_require__) {
var createCollection = __webpack_require__(/*! SG2/collection/Factory */ "SG2/collection/Factory");
module.exports = createCollection({
    model: {
        primaryKey: 'cddocument',
        proxyCfg: {
            route: '/document/request/documentListData.php'
        }
    },
    collection: {
        defaultOrder: {
            iddocument: 'ASC'
        }
    }
});


}),
"./src/document/components/DocumentList.jsx": (function (module, __unused_webpack_exports, __webpack_require__) {
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        obj[key] = value;
    }
    return obj;
}
function _object_spread(target) {
    for(var i = 1; i < arguments.length; i++){
        var source = arguments[i] != null ? arguments[i] : {};
        var ownKeys = Object.keys(source);
        if (typeof Object.getOwnPropertySymbols === "function") {
            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
                return Object.getOwnPropertyDescriptor(source, sym).enumerable;
            }));
        }
        ownKeys.forEach(function(key) {
            _define_property(target, key, source[key]);
        });
    }
    return target;
}
__webpack_require__(/*! core-js/modules/es.string.split.js */ "../node_modules/core-js/modules/es.string.split.js");
__webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
__webpack_require__(/*! core-js/modules/es.array.join.js */ "../node_modules/core-js/modules/es.array.join.js");
var React = __webpack_require__(/*! react */ "react");
var createReactClass = __webpack_require__(/*! create-react-class */ "create-react-class");
// O modal de configuração
var DocumentListConfigurationModal = (__webpack_require__(/*! ./DocumentListConfigurationModal */ "./src/document/components/DocumentListConfigurationModal.jsx")/* ["default"] */["default"]);
var tokenManagerHOC = __webpack_require__(/*! reactor/src/Atomic/components/Helpers/Language/tokenManagerHOC */ "../reactor/src/Atomic/components/Helpers/Language/tokenManagerHOC.js");
// Componentes da grid - easyGrid e cada Row da easyGrid
var easyGrid = __webpack_require__(/*! reactor/src/FlexGrid/components/Helpers/EasyGrid */ "../reactor/src/FlexGrid/components/Helpers/EasyGrid.js");
var DocumentListRow = (__webpack_require__(/*! reactorCmps/src/document/components/DocumentListRow.jsx */ "./src/document/components/DocumentListRow.jsx")/* ["default"] */["default"]);
var DocumentListCollection = __webpack_require__(/*! reactorCmps/src/document/collection/DocumentListCollection */ "./src/document/collection/DocumentListCollection.js");
var FeedbackMsg = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/FeedbackMsg */ "../reactor/src/Atomic/components/Atoms/FeedbackMsg.jsx");
// Componente de Busca acima do componente
var FormGroup = __webpack_require__(/*! reactor/src/Form/components/Mols/FormGroup */ "../reactor/src/Form/components/Mols/FormGroup.jsx");
var InputAddon = __webpack_require__(/*! reactor/src/Form/components/Atoms/InputAddon */ "../reactor/src/Form/components/Atoms/InputAddon.jsx");
var InputGroup = __webpack_require__(/*! reactor/src/Form/components/Mols/InputGroup/InputGroup */ "../reactor/src/Form/components/Mols/InputGroup/InputGroup.jsx");
var FormControl = __webpack_require__(/*! reactor/src/Form/components/Atoms/FormControl/FormControl */ "../reactor/src/Form/components/Atoms/FormControl/FormControl.js");
var keyConstants = __webpack_require__(/*! reactor/src/helpers/keyConstants.js */ "../reactor/src/helpers/keyConstants.js");
// Úteis
var workspaceInfo = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
var when = __webpack_require__(/*! when */ "when");
var Connector = __webpack_require__(/*! Connector */ "Connector");
var cursorPointer = {
    cursor: 'pointer'
};
var DocumentList = createReactClass({
    displayName: 'DocumentList',
    getDefaultProps: function() {
        return {
            DocumentListCollection: new DocumentListCollection()
        };
    },
    getInitialState: function getInitialState() {
        return {
            loading: true,
            showModal: false
        };
    },
    UNSAFE_componentWillMount: function() {
        var me = this;
        var DocumentListGrid = easyGrid({
            gridName: 'DocumentListGrid_' + me.props.oid,
            Row: DocumentListRow,
            primaryKey: 'cddocument',
            selectType: 0,
            showPagination: true,
            collectionConfig: {
                instance: me.props.DocumentListCollection,
                params: {
                    oidWidget: me.props.oid,
                    cduser: workspaceInfo.getCDUser()
                }
            }
        });
        DocumentListGrid.fetchCollection({
            oidWidget: me.props.oid,
            cduser: workspaceInfo.getCDUser()
        });
        me.DocumentListGrid = DocumentListGrid;
    },
    componentDidMount: function() {
        var me = this;
        var hasBeenConfigured;
        var hasBeenConfiguredPromise = me.hasBeenConfigured();
        hasBeenConfiguredPromise.then(function(value) {
            hasBeenConfigured = value === 'true';
            !me.isCancelled && me.setState({
                loading: false,
                hasBeenConfigured: hasBeenConfigured
            });
        });
    },
    componentWillUnmount: function() {
        var me = this;
        me.isCancelled = true;
    },
    hasBeenConfigured: function hasBeenConfigured() {
        var deferred = when.defer();
        var me = this;
        Connector.callBaseclass('document/request/documentListLoadedData.php', {
            dataType: 'json',
            data: {
                oid: me.props.oid,
                willCheckIfConfigured: true
            },
            complete: function complete(response) {
                deferred.resolve(response);
            }
        });
        return deferred.promise;
    },
    updateGrid: function(searchTerm) {
        var me = this;
        me.DocumentListGrid.fetchCollection({
            oidWidget: me.props.oid,
            searchTerm: searchTerm,
            cduser: workspaceInfo.getCDUser()
        });
    },
    updateGridWithoutSearch: function() {
        var me = this;
        me.DocumentListGrid.fetchCollection({
            oidWidget: me.props.oid,
            cduser: workspaceInfo.getCDUser()
        });
    },
    setConfigurationState: function() {
        var me = this;
        me.setState({
            hasBeenConfigured: true
        });
    },
    openModal: function openModal() {
        var me = this;
        me.refs['DocumentListConfigurationModal' + me.props.oid].setModalState();
        me.setState({
            showModal: true
        });
    },
    hideModal: function hideModal() {
        var me = this;
        me.setState({
            showModal: false
        });
    },
    updateDocumentListGrid: function(e) {
        var me = this;
        var isMouseClick = false;
        var searchTerm = '';
        /* istanbul ignore next */ if ('buttons' in e.nativeEvent && e.nativeEvent.which === keyConstants.MOUSE_LEFT_BUTTON) {
            isMouseClick = true;
            searchTerm = this.searchInput.value;
        } else if (e.target) {
            searchTerm = e.target.value;
        }
        /* istanbul ignore if */ if (isMouseClick || e.nativeEvent.keyCode === keyConstants.ENTER_KEY) {
            me.updateGrid(searchTerm);
        }
    },
    getDocumentListWidth: function getDocumentListWidth() {
        var me = this;
        return me.refs['DocumentList' + me.props.oid].offsetWidth;
    },
    getSearchFilter: function getSearchFilter() {
        var me = this;
        return /*#__PURE__*/ React.createElement("div", null, /*#__PURE__*/ React.createElement(FormGroup, null, /*#__PURE__*/ React.createElement(InputGroup, null, /*#__PURE__*/ React.createElement(InputAddon, {
            id: 'InputAddon_' + me.props.oid,
            onClick: me.updateDocumentListGrid,
            style: cursorPointer,
            className: 'seicon-search',
            title: me.props.getToken(214653)
        }), /*#__PURE__*/ React.createElement("div", null, /*#__PURE__*/ React.createElement(FormControl, {
            inputRef: function(ref) {
                me.searchInput = ref;
            },
            onKeyPress: me.updateDocumentListGrid
        })))));
    },
    getGrid: function getGrid() {
        var me = this;
        var msgToken = me.props.getToken(306906).split('\\"').join('"');
        return /*#__PURE__*/ React.createElement(FeedbackMsg, {
            title: msgToken,
            iconColor: '#ACACAC',
            textBaseColor: 'gray',
            center: true,
            type: 2
        });
    },
    render: function render() {
        var me = this;
        return /*#__PURE__*/ React.createElement("div", {
            id: 'DocumentList' + me.props.oid,
            ref: 'DocumentList' + me.props.oid
        }, /*#__PURE__*/ React.createElement(DocumentListConfigurationModal, _object_spread({
            updateGridWithoutSearch: me.updateGridWithoutSearch,
            setConfigurationState: me.setConfigurationState,
            ref: 'DocumentListConfigurationModal' + me.props.oid,
            showModal: me.state.showModal,
            openModal: me.openModal,
            hideModal: me.hideModal
        }, me.props)), me.getGrid());
    }
});
module.exports = tokenManagerHOC(DocumentList);
module.exports.PureDocumentList = DocumentList;
__webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");


}),
"./src/document/components/DocumentListConfigurationModal.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.array.map.js */ "../node_modules/core-js/modules/es.array.map.js");
/* ESM import */var core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_map_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.object.keys.js */ "../node_modules/core-js/modules/es.object.keys.js");
/* ESM import */var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_array_sort_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.array.sort.js */ "../node_modules/core-js/modules/es.array.sort.js");
/* ESM import */var core_js_modules_es_array_sort_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_sort_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.parse-int.js */ "../node_modules/core-js/modules/es.parse-int.js");
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var core_js_modules_es_string_search_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! core-js/modules/es.string.search.js */ "../node_modules/core-js/modules/es.string.search.js");
/* ESM import */var core_js_modules_es_string_search_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_search_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! core-js/modules/es.regexp.exec.js */ "../node_modules/core-js/modules/es.regexp.exec.js");
/* ESM import */var core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_regexp_exec_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! core-js/modules/es.function.bind.js */ "../node_modules/core-js/modules/es.function.bind.js");
/* ESM import */var core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_bind_js__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var create_react_class__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! create-react-class */ "create-react-class");
/* ESM import */var create_react_class__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(create_react_class__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var reactor_src_Atomic_components_Orgs_Modal_Modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! reactor/src/Atomic/components/Orgs/Modal/Modal */ "../reactor/src/Atomic/components/Orgs/Modal/Modal.jsx");
/* ESM import */var reactor_src_Atomic_components_Orgs_Modal_Modal__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Orgs_Modal_Modal__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var reactor_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactor/src/Form/components/Mols/Text */ "../reactor/src/Form/components/Mols/Text.jsx");
/* ESM import */var reactor_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var reactor_src_Atomic_components_Atoms_Button_Button_jsx__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Button/Button.jsx */ "../reactor/src/Atomic/components/Atoms/Button/Button.jsx");
/* ESM import */var reactor_src_Atomic_components_Atoms_Button_Button_jsx__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Atoms_Button_Button_jsx__WEBPACK_IMPORTED_MODULE_11__);
/* ESM import */var reactor_src_Atomic_components_Atoms_ButtonRadio__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/ButtonRadio */ "../reactor/src/Atomic/components/Atoms/ButtonRadio.jsx");
/* ESM import */var reactor_src_Atomic_components_Atoms_ButtonRadio__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Atoms_ButtonRadio__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var reactor_src_Atomic_components_Mols_ButtonRadioGroup__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactor/src/Atomic/components/Mols/ButtonRadioGroup */ "../reactor/src/Atomic/components/Mols/ButtonRadioGroup.jsx");
/* ESM import */var reactor_src_Atomic_components_Mols_ButtonRadioGroup__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Mols_ButtonRadioGroup__WEBPACK_IMPORTED_MODULE_13__);
/* ESM import */var reactor_src_Form_components_Mols_Lookup__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactor/src/Form/components/Mols/Lookup */ "../reactor/src/Form/components/Mols/Lookup.jsx");
/* ESM import */var reactor_src_Form_components_Mols_Lookup__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Form_components_Mols_Lookup__WEBPACK_IMPORTED_MODULE_14__);
/* ESM import */var reactorCmps_src_document_collection_DocumentListCategoriesCollection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactorCmps/src/document/collection/DocumentListCategoriesCollection */ "./src/document/collection/DocumentListCategoriesCollection.js");
/* ESM import */var reactorCmps_src_document_collection_DocumentListCategoriesCollection__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(reactorCmps_src_document_collection_DocumentListCategoriesCollection__WEBPACK_IMPORTED_MODULE_15__);
/* ESM import */var reactor_src_Atomic_components_Atoms_Label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Label */ "../reactor/src/Atomic/components/Atoms/Label.jsx");
/* ESM import */var reactor_src_Atomic_components_Atoms_Label__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Atoms_Label__WEBPACK_IMPORTED_MODULE_16__);
/* ESM import */var reactor_src_Form_components_Mols_FormGroup__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! reactor/src/Form/components/Mols/FormGroup */ "../reactor/src/Form/components/Mols/FormGroup.jsx");
/* ESM import */var reactor_src_Form_components_Mols_FormGroup__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Form_components_Mols_FormGroup__WEBPACK_IMPORTED_MODULE_17__);
/* ESM import */var reactor_src_Form_components_Atoms_FormControl_FormControl__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! reactor/src/Form/components/Atoms/FormControl/FormControl */ "../reactor/src/Form/components/Atoms/FormControl/FormControl.js");
/* ESM import */var reactor_src_Form_components_Atoms_FormControl_FormControl__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Form_components_Atoms_FormControl_FormControl__WEBPACK_IMPORTED_MODULE_18__);
/* ESM import */var react_dom__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-dom */ "react-dom");
/* ESM import */var react_dom__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_19__);
/* ESM import */var when__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! when */ "when");
/* ESM import */var when__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(when__WEBPACK_IMPORTED_MODULE_20__);
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_21__);
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! Connector */ "Connector");
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(Connector__WEBPACK_IMPORTED_MODULE_22__);
/* ESM import */var reactor_src_Atomic_components_Layout_Row__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! reactor/src/Atomic/components/Layout/Row */ "../reactor/src/Atomic/components/Layout/Row.jsx");
/* ESM import */var reactor_src_Atomic_components_Layout_Row__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Layout_Row__WEBPACK_IMPORTED_MODULE_23__);
/* ESM import */var reactor_src_Atomic_components_Layout_Col__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! reactor/src/Atomic/components/Layout/Col */ "../reactor/src/Atomic/components/Layout/Col.jsx");
/* ESM import */var reactor_src_Atomic_components_Layout_Col__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Layout_Col__WEBPACK_IMPORTED_MODULE_24__);
/* ESM import */var _DocumentList_css__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./DocumentList.css */ "./src/document/components/DocumentList.css");




















// Arquivos auxiliares, do workspace


//	Conector para as requisições ajax

// Layout



// Collection das categorias apresentadas no Lookup
var CategoryCollection = new (reactorCmps_src_document_collection_DocumentListCategoriesCollection__WEBPACK_IMPORTED_MODULE_15___default())();
var rowStyle = {
    paddingBottom: '10px',
    paddingTop: '2px'
};
var divStyle = {
    display: 'block',
    paddingTop: '14px',
    fontSize: '11px',
    lineHeight: '11px',
    fontWeight: 400,
    textAlign: 'left',
    fontFamily: 'Arial, sans-serif',
    color: 'rgba(0, 0, 0, 0.6)'
};
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (create_react_class__WEBPACK_IMPORTED_MODULE_8___default()({
    displayName: 'DocumentList',
    getInitialState: function getInitialState() {
        var categories = null;
        var me = this;
        var savedSearchesData = [];
        var loadedCategories = [];
        var titleState = '';
        me.isCancelled = false;
        return {
            selected: 'category',
            categoryLookupValue: loadedCategories,
            savedSearchesData: savedSearchesData,
            modalHeight: 570,
            categories: categories,
            lookupValidationState: null,
            titleValidationState: null,
            title: titleState,
            loading: true
        };
    },
    componentWillUnmount: function() {
        var me = this;
        me.isCancelled = true;
    },
    setModalState: function setModalState() {
        var categories = null;
        var me = this;
        var allCategoriesPromise = me.getCategories();
        var loadedDataPromise = me.getLoadedData();
        var savedSearchesPromise = me.getSavedSearches();
        var savedSearchesData = [];
        var loadedCategories = [];
        var loadedSavedSearch = [];
        var titleState = '';
        me.isCancelled = false;
        allCategoriesPromise.then(function(value) {
            categories = value.map(function(obj) {
                return Object.keys(obj).sort().map(function(key) {
                    return obj[key];
                });
            });
        });
        loadedDataPromise.then(function(value) {
            var counter;
            var fgtype = value[0].hasOwnProperty('notConfigured') ? -1 : parseInt(value[0].fgtype, 10);
            if (value[0].notConfigured) {
                !me.isCancelled && me.setState({
                    loadedSavedSearch: loadedSavedSearch,
                    categoryLookupValue: loadedCategories,
                    title: titleState
                });
            }
            // Category / Categoria
            if (fgtype === 1) {
                for(counter = 0; counter < value.length; counter++){
                    loadedCategories.push({
                        oid: value[counter].cdcategory,
                        cdcategory: value[counter].cdcategory,
                        cattitle: value[counter].cattitle
                    });
                    titleState = value[counter].nmwidget;
                }
            }
            // Consulta Salva / Saved Search
            if (fgtype === 2) {
                loadedSavedSearch = value[0].oidsavedsearch;
                titleState = value[0].nmwidget;
            }
            !me.isCancelled && me.setState({
                loadedSavedSearch: loadedSavedSearch,
                categoryLookupValue: loadedCategories,
                title: titleState,
                fgtype: fgtype,
                selected: fgtype === 2 ? 'savedSearch' : 'category',
                modalHeight: fgtype === 2 ? 350 : 570
            });
        });
        savedSearchesPromise.then(function(value) {
            var counter;
            for(counter = 0; counter < value.length; counter++){
                savedSearchesData.push({
                    oidconsult: value[counter].oid,
                    nmconsult: value[counter].nmconsult
                });
            }
            !me.isCancelled && me.setState({
                savedSearchesData: savedSearchesData
            });
        });
        me.setState({
            selected: 'category',
            categoryLookupValue: loadedCategories,
            savedSearchesData: savedSearchesData,
            modalHeight: 570,
            categories: categories,
            lookupValidationState: null,
            titleValidationState: null,
            title: titleState
        });
    },
    getLoadedData: function getLoadedData() {
        var deferred = when__WEBPACK_IMPORTED_MODULE_20___default().defer();
        var me = this;
        Connector__WEBPACK_IMPORTED_MODULE_22___default().callBaseclass('document/request/documentListLoadedData.php', {
            dataType: 'json',
            data: {
                oid: me.props.oid,
                willGetLoadedData: true
            },
            complete: function complete(response) {
                deferred.resolve(response);
            }
        });
        return deferred.promise;
    },
    getSavedSearches: function getSavedSearches() {
        var deferred = when__WEBPACK_IMPORTED_MODULE_20___default().defer();
        Connector__WEBPACK_IMPORTED_MODULE_22___default().callBaseclass('document/request/documentListSavedSearches.php', {
            dataType: 'json',
            data: {
                cduser: WorkspaceInfo__WEBPACK_IMPORTED_MODULE_21___default().getCDUser(),
                willGetSavedSearches: true
            },
            complete: function complete(response) {
                deferred.resolve(response);
            }
        });
        return deferred.promise;
    },
    getCategories: function getCategories() {
        var deferred = when__WEBPACK_IMPORTED_MODULE_20___default().defer();
        CategoryCollection.fetch({
            cduser: WorkspaceInfo__WEBPACK_IMPORTED_MODULE_21___default().getCDUser()
        }).then(function(results) {
            deferred.resolve(results);
        }, deferred.reject);
        return deferred.promise;
    },
    getSavedSearchesOptions: function getSavedSearchesOptions() {
        var me = this;
        var savedSearches = me.state.savedSearchesData;
        var savedSearchesOptions = [];
        savedSearches.map(function(value, index) {
            savedSearchesOptions.push(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("option", {
                style: {
                    maxWidth: '100%'
                },
                key: index,
                value: value.oidconsult
            }, value.nmconsult));
        });
        return savedSearchesOptions;
    },
    handleChange: function handleChange(i, selected) {
        var me = this;
        var newModalHeight;
        if (selected === 'category') {
            newModalHeight = 570;
        } else {
            newModalHeight = 350;
        }
        me.setState({
            selected: selected,
            modalHeight: newModalHeight
        });
    },
    onCategoryLookupValueChange: function onCategoryLookupValueChange(value) {
        var me = this;
        me.setState({
            categoryLookupValue: value,
            lookupValidationState: null
        });
        return value;
    },
    onSavedSearchSelection: function onSavedSearchSelection(value) {
        var me = this;
        var select = react_dom__WEBPACK_IMPORTED_MODULE_19___default().findDOMNode(me.refs.savedSearch);
        me.setState({
            chosenSavedSearch: select.value,
            lookupValidationState: null
        });
        return value;
    },
    searchCategories: function searchCategories(term, page) {
        var deferred = when__WEBPACK_IMPORTED_MODULE_20___default().defer();
        CategoryCollection.search({
            searchTerm: term,
            cduser: WorkspaceInfo__WEBPACK_IMPORTED_MODULE_21___default().getCDUser()
        }, page).then(function() {
            deferred.resolve({
                data: CategoryCollection.getAttributes(),
                total: CategoryCollection.getState().size
            });
        }, deferred.reject);
        return deferred.promise;
    },
    getChoice: function getChoice() {
        var me = this;
        var savedSearchValue;
        if (me.state.loadedSavedSearch && !me.state.chosenSavedSearch) {
            savedSearchValue = me.state.loadedSavedSearch ? me.state.loadedSavedSearch : '';
        } else {
            savedSearchValue = me.state.chosenSavedSearch ? me.state.chosenSavedSearch : '';
        }
        if (me.state.selected === 'category') {
            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("div", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Form_components_Mols_Lookup__WEBPACK_IMPORTED_MODULE_14___default()), {
                id: 'Lookup' + me.props.oid,
                ref: 'categoryLookup',
                required: true,
                requiredIndicator: true,
                validationState: me.state.lookupValidationState,
                title: SE.t(105287),
                data: me.state.categories,
                textField: 'cattitle',
                valueField: 'cdcategory',
                searchFn: me.searchCategories,
                value: me.state.categoryLookupValue,
                onChange: function(value) {
                    return me.onCategoryLookupValueChange(value);
                }
            }));
        }
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("div", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("div", {
            className: 'labelContainer'
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Atomic_components_Atoms_Label__WEBPACK_IMPORTED_MODULE_16___default()), {
            text: SE.t(300586),
            required: true,
            requiredIndicator: true
        })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("div", {
            style: {
                maxWidth: '100%',
                width: '100%'
            }
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Form_components_Mols_FormGroup__WEBPACK_IMPORTED_MODULE_17___default()), {
            style: {
                maxWidth: '100%',
                width: '100%'
            }
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Form_components_Atoms_FormControl_FormControl__WEBPACK_IMPORTED_MODULE_18___default()), {
            id: 'savedSearchForm' + me.props.oid,
            ref: 'savedSearch',
            value: savedSearchValue.length > 0 ? savedSearchValue : 0,
            componentClass: 'select',
            placeholder: SE.t(300586),
            onChange: function(value) {
                return me.onSavedSearchSelection(value);
            },
            style: {
                maxWidth: '100%',
                width: '100%'
            }
        }, me.getSavedSearchesOptions()))));
    },
    saveConfigurationToDatabase: function saveConfigurationToDatabase(selected) {
        var me = this;
        var title = me.refs.TitleInput.props.value;
        var select = react_dom__WEBPACK_IMPORTED_MODULE_19___default().findDOMNode(me.refs.savedSearch);
        var data = [];
        if (selected === 'category') {
            data = {
                categoriesCodes: me.refs.categoryLookup.props.value.map(function(index) {
                    return index.cdcategory;
                }),
                addCategory: true,
                oidWidget: me.props.oid
            };
        } else {
            data = {
                addSavedSearch: true,
                oidWidget: me.props.oid,
                oidSavedSearch: select.value
            };
        }
        Connector__WEBPACK_IMPORTED_MODULE_22___default().callBaseclass('document/request/ajaxCrudDocumentList.php', {
            data: data,
            complete: function complete() {
                me.afterSaved(title);
            }
        });
    },
    afterSaved: function afterSaved(title) {
        var me = this;
        curl('MessageBar', function(MessageBar) {
            MessageBar.showMessageSuccess(SE.t(114547), {
                timeOut: 3000
            });
        });
        me.props.setTitle(title);
        me.props.serverSave();
        me.props.updateGridWithoutSearch();
        me.props.setConfigurationState();
    },
    saveConfiguration: function saveConfiguration() {
        var me = this;
        if (!me.requiredFieldsAreFilled()) {
            curl('MessageBar', function(MessageBar) {
                MessageBar.showMessageError(SE.t(113870), {
                    timeOut: 3000
                });
            });
            return;
        }
        me.saveConfigurationToDatabase(me.state.selected);
        me.props.hideModal();
    },
    requiredFieldsAreFilled: function requiredFieldsAreFilled() {
        var me = this;
        var titleValidationState = null;
        var lookupValidationState = null;
        var requiredFieldsAreFilled = true;
        if (!me.refs.TitleInput.props.value) {
            titleValidationState = 'error';
            requiredFieldsAreFilled = false;
        }
        if (me.state.selected === 'category' && (!me.refs.categoryLookup.props.value || me.refs.categoryLookup.props.value.length === 0)) {
            lookupValidationState = 'error';
            requiredFieldsAreFilled = false;
        }
        me.setState({
            titleValidationState: titleValidationState,
            lookupValidationState: lookupValidationState
        });
        return requiredFieldsAreFilled;
    },
    onTitleChange: function onTitleChange(newTitle) {
        var me = this;
        me.setState({
            title: newTitle,
            titleValidationState: null
        });
    },
    getBody: function getBody() {
        var me = this;
        var title = String(SE.t(301062)).toUpperCase();
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("div", {
            id: 'ModalBodyDiv' + me.props.oid
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Form_components_Mols_Text__WEBPACK_IMPORTED_MODULE_10___default()), {
            className: 'TextStyle',
            ref: 'TitleInput',
            title: SE.t(100380),
            value: me.state.title,
            required: true,
            requiredIndicator: true,
            cleanAll: true,
            validationState: me.state.titleValidationState,
            onChange: me.onTitleChange
        }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("div", {
            style: divStyle
        }, title), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Atomic_components_Layout_Row__WEBPACK_IMPORTED_MODULE_23___default()), {
            style: rowStyle,
            className: 'RowStyle'
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Atomic_components_Layout_Col__WEBPACK_IMPORTED_MODULE_24___default()), {
            xs: 8,
            sm: 8,
            md: 8,
            lg: 8
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Atomic_components_Mols_ButtonRadioGroup__WEBPACK_IMPORTED_MODULE_13___default()), {
            className: 'ButRadioGroup',
            value: me.state.selected,
            onChange: me.handleChange.bind(me, 1)
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Atomic_components_Atoms_ButtonRadio__WEBPACK_IMPORTED_MODULE_12___default()), {
            id: 'category'
        }, SE.t(102041)), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Atomic_components_Atoms_ButtonRadio__WEBPACK_IMPORTED_MODULE_12___default()), {
            id: 'savedSearch'
        }, SE.t(300586)))), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Atomic_components_Layout_Col__WEBPACK_IMPORTED_MODULE_24___default()), {
            xs: 6,
            sm: 6,
            md: 6,
            lg: 6
        })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement("span", null), me.getChoice());
    },
    render: function render() {
        var me = this;
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Atomic_components_Orgs_Modal_Modal__WEBPACK_IMPORTED_MODULE_9___default()), {
            key: 'Modal' + me.props.oid,
            width: 560,
            height: me.state.modalHeight,
            show: me.props.showModal,
            onClose: me.props.hideModal,
            ref: 'SelectionModal' + me.props.oid,
            hasCloseBtn: true,
            title: SE.t(301060),
            Body: me.getBody(),
            Footer: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_7___default().createElement((reactor_src_Atomic_components_Atoms_Button_Button_jsx__WEBPACK_IMPORTED_MODULE_11___default()), {
                onClick: me.saveConfiguration.bind(me, null),
                tooltip: SE.t(100026),
                id: 'close',
                text: SE.t(100026)
            })
        });
    }
}));


}),
"./src/document/components/DocumentListRow.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! core-js/modules/es.function.name.js */ "../node_modules/core-js/modules/es.function.name.js");
/* ESM import */var core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_function_name_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! core-js/modules/es.parse-int.js */ "../node_modules/core-js/modules/es.parse-int.js");
/* ESM import */var core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_parse_int_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var core_js_modules_es_string_substr_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! core-js/modules/es.string.substr.js */ "../node_modules/core-js/modules/es.string.substr.js");
/* ESM import */var core_js_modules_es_string_substr_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_string_substr_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! core-js/modules/es.date.to-string.js */ "../node_modules/core-js/modules/es.date.to-string.js");
/* ESM import */var core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_date_to_string_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var create_react_class__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! create-react-class */ "create-react-class");
/* ESM import */var create_react_class__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(create_react_class__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var reactor_src_FlexGrid_components_Layout_Row_RowPresentation_jsx__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! reactor/src/FlexGrid/components/Layout/Row/RowPresentation.jsx */ "../reactor/src/FlexGrid/components/Layout/Row/RowPresentation.jsx");
/* ESM import */var reactor_src_FlexGrid_components_Layout_Row_RowPresentation_jsx__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(reactor_src_FlexGrid_components_Layout_Row_RowPresentation_jsx__WEBPACK_IMPORTED_MODULE_6__);
/* ESM import */var reactor_src_FlexGrid_components_Layout_Col__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! reactor/src/FlexGrid/components/Layout/Col */ "../reactor/src/FlexGrid/components/Layout/Col.jsx");
/* ESM import */var reactor_src_FlexGrid_components_Layout_Col__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(reactor_src_FlexGrid_components_Layout_Col__WEBPACK_IMPORTED_MODULE_7__);
/* ESM import */var reactor_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Image/Image */ "../reactor/src/Atomic/components/Atoms/Image/Image.jsx");
/* ESM import */var reactor_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_8__);
/* ESM import */var reactor_src_FlexGrid_components_Mols_ToolbarMenu_ToolBarMenu__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! reactor/src/FlexGrid/components/Mols/ToolbarMenu/ToolBarMenu */ "../reactor/src/FlexGrid/components/Mols/ToolbarMenu/ToolBarMenu.jsx");
/* ESM import */var reactor_src_FlexGrid_components_Mols_ToolbarMenu_ToolBarMenu__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(reactor_src_FlexGrid_components_Mols_ToolbarMenu_ToolBarMenu__WEBPACK_IMPORTED_MODULE_9__);
/* ESM import */var reactor_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Button/Button */ "../reactor/src/Atomic/components/Atoms/Button/Button.jsx");
/* ESM import */var reactor_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_10__);
/* ESM import */var reactor_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Link/Link */ "../reactor/src/Atomic/components/Atoms/Link/Link.jsx");
/* ESM import */var reactor_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_11__);
/* ESM import */var Formatters_Date__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! Formatters/Date */ "Formatters/Date");
/* ESM import */var Formatters_Date__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(Formatters_Date__WEBPACK_IMPORTED_MODULE_12__);
/* ESM import */var reactor_src_Atomic_components_Orgs_OverlayTrigger__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! reactor/src/Atomic/components/Orgs/OverlayTrigger */ "../reactor/src/Atomic/components/Orgs/OverlayTrigger.jsx");
/* ESM import */var reactor_src_Atomic_components_Orgs_OverlayTrigger__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Orgs_OverlayTrigger__WEBPACK_IMPORTED_MODULE_13__);
/* ESM import */var reactor_src_Atomic_components_Atoms_Tooltip__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! reactor/src/Atomic/components/Atoms/Tooltip */ "../reactor/src/Atomic/components/Atoms/Tooltip.jsx");
/* ESM import */var reactor_src_Atomic_components_Atoms_Tooltip__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Atoms_Tooltip__WEBPACK_IMPORTED_MODULE_14__);
/* ESM import */var reactor_src_Atomic_components_Orgs_ModalFeedback_ModalFeedback__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeedback */ "../reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeedback.jsx");
/* ESM import */var reactor_src_Atomic_components_Orgs_ModalFeedback_ModalFeedback__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(reactor_src_Atomic_components_Orgs_ModalFeedback_ModalFeedback__WEBPACK_IMPORTED_MODULE_15__);
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! Utils */ "Utils");
/* ESM import */var Utils__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(Utils__WEBPACK_IMPORTED_MODULE_16__);
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! WorkspaceInfo */ "WorkspaceInfo");
/* ESM import */var WorkspaceInfo__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(WorkspaceInfo__WEBPACK_IMPORTED_MODULE_17__);
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! Connector */ "Connector");
/* ESM import */var Connector__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(Connector__WEBPACK_IMPORTED_MODULE_18__);
/* ESM import */var _DocumentList_css__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./DocumentList.css */ "./src/document/components/DocumentList.css");






// Componentes de layout














var baseURL = Utils__WEBPACK_IMPORTED_MODULE_16___default().getSystemUrl();
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (create_react_class__WEBPACK_IMPORTED_MODULE_5___default()({
    getInitialState: function getInitialState() {
        var me = this;
        var documentListWidth = me.props.getDocumentListWidth();
        return me.getDynamicComponents(documentListWidth);
    },
    componentWillUnmount: function componentWillUnmount() {
        var element;
        if (document.getElementById('jnlpiframe') !== null) {
            element = document.getElementById('jnlpiframe');
            element.parentNode.removeChild(element);
        }
    },
    resizeRow: function resizeRow() {
        var me = this;
        me.setState(me.getDynamicComponents(me.props.getDocumentListWidth()));
    },
    getDynamicComponents: function getDynamicComponents(widgetWidth) {
        var dynamicLayout;
        dynamicLayout = {
            toolbarButtonSize: 'medium',
            iconWidth: 1,
            textWidth: 7,
            iconSize: 'products55x55',
            titleMaxLength: 33,
            titleFontSize: 16,
            secondaryFontSize: 12,
            rowLeftMargin: 14,
            buttonsIn: false
        };
        if (widgetWidth < 400) {
            dynamicLayout = {
                toolbarButtonSize: 'small',
                iconWidth: 2,
                textWidth: 6,
                iconSize: 'products32x32',
                titleMaxLength: 14,
                titleFontSize: 14,
                secondaryFontSize: 10,
                rowLeftMargin: 0,
                buttonsIn: true
            };
        } else if (widgetWidth < 600) {
            dynamicLayout = {
                toolbarButtonSize: 'small',
                iconWidth: 2,
                textWidth: 8,
                iconSize: 'products32x32',
                titleMaxLength: 21,
                titleFontSize: 15,
                secondaryFontSize: 11,
                rowLeftMargin: -10,
                buttonsIn: true
            };
        } else if (widgetWidth > 1200) {
            dynamicLayout = {
                toolbarButtonSize: 'medium',
                iconWidth: 1,
                textWidth: 10,
                iconSize: 'products55x55',
                titleMaxLength: 116,
                titleFontSize: 17,
                secondaryFontSize: 13,
                rowLeftMargin: -30,
                buttonsIn: false
            };
        }
        dynamicLayout.showFeedbackModal = false;
        return dynamicLayout;
    },
    viewDocumentData: function viewDocumentData() {
        var me = this;
        var record = me.props.record.toJS();
        var params = {
            cddocument: record.cddocument,
            action: 4
        };
        Utils__WEBPACK_IMPORTED_MODULE_16___default().openStealthPopUp(baseURL + '/document/dc_document/document_ribbon.php', params, null, null, true);
    },
    jnlpIframeExists: function jnlpIframeExists() {
        return window.top.document.getElementById('jnlpiframe');
    },
    createJnlpIframe: function createJnlpIframe() {
        var jnlpIframe = window.top.document.createElement('iframe');
        jnlpIframe.id = 'jnlpiframe';
        jnlpIframe.name = 'jnlpiframe';
        jnlpIframe.style.display = 'none';
        window.top.document.getElementsByTagName('body')[0].appendChild(jnlpIframe);
    },
    checkFgStatus: function checkFgStatus() {
        var me = this;
        var record = me.props.record.toJS();
        switch(parseInt(record.fgstatusdoc, 10)){
            case 1:
                curl('MessageBar', function(MessageBar) {
                    MessageBar.showMessageError(SE.t(104865) + ' ' + SE.t(110816), {
                        timeOut: 3000
                    });
                });
                return false;
            case 4:
                me.openFeedbackModal();
                break;
            default:
                me.viewElectronicFile();
                break;
        }
        return true;
    },
    viewElectronicFile: function viewElectronicFile() {
        var me = this;
        var record = me.props.record.toJS();
        if (!me.jnlpIframeExists()) me.createJnlpIframe();
        if (record.filecount === 1) {
            window.open(baseURL + '/generic/gn_eletronicfile_view/1.1/view_eletronic_file.php?' + 'resource=document' + '&cdisosystem=21&action=4&cdfile=' + record.cdfile + '&cddocument=' + record.cddocument + '&view=1&permission=2', 'jnlpiframe');
        } else {
            window.open(baseURL + '/generic/gn_eletronicfile_view/1.1/view_eletronic_file.php?' + 'resource=document' + '&cdisosystem=21&action=4&cddocument=' + record.cddocument + '&view=1&permission=2', 'jnlpiframe');
        }
        return true;
    },
    downloadElectronicFiles: function downloadElectronicFiles() {
        var me = this;
        var record = me.props.record.toJS();
        var data = '';
        if (!me.jnlpIframeExists()) me.createJnlpIframe();
        Connector__WEBPACK_IMPORTED_MODULE_18___default().callBaseclass('document/request/ajaxOpenGnScreen.php?getDownloadParam=1', {
            data: data,
            complete: function complete(response) {
                window.open(baseURL + '/generic/gn_eletronicfile_view/1.1/view_eletronic_download.php?' + 'resource=document' + '&action=4&cdisosystem=21&cddocument=' + record.cddocument + '&saveas=1&mainframe=1&cdrevision=' + record.cdrevision + response.responseText + '&cduser=' + WorkspaceInfo__WEBPACK_IMPORTED_MODULE_17___default().getCDUser(), 'jnlpiframe');
            }
        });
    },
    getToolbar: function getToolbar() {
        var me = this;
        var renderedRow;
        if (me.state.buttonsIn) {
            renderedRow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_FlexGrid_components_Mols_ToolbarMenu_ToolBarMenu__WEBPACK_IMPORTED_MODULE_9___default()), {
                ref: 'ToolBarMenu' + me.props.oid,
                itemsIn: me.getToolbarItems()
            });
        } else {
            renderedRow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_FlexGrid_components_Mols_ToolbarMenu_ToolBarMenu__WEBPACK_IMPORTED_MODULE_9___default()), {
                ref: 'ToolBarMenu' + me.props.oid,
                itemsOut: me.getToolbarItems()
            });
        }
        return renderedRow;
    },
    getToolbarItems: function getToolbarItems() {
        var me = this;
        var record = me.props.record.toJS();
        var ToolbarItems = [];
        ToolbarItems.push(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_10___default()), {
            id: 'btn2_' + me.props.oid + '_' + record.counter,
            key: 'btn2_' + me.props.oid + '_' + record.counter,
            size: me.state.toolbarButtonSize,
            icon: 'seicon-file',
            onClick: me.viewDocumentData,
            tooltip: SE.t(104636)
        }));
        ToolbarItems.push(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_10___default()), {
            id: 'btn1_' + me.props.oid + '_' + record.counter,
            key: 'btn1_' + me.props.oid + '_' + record.counter,
            size: me.state.toolbarButtonSize,
            icon: 'seicon-eye',
            onClick: me.checkFgStatus,
            tooltip: SE.t(104634)
        }));
        ToolbarItems.push(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_Atomic_components_Atoms_Button_Button__WEBPACK_IMPORTED_MODULE_10___default()), {
            id: 'btn3_' + me.props.oid + '_' + record.counter,
            key: 'btn3_' + me.props.oid + '_' + record.counter,
            size: me.state.toolbarButtonSize,
            icon: 'seicon-download',
            onClick: me.downloadElectronicFiles,
            tooltip: SE.t(111257)
        }));
        return ToolbarItems;
    },
    getDocumentTitle: function getDocumentTitle() {
        var me = this;
        var record = me.props.record.toJS();
        if (record.title === null) {
            return '';
        }
        if (record.title && record.title.length > me.state.titleMaxLength) {
            return record.title.substr(0, me.state.titleMaxLength) + '...';
        }
        return record.title;
    },
    openFeedbackModal: function openFeedbackModal() {
        var me = this;
        me.setState({
            showFeedbackModal: true
        });
    },
    closeFeedbackModal: function closeFeedbackModal() {
        var me = this;
        me.setState({
            showFeedbackModal: false
        });
    },
    getStatusLabel: function getStatusLabel(style) {
        var me = this;
        var record = me.props.record.toJS();
        var nameIsNotSet = record.nmuserupd === null;
        var dateIsNotSet = record.dtupdate === null;
        var dateAux = new Date(record.dtupdate);
        var timezoneOffset = dateAux.getTimezoneOffset() >= -660 ? dateAux.getTimezoneOffset() : 0;
        var date = new (Formatters_Date__WEBPACK_IMPORTED_MODULE_12___default())().format(dateAux.getTime() + Math.abs(timezoneOffset * 60000));
        if (nameIsNotSet && dateIsNotSet) {
            return '';
        }
        if (nameIsNotSet) {
            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("div", {
                style: style
            }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("span", {
                className: 'greenLightText'
            }, SE.t(100239) + ' '), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("span", {
                className: 'updatedDate'
            }, date));
        }
        if (dateIsNotSet) {
            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("div", {
                style: style
            }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("span", {
                className: 'greenLightText'
            }, SE.t(102130) + ' '), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("span", {
                className: 'updatedUser'
            }, record.nmuserupd));
        }
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("div", {
            style: style
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("span", {
            className: 'greenLightText'
        }, SE.t(100239) + ' '), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("span", {
            className: 'updatedDate'
        }, date), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("span", {
            className: 'greenLightText'
        }, ' ' + SE.t(101179) + ' '), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("span", {
            className: 'updatedUser'
        }, record.nmuserupd));
    },
    render: function render() {
        var me = this, record = me.props.record.toJS();
        var documentNameLink = {
            fontSize: me.state.titleFontSize + 'px',
            fontWeight: 'normal',
            fontFamily: '"arial", sans-serif',
            marginLeft: me.state.rowLeftMargin + 'px'
        };
        var secondaryTextStyle = {
            marginLeft: me.state.rowLeftMargin + 'px',
            fontSize: me.state.secondaryFontSize + 'px'
        };
        var statusLabel = me.getStatusLabel(secondaryTextStyle);
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("div", null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_Atomic_components_Orgs_ModalFeedback_ModalFeedback__WEBPACK_IMPORTED_MODULE_15___default()), {
            show: me.state.showFeedbackModal,
            title: SE.t(105336),
            body: SE.t(105167),
            buttons: (reactor_src_Atomic_components_Orgs_ModalFeedback_ModalFeedback__WEBPACK_IMPORTED_MODULE_15___default().CONFIRM),
            onClose: me.closeFeedbackModal,
            onOk: me.viewElectronicFile,
            onCancel: me.closeFeedbackModal,
            type: 'info'
        }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_FlexGrid_components_Layout_Row_RowPresentation_jsx__WEBPACK_IMPORTED_MODULE_6___default()), {
            className: 'overrideBorder',
            ref: 'Row_' + record.counter + '_' + me.props.oid,
            toolbar: me.getToolbar()
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_FlexGrid_components_Layout_Col__WEBPACK_IMPORTED_MODULE_7___default()), {
            xs: me.state.iconWidth,
            sm: me.state.iconWidth,
            md: me.state.iconWidth,
            lg: me.state.iconWidth,
            style: {
                paddingBottom: 10,
                paddingTop: 16
            }
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_Atomic_components_Atoms_Image_Image__WEBPACK_IMPORTED_MODULE_8___default()), {
            src: baseURL + '/ui/desktop/lite/resources/images/' + me.state.iconSize + '/product-73.png'
        })), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_FlexGrid_components_Layout_Col__WEBPACK_IMPORTED_MODULE_7___default()), {
            xs: me.state.textWidth,
            sm: me.state.textWidth,
            md: me.state.textWidth,
            lg: me.state.textWidth,
            style: {
                paddingBottom: 10,
                paddingTop: statusLabel === '' ? 24 : 14
            }
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_Atomic_components_Orgs_OverlayTrigger__WEBPACK_IMPORTED_MODULE_13___default()), {
            placement: 'bottom',
            overlay: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_Atomic_components_Atoms_Tooltip__WEBPACK_IMPORTED_MODULE_14___default()), {
                id: 'Tooltip' + record.counter
            }, record.title)
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement((reactor_src_Atomic_components_Atoms_Link_Link__WEBPACK_IMPORTED_MODULE_11___default()), {
            onClick: me.checkFgStatus,
            style: documentNameLink
        }, me.getDocumentTitle())), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("div", {
            id: 'Row_' + record.counter + '_' + me.props.oid + 'div',
            style: secondaryTextStyle
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("span", {
            className: 'categorySpan'
        }, SE.t(102041) + ' - '), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().createElement("span", {
            className: 'categoryItalic'
        }, record.nmcategory)), statusLabel)));
    },
    displayName: "input"
}));
__webpack_require__(/*! reactorCmps/tokens/general */ "reactorCmps/tokens/general");


}),
"../reactor/src/Form/components/Mols/InputGroup/InputGroup.css": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js */ "../node_modules/style-loader/dist/runtime/styleDomAPI.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/insertBySelector.js */ "../node_modules/style-loader/dist/runtime/insertBySelector.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js */ "../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js */ "../node_modules/style-loader/dist/runtime/insertStyleElement.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js */ "../node_modules/style-loader/dist/runtime/styleTagTransform.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_InputGroup_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! !!../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./InputGroup.css */ "../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!../reactor/src/Form/components/Mols/InputGroup/InputGroup.css");

      
      
      
      
      
      
      
      
      

var options = {};

options.styleTagTransform = (_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default());
options.setAttributes = (_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default());
options.insert = _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default().bind(null, "head");
options.domAPI = (_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default());
options.insertStyleElement = (_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default());

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_InputGroup_css__WEBPACK_IMPORTED_MODULE_6__["default"], options);




       /* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_InputGroup_css__WEBPACK_IMPORTED_MODULE_6__["default"] && _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_InputGroup_css__WEBPACK_IMPORTED_MODULE_6__["default"].locals ? _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_InputGroup_css__WEBPACK_IMPORTED_MODULE_6__["default"].locals : undefined);


}),
"./src/document/components/DocumentList.css": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js */ "../node_modules/style-loader/dist/runtime/styleDomAPI.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/insertBySelector.js */ "../node_modules/style-loader/dist/runtime/insertBySelector.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js */ "../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js */ "../node_modules/style-loader/dist/runtime/insertStyleElement.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js */ "../node_modules/style-loader/dist/runtime/styleTagTransform.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_DocumentList_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! !!../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./DocumentList.css */ "../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[2].use[1]!./src/document/components/DocumentList.css");

      
      
      
      
      
      
      
      
      

var options = {};

options.styleTagTransform = (_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default());
options.setAttributes = (_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default());
options.insert = _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default().bind(null, "head");
options.domAPI = (_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default());
options.insertStyleElement = (_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default());

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_DocumentList_css__WEBPACK_IMPORTED_MODULE_6__["default"], options);




       /* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_DocumentList_css__WEBPACK_IMPORTED_MODULE_6__["default"] && _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_DocumentList_css__WEBPACK_IMPORTED_MODULE_6__["default"].locals ? _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_2_use_1_DocumentList_css__WEBPACK_IMPORTED_MODULE_6__["default"].locals : undefined);


}),
"../reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeeedback.scss": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
"use strict";
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/styleDomAPI.js */ "../node_modules/style-loader/dist/runtime/styleDomAPI.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/insertBySelector.js */ "../node_modules/style-loader/dist/runtime/insertBySelector.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2__);
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js */ "../node_modules/style-loader/dist/runtime/setAttributesWithoutAttributes.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3__);
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/insertStyleElement.js */ "../node_modules/style-loader/dist/runtime/insertStyleElement.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4__);
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! !../../../../../../node_modules/style-loader/dist/runtime/styleTagTransform.js */ "../node_modules/style-loader/dist/runtime/styleTagTransform.js");
/* ESM import */var _node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5__);
/* ESM import */var _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_3_use_1_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_3_use_2_ModalFeeedback_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! !!../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[2]!./ModalFeeedback.scss */ "../../../../../../node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[3].use[1]!../../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[2]!../reactor/src/Atomic/components/Orgs/ModalFeedback/ModalFeeedback.scss");

      
      
      
      
      
      
      
      
      

var options = {};

options.styleTagTransform = (_node_modules_style_loader_dist_runtime_styleTagTransform_js__WEBPACK_IMPORTED_MODULE_5___default());
options.setAttributes = (_node_modules_style_loader_dist_runtime_setAttributesWithoutAttributes_js__WEBPACK_IMPORTED_MODULE_3___default());
options.insert = _node_modules_style_loader_dist_runtime_insertBySelector_js__WEBPACK_IMPORTED_MODULE_2___default().bind(null, "head");
options.domAPI = (_node_modules_style_loader_dist_runtime_styleDomAPI_js__WEBPACK_IMPORTED_MODULE_1___default());
options.insertStyleElement = (_node_modules_style_loader_dist_runtime_insertStyleElement_js__WEBPACK_IMPORTED_MODULE_4___default());

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_3_use_1_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_3_use_2_ModalFeeedback_scss__WEBPACK_IMPORTED_MODULE_6__["default"], options);




       /* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_3_use_1_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_3_use_2_ModalFeeedback_scss__WEBPACK_IMPORTED_MODULE_6__["default"] && _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_3_use_1_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_3_use_2_ModalFeeedback_scss__WEBPACK_IMPORTED_MODULE_6__["default"].locals ? _node_modules_css_loader_dist_cjs_js_ruleSet_1_rules_3_use_1_node_modules_sass_loader_dist_cjs_js_ruleSet_1_rules_3_use_2_ModalFeeedback_scss__WEBPACK_IMPORTED_MODULE_6__["default"].locals : undefined);


}),
"Connector": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Connector__;

}),
"Formatters/Date": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Formatters_Date__;

}),
"SG2/collection/Factory": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_SG2_collection_Factory__;

}),
"Utils": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"WorkspaceInfo": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_WorkspaceInfo__;

}),
"create-react-class": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_create_react_class__;

}),
"watch1749037385376": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"react-dom": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

}),
"suite-storage": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_suite_storage__;

}),
"when": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_when__;

}),
"reactorCmps/tokens/general": (function (module) {
"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("./src/document/components/DocumentList.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=DocumentList.js.map