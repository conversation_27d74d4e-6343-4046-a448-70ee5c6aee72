{"version": 3, "file": "document/AutoTranslateModal.js", "sources": ["webpack://watch1749037385376/./src/document/components/AutoTranslation/Modal/index.js", "webpack://watch1749037385376/./src/document/components/AutoTranslation/constants/responseStatus.js", "webpack://watch1749037385376/./src/document/components/AutoTranslation/constants/steps.js", "webpack://watch1749037385376/./src/document/components/AutoTranslation/helper/action.js", "webpack://watch1749037385376/./src/document/utils/DocumentUtils.js", "webpack://watch1749037385376/./src/generic/helpers/ProductConstants.js"], "sourcesContent": ["import React, { useCallback, useEffect, useState } from 'react';\nimport propTypes from 'prop-types';\nimport noop from 'lodash/noop';\n\nimport Utils from 'Utils';\nimport WorkspaceInfo from 'WorkspaceInfo';\nimport tokenManagerHOC from 'reactor2/src/Atomic/components/Helpers/Language/tokenManagerHOC';\nimport { SIZE_SMALL } from 'reactor2/src/constants/sizeConstants';\nimport Modal from 'reactor2/src/Atomic/components/Orgs/Modal/Modal';\nimport Button from 'reactor2/src/Atomic/components/Atoms/Button/Button';\nimport CircularLoading from 'reactor2/src/Atomic/components/Helpers/CircularLoading/CircularLoading';\nimport ImageTextView from 'reactor2/src/Atomic/components/Mols/ImageTextView';\nimport H5 from 'reactor2/src/Atomic/components/Atoms/Headings/H5';\n\nimport steps from 'reactorCmps/src/document/components/AutoTranslation/constants/steps';\nimport { autoTranslateDocExists, autoTranslateDocument } from 'reactorCmps/src/document/components/AutoTranslation/helper/action';\n\nconst divLanguageStyle = {\n\tdisplay: 'flex',\n\talignItems: 'center',\n\tcolumnGap: '0.5em'\n};\n\nconst AutoTranslateModal = ({\n\tshow,\n\tdisabled,\n\tcdFile,\n\tfgLanguage,\n\tonClose,\n\tgetToken,\n\tcallbackSuccess\n}) => {\n\tconst [shouldShow, setShouldShow] = useState(show);\n\tconst [step, setStep] = useState(steps.DOCUMENT_LOADING);\n\tconst setTranslationLoad = useCallback(() => setStep(steps.DOCUMENT_TRANSLATION), []);\n\tconst callStepTranslation = useCallback(() => setStep(steps.DOCUMENT_NOT_EXISTS), []);\n\tconst closeModal = useCallback(() => {\n\t\tsetStep(steps.DOCUMENT_LOADING);\n\t\tsetShouldShow(false);\n\t\tonClose();\n\t}, [onClose]);\n\tconst callCallback = useCallback(async() => {\n\t\tawait callbackSuccess();\n\t\tcloseModal();\n\t}, [callbackSuccess, closeModal]);\n\n\tuseEffect(() => {\n\t\tlet isMounted = true;\n\n\t\t/* istanbul ignore else */\n\t\tif (show) {\n\t\t\tconst isDocumentExists = async() => autoTranslateDocExists({\n\t\t\t\tcdFile,\n\t\t\t\tfgLanguage,\n\t\t\t\tokCallback: callCallback,\n\t\t\t\tnotFoundCallback: callStepTranslation,\n\t\t\t\terrorCallback: closeModal\n\t\t\t});\n\n\t\t\t/* istanbul ignore else */\n\t\t\tif (!Boolean(fgLanguage)) {\n\t\t\t\treturn callCallback();\n\t\t\t}\n\n\t\t\t/* istanbul ignore else */\n\t\t\tif (isMounted) {\n\t\t\t\tisDocumentExists();\n\t\t\t}\n\t\t}\n\n\t\tsetShouldShow(show);\n\n\t\treturn () => {\n\t\t\tisMounted = false;\n\t\t};\n\t}, [callStepTranslation, cdFile, fgLanguage, closeModal, callCallback, show, setShouldShow]);\n\n\tconst translateDocument = async() => {\n\t\tsetTranslationLoad();\n\n\t\tautoTranslateDocument({ cdFile, fgLanguage, okCallback: callCallback, errorCallback: closeModal });\n\t};\n\n\t/* istanbul ignore next */\n\tconst [item] = WorkspaceInfo.getLanguageList().filter(language => parseInt(language.value, 10) === parseInt(fgLanguage, 10));\n\n\tconst getBody = () => {\n\t\tswitch (step) {\n\t\t\tcase steps.DOCUMENT_LOADING:\n\t\t\t\treturn <CircularLoading absolute/>;\n\t\t\tcase steps.DOCUMENT_TRANSLATION:\n\t\t\t\treturn <CircularLoading subtitle={getToken('315760')} absolute/>;\n\t\t\tcase steps.DOCUMENT_NOT_EXISTS:\n\t\t\tdefault:\n\t\t\t\treturn (\n\t\t\t\t\t<>\n\t\t\t\t\t\t<p>{(disabled === false) ? getToken('315763') : getToken('315902')}</p>\n\t\t\t\t\t\t<div style={divLanguageStyle}>\n\t\t\t\t\t\t\t<ImageTextView\n\t\t\t\t\t\t\t\ttooltip={getToken(item?.term)}\n\t\t\t\t\t\t\t\tsrc={`${Utils.getSystemUrl()}/ui/desktop/lite/resources/images/flagIcons/32x32/${item?.abrev}.png`}\n\t\t\t\t\t\t\t\tsize={SIZE_SMALL}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t<H5 value={getToken(item?.term)}/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</>\n\t\t\t\t);\n\t\t}\n\t};\n\n\tconst modalProps = {\n\t\tshow: shouldShow,\n\t\ttitle: getToken('315759'),\n\t\twidth: SIZE_SMALL,\n\t\tBody: getBody(),\n\t\tonClose: closeModal,\n\t\thideFooter: (step === steps.DOCUMENT_LOADING),\n\t\ttestSelector: 'AUTOTRANSLATEMODAL'\n\t};\n\n\t/* istanbul ignore else */\n\tif (disabled === false) {\n\t\tmodalProps.primaryButton = (\n\t\t\t<Button\n\t\t\t\ttext={getToken(104642)}\n\t\t\t\tonClick={translateDocument}\n\t\t\t\tdisabled={([steps.DOCUMENT_LOADING, steps.DOCUMENT_TRANSLATION].includes(step))}\n\t\t\t/>\n\t\t);\n\t}\n\n\treturn <Modal {...modalProps}/>;\n};\n\nAutoTranslateModal.propTypes = {\n\tshow: propTypes.bool,\n\tdisabled: propTypes.bool,\n\tcdFile: propTypes.oneOfType([propTypes.string, propTypes.number]),\n\tfgLanguage: propTypes.oneOfType([propTypes.string, propTypes.number]),\n\tcallbackSuccess: propTypes.func,\n\tonClose: propTypes.func\n};\n\nAutoTranslateModal.defaultProps = {\n\tshow: false,\n\tdisabled: true,\n\tonClose: noop\n};\n\nAutoTranslateModal.displayName = 'document/components/AutoTranslation/Modal';\n\nexport default tokenManagerHOC(AutoTranslateModal);", "export default Object.freeze({\n\tOK: 200,\n\tNOT_FOUND: 404\n});", "export default Object.freeze({\n\t// Verificando se existe uma tradução para o idioma selecionado\n\tDOCUMENT_LOADING: 1,\n\t// Quando não existir um tradução para o idioma selecionado\n\tDOCUMENT_NOT_EXISTS: 2,\n\t// Traduzindo o documento para o idioma selecionado\n\tDOCUMENT_TRANSLATION: 3\n});", "import Connector from 'Connector';\n\nimport responseStatus from 'reactorCmps/src/document/components/AutoTranslation/constants/responseStatus';\nimport { getToast, handleQueryParameters } from 'reactorCmps/src/document/utils/DocumentUtils';\n\nconst toast = getToast();\nconst documentRpc = '/document/dc_document/document_rpc.php';\n\nexport const autoTranslateDocExists = async({\n\tcdFile,\n\tfgLanguage,\n\tokCallback,\n\tnotFoundCallback,\n\terrorCallback\n}) => Connector.callBaseclass(documentRpc, {\n\tmethod: Connector.GET,\n\tdata: {\n\t\tcdfile: cdFile,\n\t\tfglanguage: fgLanguage,\n\t\tautoTranslateDocExists: true\n\t}\n}\n).then(response => {\n\tconst statusCode = parseInt(response, 10);\n\n\tif (statusCode === responseStatus.OK) {\n\t\treturn okCallback instanceof Function && okCallback();\n\t}\n\tif (statusCode === responseStatus.NOT_FOUND) {\n\t\treturn notFoundCallback instanceof Function && notFoundCallback();\n\t}\n\n\ttoast.error({ message: 216225 });\n\n\t(errorCallback instanceof Function) && errorCallback();\n});\n\nexport const autoTranslateDocument = async({\n\tcdFile,\n\tfgLanguage,\n\tokCallback,\n\terrorCallback\n}) => Connector.callBaseclass(documentRpc, {\n\tmethod: Connector.POST,\n\tdata: {\n\t\tcdfile: cdFile,\n\t\tfglanguage: fgLanguage,\n\t\tautoTranslateDocument: true\n\t}\n}\n).then(response => {\n\tconst statusCode = parseInt(response, 10);\n\n\tif (statusCode === responseStatus.OK) {\n\t\treturn okCallback instanceof Function && okCallback();\n\t}\n\n\ttoast.error({ message: 216225 });\n\n\t(errorCallback instanceof Function) && errorCallback();\n});\n\nexport const getFgLanguages = ({ setResults, ...params }) => Connector.callBaseclass(\n\t`/document/request/documentFgLanguages.php?${handleQueryParameters(params)}`, {\n\t\tmethod: Connector.GET\n\t}\n).then( response => {\n\tif (response.success && setResults instanceof Function) {\n\t\tsetResults(response.results);\n\t}\n});", "import Utils from 'Utils';\n\nimport ToasterSystem from 'reactor2/src/helpers/ToasterSystem/ToasterSystem';\nimport tokenManager from 'reactor2/src/Atomic/components/Helpers/Language/tokenManager';\nimport standaloneActions from 'reactor2/src/Common/components/ActionSystem/StandaloneActions';\n\nimport PRODUCT from 'reactorCmps/src/generic/helpers/ProductConstants';\n\n/**\n * Extract value of array\n * ```javascript\n * const selected = [\n *   { cdDocument: 1302, cdRevision: 1912 },\n *   { cdDocument: 4532, cdRevision: 1105 }\n * ];\n * console.log(extractProperty(selected, 'cdDocument')); // [ 1302, 4532 ]\n *  ```\n * @param {array} data\n * @param {string} propName\n * @returns {array} values extracted\n*/\nexport const extractProperty = (data, propName) => data.map(data => data[propName]);\n\n/**\n * Handle parameters for sending in request GET\n * ```javascript\n * const params = {\n *   cdDocument: 1302,\n *   cdRevision: 1912\n * };\n * console.log(handleQueryParameters(params)); // 'cdDocument=1302&cdRevision=1912'\n *  ```\n * @param {object} object\n * @returns {string} values handled\n*/\nexport const handleQueryParameters = object => new URLSearchParams(object).toString();\n\n/**\n * Generates a string with the given prefix\n * ```javascript\n * console.log(generateKey(`confirmAlert`); // 'confirmAlert-**********'\n *  ```\n * @param {string} preFixed\n * @returns {string} value generated\n*/\nexport const generateKey = preFixed => `${preFixed}-${Utils.generateHash()}`;\n\n/**\n * Returns a getToken provider for **pure javascript files**\n * ```javascript\n * const getToken = getTokenManager();\n * console.log(getToken(100357)) // Documento\n *  ```\n * @returns {tokenManager}\n*/\nexport const getTokenManager = () => tokenManager(PRODUCT.DOCUMENT);\n\n/**\n * Returns helpers to render components\n * @returns {object}\n*/\nexport const create = () => standaloneActions({ cdProduct: PRODUCT.DOCUMENT });\n\n/**\n * Returns a toast provider with standardized properties\n * ```javascript\n * const toast = getToast();\n * toast.success({ message: 100051 });\n *  ```\n * @returns {ToasterSystem}\n*/\nexport const getToast = () => {\n\n\tconst getToken = getTokenManager();\n\n\tconst info = ({ message }) => {\n\t\tToasterSystem.addNotification(ToasterSystem.INFO, getToken(message), null, 5000, true);\n\t};\n\n\tconst warning = ({ message }) => {\n\t\tToasterSystem.addNotification(ToasterSystem.WARNING, getToken(message), null, 5000, true);\n\t};\n\n\tconst error = ({ message }) => {\n\t\tToasterSystem.addNotification(ToasterSystem.ERROR, getToken(message), null, 5000, true);\n\t};\n\n\tconst success = ({ message }) => {\n\t\tToasterSystem.addNotification(ToasterSystem.SUCCESS, getToken(message), null, 5000, true);\n\t};\n\n\treturn { info, warning, error, success };\n};\n\nexport const jnlpIframeExists = () => {\n\treturn window.top.document.getElementById('jnlpiframe');\n};\n\nexport const removeJnlpIframeIfExists = () => {\n\n\tif (jnlpIframeExists()) {\n\n\t\tconst iframe = window.top.document.getElementById('jnlpiframe');\n\n\t\tiframe.parentNode.removeChild(iframe);\n\t}\n\n};\n\nexport const createJnlpIframe = () => {\n\n\tconst jnlpIframe = window.top.document.createElement('iframe');\n\n\tjnlpIframe.id = 'jnlpiframe';\n\tjnlpIframe.name = 'jnlpiframe';\n\tjnlpIframe.style.display = 'none';\n\n\twindow.top.document.getElementsByTagName('body')[0].appendChild(jnlpIframe);\n};\n\nexport const utf8decode = value => {\n\treturn value.replace(/[\\u00e0-\\u00ef][\\u0080-\\u00bf][\\u0080-\\u00bf]/g, c => {\n\t\treturn String.fromCharCode(((c.charCodeAt(0) & 0x0f) << 12) | ((c.charCodeAt(1) & 0x3f) << 6) | (c.charCodeAt(2) & 0x3f));\n\t}).replace(/[\\u00c0-\\u00df][\\u0080-\\u00bf]/g, c => {\n\t\treturn String.fromCharCode((c.charCodeAt(0) & 0x1f) << 6 | c.charCodeAt(1) & 0x3f);\n\t});\n};\n\nexport function createAndSubmitForm(params, {\n\ttarget = 'jnlpiframe',\n\tid = 'eformDownload',\n\taction\n}) {\n\tconst form = document.createElement('form');\n\n\tform.method = 'post';\n\tform.action = action;\n\tform.id = id;\n\tform.name = id;\n\tform.target = target;\n\n\tfor (const key in params) {\n\t\tconst input = document.createElement('input');\n\n\t\tinput.id = key;\n\t\tinput.name = key;\n\t\tinput.type = 'hidden';\n\t\tinput.value = params[key];\n\t\tform.appendChild(input);\n\t}\n\n\tdocument.body.appendChild(form);\n\tdocument.getElementById(id).submit();\n\tdocument.body.removeChild(form);\n}", "export default {\n\tAUDIT: 13,\n\tDOCUMENT: 21,\n\tSTRATEGY: 16,\n\tDRIVE: 307,\n\tOKR: 303,\n\tADMINISTRATION: 153,\n\tTRAINING: 26,\n\tFORM: 49\n};"], "names": ["React", "useCallback", "useEffect", "useState", "propTypes", "noop", "Utils", "WorkspaceInfo", "tokenManagerHOC", "SIZE_SMALL", "Modal", "<PERSON><PERSON>", "CircularLoading", "ImageTextView", "H5", "steps", "autoTranslateDocExists", "autoTranslateDocument", "divLanguageStyle", "AutoTranslateModal", "show", "disabled", "cdFile", "fgLanguage", "onClose", "getToken", "callbackSuccess", "_useState", "shouldShow", "setShouldShow", "_useState1", "step", "setStep", "setTranslationLoad", "callStepTranslation", "closeModal", "callCallback", "isMounted", "isDocumentExists", "Boolean", "translateDocument", "_WorkspaceInfo_getLanguageList_filter", "language", "parseInt", "item", "getBody", "modalProps", "Object", "Connector", "responseStatus", "getToast", "handleQueryParameters", "toast", "documentRpc", "okCallback", "notFoundCallback", "<PERSON><PERSON><PERSON><PERSON>", "response", "statusCode", "Function", "getFgLanguages", "setResults", "params", "ToasterSystem", "tokenManager", "standaloneActions", "PRODUCT", "extractProperty", "data", "propName", "object", "URLSearchParams", "<PERSON><PERSON>ey", "preFixed", "getTokenManager", "create", "info", "message", "warning", "error", "success", "jnlpIframeExists", "window", "removeJnlpIframeIfExists", "iframe", "createJnlpIframe", "jnlpIframe", "utf8decode", "value", "c", "String", "createAndSubmitForm", "param", "target", "id", "action", "form", "document", "key", "input"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuJmD;AAAA;AAAA;AAAA;AAAA;AAAA;AAvJa;AAC7B;AACJ;AAEL;AACgB;AACoD;AAC5B;AACE;AACI;AAC6B;AACvB;AACZ;AAEsB;AAC0C;AAElI,IAAMkB,mBAAmB;IACxB,SAAS;IACT,YAAY;IACZ,WAAW;AACZ;AAEA,IAAMC,qBAAqB;QAC1BC,aAAAA,MACAC,iBAAAA,UACAC,eAAAA,QACAC,mBAAAA,YACAC,gBAAAA,SACAC,iBAAAA,UACAC,wBAAAA;IAEA,IAAoCC,6BAAAA,+CAAQA,CAACP,WAAtCQ,aAA6BD,cAAjBE,gBAAiBF;IACpC,IAAwBG,8BAAAA,+CAAQA,CAACf,6HAAsB,OAAhDgB,OAAiBD,eAAXE,UAAWF;IACxB,IAAMG,qBAAqBhC,kDAAWA,CAAC;eAAM+B,QAAQjB,iIAA0B;OAAG,EAAE;IACpF,IAAMmB,sBAAsBjC,kDAAWA,CAAC;eAAM+B,QAAQjB,gIAAyB;OAAG,EAAE;IACpF,IAAMoB,aAAalC,kDAAWA,CAAC;QAC9B+B,QAAQjB,6HAAsB;QAC9Bc,cAAc;QACdL;IACD,GAAG;QAACA;KAAQ;IACZ,IAAMY,eAAenC,kDAAWA,mCAAC;;;;oBAChC;;wBAAMyB;;;oBAAN;oBACAS;;;;;;IACD,IAAG;QAACT;QAAiBS;KAAW;IAEhCjC,gDAASA,CAAC;QACT,IAAImC,YAAY;QAEhB,wBAAwB,GACxB,IAAIjB,MAAM;YACT,IAAMkB;2BAAmB;;wBAAWtB;;4BAAAA,0HAAsBA,CAAC;gCAC1DM,QAAAA;gCACAC,YAAAA;gCACA,YAAYa;gCACZ,kBAAkBF;gCAClB,eAAeC;4BAChB;;;;gCANMG;;;;YAQN,wBAAwB,GACxB,IAAI,CAACC,QAAQhB,aAAa;gBACzB,OAAOa;YACR;YAEA,wBAAwB,GACxB,IAAIC,WAAW;gBACdC;YACD;QACD;QAEAT,cAAcT;QAEd,OAAO;YACNiB,YAAY;QACb;IACD,GAAG;QAACH;QAAqBZ;QAAQC;QAAYY;QAAYC;QAAchB;QAAMS;KAAc;IAE3F,IAAMW;mBAAoB;;gBACzBP;gBAEAhB,yHAAqBA,CAAC;oBAAEK,QAAAA;oBAAQC,YAAAA;oBAAY,YAAYa;oBAAc,eAAeD;gBAAW;;;;;QACjG;wBAJMK;;;;IAMN,wBAAwB,GACxB,IAAeC,yDAAAA,oEAA6B,GAAG,MAAM,CAACC,SAAAA;eAAYC,SAASD,SAAS,KAAK,EAAE,QAAQC,SAASpB,YAAY;YAAjHqB,OAAQH;IAEf,IAAMI,UAAU;QACf,OAAQd;YACP,KAAKhB,6HAAsB;gBAC1B,qBAAO,2DAACH,gHAAeA;oBAAC;;YACzB,KAAKG,iIAA0B;gBAC9B,qBAAO,2DAACH,gHAAeA;oBAAC,UAAUa,SAAS;oBAAW;;YACvD,KAAKV,gIAAyB;YAC9B;gBACC,qBACC,wIACC,2DAAC,WAAIM,aAAa,QAASI,SAAS,YAAYA,SAAS,0BACzD,2DAAC;oBAAI,OAAOP;iCACX,2DAACL,2FAAaA;oBACb,SAASY,SAASmB,iBAAAA,2BAAAA,KAAM,IAAI;oBAC5B,KAAM,UAAEtC,yDAAkB,IAAG,sDAAgE,OAAZsC,iBAAAA,2BAAAA,KAAM,KAAK,EAAC;oBAC7F,MAAMnC,6EAAUA;kCAEjB,2DAACK,0FAAEA;oBAAC,OAAOW,SAASmB,iBAAAA,2BAAAA,KAAM,IAAI;;QAInC;IACD;IAEA,IAAME,aAAa;QAClB,MAAMlB;QACN,OAAOH,SAAS;QAChB,OAAOhB,6EAAUA;QACjB,MAAMoC;QACN,SAASV;QACT,YAAaJ,SAAShB,6HAAsB;QAC5C,cAAc;IACf;IAEA,wBAAwB,GACxB,IAAIM,aAAa,OAAO;QACvByB,WAAW,aAAa,iBACvB,2DAACnC,4FAAMA;YACN,MAAMc,SAAS;YACf,SAASe;YACT,UAAW;gBAACzB,6HAAsB;gBAAEA,iIAA0B;aAAC,CAAC,QAAQ,CAACgB;;IAG5E;IAEA,qBAAO,2DAACrB,yFAAKA,EAAKoC;AACnB;AAEA3B,mBAAmB,SAAS,GAAG;IAC9B,MAAMf,yDAAc;IACpB,UAAUA,yDAAc;IACxB,QAAQA,4DAAmB,CAAC;QAACA,2DAAgB;QAAEA,2DAAgB;KAAC;IAChE,YAAYA,4DAAmB,CAAC;QAACA,2DAAgB;QAAEA,2DAAgB;KAAC;IACpE,iBAAiBA,yDAAc;IAC/B,SAASA,yDAAc;AACxB;AAEAe,mBAAmB,YAAY,GAAG;IACjC,MAAM;IACN,UAAU;IACV,SAASd,oDAAIA;AACd;AAEAc,mBAAmB,WAAW,GAAG;AAEjC,6DAAeX,uGAAeA,CAACW,mBAAmBA,EAAC;;;;;;;;;;;ACpJhD;AAHH,6DAAe4B,OAAO,MAAM,CAAC;IAC5B,IAAI;IACJ,WAAW;AACZ,EAAE,EAAC;;;;;;;;;;;ACIA;AAPH,6DAAeA,OAAO,MAAM,CAAC;IAC5B,+DAA+D;IAC/D,kBAAkB;IAClB,2DAA2D;IAC3D,qBAAqB;IACrB,mDAAmD;IACnD,sBAAsB;AACvB,EAAE,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+DA;AAAA;AAtE+B;AAEwE;AACX;AAE/F,IAAMK,QAAQF,sFAAQA;AACtB,IAAMG,cAAc;AAEb,IAAMrC;eAAyB;YACrCM,QACAC,YACA+B,YACAC,kBACAC;;YAJAlC,eAAAA,QACAC,mBAAAA,YACA+B,mBAAAA,YACAC,yBAAAA,kBACAC,sBAAAA;YACKR;;gBAAAA,8DAAuB,CAACK,aAAa;oBAC1C,QAAQL,sDAAa;oBACrB,MAAM;wBACL,QAAQ1B;wBACR,YAAYC;wBACZ,wBAAwB;oBACzB;gBACD,GACE,IAAI,CAACkC,SAAAA;oBACN,IAAMC,aAAaf,SAASc,UAAU;oBAEtC,IAAIC,eAAeT,uHAAiB,EAAE;wBACrC,OAAOK,sBAAsBK,YAAYL;oBAC1C;oBACA,IAAII,eAAeT,8HAAwB,EAAE;wBAC5C,OAAOM,4BAA4BI,YAAYJ;oBAChD;oBAEAH,MAAM,KAAK,CAAC;wBAAE,SAAS;oBAAO;oBAE7BI,yBAAyBG,YAAaH;gBACxC;;;;oBA3BaxC;;;IA2BV;AAEI,IAAMC;eAAwB;YACpCK,QACAC,YACA+B,YACAE;;YAHAlC,eAAAA,QACAC,mBAAAA,YACA+B,mBAAAA,YACAE,sBAAAA;YACKR;;gBAAAA,8DAAuB,CAACK,aAAa;oBAC1C,QAAQL,uDAAc;oBACtB,MAAM;wBACL,QAAQ1B;wBACR,YAAYC;wBACZ,uBAAuB;oBACxB;gBACD,GACE,IAAI,CAACkC,SAAAA;oBACN,IAAMC,aAAaf,SAASc,UAAU;oBAEtC,IAAIC,eAAeT,uHAAiB,EAAE;wBACrC,OAAOK,sBAAsBK,YAAYL;oBAC1C;oBAEAF,MAAM,KAAK,CAAC;wBAAE,SAAS;oBAAO;oBAE7BI,yBAAyBG,YAAaH;gBACxC;;;;oBAvBavC;;;IAuBV;AAEI,IAAM2C,iBAAiB;QAAGC,oBAAAA,YAAeC;QAAfD;;WAA4Bb,8DAAuB,CAClF,6CAA0E,OAA9BG,mGAAqBA,CAACW,UAAW;QAC7E,QAAQd,sDAAa;IACtB,GACC,IAAI,CAAES,SAAAA;QACP,IAAIA,SAAS,OAAO,IAAII,sBAAsBF,UAAU;YACvDE,WAAWJ,SAAS,OAAO;QAC5B;IACD;EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoFF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA1JyB;AAEmD;AACW;AACM;AAEvB;AAEvE;;;;;;;;;;;;AAYA,GACO,IAAMU,kBAAkB,SAACC,MAAMC;WAAaD,KAAK,GAAG,CAACA,SAAAA;eAAQA,IAAI,CAACC,SAAS;;EAAE;AAEpF;;;;;;;;;;;AAWA,GACO,IAAMlB,wBAAwBmB,SAAAA;WAAU,IAAIC,gBAAgBD,QAAQ,QAAQ;EAAG;AAEtF;;;;;;;AAOA,GACO,IAAME,cAAcC,SAAAA;WAAa,GAAcnE,OAAZmE,UAAS,KAAwB,OAArBnE,0DAAkB;EAAK;AAE7E;;;;;;;AAOA,GACO,IAAMoE,kBAAkB;WAAMV,yGAAYA,CAACE,kGAAgB;EAAE;AAEpE;;;AAGA,GACO,IAAMS,SAAS;WAAMV,0GAAiBA,CAAC;QAAE,WAAWC,kGAAgB;IAAC;EAAG;AAE/E;;;;;;;AAOA,GACO,IAAMhB,WAAW;IAEvB,IAAMzB,WAAWiD;IAEjB,IAAME,OAAO;YAAGC,gBAAAA;QACfd,wGAA6B,CAACA,+FAAkB,EAAEtC,SAASoD,UAAU,MAAM,MAAM;IAClF;IAEA,IAAMC,UAAU;YAAGD,gBAAAA;QAClBd,wGAA6B,CAACA,kGAAqB,EAAEtC,SAASoD,UAAU,MAAM,MAAM;IACrF;IAEA,IAAME,QAAQ;YAAGF,gBAAAA;QAChBd,wGAA6B,CAACA,gGAAmB,EAAEtC,SAASoD,UAAU,MAAM,MAAM;IACnF;IAEA,IAAMG,UAAU;YAAGH,gBAAAA;QAClBd,wGAA6B,CAACA,kGAAqB,EAAEtC,SAASoD,UAAU,MAAM,MAAM;IACrF;IAEA,OAAO;QAAED,MAAAA;QAAME,SAAAA;QAASC,OAAAA;QAAOC,SAAAA;IAAQ;AACxC,EAAE;AAEK,IAAMC,mBAAmB;IAC/B,OAAOC,OAAO,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC;AAC3C,EAAE;AAEK,IAAMC,2BAA2B;IAEvC,IAAIF,oBAAoB;QAEvB,IAAMG,SAASF,OAAO,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC;QAElDE,OAAO,UAAU,CAAC,WAAW,CAACA;IAC/B;AAED,EAAE;AAEK,IAAMC,mBAAmB;IAE/B,IAAMC,aAAaJ,OAAO,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC;IAErDI,WAAW,EAAE,GAAG;IAChBA,WAAW,IAAI,GAAG;IAClBA,WAAW,KAAK,CAAC,OAAO,GAAG;IAE3BJ,OAAO,GAAG,CAAC,QAAQ,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAACI;AACjE,EAAE;AAEK,IAAMC,aAAaC,SAAAA;IACzB,OAAOA,MAAM,OAAO,CAAC,kDAAkDC,SAAAA;QACtE,OAAOC,OAAO,YAAY,CAAGD,CAAAA,EAAE,UAAU,CAAC,KAAK,IAAG,KAAM,KAAQA,CAAAA,EAAE,UAAU,CAAC,KAAK,IAAG,KAAM,IAAMA,EAAE,UAAU,CAAC,KAAK;IACpH,GAAG,OAAO,CAAC,mCAAmCA,SAAAA;QAC7C,OAAOC,OAAO,YAAY,CAAED,CAAAA,EAAE,UAAU,CAAC,KAAK,IAAG,KAAM,IAAIA,EAAE,UAAU,CAAC,KAAK;IAC9E;AACD,EAAE;AAEK,SAASE,oBAAoB7B,MAAM,EAAE8B,KAI3C;wBAJ2CA,MAC3CC,QAAAA,oCAAS,0CADkCD,MAE3CE,IAAAA,4BAAK,6BACLC,SAH2CH,MAG3CG;IAEA,IAAMC,OAAOC,SAAS,aAAa,CAAC;IAEpCD,KAAK,MAAM,GAAG;IACdA,KAAK,MAAM,GAAGD;IACdC,KAAK,EAAE,GAAGF;IACVE,KAAK,IAAI,GAAGF;IACZE,KAAK,MAAM,GAAGH;IAEd,IAAK,IAAMK,OAAOpC,OAAQ;QACzB,IAAMqC,QAAQF,SAAS,aAAa,CAAC;QAErCE,MAAM,EAAE,GAAGD;QACXC,MAAM,IAAI,GAAGD;QACbC,MAAM,IAAI,GAAG;QACbA,MAAM,KAAK,GAAGrC,MAAM,CAACoC,IAAI;QACzBF,KAAK,WAAW,CAACG;IAClB;IAEAF,SAAS,IAAI,CAAC,WAAW,CAACD;IAC1BC,SAAS,cAAc,CAACH,IAAI,MAAM;IAClCG,SAAS,IAAI,CAAC,WAAW,CAACD;AAC3B;;;;;;;;;AC1JA,6DAAe;IACd,OAAO;IACP,UAAU;IACV,UAAU;IACV,OAAO;IACP,KAAK;IACL,gBAAgB;IAChB,UAAU;IACV,MAAM;AACP,CAAC,EAAC"}