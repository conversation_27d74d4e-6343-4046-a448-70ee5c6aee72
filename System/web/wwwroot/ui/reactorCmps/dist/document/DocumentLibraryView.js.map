{"version": 3, "file": "document/DocumentLibraryView.js", "sources": ["webpack://watch1749037385376/../../document/Defaultframe/honda/entryCmps/DocumentHonda.jsx"], "sourcesContent": ["/* istanbul ignore file */\nimport React from \"react\";\n\nclass DC001 extends React.Component {\n\tconstructor(props) {\n\t\tsuper(props);\n\t\tthis.state = {\n\t\t\tComponent: undefined\n\t\t};\n\t}\n\n\tcomponentDidMount() {\n\t\tcurl([\n\t\t\t\"wwwroot/document/Defaultframe/honda/dist/DocumentLibraryView\",\n\t\t\t'tokens!wwwroot/document/Defaultframe/honda/dist/tokens/general'\n\t\t], (DocumentLibraryView) => {\n\t\t\tthis.setState({\n\t\t\t\tComponent: DocumentLibraryView.default\n\t\t\t});\n\t\t});\n\t}\n\n\trender() {\n\t\tif (!this.state.Component) return null;\n\n\t\treturn <this.state.Component {...this.props} />;\n\t}\n}\n\nexport default DC001;\n"], "names": ["React", "DC001", "props", "undefined", "componentDidMount", "curl", "DocumentLibraryView", "render"], "mappings": ";;;;;;;;;;AAAA,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACE;AAE1B,IAAMC,sBAAN;;cAAMA;aAAAA,MACOC,KAAK;gCADZD;;gBAEJ,kBAFIA;YAEEC;;QACN,MAAK,KAAK,GAAG;YACZ,WAAWC;QACZ;;;kBALIF;;YAQLG,KAAAA;mBAAAA,SAAAA;;gBACCC,KAAK;oBACJ;oBACA;iBACA,EAAE,SAACC;oBACH,MAAK,QAAQ,CAAC;wBACb,WAAWA,mBAAoB,WAAO;oBACvC;gBACD;YACD;;;YAEAC,KAAAA;mBAAAA,SAAAA;gBACC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO;gBAElC,qBAAO,2DAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAK,IAAI,CAAC,KAAK;YAC5C;;;WAvBKN;EAAcD,wDAAe;AA0BnC,6DAAeC,KAAKA,EAAC"}