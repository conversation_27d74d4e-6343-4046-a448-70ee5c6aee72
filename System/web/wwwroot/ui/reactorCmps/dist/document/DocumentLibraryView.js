"use strict";
define(["react","js!wwwroot/ui/reactorCmps/dist/watch1749037385376","tokens!reactorCmps/tokens/general","Utils"], function(__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_watch1749037385376__, __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__, __WEBPACK_EXTERNAL_MODULE_Utils__){
 return (self['webpackChunkwatch1749037385376'] = self['webpackChunkwatch1749037385376'] || []).push([["document/DocumentLibraryView"], {
"../../document/Defaultframe/honda/entryCmps/DocumentHonda.jsx": (function (__unused_webpack_module, __webpack_exports__, __webpack_require__) {
__webpack_require__.r(__webpack_exports__);
__webpack_require__.d(__webpack_exports__, {
  "default": function() { return __WEBPACK_DEFAULT_EXPORT__; }
});
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* ESM import */var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* istanbul ignore file */ function _assert_this_initialized(self) {
    if (self === void 0) {
        throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    }
    return self;
}
function _call_super(_this, derived, args) {
    derived = _get_prototype_of(derived);
    return _possible_constructor_return(_this, _is_native_reflect_construct() ? Reflect.construct(derived, args || [], _get_prototype_of(_this).constructor) : derived.apply(_this, args));
}
function _class_call_check(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
    }
}
function _defineProperties(target, props) {
    for(var i = 0; i < props.length; i++){
        var descriptor = props[i];
        descriptor.enumerable = descriptor.enumerable || false;
        descriptor.configurable = true;
        if ("value" in descriptor) descriptor.writable = true;
        Object.defineProperty(target, descriptor.key, descriptor);
    }
}
function _create_class(Constructor, protoProps, staticProps) {
    if (protoProps) _defineProperties(Constructor.prototype, protoProps);
    if (staticProps) _defineProperties(Constructor, staticProps);
    return Constructor;
}
function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}
function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}
function _possible_constructor_return(self, call) {
    if (call && (_type_of(call) === "object" || typeof call === "function")) {
        return call;
    }
    return _assert_this_initialized(self);
}
function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}
function _type_of(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
}
function _is_native_reflect_construct() {
    try {
        var result = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
    } catch (_) {}
    return (_is_native_reflect_construct = function() {
        return !!result;
    })();
}

var DC001 = /*#__PURE__*/ function(_React_Component) {
    "use strict";
    _inherits(DC001, _React_Component);
    function DC001(props) {
        _class_call_check(this, DC001);
        var _this;
        _this = _call_super(this, DC001, [
            props
        ]);
        _this.state = {
            Component: undefined
        };
        return _this;
    }
    _create_class(DC001, [
        {
            key: "componentDidMount",
            value: function componentDidMount() {
                var _this = this;
                curl([
                    "wwwroot/document/Defaultframe/honda/dist/DocumentLibraryView",
                    'tokens!wwwroot/document/Defaultframe/honda/dist/tokens/general'
                ], function(DocumentLibraryView) {
                    _this.setState({
                        Component: DocumentLibraryView["default"]
                    });
                });
            }
        },
        {
            key: "render",
            value: function render() {
                if (!this.state.Component) return null;
                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(this.state.Component, this.props);
            }
        }
    ]);
    return DC001;
}((react__WEBPACK_IMPORTED_MODULE_0___default().Component));
/* ESM default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DC001);


}),
"Utils": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_Utils__;

}),
"watch1749037385376": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_watch1749037385376__;

}),
"react": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

}),
"reactorCmps/tokens/general": (function (module) {
module.exports = __WEBPACK_EXTERNAL_MODULE_reactorCmps_tokens_general__;

}),

},function(__webpack_require__) {
var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId) }
var __webpack_exports__ = (__webpack_exec__("reactorCmps/tokens/general"), __webpack_exec__("../reactor2/src/helpers/publicPath.js"), __webpack_exec__("watch1749037385376"), __webpack_exec__("../../document/Defaultframe/honda/entryCmps/DocumentHonda.jsx"));
return __webpack_exports__;

}
])
});
//# sourceMappingURL=DocumentLibraryView.js.map